import React, { useEffect, useState,useRef } from 'react';
import { View, Text } from '@tarojs/components';
import { Button } from '@arco-design/mobile-react';
import { IconCircleChecked } from '@arco-design/mobile-react/esm/icon';
import YkNavBar from '@/components/ykNavBar';
import Taro from '@tarojs/taro';
import './result.less';
const BatchForwardResult: React.FC = () => {
  const [forwardCount, setForwardCount] = useState<number>(0);
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  useEffect(() => {
    // 获取URL参数中的转发条数
    const params = Taro.getCurrentInstance().router?.params;
    if (params?.count) {
      setForwardCount(parseInt(params.count));
    }
  }, []);

  const handleGoBack = () => {
    // 返回上一页
    Taro.navigateBack({
      delta: 1
    });
  };

  const handleGoHome = () => {
    // 返回首页或相册页面
    Taro.switchTab({
      url: '/pages/index/index'
    });
  };

  return (
    <View className="batch-forward-result">
      {platform !== "WX" &&<YkNavBar title="批量转发" />}
      
      {/* 主要内容区域 */}
      <View className="main-content">
        {/* 成功图标 */}
        <View>
          <IconCircleChecked className="success-icon"/>
        </View>
        
        {/* 转发成功文字 */}
        <Text className="success-text">转发成功</Text>
        
        {/* 描述文字 */}
        <Text className="success-desc">
          已成功转发 <Text className="highlight">{forwardCount}</Text> 条动态
        </Text>
      </View>
    </View>
  );
};

export default BatchForwardResult;
