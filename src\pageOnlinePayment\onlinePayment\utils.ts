/**
 * 在线支付模块工具函数
 * 统一管理与在线支付相关的工具函数
 */

import { BusinessType, MerchantType } from './types';

/**
 * 生成业务请求唯一标识
 * 格式：{业务类型}_{时间戳}_{商户类型}_{用户ID}_{6位随机数}
 * 示例：APPLYMENT_20250630173311_ENT_12345_123456
 *
 * @param businessType 业务类型
 * @param userId 用户ID
 * @param merchantType 商户类型（可选，某些业务可能不需要）
 * @param customSuffix 自定义后缀（可选，用于特殊业务场景）
 * @returns 生成的唯一业务编号
 */
export const generateBusinessRequestNo = (
  businessType: BusinessType,
  userId: number,
  merchantType?: MerchantType,
  customSuffix?: string
): string => {
  // 获取当前时间戳 (YYYYMMDDHHMMSS)
  const now = new Date();
  const timestamp = now.getFullYear().toString() +
    (now.getMonth() + 1).toString().padStart(2, '0') +
    now.getDate().toString().padStart(2, '0') +
    now.getHours().toString().padStart(2, '0') +
    now.getMinutes().toString().padStart(2, '0') +
    now.getSeconds().toString().padStart(2, '0');

  // 商户类型简码（如果提供）
  const typeCode = merchantType ? {
    [MerchantType.ENTERPRISE]: 'ENT',    // 企业
    [MerchantType.INDIVIDUAL]: 'IND',    // 个体工商户
    [MerchantType.PERSONAL]: 'PER',       // 小微商户
    [MerchantType.SELLER]: 'SEL'     // 个人卖家
  }[merchantType] || 'UNK' : '';

  // 生成6位随机数
  const randomNum = Math.floor(Math.random() * 900000) + 100000;

  // 组装完整的业务编号
  const parts = [businessType, timestamp];

  if (typeCode) {
    parts.push(typeCode);
  }

  parts.push(userId.toString());

  if (customSuffix) {
    parts.push(customSuffix);
  }

  parts.push(randomNum.toString());

  return parts.join('_');
};

/**
 * 生成商户进件申请唯一标识（向后兼容的便捷函数）
 * @param merchantType 商户类型
 * @param userId 用户ID
 * @returns 生成的商户进件申请编号
 */
export const generateOutRequestNo = (merchantType: MerchantType, userId: number): string => {
  return generateBusinessRequestNo(BusinessType.APPLYMENT, userId, merchantType);
};
