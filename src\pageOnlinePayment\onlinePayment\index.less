@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageOnlinePayment/onlinePayment/index"] {
  background-color: @background-color !important;

  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });

  .online-payment {
    min-height: 100vh;
    background-color: @background-color;

    .use-dark-mode-query({
    background-color: @dark-background-color;
  });

    .loading-container,
    .error-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 400px;

      .loading-text,
      .error-text {
        font-size: 16px;
        color: @sub-font-color;

        .use-dark-mode-query({
        color: @dark-sub-font-color;
      });
      }
    }

    .test-panel {
      background-color: @card-background-color;
      margin: 16px;
      padding: 16px;
      border-radius: 12px;
      border: 2px dashed @primary-color;

      .use-dark-mode-query({
      background-color: @dark-card-background-color;
      border-color: @dark-primary-color;
    });

      .test-panel-title {
        display: block;
        font-size: 16px;
        font-weight: 700;
        color: @primary-color;
        margin-bottom: 12px;
        text-align: center;

        .use-dark-mode-query({
        color: @dark-primary-color;
      });
      }

      .test-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 12px;

        .test-button {
          padding: 6px 12px;
          border-radius: 6px;
          color: @mask-content-color;
          font-size: 11px;
          font-weight: 600;
          cursor: pointer;
          white-space: nowrap;
          border: none;
          transition: all 0.2s ease;
          text-align: center;
          min-width: 80px;

          .use-dark-mode-query({
          color: @dark-mask-content-color;
        });

          &:hover {
            opacity: 0.8;
            transform: translateY(-1px);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }

      .test-panel-tip {
        display: block;
        font-size: 10px;
        color: @sub-info-font-color;
        text-align: left;
        line-height: 1.5;
        margin-bottom: 4px;
        padding: 4px 8px;
        background-color: @lighter-line-color;
        border-radius: 4px;

        .use-dark-mode-query({
        color: @dark-sub-info-font-color;
        background-color: @dark-lighter-line-color;
      });
      }
    }

    .content {
      padding: 16px;

      .custom-steps {
        // 使用深度选择器替代 :global，避免影响其他模块
        .arco-steps-item-title {
          font-size: 14px;
          font-weight: 500;
          color: @font-color;

          .use-dark-mode-query({
          color: @dark-font-color;
        });
        }

        .arco-steps-item-process .arco-steps-item-title {
          color: @primary-color;
          font-weight: 600;

          .use-dark-mode-query({
          color: @dark-primary-color;
        });
        }

        .arco-steps-item-finish .arco-steps-item-icon {
          background-color: @success-color;
          border-color: @success-color;

          .use-dark-mode-query({
          background-color: @dark-success-color;
          border-color: @dark-success-color;
        });
        }

        .step-desc {
          display: block;
          margin: 4px 0;
          font-size: 12px;
          color: @sub-info-font-color;
          line-height: 1.5;

          .use-dark-mode-query({
          color: @dark-sub-info-font-color;
        });
        }

        .step-action {
          display: block;
          margin-top: 15px;
          font-weight: 500;
          font-size: 14px;
          cursor: pointer;

          &.blue {
            color: @primary-color;

            .use-dark-mode-query({
            color: @dark-primary-color;
          });
          }

          &.green {
            color: @success-color;

            .use-dark-mode-query({
            color: @dark-success-color;
          });
          }

          &:hover {
            opacity: 0.8;
          }
        }

        .reject-reasons {
          margin: 8px 0;

          .reject-title {
            display: block;
            font-size: 12px;
            color: @danger-color;
            font-weight: 500;
            margin-bottom: 8px;

            .use-dark-mode-query({
            color: @dark-danger-color;
          });
          }

          .reject-item {
            display: block;
            font-size: 12px;
            color: @font-color;
            line-height: 1.6;
            margin-bottom: 4px;

            .use-dark-mode-query({
            color: @dark-font-color;
          });
          }
        }

        .review-tips {
          margin: 8px 0;

          .review-tips-title {
            display: block;
            font-size: 12px;
            color: @warning-color;
            font-weight: 500;
            margin-bottom: 8px;

            .use-dark-mode-query({
            color: @dark-warning-color;
          });
          }

          .review-tip-item {
            display: block;
            font-size: 12px;
            color: @font-color;
            line-height: 1.6;
            margin-bottom: 4px;

            .use-dark-mode-query({
            color: @dark-font-color;
          });
          }
        }
      }
    }
  }
}
