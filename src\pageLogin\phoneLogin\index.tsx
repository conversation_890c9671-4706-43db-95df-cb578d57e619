import { View, Text } from '@tarojs/components'
import { Button, Image, Input, Checkbox } from '@arco-design/mobile-react'
import './index.less'
import Taro from '@tarojs/taro'
import YkNavBar from '@/components/ykNavBar'
import UserAgreementPopup from '@/components/UserAgreementPopup'
import { useState, useEffect } from 'react';
import { login, getUserInfo } from '@/utils/api/common/common_user';
import { md5 } from 'js-md5'
import { toast } from "@/utils/yk-common";
export default function NewLogin() {
  const [visible, setVisible] = useState(false);
  const [check, setCheck] = useState(false);
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [isShowKeyBoard, setIsShowKeyBoard] = useState(false);

  useEffect(() => {
    window.webShowKeyBoard = showKeyBoard;
    window.webHideKeyBoard = hideKeyBoard;
    return () => {
      delete window.webShowKeyBoard;
      delete window.webHideKeyBoard;
    }
  }, []);

  const handlePwdLoginCheck = () => {
    if (!check) {
      setVisible(true);
      return
    }
    handlePwdLogin();
  }

  const showKeyBoard = () => {
    setIsShowKeyBoard(true);
  }
  const hideKeyBoard = () => {
    setIsShowKeyBoard(false);
  }

  const handlePwdLogin = () => {
    let data: any = {
      type: 3
    }
    data.mobile = phone;
    data.password = password;
    // data.channel_name = 'H5';


    login(data).then((res: any) => {

      if (res && res.code == 0) {
        Taro.setStorageSync('userInfo', res.data)
        toast("info", {
        content: "登录成功",
        duration: 2000
      })
        getUserInfo().then((res2: any) => {
          if (res2 && res2.code == 0) {
            //合并用户信息
            Taro.setStorageSync('userInfo', {
              ...res2.data,
              ...res.data
            })
            Taro.reLaunch({ url: '/pages/index/index' })
          }
        })
      } else {
        toast("info", {
        content: res.msg,
        duration: 2000
      })
      }
    })

  }


  return (
    <View className='new-login'>
      <YkNavBar title=''/>
      <View className='logo-container'>
        <Image className='logo' src={require('../../assets/images/login/logo.png')} bottomOverlap={null} />
        <Text className='app-name'>账号密码登录</Text>
      </View>

      <View className='input-container'>
        <Input className='input-field' clearable placeholder='请输入手机号/用户名/账号ID' border='none' onChange={(_, value) => { setPhone(value) }} maxLength={30}/>
        <Input className='input-field' clearable placeholder='请输入密码' type='password' border='none' onChange={(_, value) => { setPassword(value) }} maxLength={30}/>
      </View>

      <View className='action-buttons'>
        <Button className='forgot-password' needActive={false} size='small' type='ghost'  onClick={() => {Taro.navigateTo({url: '/pageLogin/resetPassword/index'})}} >忘记密码？</Button>
        <Button className='verification-code' needActive={false} size='small' type='ghost' onClick={() => {Taro.navigateTo({url: '/pageLogin/codeLogin/index'})}}>验证码登录</Button>
      </View>

      <View className='login-buttons'>
        <Button className='login-button' onClick={handlePwdLoginCheck}>登录</Button>
      </View>

{!isShowKeyBoard && (
      <View className='agreement'>
      <Checkbox checked={check} onChange={() => { setCheck(!check) }} value={''}></Checkbox>
      <Text>已阅读并同意</Text>
        <Text className='link' onClick={() => {Taro.navigateTo({url: `/pageSetting/webView/index?url=${USER_AGREEMENT}&name=用户协议`})}}>《用户协议》</Text>
        <Text>和</Text>
        <Text className='link' onClick={() => {Taro.navigateTo({url: `/pageSetting/webView/index?url=${PRIVACY_POLICY}&name=隐私政策`})}}>《隐私政策》</Text>
      </View>
      )}

      <UserAgreementPopup
        visible={visible}
        onClose={() => setVisible(false)}
        onConfirm={handlePwdLogin}
        onSetCheck={setCheck}
      />
    </View>
  )
} 