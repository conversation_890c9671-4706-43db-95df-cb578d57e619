import { View, Text, Image } from "@tarojs/components";
import React, { useState,useRef,useEffect } from "react";
import Taro, { useDidShow } from "@tarojs/taro";
import { Input, Textarea, Checkbox } from "@arco-design/mobile-react";
import YkNavBar from "@/components/ykNavBar/index";
import { toast } from "@/utils/yk-common";
import "./index.less";
import { IconDown } from "@arco-iconbox/react-yk-arco";
export default function EditContent() {
  const [showPage, setShowPage] = useState(false);
  const [contentType, setContentType] = useState(0);// 0 保留文案 // 1: 关键字替换, 2: 全文替换, 3: 追加文案
  const [appendType, setAppendType] = useState(1); // 1: 追加到文首, 2: 追加到文末
  const [content, setContent] = useState('');
  const [replaceList, setReplaceList] = useState([
    { oldText: '', newText: '' },
    { oldText: '', newText: '' },
    { oldText: '', newText: '' }
  ]);
  // const [dynamicList, setDynamicList] = useState<any[]>([]);
  const dynamicListRef = useRef<any[]>([]);
  const appendTypeRef = useRef(1);
  const replaceListRef = useRef([
    { oldText: '', newText: '' },
    { oldText: '', newText: '' },
    { oldText: '', newText: '' }
  ]);
  const contentRef = useRef('');
  const [paddingBottom, setPaddingBottom] = useState('0px');
  const [platform,setPlatform] = useState<string>("H5");

  
  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  useDidShow(() => {
    // 检测平台类型设置底部边距
    const systemInfo = Taro.getSystemInfoSync();
    const isIos = systemInfo.platform === 'ios';
    const isAndroid = systemInfo.platform === 'android';
    
    if (isIos) {
      setPaddingBottom('34px');
    } else if (isAndroid) {
      setPaddingBottom('0px');
    } 

    // 获取传递的数据
    const selectedDynamics = Taro.getStorageSync('selectedDynamics') || [];
    // setDynamicList(selectedDynamics);
    dynamicListRef.current = selectedDynamics;

    // 恢复编辑状态
    const editState = Taro.getStorageSync('editContentState');
    if (editState) {
      setContentType(editState.contentCurType || 0);
      setAppendType(editState.appendType || 1);
      appendTypeRef.current = editState.appendType || 1;
      setReplaceList(editState.replaceList || [
        { oldText: '', newText: '' },
        { oldText: '', newText: '' },
        { oldText: '', newText: '' }
      ]);
      replaceListRef.current = editState.replaceList || [
        { oldText: '', newText: '' },
        { oldText: '', newText: '' },
        { oldText: '', newText: '' }
      ];
      setContent(editState.content || '');
      contentRef.current = editState.content || '';
      console.log('恢复编辑内容状态:', editState);
    }

    setTimeout(() => {
      setShowPage(true);
    }, 100);
  });

  const changeType = (type: number) => {
    setContentType(type);
  };

  const changeAppendType = (type: number) => {
    setAppendType(type);
    appendTypeRef.current = type;  
  };

  const handleReplaceChange = (index: number, field: 'oldText' | 'newText', value: string) => {
    const newList = [...replaceList];
    newList[index][field] = value;
    setReplaceList(newList);
    replaceListRef.current = newList;
  };

  const showMoreReplace = () => {
    // 如果已经达到最大数量（9条），则不再添加
    if (replaceList.length >= 9) return;

    // 每次添加3条
    const newItems = [];
    for (let i = 0; i < 3; i++) {
      newItems.push({ oldText: '', newText: '' });
    }

    const newList = [...replaceList, ...newItems];
    // 确保不超过9条
    const finalList = newList.slice(0, 9);
    setReplaceList(finalList);
    replaceListRef.current = finalList;
  };

  const next = () => {
    if (contentType === 0) {
      dynamicListRef.current = dynamicListRef.current;
      setAppendType(1);
      appendTypeRef.current = 1;
      setReplaceList([
        { oldText: '', newText: '' },
        { oldText: '', newText: '' },
        { oldText: '', newText: '' }
      ]);
      replaceListRef.current = [
        { oldText: '', newText: '' },
        { oldText: '', newText: '' },
        { oldText: '', newText: '' }
      ];
      setContent('');
      contentRef.current = '';
    }
    if (contentType === 1) {
      // 关键字替换
      const validReplaces = replaceList.filter(item => item.oldText.trim() && item.newText.trim());
      if (validReplaces.length === 0) {
        toast("info", {
          content: "请至少填写一组关键字替换",
          duration: 2000
        });
        return;
      }

      setAppendType(1);
      appendTypeRef.current = 1;
      setContent('');
      contentRef.current = '';

      const updatedList = dynamicListRef.current.map(item => {
        let newContent = item.content || '';
        validReplaces.forEach(replace => {
          const regex = new RegExp(replace.oldText, 'g');
          newContent = newContent.replace(regex, replace.newText);
        });
        return {
          ...item,
          content: newContent
        };
      });
      dynamicListRef.current = updatedList;
      console.log('updatedList', updatedList);
    } else if (contentType === 2) {
      // 全文替换
      if (!content.trim()) {
        toast("info", {
          content: "请输入替换内容",
          duration: 2000
        });
        return;
      }

      setAppendType(1);
      appendTypeRef.current = 1;
      setReplaceList([
        { oldText: '', newText: '' },
        { oldText: '', newText: '' },
        { oldText: '', newText: '' }
      ]);
      replaceListRef.current = [
        { oldText: '', newText: '' },
        { oldText: '', newText: '' },
        { oldText: '', newText: '' }
      ];

      const updatedList = dynamicListRef.current.map(item => ({
        ...item,
        content: content
      }));
      dynamicListRef.current = updatedList;
      console.log('updatedList', updatedList);
    } else if (contentType === 3) {
      // 追加文案
      if (!content.trim()) {
        toast("info", {
          content: "请输入追加内容",
          duration: 2000
        });
        return;
      }

      setReplaceList([
        { oldText: '', newText: '' },
        { oldText: '', newText: '' },
        { oldText: '', newText: '' }
      ]);
      replaceListRef.current = [
        { oldText: '', newText: '' },
        { oldText: '', newText: '' },
        { oldText: '', newText: '' }
      ];

      const updatedList = dynamicListRef.current.map(item => {
        const originalContent = item.content || '';
        const newContent = appendType === 1 
          ? content + originalContent 
          : originalContent + content;
        return {
          ...item,
          content: newContent
        };
      });
      dynamicListRef.current = updatedList;
      console.log('updatedList', updatedList);
    }


    // 保存修改后的数据
    Taro.setStorageSync('selectedDynamics', dynamicListRef.current);

    // 保存当前的编辑状态，供下次进入时使用
    const editState = {
      contentCurType: contentType,
      appendType: appendTypeRef.current,
      replaceList: replaceListRef.current,
      content: contentRef.current
    };
    console.log('editState', editState);
    Taro.setStorageSync('editContentState', editState);

    toast("info", {
      content: "修改成功",
      duration: 2000
    });

    setTimeout(() => {
      Taro.navigateBack({
        delta: 1
      });
    }, 1000);
  };

  const getNavigateBack = () => {
    Taro.navigateBack({
      delta: 1
    });
  };

  return (
    <View className={`edit-content-page ${showPage ? '' : 'hidden-style'}`}>
      {platform !== "WX" &&<YkNavBar title="修改文案" />}
      
      <View className="box-content">
        <View className="type">
          <View className="type-item0" onClick={() => changeType(0)}>
            <Text className={contentType === 0 ? 'type-item0-textA' : 'type-item0-text'}>
              保留文案
            </Text>
          </View>
          <View 
            className="type-item1" 
            onClick={() => changeType(1)}
          >
            <Text className={contentType === 1 ? 'type-item1-textA' : 'type-item1-text'}>
              关键字替换
            </Text>
          </View>
          <View 
            className="type-item2" 
            onClick={() => changeType(2)}
          >
            <Text className={contentType === 2 ? 'type-item2-textA' : 'type-item2-text'}>
              全文替换
            </Text>
          </View>
          <View 
            className="type-item3" 
            onClick={() => changeType(3)}
          >
            <Text className={contentType === 3 ? 'type-item3-textA' : 'type-item3-text'}>
              追加文案
            </Text>
          </View>
        </View>

        {contentType === 1 && (
          <View className="item1">
            {replaceList.map((item, index) => (
              <View key={index} className="item1-item">
                <Input
                 border="none"
                  className="item1-item-oldInput"
                  placeholder="原文字"
                  value={item.oldText}
                  onChange={(_, value) => handleReplaceChange(index, 'oldText', value)}
                />
                <Text className="item1-item-text">替换为</Text>
                <Input
                 border="none"
                  className="item1-item-newInput"
                  placeholder="新文字"
                  value={item.newText}
                  onChange={(_, value) => handleReplaceChange(index, 'newText', value)}
                />
              </View>
            ))}
            
            {replaceList.length < 9 && (
              <View className="more" onClick={showMoreReplace}>
                <Text className="more-text">展开更多</Text>
                {/* <Image
                  className="more-img"
                  src={downArrowIcon}
                /> */}
                <IconDown className="more-img" />
              </View>
            )}
          </View>
        )}

        {contentType === 2 && (
          <View className="item2">
            <View className="item2-title">
              <Text>输入文案</Text>
            </View>
            <View className="item2-textarea">
              <Textarea
               border="none"
                className="item2-textarea-text"
                placeholder="已选中商品的文案将被你在此处输入的文案全文替换"
                value={content}
                onChange={(_, value) => {
                  setContent(value);
                  contentRef.current = value;
                }}
                rows={4}
              />
            </View>
          </View>
        )}

        {contentType === 3 && (
          <View className="item3">
            <View className="item3-title">
              <Text>追加文案</Text>
            </View>
            <View className="item3-content">
              <View
                className="item3-content-item1"
              >
                <Checkbox
                  className="item3-content-item1-checkbox"
                  checked={appendType === 1}
                  onChange={() => changeAppendType(1)}
                />
                <Text className="item3-content-item1-text">追加到文首</Text>
              </View>
              <View
                className="item3-content-item2"
              >
                <Checkbox
                  className="item3-content-item2-checkbox"
                  checked={appendType === 2}
                  onChange={() => changeAppendType(2)}
                />
                <Text className="item3-content-item2-text">追加到文末</Text>
              </View>
            </View>
            <View className="item3-textarea">
              <Textarea
              border="none"
                className="item3-textarea-text"
                placeholder={appendType === 1 ? "您在此处输入的文案将会被批量追加到已选中商品的文案的文首" : "您在此处输入的文案将会被批量追加到已选中商品的文案的文末"}
                value={content}
                onChange={(_, value) => 
                {
                  setContent(value);
                  contentRef.current = value;
                }
                }
                rows={4}
              />
            </View>
          </View>
        )}
      </View>

      <View className="holder"></View>

      <View className="footerbtn" style={{ paddingBottom }}>
        <View className="footerbtn-btn clickBg" onClick={next}>
          <Text>确认修改</Text>
        </View>
      </View>
    </View>
  );
}
