import { View, Text } from "@tarojs/components";
import { useLoad, useDidShow } from "@tarojs/taro";
import "./index.less";
import { Cell, Image, Button, Dialog } from "@arco-design/mobile-react";
import { cls } from "@arco-design/mobile-utils";
import {
  getOrderNum,
  getUserHomeTopData,
  getUserInfo,
} from "@/utils/api/common/common_user";
import { getBalance } from "@/utils/api/common/common_wechat";
import { getCSSVariableValue } from "@/utils/utils";
import { IconRight } from "@arco-iconbox/react-yk-arco";
import React, { useEffect, useState, useRef } from "react";
import { usePermission } from "@/hooks/usePermission";
import wx from "weixin-webview-jssdk";
import Taro from "@tarojs/taro";
// 组件
import YkNavBar from "@/components/ykNavBar/index";
import YkSwitchTabBar from "@/components/ykSwitchTabBar/index";

// 本地类型和工具
import {
  UserInfo,
  VipInfo,
  VipStatus,
  getMemberStatusText,
  isMemberValid,
} from "./utils";
// import initCustomerServer from "./customerServer";
import {
  IconUserGroup,
  IconImage,
  IconQrcode,
} from "@arco-iconbox/react-yk-arco";
import { IconPaymentStroken, IconFans } from "@/components/YkIcons";

export default function My() {
  // 用户资料（包含会员信息）
  const [userInfo, setUserInfo] = React.useState<UserInfo | null>(null);
  // 钱包金额
  const [walletAmount, setWalletAmount] = React.useState<string>("0");
  // 在线收款信息
  const [userHomeTopData, setUserHomeTopData] = React.useState<any>(null);
  // 会员信息（保留用于调试）
  const [vipInfo, setVipInfo] = React.useState<VipInfo>({
    isVip: VipStatus.NON_VIP,
  });
  // 修改订单统计数据，改为从接口获取
  const [orderStats, setOrderStats] = React.useState([
    { number: 0, label: "待付款" },
    { number: 0, label: "已付款" },
    { number: 0, label: "已发货" },
    { number: 0, label: "已完成" },
    { number: 0, label: "退款" },
  ]);

  const [platform, setPlatform] = useState<string>("H5");
  const platformRef = useRef<string>("H5");
  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
      platformRef.current = "Android";
    } else if (isIos) {
      setPlatform("IOS");
      platformRef.current = "IOS";
    } else if (isHM) {
      setPlatform("HM");
      platformRef.current = "HM";
    } else {
      setPlatform("H5");
      platformRef.current = "H5";
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
      platformRef.current = "WX";
    }
  }, []);

  useLoad(() => {
    setUserInfo(Taro.getStorageSync("userInfo"));
    console.log(Taro.getStorageSync("userInfo"));
    fetchOrderNumApi();
    fetchWalletApi();
    fetchUserHomeTopDataApi();
  });

  const checkLogin = () => {
    const userInfo = Taro.getStorageSync("userInfo");
    if (!userInfo || !userInfo.id) {
      if (platform === "WX") {
        wx.miniProgram.navigateTo({ url: "/pages/index/index" });
      } else {
        Taro.navigateTo({ url: "/pages/login/index" });
      }
      return false
    } 
    return true
  };

  // 支付成功返回后刷新用户信息（由会员中心设置的标记）
  useDidShow(() => {
    if (platformRef.current == "IOS") {
      window.webkit.messageHandlers.configStatusBarStyle.postMessage("black");
    } else if (platformRef.current == "Android") {
      window.StatusBarDarkMode.StatusBarDarkMode();
      console.log(
        "useDidHide window.StatusBarDarkMode",
        window.StatusBarDarkMode
      );
    } else if (platformRef.current == "HM") {
      window.harmony.StatusBarDarkMode();
    }
    Taro.eventCenter.trigger("changeTheme");
    try {
      // const needRefresh = Taro.getStorageSync('vip_payment_success_refresh');
      // if (needRefresh) {
      (async () => {
        try {
          console.log("每次页面显示都刷新用户信息");
          const localUserInfo = Taro.getStorageSync("userInfo");
          const res: any = await getUserInfo();
          if (res && res.code === 0 && res.data) {
            const updatedUserInfo = { ...localUserInfo, ...res.data };
            Taro.setStorageSync("userInfo", updatedUserInfo);
            setUserInfo(updatedUserInfo);
          }
        } catch (e) {
          // 忽略异常，保持页面可用
        } finally {
          // Taro.removeStorageSync('vip_payment_success_refresh');
        }
      })();
      // }
    } catch (e) {}
  });

  // 获取订单数据
  const fetchOrderNumApi = async () => {
    const res: any = await getOrderNum({
      userId: Taro.getStorageSync("userInfo")?.id,
      orderTypeFlag: 0,
    });
    // 确保res不为null且有code属性
    if (res && res.code === 0 && res.data) {
      setOrderStats([
        { number: res.data.pendingPaymentCount || 0, label: "待付款" },
        { number: res.data.pendingShipmentCount || 0, label: "待发货" },
        { number: res.data.shippedCount || 0, label: "已发货" },
        { number: res.data.complete_count || 0, label: "已完成" },
        { number: res.data.refundCount || 0, label: "退款" },
      ]);
    }
  };

  const fetchWalletApi = async () => {
    const res: any = await getBalance({
      userId: Taro.getStorageSync("userInfo")?.id,
    });
    if (res) {
      const data =
        res && typeof res === "object" && "data" in res
          ? (res as any).data
          : res;
      const totalAmount = data?.available_amount + data?.pending_amount;
      setWalletAmount((totalAmount / 100).toFixed(2));
    }
  };

  const fetchUserHomeTopDataApi = async () => {
    const res: any = await getUserHomeTopData({
      userId: Taro.getStorageSync("userInfo")?.id,
      homePageCountType: 2,
    });
    if (res && res.code === 0 && res.data) {
      setUserHomeTopData(res.data);
    }
  };

  useDidShow(() => {
    setUserInfo(Taro.getStorageSync("userInfo"));
    console.log(Taro.getStorageSync("userInfo"));
  });

  // 跳转到会员中心
  const goToVipCenter = () => {
    if (!checkLogin()) return;
    if (platform === "WX") {
      wx.miniProgram.navigateTo({
        url: "/pages/web?path=/pageVip/vipCenter/index",
      });
    } else {
      Taro.navigateTo({ url: "/pageVip/vipCenter/index" });
    }
  };

  const handleCustomerService = () => {
    // 显示第一个弹框：询问是否打开微信添加客服
    Dialog.confirm({
      title: "联系客服",
      children: "已复制客服微信号，是否打开微信添加客服？",
      okText: "打开微信",
      cancelText: "取消",
      platform: "ios",
      onOk: () => {
        if (platform === "Android") {
          window.openWx.openWx();
        } else if (platform === "IOS") {
          window.webkit.messageHandlers.openWx.postMessage("");
        } else if (platform === "HM") {
          window.harmony.openWx();
        }
      },
    });
  };

  // 新增：显示客服微信号弹框的函数
  const showCustomerServiceDialog = () => {
    Dialog.confirm({
      title: "联系客服",
      children: (
        <View>
          <Text>客服微信号：</Text>
          <Text style={{ color: getCSSVariableValue("--primary-color") }}>
            haidanxc
          </Text>
        </View>
      ),
      okText: "复制",
      cancelText: "取消",
      platform: "ios",
      onOk: () => {
        // 在其他平台使用Taro的复制功能
        Taro.setClipboardData({
          data: "haidanxc",
          success: () => {
            Taro.hideToast();
            // 复制成功后显示第一个弹框
            setTimeout(() => {
              handleCustomerService();
            }, 1000);
          },
        });
      },
    });
  };

  // const handleCustomerService = () => {
  //   // const option = {
  //   //   openUrl: "https://pipakf.fjpipixia.com",
  //   //   token: "f99daaddc7c3a45adb0384ed34acf489",
  //   //   kefuid: '',
  //   //   isShowTip: false,
  //   //   windowStyle: 'center',
  //   //   domId: 'customerServerTip',
  //   //   insertDomNode: 'body',
  //   //   sendUserData: {
  //   //     uid: userInfo?.id,
  //   //     nickName: userInfo?.nickname,
  //   //     sex: '1',
  //   //     avatar: userInfo?.head_img,
  //   //     phone: '',
  //   //     openid: ''
  //   //   },
  //   //   deviceType: "Mobile"
  //   // };
  //   // console.log(option)

  //   // // 初始化客服系统
  //   // const customerServer = new initCustomerServer(option);
  //   // customerServer.init();
  //   // customerServer.getCustomeServer();
  //   //Taro.navigateTo({ url: "/pageCustomerService/mobileCustomerServer/index" })
  //   if (platform=== "WX") {
  //     wx.miniProgram.navigateTo({ url: "/pages/web?path=/pageCustomerService/ai/chat" });
  //   } else {
  //     Taro.navigateTo({ url: "/pageCustomerService/ai/chat" });
  //   }

  //   // navigateToVue('/pages/pagesA/customerService/customerService')
  // };

  // 跳转到在线收款（简化版 - 直接跳转到步骤页面，状态检查在目标页面处理）
  const handleOnlinePayment = () => {
    if (!checkLogin()) return;
    if (userHomeTopData?.hasOpenOnlinePayment) {
      if (platform === "WX") {
        wx.miniProgram.navigateTo({
          url:
            "/pages/web?path=" +
            encodeURIComponent(
              "/pageOnlinePayment/onlinePayment/information/index?subMchid=" +
                userHomeTopData?.subMchid
            ),
        });
      } else {
        Taro.navigateTo({
          url:
            "/pageOnlinePayment/onlinePayment/information/index?subMchid=" +
            userHomeTopData?.subMchid,
        });
      }
    } else {
      if (platform === "WX") {
        wx.miniProgram.navigateTo({
          url: "/pages/web?path=/pageOnlinePayment/onlinePayment/index",
        });
      } else {
        Taro.navigateTo({ url: "/pageOnlinePayment/onlinePayment/index" });
      }
    }
  };

  // 菜单配置
  const menuItems = [
    {
      iconType: "fans",
      text: "我的粉丝",
      onClick: () => {
        if (!checkLogin()) return;
        if (platform === "WX") {
          wx.miniProgram.navigateTo({
            url: "/pages/web?path=/pageUserInfo/fans/index",
          });
        } else {
          Taro.navigateTo({ url: "/pageUserInfo/fans/index" });
        }
      },
    },
    {
      iconType: "album",
      text: "我的相册",
      onClick: () => {
        if (!checkLogin()) return;
        if (platform === "WX") {
          wx.miniProgram.navigateTo({
            url: "/pages/web?path=/pageDynamic/album/index",
          });
        } else {
          Taro.navigateTo({ url: "/pageDynamic/album/index" });
        }
      },
    },
    {
      iconType: "qrcode",
      text: "二维码",
      onClick: () => {
        if (!checkLogin()) return;
        if (platform === "WX") {
          // wx.miniProgram.navigateTo({ url: "/pages/web?path=/pageUserInfo/qrcode/index" });

          let userInfoTemp = {
            userId: userInfo?.id,
            nickname: userInfo?.nickname,
            avatar: userInfo?.avatar,
          };
          wx.miniProgram.navigateTo({
            url:
              "/pages/qrcode/index?userInfo=" +
              encodeURIComponent(JSON.stringify(userInfoTemp)),
          });
        } else {
          Taro.navigateTo({ url: "/pageUserInfo/qrcode/index" });
        }
      },
    },
    // {
    //   // icon: <IconNotification />,
    //   icon: <Image src={require('@/assets/images/my/my_xiaochengxuma.png')} className="menuItem-icon" bottomOverlap={null} fit="contain"/>,
    //   text: "小程序码",
    //   // onClick: () => navigateToVue('/pages/pagesA/realNameAuthentication/realNameAuthenticationFinish')
    // },
    {
      iconType: "payment",
      text: "在线收款",
      onClick: handleOnlinePayment,
    },
    // {
    //   // icon: <IconNotification />,
    //   icon: <Image src={require('@/assets/images/my/my_shimingrenzheng.png')} className="menuItem-icon" bottomOverlap={null} fit="contain"/>,
    //   text: "实名认证",
    //   onClick: () => Taro.navigateTo({ url:'/pages/pagesA/realNameAuthentication/realNameAuthenticationFinish'})
    // },
    // {
    //   // icon: <IconNotification />,
    //   icon: <Image src={require('@/assets/images/my/my_shouhou.png')} className="menuItem-icon" bottomOverlap={null} fit="contain"/>,
    //   text: "售后",
    //   onClick: () => Taro.navigateTo({ url:'/pages/pagesA/afterSales/index'})
    // }
  ];

  const bottomMenus = [
    // {
    //   text: "客服",
    //   onClick: handleCustomerService
    // },
    {
      text: "我的钱包",
      extra: `￥${walletAmount}`,
      onClick: () => {
        if (!checkLogin()) return;
        if (userHomeTopData?.hasOpenOnlinePayment) {
          if (platform === "WX") {
            wx.miniProgram.navigateTo({
              url: "/pages/web?path=/pageOnlinePayment/wallet/index",
            });
          } else {
            Taro.navigateTo({ url: "/pageOnlinePayment/wallet/index" });
          }
        } else {
          Dialog.confirm({
            title: "提示",
            children: "请先开通在线收款",
            okText: "去开通",
            cancelText: "取消",
            platform: "ios",
            onOk: () => {
              if (platform === "WX") {
                wx.miniProgram.navigateTo({
                  url: "/pages/web?path=/pageOnlinePayment/onlinePayment/index",
                });
              } else {
                Taro.navigateTo({
                  url: "/pageOnlinePayment/onlinePayment/index",
                });
              }
            },
          });
        }
      },
    },
    {
      text: "我的收藏",
      onClick: () => {
        if (!checkLogin()) return;
        if (platform === "WX") {
          wx.miniProgram.navigateTo({
            url: "/pages/web?path=/pageDynamic/myCollect/index",
          });
        } else {
          Taro.navigateTo({ url: "/pageDynamic/myCollect/index" });
        }
      },
    },
    // 只在非小程序平台显示联系客服
    ...(platform !== "WX"
      ? [
          {
            text: "联系客服",
            onClick: showCustomerServiceDialog,
          },
        ]
      : []),
    {
      text: "设置",
      onClick: () => {
        if (!checkLogin()) return;
        if (platform === "WX") {
          wx.miniProgram.navigateTo({
            url: "/pages/web?path=/pageSetting/settings/index",
          });
        } else {
          Taro.navigateTo({ url: "/pageSetting/settings/index" });
        }
      },
    },
  ];

  return (
    <View className="page">
      {platform !== "WX" && <YkNavBar switchTab title="我的" />}

      {/* 用户信息 */}
      <View
        className="userInfo"
        onClick={() => {
          if (!checkLogin()) return;
          if (platform === "WX") {
            wx.miniProgram.navigateTo({
              url: "/pages/web?path=/pageUserInfo/editUserInfo/index",
            });
          } else {
            Taro.navigateTo({ url: "/pageUserInfo/editUserInfo/index" });
          }
        }}
      >
        <Image src={userInfo?.avatar || ""} className="avatar" radius="50%" />
        <View className="info">
          <View className="nickname">{userInfo?.nickname ?? "登录/注册"}</View>
          <View className="userId">ID: {userInfo?.id ?? "0"}</View>
        </View>
        <IconRight className="arrow" />
      </View>

        {
            !(platform == "HM" &&
                window.localStorage.getItem(`${APP_NAME}_version`) &&
                window.localStorage
                  .getItem(`${APP_NAME}_version`)
                  .includes(Taro.getStorageSync("versionConfig").harmonyosReviewVersion))&&(
                    <View
                    className={cls("member-card", {
                      "member-valid": isMemberValid(userInfo),
                    })}
                  >
                    <View className="member-card-blur1"></View>
                    <View className="member-card-blur2"></View>
                    {isMemberValid(userInfo) && (
                      <View className="member-card-text-container">
                        <View className="vip-badge">
                          <Text className="vip-text">VIP</Text>
                        </View>
                        <View className="member-card-text">
                          {getMemberStatusText(userInfo)}
                        </View>
                      </View>
                    )}
                    {!isMemberValid(userInfo) && (
                      <View className="member-card-text">
                        {getMemberStatusText(userInfo)}
                      </View>
                    )}
            
                    <Button className="member-card-btn" onClick={goToVipCenter}>
                      {isMemberValid(userInfo) ? "去续费" : "去开通"}
                    </Button>
                  </View>
                  )
        }


      {/* 功能菜单 */}
      <View className="menuGrid">
        {menuItems.map((item, index) => (
          <View key={index} className="menuItem" onClick={item.onClick}>
            {item.iconType === "fans" && <IconFans className="menuItem-icon" />}
            {item.iconType === "album" && (
              <IconImage className="menuItem-icon" />
            )}
            {item.iconType === "qrcode" && (
              <IconQrcode className="menuItem-icon" />
            )}
            {item.iconType === "payment" && (
              <IconPaymentStroken className="menuItem-icon" />
            )}
            <Text className="menuText">{item.text}</Text>
          </View>
        ))}
      </View>

      <View className="orderStatsBox">
        <View className="orderStatsTitle">
          <Text>我买的</Text>
          <View className="viewAll-container">
            <Text
              className="viewAll"
              onClick={() => {
                if (!checkLogin()) return;
                if (platform === "WX") {
                  wx.miniProgram.navigateTo({
                    url: `/pages/web?path=${encodeURIComponent(
                      "/pageOrder/order/index?current=0"
                    )}`,
                  });
                } else {
                  Taro.navigateTo({ url: "/pageOrder/order/index?current=0" });
                }
              }}
            >
              全部
            </Text>
            <IconRight className="viewAll-icon" />
          </View>
        </View>
        <View className="orderStatsContent">
          {orderStats.map((item, index) => (
            <View
              key={index}
              className="statsItem"
              onClick={() => {
                if (!checkLogin()) return;
                if (platform === "WX") {
                  wx.miniProgram.navigateTo({
                    url: `/pages/web?path=${encodeURIComponent(
                      `/pageOrder/order/index?current=${index + 1}`
                    )}`,
                  });
                } else {
                  Taro.navigateTo({
                    url: `/pageOrder/order/index?current=${index + 1}`,
                  });
                }
                // navigateToVue(`/pages/pagesA/order/buyOrder?current=${index + 1}`)
              }}
            >
              <Text className="number">{item.number}</Text>
              <Text className="label">{item.label}</Text>
            </View>
          ))}
        </View>
      </View>

      {/* 底部菜单列表 */}
      <Cell.Group className="bottomMenu" bordered={false}>
        {bottomMenus.map((item, index) => (
          <Cell
            key={index}
            label={item.text}
            text={item.extra}
            showArrow
            onClick={item.onClick}
          />
        ))}
      </Cell.Group>

      <YkSwitchTabBar activeTab={3} />
    </View>
  );
}
