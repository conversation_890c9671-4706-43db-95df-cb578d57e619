@import "@arco-design/mobile-react/style/mixin.less";

.select-classify {
  height: 100%;
  background: transparent; // 移除背景，由父组件 ClassifyModal 处理
  display: flex;
  flex-direction: column;
  position: relative;

  .mask-overlay {
    display: none; // 隐藏，由父组件处理蒙版
  }

  .top-blank-area {
    display: none; // 移除顶部空白区域
  }

  .top-header {
    height: 44px;
    // background: #ffffff; // 填充 Fill/Container 容器背景色
    background: var(--page-primary-background-color) !important;
    .use-dark-mode-query({
      background: @dark-background-color !important;
    });
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    position: relative;
    z-index: 10;
    border-radius: 14px 14px 0 0;

    .cancel-button {
      display: flex;
      align-items: center;
      gap: 4px;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.3s;

      &:active {
        background: rgba(134, 144, 156, 0.1);
      }

      .cancel-text {
        font-family: PingFangSC-Regular;
        font-size: 16px;
        line-height: 1.4;
        color: #86909c; // 文字 Text/文字-3-附加信息-Grey 6
      }
    }

    .multi-select-button {
      display: flex;
      align-items: center;
      gap: 4px;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.3s;

      &:active {
        background: rgba(22, 93, 255, 0.1);
      }

      .multi-select-text {
        font-family: PingFangSC-Regular;
        font-size: 16px;
        line-height: 1.4;
        color: #165dff; // 主色 Brand Color/主色-6-基础
      }
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    // background: #f7f8fa; // 填充 Fill/填充fill-1
    background: var(--page-primary-background-color) !important;
    .use-dark-mode-query({
      background: @dark-background-color !important;
    });
    overflow: hidden; // 防止内容溢出
    position: relative;
    z-index: 10;

    .left-sidebar {
      width: 100px;
      flex-shrink: 0;
      // background: #ffffff; // 填充 Fill/填充white

      .category-list {
        height: 100%;

        .category-item {
          height: 55px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 16px;
          border-bottom: 0.5px solid #e5e6eb; // 线条 Line/线条line-1-基础

          &.active {
            .category-name {
              color: #165dff;
              .use-dark-mode-query({
          color: #165dff;
        });
            }
          }

          .category-name {
            font-family: PingFangSC-Regular;
            font-size: 16px;
            line-height: 1.4;
            color: @font-color;
            .use-dark-mode-query({
          color: @dark-font-color;
        });
          }
        }
      }
    }

    .right-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      min-width: 0; // 确保flex子元素可以收缩

      .content-scroll {
        flex: 1;
        padding: 0;
        height: 100%; // 确保滚动容器有明确高度
        width: 100%;
        box-sizing: border-box;

        .category-section {
          margin-bottom: 20px;

          .category-header {
            height: 54px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 16px;
            padding-left: 16px;

            .header-info {
              display: flex;
              align-items: center;
              gap: 12px;

              .avatar-placeholder {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background: #f7f8fa;
              }

              .info-text {
                .list-content {
                  font-family: PingFangSC-Medium;
                  font-size: 16px;
                  line-height: 1.4;
                  color: @font-color;
                  .use-dark-mode-query({
          color: @dark-font-color;
        });
                }
              }
            }
          }
        }

        .tags-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 12px;
          padding: 12px 16px;
          justify-items: center;
          width: 100%;
          box-sizing: border-box;

          .tag-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 6px;
            cursor: pointer;
            transition: all 0.3s;
            width: 100%;
            max-width: 80px;

            .tag-image-container {
              position: relative;
              width: 64px;
              height: 64px;

              .tag-image,
              .tag-placeholder {
                width: 100%;
                height: 100%;
                border-radius: 8px;
                border: 2px solid #e5e6eb;
                transition: all 0.3s;
              }

              .tag-image {
                object-fit: cover;
                background-image: url("https://image-resource.mastergo.com/92419737293347/152770503910353/0e6fd091b6f9898d871dfe12d5c654f2.webp");
                background-size: cover;
                background-position: center;
              }

              .tag-placeholder {
                display: flex;
                align-items: center;
                justify-content: center;
                background: #f7f8fa;

                &.private-placeholder {
                  background: #f7f8fa;
                }

                &.add-placeholder {
                  background: #f7f8fa;

                  .add-icon {
                    font-size: 24px;
                    color: #4e5969; // 中性色 Neutral Color/文字 Text/text-4
                    font-weight: normal;
                  }
                }
              }

              .selected-mark {
                position: absolute;
                top: 0;
                right: 0;
                width: 18px;
                height: 18px;
                background: #165dff; // 主色 Brand Color/主色-6-基础
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

                &.disabled {
                  background: rgba(0, 0, 0, 0.2);
                }
              }
            }

            .tag-name {
              font-family: PingFang SC;
              font-size: 12px;
              font-style: Medium;
              line-height: 1.3;
              color: @font-color;
              .use-dark-mode-query({
          color: @dark-font-color;
        });
              text-align: center;
              width: 100%;
              max-width: 80px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              word-break: break-all;

              &.private-name {
                white-space: pre-line;
                line-height: 1.2;
              }
            }

            &.selected {
              .tag-image-container {
                .tag-image,
                .tag-placeholder {
                  border: 2px solid #165dff;
                }
              }
            }

            &.private-category,
            &.add-category {
              .tag-image-container {
                .tag-placeholder {
                  border: 2px solid #e5e6eb;
                }
              }
            }

            &:active {
              transform: scale(0.98);
            }
          }
        }
      }
    }
  }

  .bottom-section {
    // background: #ffffff;
    padding: 16px;
    position: relative;
    z-index: 10;
    flex-shrink: 0; // 防止被压缩

    .confirm-button {
      height: 36px;
      background: #165dff; // 主色 Brand Color/主色-6-基础
      border: 1px solid #165dff;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s;

      .confirm-text {
        font-family: PingFangSC-Regular;
        font-size: 15px;
        line-height: 1.4;
        color: #ffffff; // 填充 Fill/Mask 蒙层内容字体颜色
      }

      &:active {
        opacity: 0.8;
      }
    }

    .safe-area {
      height: 34px;
      background: transparent;
      position: relative;

      &::after {
        content: "";
        position: absolute;
        bottom: 6px;
        left: 50%;
        transform: translateX(-50%);
        width: 134px;
        height: 5px;
        background: rgba(
          0,
          0,
          0,
          0.9
        ); // 特殊 Special Color/image-preview-mask-bg
        border-radius: 100px;
      }
    }
  }
}

/* 适配小屏幕 */
@media (max-width: 750px) {
  .select-classify {
    .main-content {
      .left-sidebar {
        width: 90px;

        .category-list {
          .category-item {
            padding: 0 10px;

            .category-name {
              font-size: 14px;
            }
          }
        }
      }

      .right-content {
        .content-scroll {
          .tags-grid {
            gap: 10px;
            padding: 10px 12px;

            .tag-item {
              max-width: 70px;

              .tag-image-container {
                width: 56px;
                height: 56px;
              }

              .tag-name {
                font-size: 11px;
                max-width: 70px;
              }
            }
          }
        }
      }
    }
  }
}

/* 适配更小屏幕 */
@media (max-width: 375px) {
  .select-classify {
    .main-content {
      .left-sidebar {
        width: 80px;
      }

      .right-content {
        .content-scroll {
          .tags-grid {
            gap: 8px;
            padding: 8px 10px;

            .tag-item {
              max-width: 65px;

              .tag-image-container {
                width: 52px;
                height: 52px;
              }

              .tag-name {
                font-size: 10px;
                max-width: 65px;
              }
            }
          }
        }
      }
    }
  }
}
