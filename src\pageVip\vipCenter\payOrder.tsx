import { View, Text } from '@tarojs/components';
import { useEffect, useState ,  useRef } from 'react';
import {
  PullRefresh,
  LoadMore,
  Toast,
  Button,
} from '@arco-design/mobile-react';
import YkNavBar from '@/components/ykNavBar';
import YkNoticeBar from '@/components/YkNoticeBar';
import { getVipOrderList } from '@/utils/api/common/common_user';
import { IconLoadEmpty } from '@/components/YkIcons';
import Taro from '@tarojs/taro';
import {
  VipType,
  VipPackage,
  PayStatus,
  VipOrderRecordForUI,
  VipOrderStatus,
  PaginationInfo,
  GetVipOrderListRequest,
} from './types';
import {
  generateMockOrderData,
  mockApiResponse,
  formatTime,
  transformOrdersForUI,
} from './utils';
import './payOrder.less';

const PayOrder = () => {
  const [loading, setLoading] = useState(false);
  const [platform,setPlatform] = useState<string>("H5");
  const [orderList, setOrderList] = useState<VipOrderRecordForUI[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 10,
    hasMore: true,
    isLoading: false,
  });

  const fetchOrderList = async (isRefresh = false, forceScenario?: string) => {
    const { isLoading, hasMore } = pagination;
    if (isLoading && !isRefresh) return;
    if (!hasMore && !isRefresh) return;

    const currentPage = isRefresh ? 1 : pagination.page;
    setPagination(prev => ({ ...prev, isLoading: true }));
    if (isRefresh) {
      setLoading(true);
    }

    try {
      // 获取本地用户信息
      const localUserInfo = Taro.getStorageSync("userInfo");
      if (!localUserInfo || !localUserInfo.id) {
        return;
      }

      // API CALL
      const requestData: GetVipOrderListRequest = {
        pageNo: currentPage,
        pageSize: pagination.limit,
        userId: localUserInfo.id,
        payStatus: PayStatus.PAIDED // 已支付订单
      };
      const response = await getVipOrderList(requestData);
      if (response && response.code === 0) {
        // 确保 response.data 是分页结构
        const responseData = response.data || {};
        const rawOrders = Array.isArray(responseData) ? responseData : (responseData.list || []);
        const newOrders = transformOrdersForUI(rawOrders);

        if (isRefresh) {
          setOrderList(newOrders);
        } else {
          setOrderList(prev => Array.isArray(prev) ? [...prev, ...newOrders] : newOrders);
        }
        const newHasMore = newOrders.length >= pagination.limit;
        setPagination(prev => ({
          ...prev,
          page: newHasMore ? currentPage + 1 : currentPage,
          hasMore: newHasMore,
        }));
      } else {
        Toast.error(response.msg || '获取订单列表失败');
      }
    } catch (error) {
      Toast.error('请求出错，请稍后再试');
    } finally {
      setLoading(false);
      setPagination(prev => ({ ...prev, isLoading: false }));
    }
  };

  const handleRefresh = async () => {
    await fetchOrderList(true);
  };

  const handleLoadMore = (callback: (status: 'prepare' | 'loading' | 'nomore' | 'retry') => void) => {
    fetchOrderList(false).then(() => {
      if (pagination.hasMore) {
        callback('prepare');
      } else {
        callback('nomore');
      }
    }).catch(() => {
      callback('retry');
    });
  };

  

  useEffect(() => {
    fetchOrderList(true);
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);

  const handleBackToPurchase = () => {
    Taro.navigateBack();
  };

  const renderEmptyState = () => {
    return (
      <View className="empty-state-container">
        <View className="empty-content">
          <View>
            <IconLoadEmpty className='empty-icon'/>
          </View>
          <Text className="empty-text">您还未购买任何服务</Text>
          <Text className="empty-description">购买会员服务后，您的订单将在这里显示</Text>
          <Button
            className="back-button"
            type="primary"
            size="small"
            onClick={handleBackToPurchase}
          >
            返回购买
          </Button>
        </View>
      </View>
    );
  };

  const renderOrderItem = (order: VipOrderRecordForUI) => (
    <View key={order.id} className="vip-order-item">
      <View className="vip-box">
        <View className="vip-badge">
          <Text className="vip-text">VIP</Text>
        </View>
      </View>
      <View className="vip-order-details">
        <Text className="vip-order-title">{order.package_name}</Text>
        <Text className="vip-order-expire">支付时间：{formatTime(order.payTime)}</Text>
      </View>
    </View>
  );

  return (
    <View className="pay-order-page">
      {platform !== "WX" && <YkNavBar title="已购服务" />}
      <View className="content-wrapper">
        {(!Array.isArray(orderList) || orderList.length === 0) && !loading ? (
          renderEmptyState()
        ) : (
          <PullRefresh onRefresh={handleRefresh}>
            <View className="vip-order-list">
              {Array.isArray(orderList) && orderList.map(renderOrderItem)}
              {/* 只有在有数据且可能有更多数据时才显示 LoadMore */}
              {pagination.page   > 1 && (
                <LoadMore
                  status={pagination.isLoading ? 'loading' : pagination.hasMore ? 'prepare' : 'nomore'}
                  getData={handleLoadMore}
                />
              )}
            </View>
          </PullRefresh>
        )}
      </View>
    </View>
  );
};

export default PayOrder;