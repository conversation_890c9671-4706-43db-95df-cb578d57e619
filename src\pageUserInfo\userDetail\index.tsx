import React, { useState, useEffect   , useRef } from "react";
import Taro from "@tarojs/taro";
import Album from "@/components/Album";
import { toast } from "@/utils/yk-common";
import wx from "weixin-webview-jssdk";
import { Toast } from "@arco-design/mobile-react";
import { fetchCartCountUtil } from "@/utils/cartUtils";
import {
  getMyAlbumList,
  getTreeCatalogApi,
  getUserHomeTopData,
  addCart,
} from "@/utils/api/common/common_user";
import ClassifyModal from '@/pageDynamic/categoryDynamic/ClassifyModal';
import { usePermission } from "@/hooks/usePermission";
import { useGlobalCallbacks } from "@/utils/globalCallbackManager";
import {
  AuthTypes,
  AuthStatusAndroid,
  AuthStatusIos,
} from "@/utils/config/authTypes";

interface Tag {
  id: number;
  userId: number | null;
  parentId: number | null;
  dynamicsId: string | null;
  name: string;
  coverImage: string;
  type: number;
  sort: number | null;
  isTop: number | null;
  sortType: number | null;
  children: Tag[];
}

export default function OtherAlbum() {
  // 从路由参数获取用户ID
  const { userId } = Taro.getCurrentInstance().router?.params || {};
  const currentItem = useRef<any>(null);
  // 分类弹窗状态
  const [classifyModalVisible, setClassifyModalVisible] = useState(false);
  const [selectedTags, setSelectedTags] = useState<Tag[]>([]);

   // 自定义权限同意处理，处理首页特有的逻辑
 const customWebPermissonConsent = () => {
  // 下载操作
  if (platformRef.current === "Android") {
    downloadConfirm(currentItem.current);
  } else if (platformRef.current === "IOS") {
    window.webkit.messageHandlers.checkPermission.postMessage("");
  } 
return true;
};
  // 使用全局权限管理
  const {
    initPermissions,
    hasPermission,
    requestPermission,
    webPermissonConsent,
    webPermissonDeny,
    permissionPopupProps,
    platformRef,
    authConfirmType,
  } = usePermission(customWebPermissonConsent);

  // 注册 H5/原生容器下载回调
  useEffect(() => {
    const cleanup = initPermissions();

    // 定义下载回调函数
    const webDownloadSuc = () => {
      if(currentItem.current.pictures&&currentItem.current.content){
        toast("success", {
          content: "图片下载成功，动态文案已复制",
          duration: 2000,
        });
      }
  
      if(currentItem.current.pictures&&currentItem.current.content===""){
        toast("success", {
          content: "图片下载成功",
          duration: 2000,
        });
      }
    };

    const webDownloadFail = () => {
      try {
        toast('error', { content: '下载失败', duration: 2000 });
      } catch (e) {}
    };

    // 使用全局回调管理器注册下载回调
    const callbackCleanup = useGlobalCallbacks('otherAlbum', {
      webDownloadSuc: webDownloadSuc,
      webDownloadFail: webDownloadFail,
    });

    return () => {
      try {
        callbackCleanup && callbackCleanup();
        cleanup && cleanup();
      } catch (e) {}
    };
  }, []);

  // 获取用户信息
  const handleGetUserInfo = async (targetUserId: string | number) => {
    try {
      const res: any = await getUserHomeTopData({ userId: targetUserId, homePageCountType: 2 });
      if (res && res.code === 0 && res.data) {
        return res.data;
      }
      return {};
    } catch (error) {
      console.error("getUserInfo failed:", error);
      return {};
    }
  };

  // 获取相册列表
  const handleGetAlbumList = async (params: any) => {
    try {
      const res: any = await getMyAlbumList(params);
      return res;
    } catch (error) {
      console.error("获取相册列表失败:", error);
      return { code: -1, msg: "获取失败" };
    }
  };

  // 查看详情
  const handleDetail = (item: any) => {
    Taro.navigateTo({
      url: `/pageDynamic/detail/index?dynamicId=${item.id}&dynamicUserId=${item.userId}`,
    });
  };

  // 下载
  const handleDownload = (item: any) => {
    currentItem.current = item;
    if (platformRef.current === "HM"  || platformRef.current === "WX") {
      downloadConfirm(item);
    } else {
      // 检查存储权限
      if (!hasPermission(AuthTypes.STORAGE)) {
        console.log("没有权限");
        // 如果没有权限，请求权限
        requestPermission(AuthTypes.STORAGE);
        return;
      }
      // 有权限则执行下载
      downloadConfirm(item);
    }
  };

  const downloadConfirm = (item: any) => {
    let imageList = item.pictures.split(",");
    if(platformRef.current === "Android"){
      (window as any).downloadImg?.downloadImg(item.pictures);
    }else if(platformRef.current === "HM"){
      (window as any).harmony?.downloadImg(item.pictures);
    }else if(platformRef.current === "WX"){
      let imgs = imageList.join("|");
      wx.miniProgram.navigateTo({ url: "/pages/downloadImgs/index?imgs=" + encodeURIComponent(imgs) });
    }
    else{
    for (let i = 0; i < imageList.length; i++) {
      // if (platformRef.current === "Android") {
      //   (window as any).downloadImg?.downloadImg(imageList[i]);
      // } else
       if (platformRef.current === "IOS") {
        (window as any).webkit?.messageHandlers?.saveImgWithUrlStr?.postMessage(imageList[i]);
      }
      //  else if (platformRef.current === "HM") {
      //   (window as any).harmony?.downloadImg(imageList[i]);
      // }
    }
  }
  };

  // 分享
  const handleShare = () => {
    toast("info", {
      content: "分享功能开发中",
      duration: 2000,
    });
  };

  // 商品分类
  const handleClassify = () => {
    // 清空已选tags
    setSelectedTags([]);
    setClassifyModalVisible(true);
  };

  // 确认选择分类
  const handleClassifyConfirm = (tags: Tag[]) => {
    setSelectedTags(tags);

    // 跳转到新页面，传递选择的标签信息
    const tagIds = tags.map((tag) => tag.id).join(",");
    const tagNames = tags.map((tag) => tag.name).join(",");

    Taro.navigateTo({
      url: `/pageDynamic/categoryDynamic/tagResult/index?userId=${userId}&tagIds=${tagIds}&tagNames=${encodeURIComponent(
        tagNames
      )}`,
    });
  };

  // 关闭分类弹窗
  const handleClassifyClose = () => {
    setClassifyModalVisible(false);
  };

  return (
    <>
      <Album
        title='Ta的相册'
        userId={userId}
        isOwnAlbum={false}
        onGetUserInfo={handleGetUserInfo}
        onGetAlbumList={handleGetAlbumList}
        onDetail={handleDetail}
        onDownload={handleDownload}
        onClassify={handleClassify}
        showCreate={false} // 他人的相册不显示创建按钮
        showSort={false}
        showContactPopup // 启用内置联系弹窗
        showCartModal // 启用内置购物车弹窗
        showMore // 启用内置更多弹窗
      />

      {/* 分类选择弹窗 */}
      <ClassifyModal
        visible={classifyModalVisible}
        userId={userId as string}
        onClose={handleClassifyClose}
        onConfirm={handleClassifyConfirm}
        selectedIds={selectedTags.map((tag) => tag.id)}
        isOwnAlbum={false} // 他人相册页面，设置为 false
      />
    </>
  );
}
