@import '@arco-design/mobile-react/style/mixin.less';

.bottom-popup {
    z-index: 9999;
    position: fixed;
    bottom: 0;
    width: 100%;
    .use-var(background-color, background-color);
    .use-dark-mode-query({
        background-color: @dark-background-color;
    });
    .popup-content {
      margin: 0;
      padding: 0;
      width: 100%;
      .popup-header {
        .rem(padding,13);
        .rem(font-size,15);
        // color: #666666;
        .use-var(color, font-color);
        .use-dark-mode-query({
          color: @dark-font-color;
        });
        text-align: center;
        border-bottom-style: solid;
        .rem(border-bottom-width,1);
        .use-var(border-bottom-color, line-color);
        .use-dark-mode-query({
          border-bottom-color: @dark-line-color;
        });
      }
  
      .popup-options {
        display: flex;
        flex-direction: column;
  
        .popup-option {
          .rem(padding,13);
          .rem(font-size,15);
          .use-var(color, font-color);
          .use-dark-mode-query({
            color: @dark-font-color;
          });
          // color: #666666;
          // border-bottom: 1px solid #f5f5f5;
          border-bottom-style: solid;
         .rem(border-bottom-width,1);
         .use-var(border-bottom-color, line-color);
         .use-dark-mode-query({
          border-bottom-color: @dark-line-color;
        });

          .popup-option-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;

            .popup-option-icon {
              display: flex;
              align-items: center;
              justify-content: center;
              .rem(width, 20);
              .rem(height, 20);
            }

            .popup-option-text {
              flex: 1;
              display: flex;
              flex-direction: column;
              align-items: center;

              .popup-option-label {
                .rem(font-size, 15);
                .use-var(color, font-color);
                .use-dark-mode-query({
                  color: @dark-font-color;
                });
              }

              .popup-option-description {
                .rem(font-size, 12);
                .rem(margin-top, 4);
                opacity: 0.7;
                .use-var(color, font-color);
                .use-dark-mode-query({
                  color: @dark-font-color;
                });
              }
            }
          }

          &.popup-option-disabled {
            opacity: 0.5;
            cursor: not-allowed;

            .use-var(color, disabled-color);
            .use-dark-mode-query({
              color: @dark-disabled-color;
            });
          }
        }
      }
  
      .popup-cancel-btn {
        .rem(padding,13);
        .rem(font-size,15);
        text-align: center;
        .use-var(color, font-color);
        .use-dark-mode-query({
          color: @dark-font-color;
        });
        // color: #666666;
      }
    }
  }
  