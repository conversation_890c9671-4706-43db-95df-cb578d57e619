@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageDynamic/tagAndCategoryManage/form/index"] {
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });

  .unified-form-page {
    width: 100vw;
    min-height: 100vh;
    background: transparent;
    display: flex;
    flex-direction: column;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });

    .loading-content {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
      color: #999;
    }

    .form-content {
      flex: 1;
      padding: 0 10px;
      box-sizing: border-box;
    }

    .cover-content {
      display: flex;
      width: 100%;
      flex-direction: column;
    }

    .form-group {
      margin-top: 16px;
      margin-bottom: 12px;

      .form-label {
        font-size: 13px;
        color: #999;
        margin-bottom: 6px;
        display: block;
      }

      .form-input {
        width: 100%;
        // height: 40px;
        border: none;
        background: #ffffff;
        font-size: 15px;
        color: #1d2129;
        box-sizing: border-box;
        padding: 13px 16px;
        .use-dark-mode-query({
        background-color: var(--dark-container-background-color);
      });
        .use-dark-mode-query({
        color: var(--dark-font-color);
      });
      }
    }

    .group-title {
      font-size: 13px;
      color: #999;
      margin: 0 0 6px 0;
      display: block;
    }

    .group-title2 {
      font-size: 13px;
      color: #999;
      margin: 10px 0 0 0;
      display: block;
    }

    // 封面图相关样式
    .cover-cell {
      font-size: 15px;
      color: #1d2129;
      .use-dark-mode-query({
      background-color: @dark-background-color;
    });
      margin: 0;
      padding: 0 16px;
    }

    .cover-preview-thumb {
      width: 36px;
      height: 36px;
      border-radius: 6px;
      object-fit: cover;
      background: #f7f8fa;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
    }

    .cover-placeholder {
      color: #999;
      font-size: 15px;
    }

    // 已选择图文样式
    .selected-articles {
      // background: #fff;
      // border-radius: 10px;
      overflow: hidden;
      margin-bottom: 12px;
      // .use-dark-mode-query({
      //   background-color: var(--dark-container-background-color);
      // });
      .use-dark-mode-query({
      color: var(--dark-font-color);
    });

      .article-item {
        margin: 12px 0 0 0;
        border-bottom: 1px solid var(--line-color);
        .use-dark-mode-query({
        border-bottom: 1px solid @dark-line-color;
      });
        display: flex;
        flex-direction: column;
        position: relative;

        &:last-child {
          border-bottom: none;
        }

        .article-main {
          display: flex;
          flex-direction: column;
          gap: 6px;
        }

        .article-title {
          font-size: 15px;
          color: #222;
          font-weight: 500;
          margin-bottom: 2px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          .use-dark-mode-query({
          color: var(--dark-font-color);
        });
        }

        .article-images {
          display: flex;
          gap: 4px;
          margin-bottom: 2px;
        }

        .article-img-container {
          position: relative;
          width: 44px;
          height: 44px;
          border-radius: 4px;
          overflow: hidden;
        }

        .article-img {
          width: 100%;
          height: 100%;
          border-radius: 4px;
          object-fit: cover;
          background: #f7f8fa;
        }

        .article-img-mask {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          font-size: 12px;
          border-radius: 4px;
        }

        .article-desc {
          font-size: 13px;
          color: #999;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .article-remove {
          position: absolute;
          right: 16px;
          top: 16px;
          color: #f53f3f;
          font-size: 13px;
          cursor: pointer;
        }
      }
    }

    // 添加图文按钮
    .add-article-row {
      padding-bottom: 80px;
      display: flex;
      align-items: center;
      // padding: 8px 0 0 0;

      .add-article-icon {
        color: var(--primary-color);
        font-size: 16px;
        margin-right: 8px;
      }

      .add-article-btn {
        color: var(--primary-color);
        background: none;
        border: none;
        font-size: 15px;
        display: flex;
        align-items: center;
        padding: 0;
      }
    }

    // 目录标签相关样式
    .catalog-label {
      font-size: 14px;
      color: #8c8c8c;
      // background: #f7f8fa;
      // padding: 12px 16px 0 16px;
      height: 30px;
      line-height: 20px;
      // .use-dark-mode-query({
      //   background-color: var(--dark-container-background-color);
      // });
    }

    .catalog-tag-list {
      background: #fff;
      padding: 0 0 0 0;
      .use-dark-mode-query({
      background-color: @dark-background-color;
    });

      .catalog-tag-add {
        display: flex;
        align-items: center;
        color: var(--primary-color);
        font-size: 16px;
        padding: 12px 0;
        cursor: pointer;

        .catalog-tag-add-icon {
          font-size: 20px;
          margin-right: 4px;
        }

        .catalog-tag-add-text {
          font-size: 16px;
          color: var(--primary-color);
        }
      }

      .catalog-tag-item {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        border-bottom: 1px solid var(--line-color);
        .use-dark-mode-query({
        border-bottom: 1px solid @dark-line-color;
      });
        // background: #fff;
        // .use-dark-mode-query({
        //   background-color: @dark-background-color;
        // });

        .catalog-tag-name {
          font-size: 16px;
          color: #222;
          flex: 1;
          .use-dark-mode-query({
          color: var(--dark-font-color);
        });
        }
      }
    }

    // 提交按钮
    .submit-btn {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      width: calc(100% - 32px);
      margin: 24px 16px 16px 16px;
      background: var(--primary-color);
      color: #fff;
      border-radius: 6px;
      font-size: 16px;
      height: 44px;
      line-height: 44px;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;

      &:disabled {
        background: #ccc;
      }
    }
  }
}
