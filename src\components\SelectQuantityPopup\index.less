@import '@arco-design/mobile-react/style/mixin.less';
/* 选择数量弹窗样式 */
.select-quantity-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
  padding-bottom: 60px;
  padding-left: 15px;
}

.select-quantity-popup-container {
  background-color: #ffffff;
  .use-dark-mode-query({
    background-color: @dark-container-background-color;
  });
  border-radius: 6px;
  margin: 0 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  min-width: 120px;
}

.select-quantity-popup-content {
  .select-quantity-option {
    padding: 10px 28px;
    text-align: center;
    border-bottom: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-bottom: 1px solid @dark-line-color;
  });
    cursor: pointer;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: #f8f9fa;
    }

    .select-quantity-option-text {
      font-size: 13px;
      color: #333333;
      .use-dark-mode-query({
        color: var(--dark-font-color);
      });
    }
  }
}


.num-input {
  width: 100%;
  padding: 0 12px;
  border-radius: 4px;
  background-color: #f7f8fa !important;
  .use-dark-mode-query({
    background-color: var(--dark-background-color) !important;
  });
  font-size: 14px;
  box-sizing: border-box;
}
