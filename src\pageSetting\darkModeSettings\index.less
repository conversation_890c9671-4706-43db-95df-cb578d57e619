@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageSetting/darkModeSettings/index"] {
  background-color: var(--page-primary-background-color, #f7f8fa);
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
  .darkModeSettingsPage {
  min-height: 100vh;

  .settings-content {
    padding: 20px 0;

    .padding-item {
      padding: 10px 15px 10px 0;
    }

    .setting-item {
      // background-color: #fff;
      // .use-dark-mode-query({
      //   background-color: @dark-card-background-color !important;
      // });

      .arco-cell-label {
        font-size: 16px;
        color: #1d2129;
        .use-dark-mode-query({
          color: @dark-font-color !important;
        });
      }

      .arco-cell-description {
        font-size: 14px;
        color: #86909c;
        margin-top: 4px;
        .use-dark-mode-query({
          color: @dark-font-color !important;
        });
      }

      .description {
        font-size: 14px;
        color: #86909c;
        margin-top: 4px;
        display: block;
        .use-dark-mode-query({
          color: @dark-font-color !important;
        });
      }
    }

    .manual-section {

      .section-title {
        padding: 12px 15px; 
        font-size: 14px;
        color: #86909c;
        font-weight: 500;
        margin: 0;
        .use-dark-mode-query({
          color: @dark-font-color !important;
          background-color: @dark-card-background-color !important;
        });
      }
    }
  }
}
}