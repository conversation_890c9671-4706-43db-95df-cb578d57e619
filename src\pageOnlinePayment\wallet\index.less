// 导入Arco Design变量以保持风格统一
@import "@arco-design/mobile-react/style/mixin.less";
@import "@/utils/css/variables.less";
[id^="/pageOnlinePayment/wallet/index"] {
  .wallet {
    background-color: #f7f8fa;
    background-color: @card-background-color !important;
    .use-dark-mode-query({
        background-color: @dark-card-background-color !important;
    });
    min-height: 100vh;

  

    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 50vh;
    }

    .wallet-content {
      padding: 16px;
    }

    .balance-info {
      background: #ffffff;
      background-color: @container-background-color !important;
      .use-dark-mode-query({
            background-color: @dark-container-background-color !important;
        });
      border-radius: 12px;
      padding: 20px 0px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
      text-align: center;
    }

    .balance-total {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 20px;

      .balance-total-title {
        font-size: 12px;
        color: #86909c; // 文字-3-附加信息-Grey 6
        .use-var(color, sub-font-color);
        .use-dark-mode-query({
                color: @dark-sub-font-color !important;
            });
        margin-bottom: 5px;
      }

      .balance-total-value {
        font-size: 24px;
        font-weight: bold;
        color: #1d2129; // 文字-5-基础 Grey 10
        .use-var(color, font-color);
        .use-dark-mode-query({
                color: @dark-font-color !important;
                color: #fff !important;
            });
        font-family: "PingFang SC", sans-serif;
      }
    }

    .balance-extra {
      display: flex;
      justify-content: space-around;
      margin-bottom: 25px;

      .balance-extra-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        .balance-extra-item-label {
          display: flex;
          align-items: center;
          gap: 4px;
          margin-bottom: 5px;

          .balance-extra-item-title {
            font-size: 12px;
            color: #86909c; // 文字-3-附加信息-Grey 6
            .use-var(color, sub-font-color);
            .use-dark-mode-query({
                        color: var(--dark-sub-font-color) !important;
                    });
          }

          .arco-icon {
            font-size: 14px;
            color: #4e5969; // 文字-4-副标- Grey 8
            .use-var(color, sub-font-color);
            .use-dark-mode-query({
                        color: @dark-sub-font-color !important;
                    });
          }
        }

        .balance-extra-item-value {
          font-size: 16px;
          font-weight: bold;
          color: #1d2129; // 文字-5-基础 Grey 10
          .use-var(color, font-color);
          .use-dark-mode-query({
                    color: @dark-font-color !important;
                });
        }
      }
      .question-popover {
        display: flex;
        align-items: center;
        justify-content: center;
        .question-popover-wrapper {
          width: 100%;
          padding: 8px 12px;
          .question-popover-content {
            font-size: 13px;
            line-height: 1.2;
            text-align: left;
          }
        }
      }
    }

    .balance-btn {
      padding: 16px;

      .withdrawal-btn {
        --button-primary-bg-color: #165dff; // 主色
        --button-primary-border-color: #165dff;
        border-radius: 7px;
        height: 32px;
        font-size: 14px;
      }
    }

    .wallet-menu {
      margin-top: 10px;
      background-color: #ffffff;
      border-radius: 10px;
      overflow: hidden;

      .arco-cell {
        .arco-cell-label-text {
          font-size: 16px;
          color: #1d2129;
        }
        // .arco-icon {
        //     color: #4e5969;
        // }
      }

      .wallet-cell-icon {
        height: 20px;
        width: 20px;
        color: #4e5969;
      }
    }
  }
}
