import { View, Text, Image } from "@tarojs/components";
import Taro, { useDidShow } from "@tarojs/taro";
import React, { useState, useEffect } from "react";
import {
  getCollectLinkList,
  createCollectLink,
  delCollectLink,
} from "@/utils/api/common/common_user";
import { Textarea, Dialog } from "@arco-design/mobile-react";
import YkNavBar from "@/components/ykNavBar/index";
import BottomPopup from "@/components/BottomPopup";
import { toast } from "@/utils/yk-common";
import "./index.less";

export default function CollectLink() {
  const [input, setInput] = useState("");
  //const [links, setLinks] = useState<any[]>([]);  // 明确定义类型
  const [links, setLinks] = useState<any[]>([]); // 明确定义类型
  const [selected, setSelected] = useState<number | null>(null);
  const [showAction, setShowAction] = useState(false); // 控制操作弹窗显示
  const userInfo = Taro.getStorageSync("userInfo");
  const [platform, setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  const handleConfirm = async () => {
    if (input === "") {
      toast("info", {
        content: "请输入链接",
        duration: 2000,
      });
      return;
    }
    let res: any = await createCollectLink({ userId: userInfo.id, url: input }); //创建链接
    if (res && res.code == 0) {
      //getCollectLink();
      // setLinks(prevLinks => {
      //     const newLinks = Array.isArray(prevLinks) ? [...prevLinks, data] : [data];
      //     Taro.setStorageSync("collectLinks", newLinks);
      //     return newLinks;
      //   });
      Taro.navigateTo({
        url: "/pageDynamic/moveMaterials/selectMaterials/index?id=" + res.data,
      });
      setInput("");
    }
  };

  const delLink = async () => {
    if (!selected) return Promise.reject("No link selected");
    const data = {
      id: String(selected),
    };
    try {
      let res: any = await delCollectLink(data);
      if (res && res.code == 0) {
        await getCollectLink();
        return res;
      } else {
        throw new Error(res.msg || "删除失败");
      }
    } catch (error) {
      throw error;
    }
  };

  const getCollectLink = async () => {
    const data = {
      pageNo: 1,
      pageSize: "10",
      userId: userInfo.id,
    };
    let res: any = await getCollectLinkList(data);
    if (res && res.code == 0) {
      // setLinks(prevLinks => {
      //   const newLinks = Array.isArray(prevLinks) ? [...prevLinks, res.data.list] : [res.data.list];
      //   Taro.setStorageSync("collectLinks", newLinks);
      //   return newLinks;
      // });
      setLinks(res.data.list);
    }
  };
  useEffect(() => {
    //getCollectLink();
    //setLinks(Taro.getStorageSync("collectLinks") || []);
  }, []);

  useDidShow(() => {
    getCollectLink();
  });

  return (
    <View className="collect-link-page">
      {/* 顶部返回 */}
      {platform !== "WX" && <YkNavBar title="采集链接" />}
      {/* 输入框 */}
      <View className="collect-link-input-wrap">
        <View className="collect-link-textarea-container">
          <Textarea
            border="none"
            placeholder="请填写链接，或者长按粘贴微购相册的店铺链接"
            value={input}
            onChange={(_, value) => setInput(value)}
            className="collect-link-textarea"
            autosize
          />
          <Text
            className="collect-link-action-btn"
            onClick={() => handleConfirm()}
          >
            前往采集
          </Text>
        </View>
      </View>
      {/* 轻触提示 */}
      <Text className="collect-link-tip">轻触以选择链接</Text>
      {/* 链接列表 */}
      <View className="collect-link-list">
        {/* 链接列表 */}

        {links.map((link) => (
          <View
            className={`collect-link-item${
              selected === link.id ? " selected" : ""
            }`}
            key={link.id}
            //onClick={() => setSelected(link.id)}
          >
            {!link.urlAvatar ? (
              <Text className="collect-link-icon">🔗</Text>
            ) : (
              <Image className="user-header-avatar" src={link.urlAvatar} />
            )}

            <View
              className="collect-link-info"
              onClick={() => {
                Taro.navigateTo({
                  url:
                    "/pageDynamic/moveMaterials/selectMaterials/index?id=" +
                    link.id,
                });
              }}
            >
              <Text className="collect-link-title">{link.urlName}</Text>
              <Text className="collect-link-url">{link.url}</Text>
            </View>
            <Text
              className="collect-link-more"
              onClick={(e) => {
                e.stopPropagation();
                setSelected(link.id);
                setShowAction(true);
              }}
            >
              ...
            </Text>
          </View>
        ))}
      </View>

      {/* 底部按钮 */}
      {/* <Button
        className="collect-link-btn"
        disabled={selected === null}
        onClick={() => {
          // 跳转或采集逻辑
                Taro.navigateTo({
                        url: '/pageDynamic/moveMaterials/selectMaterials/index'
                });
        }}
      >
        前往采集
      </Button> */}
      {/* 底部操作弹窗 */}
      <BottomPopup
        visible={showAction}
        onClose={() => setShowAction(false)}
        onConfirm={(index) => {
          if (index === 0) {
            // 删除操作
            const selectedLink = links.find((link) => link.id === selected);
            Dialog.confirm({
              title: "删除链接",
              children: (
                <div className="dialog-input-demo-hint">
                  删除后，数据将不可恢复
                </div>
              ),
              okText: "删除",
              cancelText: "取消",
              onOk: () => {
                delLink()
                  .then(() => {
                    toast("success", {
                      content: "删除成功",
                      duration: 2000,
                    });
                    Taro.setStorageSync(
                      "collectLinks",
                      links.filter((link) => link.id !== selected)
                    );
                    setSelected(null);
                  })
                  .catch((error) => {
                    console.error("删除失败:", error);
                    toast("error", {
                      content: "删除失败，请重试",
                      duration: 2000,
                    });
                  });
              },
              platform: "ios",
            });
          }
        }}
        options={[{ label: "删除", style: { color: "#f53f3f" } }]}
        btnCloseText="取消"
      />
    </View>
  );
}
