import React from 'react';
import { Dialog, Input } from '@arco-design/mobile-react';

interface CustomQuantityDialogProps {
  /** 控制弹窗显示/隐藏 */
  visible: boolean;
  /** 输入框的值 */
  value: string;
  /** 输入值变化回调 */
  onValueChange: (value: string) => void;
  /** 确认回调 */
  onConfirm: () => void;
  /** 取消回调 */
  onCancel: () => void;
  /** 最大数量限制（可选） */
  maxCount?: number;
}

const CustomQuantityDialog: React.FC<CustomQuantityDialogProps> = ({
  visible,
  value,
  onValueChange,
  onConfirm,
  onCancel,
  maxCount
}) => {
  return (
    <Dialog
      visible={visible}
      title="自定义选择数量"
      platform="ios"
      close={onCancel}
      className="custom-num-dialog"
      footer={[
        {
          content: '取消',
          onClick: onCancel
        },
        {
          content: '确定',
          onClick: onConfirm
        }
      ]}
    >
      <Input
        border='none'
        type="number"
        placeholder="请输入自定义数量"
        value={value}
        onChange={(e) => onValueChange(e.target.value)}
        className="num-input"
      />
    </Dialog>
  );
};

export default CustomQuantityDialog;
