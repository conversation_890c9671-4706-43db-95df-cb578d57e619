@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageSetting/accountCancellation/index"] {
  .account-cancellation {
    overflow-y: auto;
    margin-top: 30px;
    background-color: #f8f9fa;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
  }

  .head-box {
    padding-bottom: 25px;
    background-color: #ffffff;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    &-img {
      display: block;
      width: 50px;
      height: 50px;
    }

    &-text1 {
      font-weight: bold;
      font-size: 17px;
      color: #333333;
      .use-dark-mode-query({
      color: @dark-font-color;
    });
      margin-top: 25px;
    }

    &-text2 {
      margin: 0 20px;
      font-size: 14px;
      color: #666666;
      .use-dark-mode-query({
      color: @dark-font-color;
    });
      margin-top: 5px;
      text-align: center;
    }
  }

  .content-box {
    position: relative;
    .use-dark-mode-query({
    background-color: @dark-card-background-color;
  });
    padding: 0 15px 15px 15px;
    display: flex;
    flex-direction: column;

    &-item {
      display: flex;
      margin-top: 15px;

      &-num {
        font-size: 15px;
        color: var(--primary-color);
        font-weight: bold;
      }

      &-right {
        display: flex;
        flex-direction: column;
        margin-left: 10px;
        flex: 1;

        &-title {
          font-weight: bold;
          font-size: 15px;
          color: #000000;
          .use-dark-mode-query({
          color: @dark-font-color;
        });
        }

        &-hint {
          text-align: left;
          margin-top: 8px;
          font-size: 14px;
          color: #666666;
          .use-dark-mode-query({
          color: @dark-font-color;
        });
          line-height: 1.5;
        }
      }
    }
  }

  .logout-box {
    position: relative;
    width: calc(100% - 30px);
    padding: 20px 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    &-btn {
      padding: 9px 0;
      width: 345px;
      text-align: center;
      border-radius: 50px;
      background-color: var(--primary-color);
      opacity: 0.3;
      font-size: 16px;
      color: #ffffff;

      transition: background-color 0.3s;

      &.active {
        background-color: var(--primary-color);
        opacity: 1;
      }
    }

    &-agreement {
      margin-top: 17px;
      display: flex;
      align-items: center;
      justify-content: center;

      &-text {
        font-size: 12px;
        color: #999999;
        .use-dark-mode-query({
        color: @dark-font-color;
      });
        margin-left: 5px;
      }
    }
  }
}
