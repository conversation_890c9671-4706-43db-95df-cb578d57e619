import { View, Text } from "@tarojs/components";
import "./editContact.less";
import { Form, Input, Button, CountDown } from "@arco-design/mobile-react";
import Taro from "@tarojs/taro";
import { getSmsCode, validateSmsCode } from "@/utils/api/common/common_user";
import { updateMerchantEntryApplication } from "@/utils/api/common/common_wechat";
import { toast } from "@/utils/yk-common";
import React, { useEffect,useRef,useState } from "react";
// 组件
import YkNavBar from "@/components/ykNavBar/index";

// 表单数据类型定义
interface FormData {
  newPhone: string;
  verifyCode: string;
}

export default function EditContact() {
  // 页面类型：phone-修改手机号，servicePhone-修改客服电话
  const [pageType, setPageType] = React.useState<'phone' | 'servicePhone'>('phone');
  // 旧手机号/客服电话
  const [oldPhone, setOldPhone] = React.useState<string>('');
  // 商户进件申请ID
  const [merchantApplicationId, setMerchantApplicationId] = React.useState<number | null>(null);

  // 表单数据
  const [newPhone, setNewPhone] = React.useState<string>('');
  const [verifyCode, setVerifyCode] = React.useState<string>('');

  // 验证码倒计时
  const [autoStart, setAutoStart] = React.useState<boolean>(false);
  const [countDown, setCountDown] = React.useState<number>(60);
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  // 手机号脱敏处理
  const maskPhone = (phone: string): string => {
    if (!phone) return '';
    if (phone.length === 11 && /^1[3-9]\d{9}$/.test(phone)) {
      // 手机号脱敏：173 **** 6578
      return `${phone.slice(0, 3)} **** ${phone.slice(-4)}`;
    }
    // 其他格式电话号码不脱敏
    return phone;
  };



  // 验证手机号格式
  const validatePhoneFormat = (phone: string): boolean => {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  };

  // 处理手机号输入（参照merchantApplication格式）
  const handlePhoneInput = (value: string) => {
    console.log('[handlePhoneInput] 输入值:', value);
    // 移除所有空格，只保留数字
    const cleanValue = (value || '').replace(/\s/g, '');
    console.log('[handlePhoneInput] 清理后:', cleanValue);
    setNewPhone(cleanValue);
  };

  // 格式化手机号显示（完全参照merchantApplication格式）
  const formatPhoneDisplay = (phone: string): string => {
    if (!phone) return '';
    console.log('[formatPhoneDisplay] 输入:', phone, '长度:', phone.length);

    // 完全按照merchantApplication的方式格式化
    // 注意：merchantApplication中是直接在JSX中使用这个表达式
    const formatted = `${phone.slice(0, 3)} ${phone.slice(3, 7)} ${phone.slice(7)}`.trim();
    console.log('[formatPhoneDisplay] 输出:', formatted);
    return formatted;
  };

  // 验证客服电话格式（支持手机号、固话、400电话）
  const validateServicePhoneFormat = (phone: string): boolean => {
    // 手机号
    const mobileRegex = /^1[3-9]\d{9}$/;
    // 固话（区号-号码或直接号码）
    const landlineRegex = /^(\d{3,4}-?)?\d{7,8}$/;
    // 400电话
    const hotlineRegex = /^400-?\d{3}-?\d{4}$/;

    return mobileRegex.test(phone) || landlineRegex.test(phone) || hotlineRegex.test(phone);
  };

  // 获取表单数据
  const getFormData = () => {
    return { newPhone, verifyCode };
  };

  // 验证表单
  const validateFormData = () => {
    const errors: string[] = [];

    // 验证新手机号/新客服电话
    if (!newPhone) {
      errors.push(pageType === 'phone' ? '请输入新手机号' : '请输入新客服电话');
    } else if (pageType === 'phone' && !validatePhoneFormat(newPhone)) {
      errors.push('请输入正确的手机号格式');
    } else if (pageType === 'servicePhone' && !validateServicePhoneFormat(newPhone)) {
      errors.push('请输入正确的电话格式');
    } else if (newPhone === oldPhone) {
      errors.push(pageType === 'phone' ? '新手机号不能与旧手机号相同' : '新客服电话不能与旧客服电话相同');
    }

    // 验证验证码
    if (!verifyCode) {
      errors.push('请输入验证码');
    } else if (verifyCode.length < 4 || verifyCode.length > 6) {
      errors.push('验证码长度为4-6位数字');
    }

    return {
      isValid: errors.length === 0,
      values: errors.length === 0 ? { newPhone, verifyCode } : null,
      errors
    };
  };

  // 获取验证码
  const getCode = () => {
    const formData = getFormData();
    const newPhone = formData.newPhone;

    // 根据场景确定验证码发送的手机号
    let targetPhone = '';
    let errorMessage = '';

    if (!newPhone) {
      toast("error", {
        content: pageType === 'phone' ? "请先输入新手机号" : "请先输入新客服电话",
        duration: 2000,
      });
      return;
    }

    if (pageType === 'phone') {
      // 修改手机号场景：向新手机号发送验证码
      if (!validatePhoneFormat(newPhone)) {
        toast("error", {
          content: "请输入正确的手机号格式",
          duration: 2000,
        });
        return;
      }
      targetPhone = newPhone;
      errorMessage = "新手机号";
    } else {
      // 修改客服电话场景：向旧手机号发送验证码
      if (!oldPhone || !validatePhoneFormat(oldPhone)) {
        toast("error", {
          content: "当前手机号格式不正确，无法发送验证码",
          duration: 2000,
        });
        return;
      }
      targetPhone = oldPhone;
      errorMessage = "当前手机号";
    }

    setAutoStart(true);
    const data = {
      mobile: targetPhone,
      scene: 1, 
    };

    getSmsCode(data)
      .then((res: any) => {
        if (res && res.code === 0) {
          toast("success", {
            content: `验证码已发送至${errorMessage}`,
            duration: 2000,
          });
        } else {
          toast("error", {
            content: res.msg || "验证码发送失败",
            duration: 2000,
          });
          setAutoStart(false);
        }
      })
      .catch(() => {
        toast("error", {
          content: "验证码发送失败",
          duration: 2000,
        });
        setAutoStart(false);
      });
  };

  // 倒计时结束
  const onFinishCountDown = () => {
    setAutoStart(false);
    setCountDown(60);
  };

  // 确认修改
  const handleConfirm = () => {
    const { isValid, values, errors } = validateFormData();
    if (!isValid || !values) {
      // 显示第一个错误
      if (errors && errors.length > 0) {
        toast("error", {
          content: errors[0],
          duration: 2000,
        });
      }
      return;
    }

    // 防抖处理
    if (autoStart) {
      return;
    }

    if (pageType === 'phone') {
      // 修改手机号逻辑
      handleUpdatePhone(values);
    } else {
      // 修改客服电话逻辑
      handleUpdateServicePhone(values);
    }
  };

  // 修改手机号
  const handleUpdatePhone = (formValues: FormData) => {
    const { newPhone, verifyCode } = formValues;

    // 先验证验证码
    const validateData = {
      mobile: newPhone, // 向新手机号发送的验证码
      scene: 1,
      code: verifyCode,
    };

    validateSmsCode(validateData)
      .then((res: any) => {
        if (res && res.code === 0) {
          // 验证码正确，调用更新商户进件申请接口
          if (!merchantApplicationId) {
            toast("error", {
              content: "缺少必要参数，请重新进入页面",
              duration: 2000,
            });
            return;
          }

          // 获取用户信息
          const userInfo = Taro.getStorageSync('userInfo');
          const userId = userInfo?.userInfo?.id || userInfo?.id;

          if (!userId) {
            toast("error", {
              content: "获取用户信息失败，请重新登录",
              duration: 2000,
            });
            return;
          }

          const updateData = {
            id: merchantApplicationId,
            userId: userId,
            mobilePhone: newPhone, // 更新联系人手机号
          };

          updateMerchantEntryApplication(updateData)
            .then((updateRes: any) => {
              if (updateRes && updateRes.code === 0) {
                toast("success", {
                  content: "手机号修改成功",
                  duration: 2000,
                });

                // 清空表单
                resetForm();

                // 返回上一页
                setTimeout(() => {
                  Taro.navigateBack();
                }, 1500);
              } else {
                toast("error", {
                  content: updateRes.msg || "手机号修改失败",
                  duration: 2000,
                });
              }
            })
            .catch(() => {
              toast("error", {
                content: "手机号修改失败",
                duration: 2000,
              });
            });
        } else {
          toast("error", {
            content: res.msg || "验证码验证失败",
            duration: 2000,
          });
        }
      })
      .catch(() => {
        toast("error", {
          content: "验证码验证失败",
          duration: 2000,
        });
      });
  };

  // 修改客服电话
  const handleUpdateServicePhone = (formValues: FormData) => {
    const { newPhone, verifyCode } = formValues;

    // 先验证验证码（向旧手机号发送的验证码）
    const validateData = {
      mobile: oldPhone, // 向旧手机号发送的验证码
      scene: 1,
      code: verifyCode,
    };

    validateSmsCode(validateData)
      .then((res: any) => {
        if (res && res.code === 0) {
          // 验证码正确，调用更新商户进件申请接口
          if (!merchantApplicationId) {
            toast("error", {
              content: "缺少必要参数，请重新进入页面",
              duration: 2000,
            });
            return;
          }

          // 获取用户信息
          const userInfo = Taro.getStorageSync('userInfo');
          const userId = userInfo?.userInfo?.id || userInfo?.id;

          if (!userId) {
            toast("error", {
              content: "获取用户信息失败，请重新登录",
              duration: 2000,
            });
            return;
          }

          const updateData = {
            id: merchantApplicationId,
            userId: userId,
            customerServicePhone: newPhone, // 更新客服电话
          };

          updateMerchantEntryApplication(updateData)
            .then((updateRes: any) => {
              if (updateRes && updateRes.code === 0) {
                toast("success", {
                  content: "客服电话修改成功",
                  duration: 2000,
                });

                // 清空表单
                resetForm();

                // 返回上一页
                setTimeout(() => {
                  Taro.navigateBack();
                }, 1500);
              } else {
                toast("error", {
                  content: updateRes.msg || "客服电话修改失败",
                  duration: 2000,
                });
              }
            })
            .catch(() => {
              toast("error", {
                content: "客服电话修改失败",
                duration: 2000,
              });
            });
        } else {
          toast("error", {
            content: res.msg || "验证码验证失败",
            duration: 2000,
          });
        }
      })
      .catch(() => {
        toast("error", {
          content: "验证码验证失败",
          duration: 2000,
        });
      });
  };

  // 重置表单
  const resetForm = () => {
    setNewPhone('');
    setVerifyCode('');
    setAutoStart(false);
    setCountDown(60);
  };

  // 使用 useEffect 替代 useLoad 确保状态更新
  React.useEffect(() => {
    // 获取路由参数
    const router = Taro.getCurrentInstance().router;
    const { id, type, phone } = router?.params || {};

    if (type === 'servicePhone') {
      setPageType('servicePhone');
    } else {
      setPageType('phone');
    }

    if (phone && phone.trim() !== '') {
      setOldPhone(phone);
    }

    if (id) {
      setMerchantApplicationId(Number(id));
    }
  }, []);



  return (
    <View className="editContactPageContent">
      {platform !== "WX" &&<YkNavBar title={pageType === 'phone' ? '修改手机号' : '修改客服电话'} />}

      <View className="pageDescribe">
        <Text className="pageDescribe-title">
          {pageType === 'phone' ? '设置新的手机号' : '设置新的客服电话'}
        </Text>
        <Text className="pageDescribe-desc">
          {pageType === 'phone'
            ? '请填写经营者本人实名登记的手机号码'
            : '客服电话对买家展示，便于买家咨询和售后处理'
          }
        </Text>
      </View>

      <Form
        className="form-content"
        initialValues={{
          phone: maskPhone(oldPhone),
          newPhone: '',
          verifyCode: ''
        }}
        key={`form-${oldPhone}`} // 当 oldPhone 变化时重新初始化表单
      >
        <Form.Item
          className="form-item"
          label={<View>{pageType === 'phone' ? '旧手机号' : '客服电话'}</View>}
          field="phone"
        >
          <Input
            className="form-item-input"
            value={maskPhone(oldPhone)}
            readOnly
            border="none"
          />
        </Form.Item>
      
        {/* New phone number input */}
        <Form.Item
          className="form-item"
          label={pageType === 'phone' ? '新手机号' : '新的号码'}
          field="newPhone"
        >
          <Input
            className="form-item-input"
            placeholder={pageType === 'phone' ? '请输入手机号' : '请输入客服电话'}
            value={formatPhoneDisplay(newPhone)}
            onInput={(e) => {
              const cleanValue = (e.target.value || '').replace(/ /g, '');
              setNewPhone(cleanValue);
            }}
            border="none"
            maxLength={13}
            type="tel"
          />
        </Form.Item>

        {/* Verification code input */}
        <Form.Item 
          className="form-item"
          label="验证码" 
          field="verifyCode"
        >
          <Input
            className="form-item-input"
            placeholder="请输入验证码"
            value={verifyCode}
            onChange={(_, value) => setVerifyCode(value)}
            border="none"
            suffix={
              !autoStart ? (
                <Text className="verify-code-btn" onClick={getCode}>
                  获取验证码
                </Text>
              ) : (
                <CountDown
                  millisecond
                  format="ss"
                  time={{
                    days: 0,
                    hours: 0,
                    minutes: 0,
                    seconds: countDown,
                    milliseconds: 0,
                  }}
                  autoStart={autoStart}
                  onFinish={onFinishCountDown}
                  renderChild={() => (
                    <Text className="verify-code-btn disabled">
                      获取验证码
                    </Text>
                  )}
                />
              )}
          />
        </Form.Item>
      </Form>

      {/* Submit button */}
      <View className="footer-btn">
        <Button
          type="primary"
          onClick={handleConfirm}
          disabled={!newPhone || !verifyCode}
        >
          <Text className="footerbtn-btn-text">立即修改</Text>
        </Button>
      </View>
    </View>
  );
}