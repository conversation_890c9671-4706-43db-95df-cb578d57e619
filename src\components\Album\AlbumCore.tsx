import { View, ScrollView, Text, Image } from "@tarojs/components";
import React, { useState, useEffect, useRef } from "react";
import Taro, { useDidShow, useReady, useReachBottom } from "@tarojs/taro";
import {
  Tabs,
  SearchBar,
  Toast,
  LoadMore,
  Popup,
  Dialog,
} from "@arco-design/mobile-react";
import { LoadMoreStatus } from "@arco-design/mobile-react/cjs/load-more";
import YkNavBar from "@/components/ykNavBar/index";
import AlbumHeader from "./AlbumHeader";
import AlbumList from "./AlbumList";
import CartModal from "@/components/CartModal";
import ContactModal from "@/components/ContactModal";
import { formatDate, getCSSVariableValue } from "@/utils/utils";
import {
  addCart,
  unfollowUser,
  followUser,
} from "@/utils/api/common/common_user";
import { fetchCartCountUtil } from "@/utils/cartUtils";
import { toast } from "@/utils/yk-common";
import "./index.less";
import BottomPopup from "@/components/BottomPopup";
import { IconTriDown } from "@arco-design/mobile-react/esm/icon";
import { useGlobalCallbacks } from "@/utils/globalCallbackManager";
import { usePermission } from "@/hooks/usePermission";
import wx from "weixin-webview-jssdk";

// 定义 dynamic 类型
export interface DynamicItem {
  id: string | number;
  content: string;
  price: number;
  time: string;
  pictures: string;
  coverImage: string;
  skus?: string[];
  color?: string[];
  digitalWatermark?: string;
  isTop?: number;
  userId?: string | number;
  createTime?: string;
  updateTime?: string;
  colors?: Record<string, string>;
  specifications?: Record<string, string>;
  productSpecificationsNames?: string;
  productColorNames?: string;
}

export interface GroupedItem {
  group: string;
  items: DynamicItem[];
}

export interface DynamicData {
  items: GroupedItem[];
  totalCount?: number;
  hasMore?: boolean;
  allItems?: DynamicItem[];
}

export interface UserHomeTopData {
  avatar?: string;
  nickname?: string;
  total?: number;
  newNumbers?: number;
  personalProfile?: string;
  hasOpenOnlinePayment?: number;
  wechatNumber?: string;
  contactMobile?: string;
  wechatQrCode?: string;
  homePageSort?: number;
  userId?: string | number;
  isFollow?: number;
}

// 定义 tab 类型
export type TabType = "all" | "new" | "video" | "image";

export interface TagItem {
  id: number;
  name: string;
  coverImage?: string;
}

export interface AlbumCoreProps {
  // 页面配置
  title?: string;
  userId?: string | number;
  isOwnAlbum?: boolean; // 是否是自己的相册

  // 数据获取函数
  onGetUserInfo?: (userId: string | number) => Promise<UserHomeTopData>;
  onGetAlbumList?: (params: any) => Promise<any>;
  onDeleteDynamic?: (id: string | number) => Promise<any>;
  onUpdateDynamic?: (params: any) => Promise<any>;

  // 事件回调
  onCreate?: () => void;
  onEdit?: (item: DynamicItem) => void;
  onDetail?: (item: DynamicItem) => void;
  onDownload?: (item: DynamicItem) => void;
  onRefresh?: (id: string | number) => Promise<any>;
  onToggleTop?: (id: string | number, isTop: number) => Promise<any>;
  onBatch?: () => void;
  onContact?: () => void;
  onShare?: () => void;
  onMultiShare?: () => void;
  onMore?: () => void;
  onSort?: () => void;
  onClassify?: () => void;
  onAddCart?: (item: DynamicItem) => void;

  // 外部刷新键，用于触发组件内部数据刷新而不重新挂载组件
  externalRefreshKey?: number;
  // 外部可控排序类型：1 按创建时间，2 按更新时间
  externalSortType?: number;
  // 外部可控标签：'all' | 'new'
  externalTab?: TabType;

  // 联系弹窗相关回调
  onCopyWechat?: (wechatNumber: string) => void;
  onCopyPhone?: (contactMobile: string) => void;
  onCallPhone?: (contactMobile: string) => void;
  onDownloadQrCode?: (qrCodeUrl: string) => void;
  onChat?: () => void;

  // 购物车弹窗相关回调
  onAddToCart?: (cartData: any) => Promise<void>;
  onBuyNow?: (buyNowData: any) => void;

  // 自定义渲染
  renderActions?: (item: DynamicItem) => React.ReactNode;
  renderHeader?: (userInfo: UserHomeTopData) => React.ReactNode;
  renderEmpty?: () => React.ReactNode;

  // 其他配置
  showTabs?: boolean;
  showSearch?: boolean;
  showSort?: boolean;
  showCreate?: boolean;
  showBottomTabs?: boolean;
  showContactPopup?: boolean; // 是否显示内置联系弹窗
  showCartModal?: boolean; // 是否显示内置购物车弹窗
  showMore?: boolean;

  // Tags相关
  selectedTags?: TagItem[]; // 选中的标签列表
  showTags?: boolean; // 是否显示标签区域
}

const tabList = [
  { title: "全部", type: "all" },
  { title: "上新", type: "new" },
  // { title: '视频', type: 'video' },
  // { title: '图集', type: 'image' },
];

const AlbumCore: React.FC<AlbumCoreProps> = ({
  title = "相册",
  userId,
  isOwnAlbum = false,
  onGetUserInfo,
  onGetAlbumList,
  onDeleteDynamic,
  onUpdateDynamic,
  onCreate,
  onEdit,
  onDetail,
  onDownload,
  onRefresh,
  onToggleTop,
  onBatch,
  onContact,
  onShare,
  onMultiShare,
  onMore,
  onSort,
  onClassify,
  onAddCart,
  externalRefreshKey = 0,
  externalSortType,
  externalTab,
  onCopyWechat,
  onCopyPhone,
  onCallPhone,
  onDownloadQrCode,
  onChat,
  onAddToCart,
  onBuyNow,
  renderActions,
  renderHeader,
  renderEmpty,
  showTabs = true,
  showSearch = true,
  showSort = true,
  showCreate = true,
  showBottomTabs = true,
  showContactPopup = false,
  showCartModal = false,
  showMore = false,
  selectedTags = [],
  showTags = false,
}) => {
  // 状态管理
  const [search, setSearch] = useState("");
  const [currentTab, setCurrentTab] = useState<TabType>("all");
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [attrExpandedKeys, setAttrExpandedKeys] = useState<
    Array<string | number>
  >([]);
  const [pinnedAttrExpandedKeys, setPinnedAttrExpandedKeys] = useState<
    Array<string | number>
  >([]);
  const [dynamic, setDynamic] = useState<DynamicData>({
    items: [],
    hasMore: true,
    allItems: [],
  });
  // 各标签独立数据与分页
  const [dynamicAll, setDynamicAll] = useState<DynamicData>({
    items: [],
    hasMore: true,
    allItems: [],
  });
  const [dynamicNew, setDynamicNew] = useState<DynamicData>({
    items: [],
    hasMore: true,
    allItems: [],
  });
  const [userHomeTop, setUserHomeTop] = useState<UserHomeTopData>({});
  const [pageNo, setPageNo] = useState(1);
  const [pageNoAll, setPageNoAll] = useState(1);
  const [pageNoNew, setPageNoNew] = useState(1);
  const [pageSize] = useState(10);
  const [sortType, setSortType] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [isContentExpanded, setIsContentExpanded] = useState(false);
  const [loadStatus, setLoadStatus] = useState<LoadMoreStatus>("prepare");
  const [dataRefreshKey, setDataRefreshKey] = useState(0); // 用于局部数据刷新

  // 更多弹窗相关状态
  const [showMoreState, setShowMoreState] = useState(false);

  // 联系弹窗相关状态
  const [showContactPopupState, setShowContactPopupState] = useState(false);

  // 购物车弹窗相关状态
  const [cartModalVisible, setCartModalVisible] = useState(false);
  const [currentCartItem, setCurrentCartItem] = useState<DynamicItem | null>(
    null
  );
  const { initPermissions, platformRef } = usePermission();

  const PINNED_KEY = "__PINNED__";
  // 独立置顶数据
  const [pinnedItems, setPinnedItems] = useState<DynamicItem[]>([]);

  // 获取用户信息
  const getUserHomeTop = async () => {
    if (!onGetUserInfo || !userId) return;
    console.log("showBottomTabs", showBottomTabs);

    try {
      const userInfo = await onGetUserInfo(userId);
      // userInfo.wechatNumber = '11212122';
      // userInfo.mobile = userInfo.mobile || '123213213213';
      // userInfo.wechatQrCode = userInfo.wechatQrCode || 'https://tse1.mm.bing.net/th/id/OIP.mH9YLFEL5YdVxJM82mjVJQHaEo?pid=Api&rs=1&c=1&qlt=95&w=197&h=123';

      setUserHomeTop(userInfo);

      // 根据用户的排序设置更新sortType
      if (
        userInfo.homePageSort &&
        (userInfo.homePageSort === 1 || userInfo.homePageSort === 2)
      ) {
        setSortType(userInfo.homePageSort);
      }
    } catch (error) {
      console.error("getUserInfo failed:", error);
    }
  };
  // 监听外部排序变更并同步到内部
  useEffect(() => {
    if (
      externalSortType &&
      (externalSortType === 1 || externalSortType === 2) &&
      externalSortType !== sortType
    ) {
      setSortType(externalSortType);
      // 重置“全部”分页
      setPageNoAll(1);
      setDynamicAll({ items: [], hasMore: true, allItems: [] });
      if (currentTab === "all") {
        getDynamicListData();
      }
    }
  }, [externalSortType]);

  // 监听外部标签变更并同步到内部（受控）
  useEffect(() => {
    if (!externalTab) return;
    if (externalTab !== currentTab) {
      setCurrentTab(externalTab);
      if (externalTab === "new") {
        setPageNoNew(1);
        setDynamicNew({ items: [], hasMore: true, allItems: [] });
        setTimeout(() => getDynamicListData(" ", "new"), 0);
      } else {
        setPageNoAll(1);
        setDynamicAll({ items: [], hasMore: true, allItems: [] });
        setTimeout(() => {
          getDynamicListData(" ", "all");
          getPinnedList();
        }, 0);
      }
    }
  }, [externalTab]);

  // 获取相册列表
  const getDynamicListData = async (content = " ", tabOverride?: TabType) => {
    if (!onGetAlbumList) return;

    // 首页加载显示loading，分页加载显示LoadMore的loading
    const effTab = tabOverride || currentTab;
    const curPageNo = effTab === "new" ? pageNoNew : pageNoAll;
    if (curPageNo === 1 && !isLoading) {
      setIsLoading(true);
      // Taro.showLoading({
      //   title: '加载中...',
      //   mask: true
      // });
    } else if (curPageNo > 1) {
      setLoadStatus("loading");
    }

    // 基础参数
    const data: any = {
      pageNo: curPageNo,
      pageSize: pageSize,
      userId: userId,
      isListed: 1,
      content: content === "" ? content : search,
    };
    if (effTab === "new") {
      // 上新：不传 isTop，homePageCountType=2，sort=1
      data.homePageCountType = 2;
      data.sort = 1;
    } else {
      // 全部：isTop=0，homePageCountType=1，sort 由 sortType 决定
      data.homePageCountType = 1;
      data.isTop = 0;
      data.sort = sortType;
    }

    try {
      const res = await onGetAlbumList(data);

      if (curPageNo === 1) {
        Taro.hideLoading();
      }

      if (res && res.code === 0 && res.data) {
        const itemsWithSkus =
          res.data.list?.map((item) => {
            const pictures = item.pictures || "";
            const pictureArray = pictures.split(",").filter(Boolean);
            const coverImage = pictureArray[0] || "";

            return {
              ...item,
              pictures,
              coverImage: coverImage,
              color: ["红色", "蓝色", "绿色", "黄色", "紫色", "粉色"],
            };
          }) || [];

        // 正文列表独立：前端剔除置顶项，避免正文夹杂置顶
        const nonPinnedBatch = itemsWithSkus.filter((it) => it.isTop !== 1);
        const setDynamicForTab =
          effTab === "new" ? setDynamicNew : setDynamicAll;
        setDynamicForTab((prevDynamic) => {
          const allItems =
            curPageNo === 1
              ? nonPinnedBatch
              : [...(prevDynamic.allItems || []), ...nonPinnedBatch];

          // 处理分组数据
          const grouped = {};
          allItems.forEach((item) => {
            // 根据sortType决定使用createTime还是updateTime进行分组
            const timeField =
              effTab === "new"
                ? item.createTime
                : sortType === 1
                ? item.createTime
                : item.updateTime || item.createTime;
            const time = formatDate(timeField);
            if (!grouped[time]) {
              grouped[time] = [];
            }
            grouped[time].push(item);
          });

          const newFilteredData_s = Object.keys(grouped).map((time) => ({
            group: time,
            items: grouped[time].map((item) => ({
              ...item,
              digitalWatermark: item.digitalWatermark,
            })),
          }));

          // 修正 hasMore 判断逻辑
          const currentPageCount = res.data.list?.length || 0;
          const totalCount = res.data.total || 0;
          const currentTotalLoaded =
            (curPageNo - 1) * pageSize + currentPageCount;
          const hasMore =
            currentTotalLoaded < totalCount && currentPageCount === pageSize;

          console.log("分页信息:", {
            pageNo,
            pageSize,
            currentPageCount,
            totalCount,
            currentTotalLoaded,
            hasMore,
          });

          // 设置所有分组为展开状态
          const allGroupKeys = newFilteredData_s.map((group) => group.group);
          if (curPageNo === 1) {
            setExpandedKeys(allGroupKeys);
          } else {
            setExpandedKeys((prev) => {
              const existingKeys = prev || [];
              const newKeys = allGroupKeys.filter(
                (key) => !existingKeys.includes(key)
              );
              return [...existingKeys, ...newKeys];
            });
          }

          // 更新加载状态
          if (hasMore) {
            setLoadStatus("prepare");
          } else {
            setLoadStatus("nomore");
          }

          return {
            totalCount: res.data.total || 0,
            items: newFilteredData_s,
            hasMore,
            allItems,
          };
        });
      } else {
        setLoadStatus("retry");
        Toast.info({
          content: res.msg || "网络异常，请重试",
          duration: 2000,
        });
      }
    } catch (error) {
      if (curPageNo === 1) {
        Taro.hideLoading();
      }
      setLoadStatus("retry");
      Toast.info({
        content: "网络异常，请重试",
        duration: 2000,
      });
      console.error("获取动态列表失败:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // 独立获取置顶列表（正文不再混入置顶）
  const getPinnedList = async () => {
    if (!onGetAlbumList) return;
    try {
      const res = await onGetAlbumList({
        pageNo: 1,
        pageSize: 100,
        userId,
        isListed: 1,
        // 仅置顶
        isTop: 1,
        // 置顶属于“全部”的语义
        homePageCountType: 1,
      });
      if (res && res?.code === 0) {
        const items: DynamicItem[] = (res.data?.list || []).map((item: any) => {
          const pictures = item.pictures || "";
          const pictureArray = pictures.split(",").filter(Boolean);
          const coverImage = pictureArray[0] || "";
          return { ...item, pictures, coverImage };
        });
        setPinnedItems(items);
      }
    } catch (e) {
      // 忽略
    }
  };

  // 处理 tab 切换
  const handleTabChange = (tab: TabType) => {
    setCurrentTab(tab);
    // 切换标签页时，如该标签无数据则初始化加载
    const hasData =
      (tab === "new" ? dynamicNew.items : dynamicAll.items).length > 0;
    if (!hasData) {
      if (tab === "new") {
        setPageNoNew(1);
        setDynamicNew({ items: [], hasMore: true, allItems: [] });
      } else {
        setPageNoAll(1);
        setDynamicAll({ items: [], hasMore: true, allItems: [] });
      }
      setTimeout(() => getDynamicListData(" ", tab), 0);
    }
    // 非上新时刷新置顶
    if (tab !== "new") {
      getPinnedList();
    }
  };

  // 处理折叠面板展开/收起
  const handleCollapseChange = (value: string) => {
    setExpandedKeys((prev) => {
      const currentKeys = prev;
      const newKeys = currentKeys.includes(value)
        ? currentKeys.filter((key) => key !== value)
        : [...currentKeys, value];
      return newKeys;
    });
  };

  // 商品属性折叠切换
  const handleAttrCollapseChange = (value: string | number) => {
    setAttrExpandedKeys((prev) =>
      prev.includes(value) ? prev.filter((k) => k !== value) : [...prev, value]
    );
  };

  // 置顶列表商品属性折叠切换
  const handlePinnedAttrCollapseChange = (value: string | number) => {
    setPinnedAttrExpandedKeys((prev) =>
      prev.includes(value) ? prev.filter((k) => k !== value) : [...prev, value]
    );
  };

  // 加载更多数据
  const loadingData = () => {
    const hasMore = (
      currentTab === "new" ? dynamicNew.hasMore : dynamicAll.hasMore
    ) as boolean;
    if (!isLoading && hasMore) {
      if (currentTab === "new") {
        setPageNoNew((prev) => prev + 1);
      } else {
        setPageNoAll((prev) => prev + 1);
      }
      getDynamicListData();
    }
  };

  // 搜索处理
  const handleSearch = () => {
    if (currentTab === "new") {
      setPageNoNew(1);
      setDynamicNew({ items: [], hasMore: true, allItems: [] });
    } else {
      setPageNoAll(1);
      setDynamicAll({ items: [], hasMore: true, allItems: [] });
    }
    getDynamicListData();
  };

  const handleClearSearch = () => {
    setSearch("");
    if (currentTab === "new") {
      setPageNoNew(1);
      setDynamicNew({ items: [], hasMore: true, allItems: [] });
    } else {
      setPageNoAll(1);
      setDynamicAll({ items: [], hasMore: true, allItems: [] });
    }
    getDynamicListData("");
  };

  // 联系弹窗相关函数
  const handleContact = () => {
    if (showContactPopup) {
      // 使用内置联系弹窗
      if (
        !userHomeTop?.wechatNumber &&
        !userHomeTop?.contactMobile &&
        !userHomeTop?.wechatQrCode
      ) {
        Toast.info({
          content: "暂无联系方式",
          duration: 2000,
        });
        return;
      }
      setShowContactPopupState(true);
    } else if (onContact) {
      // 使用外部传入的联系处理函数
      onContact();
    }
  };

  const handleMore = () => {
    if (showMore) {
      setShowMoreState(true);
    } else if (onMore) {
      onMore();
    }
  };

  const closeContactPopup = () => {
    setShowContactPopupState(false);
  };

  // 购物车弹窗相关函数
  const handleAddCartClick = (item: DynamicItem) => {
    if (showCartModal) {
      // 使用内置购物车弹窗
      const userInfo = Taro.getStorageSync("userInfo");
      if (!userInfo?.id) {
        Toast.error({ content: "请先登录" });
        return;
      }
      setCurrentCartItem(item);
      setCartModalVisible(true);
    } else if (onAddCart) {
      // 使用外部传入的购物车处理函数
      onAddCart(item);
    }
  };

  const closeCartModal = () => {
    setCartModalVisible(false);
    setCurrentCartItem(null);
  };

  // 处理加入购物车API调用
  const handleAddToCartInternal = async (cartData: any) => {
    if (onAddToCart) {
      await onAddToCart(cartData);
    } else {
      // 默认实现
      try {
        const res: any = await addCart(cartData);
        if (res && res.code === 0) {
          Toast.success({ content: "加入购物车成功" });
          await fetchCartCountUtil();
        } else {
          Toast.error({ content: res.msg || "加入购物车失败" });
        }
      } catch (error) {
        console.error("加入购物车失败:", error);
        Toast.error({ content: "加入购物车失败" });
      }
    }
    closeCartModal();
  };

  // 处理立即购买
  const handleBuyNowInternal = (buyNowData: any) => {
    if (onBuyNow) {
      onBuyNow(buyNowData);
    } else {
      // 默认实现
      Taro.setStorageSync("selectedItems", buyNowData);
      Taro.navigateTo({
        url: "/pageOrder/order/pay/index?from=detail",
      });
    }
    closeCartModal();
  };

  const handleMoreActionConfirm = (index: number) => {
    if (index === 0) {
      // 拉黑商家
      handleBlockMerchant();
    } else if (index === 1) {
      // 举报
      handleReportMerchant();
    } else if (index === 2) {
      // 关注/取消关注
      handleFollowToggle();
    }
  };

  const handleMoreActionClose = () => {
    setShowMoreState(false);
  };

  // 拉黑商家
  const handleBlockMerchant = () => {
    console.log("拉黑商家", userHomeTop);
    Dialog.confirm({
      title: "拉黑商家",
      children: (
        <View>
          <Text>是否拉黑 </Text>
          <Text style={{ color: getCSSVariableValue("--primary-color") }}>
            {userHomeTop.nickname}
          </Text>
        </View>
      ),
      okText: "确定",
      cancelText: "取消",
      onOk: () => {
        unfollowUser(userHomeTop.userId || userId)
          .then((res: any) => {
            if (res && res.code == 0) {
              toast("success", {
                content: "拉黑成功",
                duration: 2000,
              });
              // 可以选择返回上一页或刷新页面
              setTimeout(() => {
                Taro.navigateBack({
                  delta: 1,
                });
              }, 1000);
            } else {
              toast("error", {
                content: res.msg || "拉黑失败",
                duration: 2000,
              });
            }
          })
          .catch((error) => {
            console.error("拉黑失败:", error);
            toast("error", {
              content: "拉黑失败，请重试",
              duration: 2000,
            });
          });
      },
      platform: "ios",
    });
  };

  // 举报商家
  const handleReportMerchant = () => {
    console.log("举报商家", userHomeTop);
    Taro.navigateTo({
      url: `/pageDynamic/report/reportType/index?type=user&id=${
        userHomeTop.userId || userId
      }`,
    });
  };

  // 关注/取消关注
  const handleFollowToggle = async () => {
    // 获取当前登录用户信息
    const currentUserInfo = Taro.getStorageSync("userInfo");
    const isFollowing = userHomeTop?.isFollow;
    const actionText = isFollowing ? "取消关注" : "关注";

    // 执行关注/取消关注操作
    const executeAction = async () => {
      try {
        let res: any;
        if (isFollowing) {
          // 取消关注
          res = await unfollowUser(userHomeTop.userId || userId);
        } else {
          // 关注
          res = await followUser({
            userId: currentUserInfo.id,
            followUserId: String(userHomeTop.userId || userId),
          });
        }

        if (res && res.code === 0) {
          // 更新本地状态
          setUserHomeTop((prev) => ({
            ...prev,
            isFollow: isFollowing ? 0 : 1,
          }));

          toast("success", {
            content: `${actionText}成功`,
            duration: 2000,
          });
        } else {
          toast("error", {
            content: res.msg || `${actionText}失败`,
            duration: 2000,
          });
        }
      } catch (error) {
        console.error(`${actionText}失败:`, error);
        toast("error", {
          content: `${actionText}失败，请重试`,
          duration: 2000,
        });
      }
    };

    // 如果是取消关注，需要确认；如果是关注，直接执行
    if (isFollowing) {
      Dialog.confirm({
        title: "确认操作",
        children: `确定要取消关注 ${userHomeTop.nickname} 吗？`,
        okText: "确定",
        cancelText: "取消",
        onOk: executeAction,
        platform: "ios",
      });
    } else {
      // 关注操作直接执行，不需要确认
      await executeAction();
    }
  };

  // 初始化用户信息（只在组件挂载时调用一次）
  useEffect(() => {
    const cleanup = initPermissions();
    getUserHomeTop();
    return () => {
      cleanup && cleanup();
    };
  }, []);

  // 监听外部刷新键变化
  useEffect(() => {
    if (externalRefreshKey > 0) {
      setDataRefreshKey(externalRefreshKey);
      setPageNo(1); // 重置到第一页
    }
  }, [externalRefreshKey]);

  // 处理分页和排序数据
  useEffect(() => {
    if (pageNo === 1) {
      setDynamic({
        items: [],
        hasMore: true,
        allItems: [],
      });
    }
    getDynamicListData();
  }, [pageNo, sortType, dataRefreshKey]); // 添加dataRefreshKey依赖

  // 设置全局下载回调
  // useEffect(() => {
  //   // 下载成功回调
  //   // const webDownloadSuc = () => {
  //   //   console.log('webDownloadSuc 被调用');

  //   //   // 直接使用 Toast 组件
  //   //   Toast.success({
  //   //     content: "下载成功",
  //   //     duration: 2000,
  //   //   });
  //   // };

  //   // // 下载失败回调
  //   // const webDownloadFail = () => {
  //   //   console.log('webDownloadFail 被调用');

  //   //   // 直接使用 Toast 组件
  //   //   Toast.error({
  //   //     content: "下载失败",
  //   //     duration: 2000,
  //   //   });
  //   // };

  //   // 检查是否是 Android 环境
  //   let uaAll = window.navigator.userAgent;
  //   let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
  //   let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
  //   let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;

  //   console.log('User Agent:', uaAll);
  //   console.log('APP_NAME:', APP_NAME);
  //   console.log('是否为 Android 环境:', isAndroid);

  //   let callbackCleanup: (() => void) | null = null;

  //   // if (isAndroid || isHM) {
  //   //   console.log('注册下载回调方法到全局回调管理器');
  //   //   // 使用全局回调管理器注册下载回调
  //   //   // callbackCleanup = useGlobalCallbacks('albumCore', {
  //   //   //   webDownloadSuc: webDownloadSuc,
  //   //   //   webDownloadFail: webDownloadFail,
  //   //   // });

  //   //   // 验证注册是否成功
  //   //   console.log('已注册到全局回调管理器');
  //   // } else {
  //   //   console.log('非 Android/HM 环境，不注册下载回调');
  //   // }

  //   // 清理事件监听
  //   return () => {
  //     if (callbackCleanup) {
  //       console.log('清理下载回调方法');
  //       callbackCleanup();
  //     }
  //   };
  // }, []);

  useDidShow(() => {
    // 页面显示时的处理
    // 初始化加载置顶（非上新场景显示）
    getPinnedList();
  });

  // 上拉触底加载更多
  useReachBottom(() => {
    console.log("useReachBottom 被触发:", {
      isLoading,
      hasMore: currentTab === "new" ? dynamicNew.hasMore : dynamicAll.hasMore,
      currentPageNo: currentTab === "new" ? pageNoNew : pageNoAll,
    });

    const hasMore = (
      currentTab === "new" ? dynamicNew.hasMore : dynamicAll.hasMore
    ) as boolean;
    if (!isLoading && hasMore) {
      console.log("满足加载条件，准备加载下一页");
      if (currentTab === "new") {
        setPageNoNew((prev) => prev + 1);
      } else {
        setPageNoAll((prev) => prev + 1);
      }
      getDynamicListData();
    } else {
      console.log("不满足加载条件，跳过加载");
    }
  });

  return (
    // <ScrollView
    //   className="album-list-scroll"
    //   style={{ height: "100%" }}
    //   scrollY
    //   onScrollToLower={loadingData}
    // >
    <>
      <View className="album-page">
        {/* 顶部导航栏 */}

        {platformRef.current !== "WX" && <YkNavBar title={title} />}

        {/* 页面主要内容区域 */}
        <View className="album-content-page">
          {/* 用户头部信息区域 */}
          {renderHeader ? (
            renderHeader(userHomeTop)
          ) : (
            <AlbumHeader
              userInfo={userHomeTop}
              isOwnAlbum={isOwnAlbum}
              onQrcodeClick={({ userId: targetUserId, nickname, avatar }) => {
                if (platformRef.current === "WX") {
                  let userInfoTemp = {
                    userId: targetUserId,
                    nickname: nickname,
                    avatar: avatar,
                  };
                  wx.miniProgram.navigateTo({
                    url:
                      "/pages/qrcode/index?userInfo=" +
                      encodeURIComponent(JSON.stringify(userInfoTemp)),
                  });
                } else {
                  if (isOwnAlbum) {
                    Taro.navigateTo({ url: "/pageUserInfo/qrcode/index" });
                  } else {
                    const uid = targetUserId || userId;
                    const userinfo = encodeURIComponent(
                      JSON.stringify({
                        uid,
                        nickname: nickname || "",
                        avatar: avatar || "",
                      })
                    );
                    Taro.navigateTo({
                      url: `/pageUserInfo/qrcode/index?userinfo=${userinfo}`,
                    });
                  }
                }
              }}
            />
          )}

          {/* Tab栏 */}
          {showTabs && (
            <Tabs
              tabs={tabList.map((item) => ({ title: item.title }))}
              type="line-divide"
              defaultActiveTab={0}
              tabBarHasDivider={false}
              onChange={(_, index) =>
                handleTabChange(tabList[index].type as TabType)
              }
            />
          )}

          {/* 搜索栏 */}
          {showSearch && (
            <View className="user-header-searchbar-wrap">
              <SearchBar
                actionButton={
                  <span className="user-header-filter" onClick={handleSearch}>
                    {/* 搜索 */}
                  </span>
                }
                placeholder="请输入要搜索的内容"
                onChange={(e, value) => setSearch(value)}
                value={search}
                className="user-header-searchbar"
                clearable
                onClear={handleClearSearch}
                onPressEnter={handleSearch}
              />
            </View>
          )}

          {/* 选中的标签显示区域 */}
          {showTags && selectedTags && selectedTags.length > 0 && (
            <View className="selected-tags-container">
              <View className="selected-tags-list">
                {selectedTags.map((tag) => (
                  <View key={tag.id} className="selected-tag-item">
                    <Text className="selected-tag-text">{tag.name}</Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* 排序和创建按钮 */}
          {showSort && currentTab !== "new" && (
            <View className="user-header-sort">
              {showCreate && (
                <Image
                  className="move-img-add-img"
                  src={require("@/assets/images/common/album_publish.png")}
                  onClick={onCreate}
                />
              )}
              <View className="user-header-sort-view">
                <Text className="user-header-sort-text" onClick={onSort}>
                  {sortType === 1 ? "按发布时间排序" : "按更新时间排序"}
                </Text>
                {/* <Image
                  className="user-header-sort-icon"
                  src={require("@/assets/images/common/arrow_down_blue.png")}
                /> */}
                <IconTriDown className="user-header-sort-icon" />
              </View>
            </View>
          )}

          {/* 商品列表（各标签独立数据；置顶独立传入）*/}
          {(currentTab === "new" ? dynamicNew.items : dynamicAll.items).length >
          0 ? (
            <AlbumList
              items={currentTab === "new" ? dynamicNew.items : dynamicAll.items}
              pinnedItems={currentTab !== "new" ? pinnedItems : []}
              expandedKeys={expandedKeys}
              attrExpandedKeys={attrExpandedKeys}
              pinnedAttrExpandedKeys={pinnedAttrExpandedKeys}
              isOwnAlbum={isOwnAlbum}
              hasOpenOnlinePayment={userHomeTop.hasOpenOnlinePayment}
              showPinned={currentTab !== "new"}
              onCollapseChange={handleCollapseChange}
              onAttrCollapseChange={handleAttrCollapseChange}
              onPinnedAttrCollapseChange={handlePinnedAttrCollapseChange}
              onDelete={onDeleteDynamic}
              onEdit={onEdit}
              onDownload={onDownload}
              onRefresh={onRefresh}
              onToggleTop={onToggleTop}
              onShare={onShare}
              onDetail={onDetail}
              onAddCart={handleAddCartClick}
            />
          ) : renderEmpty ? (
            renderEmpty()
          ) : (
            <View className="empty-state">
              {/* <Image
                  className="empty-image"
                  src="https://img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"
                /> */}
              <Text className="empty-text">暂无数据</Text>
            </View>
          )}

          {/* 使用arco-design的LoadMore组件 */}
          {(currentTab === "new" ? pageNoNew : pageNoAll) > 1 && (
            <LoadMore
              style={{ paddingTop: 16, paddingBottom: 80 }}
              status={loadStatus}
            />
          )}

          {/* 底部操作栏 */}
          {showBottomTabs &&
            (isOwnAlbum ? (
              <View className="bottom-tabs">
                <View className="bottom-tab-item">
                  <Text className="bottom-tab-text" onClick={onClassify}>
                    商品分类
                  </Text>
                </View>
                <View className="bottom-tab-item">
                  <Text className="bottom-tab-text" onClick={onBatch}>
                    批量操作
                  </Text>
                </View>
                {false && (
                  <View className="bottom-tab-item">
                    <Text className="bottom-tab-text" onClick={onShare}>
                      分享
                    </Text>
                  </View>
                )}
              </View>
            ) : (
              <View className="bottom-tabs">
                <View className="bottom-tab-item">
                  <Text className="bottom-tab-text" onClick={onClassify}>
                    商品分类
                  </Text>
                </View>
                <View className="bottom-tab-item">
                  <Text className="bottom-tab-text" onClick={handleContact}>
                    联系Ta
                  </Text>
                </View>
                <View className="bottom-tab-item">
                  <Text
                    className="bottom-tab-text"
                    onClick={() => {
                      if (onMultiShare) {
                        onMultiShare();
                      } else {
                          // 默认跳转逻辑
                          Taro.navigateTo({
                            url: `/pageDynamic/album/batchForward/index?userId=${userId}`,
                          });
                      }
                    }}
                  >
                    批量转发
                  </Text>
                </View>
                <View className="bottom-tab-item">
                  <Text className="bottom-tab-text" onClick={handleMore}>
                    更多
                  </Text>
                </View>
              </View>
            ))}
        </View>
      </View>

      {/* 联系Ta弹框 */}
      {showContactPopup && (
        <ContactModal
          visible={showContactPopupState}
          onClose={closeContactPopup}
          contactInfo={{
            wechatQrCode: userHomeTop?.wechatQrCode,
            wechatNumber: userHomeTop?.wechatNumber,
            contactMobile: userHomeTop?.contactMobile,
            nickname: userHomeTop?.nickname,
          }}
          // onDownloadQrCode={handleDownloadQrCode}
          // onCopyWechat={handleCopyWechat}
          // onCopyPhone={handleCopyPhone}
          // onCallPhone={handleCallPhone}
          // onChat={handleChatContact}
        />
      )}

      {/* 购物车弹窗 */}
      {showCartModal && currentCartItem && (
        <CartModal
          visible={cartModalVisible}
          onClose={closeCartModal}
          productData={{
            id: String(currentCartItem.id),
            pictures: currentCartItem.pictures,
            content: currentCartItem.content,
            price: currentCartItem.price,
            // 新格式：将分离的数组转换为对象格式
            colors: currentCartItem.colors,
            specifications: currentCartItem.specifications,
            userId: String(currentCartItem.userId),
          }}
          merchantInfo={{
            nickname: userHomeTop.nickname,
            avatar: userHomeTop.avatar,
          }}
          onAddCart={handleAddToCartInternal}
          onBuyNow={handleBuyNowInternal}
        />
      )}

      {/* 更多弹窗 */}
      {showMore && (
        <BottomPopup
          options={[
            {
              label: "拉黑商家",
              description: userHomeTop?.nickname || "",
            },
            {
              label: "举报",
              description: "发现商家存在违禁行为",
            },
            {
              label: `${userHomeTop?.isFollow ? "取消关注" : "关注"}`,
              style: { color: "#ff4d4f" }, // 红色样式
            },
          ]}
          btnCloseText="取消"
          onConfirm={handleMoreActionConfirm}
          onClose={handleMoreActionClose}
          visible={showMoreState}
        />
      )}
    </>
    // </ScrollView>
  );
};

export default AlbumCore;
