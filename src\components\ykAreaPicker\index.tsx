import { View } from "@tarojs/components";
import { Popup, PickerView } from "@arco-design/mobile-react";
import React, { useState, useEffect, useMemo} from "react";
import './index.less';
import areaDataType from '@/constants/area.json'; // 引入公有目录下的完整地区数据

export type AreaDataType = typeof areaDataType;

// 数据转换函数：将 id/name 格式转换为 PickerView 需要的 value/label 格式
const transformAreaData = (data: any[]): any[] => {
  return data.map(item => ({
    value: item.name, // 使用 name 作为 value
    label: item.name, // 使用 name 作为 label
    children: item.children ? transformAreaData(item.children) : undefined
  }));
};

// 根据地区名称查找对应的 ID
const findAreaIdByName = (data: any[], provinceName: string, cityName: string, areaName: string): string => {
  for (const province of data) {
    if (province.name === provinceName) {
      if (!cityName) return province.id;

      for (const city of province.children || []) {
        if (city.name === cityName) {
          if (!areaName) return city.id;

          for (const area of city.children || []) {
            if (area.name === areaName) {
              return area.id;
            }
          }
        }
      }
    }
  }
  return '';
};

export interface YkAreaPickerProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (value: { province: string; city: string; area: string; areaId: string }) => void;
  title?: string;
  data?: AreaDataType; // 使用本地数据类型
  currentValue?: string[];
}

const DEFAULT_VALUE = ['北京市', '北京市', '东城区'];

const YkAreaPicker: React.FC<YkAreaPickerProps> = ({
  visible,
  onClose,
  onConfirm,
  title = "选择地区",
  data = areaDataType, // 默认用本地完整数据
  currentValue
}) => {
  const [selectedValue, setSelectedValue] = useState<string[]>(currentValue || DEFAULT_VALUE);

  // 转换数据格式为 PickerView 需要的格式
  const transformedData = useMemo(() => transformAreaData(data), [data]);

  useEffect(() => {
    if (visible) {
      setSelectedValue(currentValue || DEFAULT_VALUE);
    }
  }, [currentValue, visible]);

  const handleConfirm = () => {
    const [province, city, area] = selectedValue;

    // 查找选中地区的 ID
    const areaId = findAreaIdByName(
      data,
      province || DEFAULT_VALUE[0],
      city || DEFAULT_VALUE[1],
      area || DEFAULT_VALUE[2]
    );

    onConfirm({
      province: province || DEFAULT_VALUE[0],
      city: city || DEFAULT_VALUE[1],
      area: area || DEFAULT_VALUE[2],
      areaId: areaId
    });
    onClose();
  };

  return (
    <Popup
      visible={visible}
      close={onClose}
      className="area-picker"
    >
      <View className="picker-content">
        <View className="picker-header">
          <View className="cancel-btn" onClick={onClose}>取消</View>
          <View className="title">{title}</View>
          <View className="confirm-btn" onClick={handleConfirm}>确定</View>
        </View>
        <PickerView
          cascade={true}
          data={transformedData}
          value={selectedValue}
          onPickerChange={(value) => setSelectedValue(value.map(String))}
          className="picker-view"
        />
      </View>
    </Popup>
  );
};

export default YkAreaPicker;
