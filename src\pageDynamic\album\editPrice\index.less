@import "@arco-design/mobile-react/style/mixin.less";
.edit-price-page {
  overflow-y: auto;
  height: 100vh;
  background-color: #ffffff;
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });
}

.hidden-style {
  opacity: 0;
}

.box {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 7px;
  margin: 10px 10px 0 10px;
  padding: 15px 15px 0 15px;
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });
}

.rule {
  margin-left: 15px;
  margin-top: 10px;
  display: flex;
  align-items: center;
  .rule-img {
    width: 18px;
    height: 18px;
    margin-right: 4px;
  }
  .rule-text {
    font-size: 14px;
    color: #86909c;
    .use-dark-mode-query({
      color: var(--dark-font-color);
    });
  }
}

.type {
  display: flex;
  border-radius: 5px;
  background-color: #f7f5f5;
  padding: 5px;
  .use-dark-mode-query({
    background-color: #2a2a2a;
  });

  &-change {
    display: flex;
    padding: 5px 0;
    text-align: center;
    flex: 1;

    &-text {
      padding: 5px 0;
      flex: 1;
      font-size: 13px;
      color: #333333;
      .use-dark-mode-query({
        color: var(--dark-font-color);
      });
    }

    &-textA {
      padding: 5px 0;
      flex: 1;
      font-size: 13px;
      color: #165dff;
      background-color: #ffffff;
      border-radius: 5px;
      .use-dark-mode-query({
        background-color: #1a1a1a;
      });
    }
  }

  &-save {
    display: flex;
    padding: 5px 0;
    text-align: center;
    flex: 1;

    &-text {
      padding: 5px 0;
      flex: 1;
      font-size: 13px;
      color: #333333;
      .use-dark-mode-query({
        color: var(--dark-font-color);
      });
    }

    &-textA {
      padding: 5px 0;
      flex: 1;
      font-size: 13px;
      color: #165dff;
      background-color: #ffffff;
      border-radius: 5px;
      .use-dark-mode-query({
        background-color: #1a1a1a;
      });
    }
  }

  &-del {
    display: flex;
    padding: 5px 0;
    text-align: center;
    flex: 1;

    &-text {
      padding: 5px 0;
      flex: 1;
      font-size: 13px;
      color: #333333;
      .use-dark-mode-query({
        color: var(--dark-font-color);
      });
    }

    &-textA {
      padding: 5px 0;
      flex: 1;
      font-size: 13px;
      color: #165dff;
      background-color: #ffffff;
      border-radius: 5px;
      .use-dark-mode-query({
        background-color: #1a1a1a;
      });
    }
  }
}

.change {
  margin-top: 15px;
  display: flex;
  flex-direction: column;

  &-title {
    margin-bottom: 10px;
    font-size: 13px;
    color: #666666;
    .use-dark-mode-query({
      color: var(--dark-font-color);
    });
  }

  &-type {
    display: flex;

    &-money {
      padding: 7px 0;
      text-align: center;
      flex: 1;
      font-size: 13px;
      color: #333333;
      border-radius: 5px;
      border: 1px solid #f7fbfa;
      background-color: #f7fbfa;
      .use-dark-mode-query({
        color: var(--dark-font-color);
        background-color: #2a2a2a;
        border-color: #3a3a3a;
      });
      cursor: pointer;
    }

    &-moneyA {
      padding: 7px 0;
      text-align: center;
      flex: 1;
      font-size: 13px;
      color: #165dff;
      border-radius: 5px;
      border: 1px solid #165dff;
      background-color: #f7fbfa;
      .use-dark-mode-query({
        background-color: #2a2a2a;
      });
      cursor: pointer;
    }

    &-percent {
      margin-left: 7px;
      padding: 7px 0;
      text-align: center;
      flex: 1;
      font-size: 13px;
      color: #333333;
      border-radius: 5px;
      border: 1px solid #f7fbfa;
      background-color: #f7fbfa;
      .use-dark-mode-query({
        color: var(--dark-font-color);
        background-color: #2a2a2a;
        border-color: #3a3a3a;
      });
      cursor: pointer;
    }

    &-percentA {
      margin-left: 7px;
      padding: 7px 0;
      text-align: center;
      flex: 1;
      font-size: 13px;
      color: #165dff;
      border-radius: 5px;
      border: 1px solid #165dff;
      background-color: #f7fbfa;
      .use-dark-mode-query({
        background-color: #2a2a2a;
      });
      cursor: pointer;
    }

    &-same {
      margin-left: 7px;
      padding: 7px 0;
      text-align: center;
      flex: 1;
      font-size: 13px;
      color: #333333;
      border-radius: 5px;
      border: 1px solid #f7fbfa;
      background-color: #f7fbfa;
      .use-dark-mode-query({
        color: var(--dark-font-color);
        background-color: #2a2a2a;
        border-color: #3a3a3a;
      });
      cursor: pointer;
    }

    &-sameA {
      margin-left: 7px;
      padding: 7px 0;
      text-align: center;
      flex: 1;
      font-size: 13px;
      color: #165dff;
      border-radius: 5px;
      border: 1px solid #165dff;
      background-color: #f7fbfa;
      .use-dark-mode-query({
        background-color: #2a2a2a;
      });
      cursor: pointer;
    }
  }
}

.fanshi {
  margin-top: 15px;
  display: flex;
  flex-direction: column;

  &-title {
    margin-bottom: 10px;
    font-size: 13px;
    color: #666666;
    .use-dark-mode-query({
      color: var(--dark-font-color);
    });
  }

  &-type {
    display: flex;

    &-add {
      padding: 7px 24px;
      text-align: center;
      font-size: 13px;
      color: #333333;
      border-radius: 5px;
      border: 1px solid #f7fbfa;
      background-color: #f7fbfa;
      .use-dark-mode-query({
        color: var(--dark-font-color);
        background-color: #2a2a2a;
        border-color: #3a3a3a;
      });
      cursor: pointer;
    }

    &-addA {
      padding: 7px 24px;
      text-align: center;
      font-size: 13px;
      color: #165dff;
      border-radius: 5px;
      border: 1px solid #165dff;
      background-color: #f7fbfa;
      .use-dark-mode-query({
        background-color: #2a2a2a;
      });
      cursor: pointer;
    }

    &-sub {
      margin-left: 7px;
      padding: 7px 24px;
      text-align: center;
      font-size: 13px;
      color: #333333;
      border-radius: 5px;
      border: 1px solid #f7fbfa;
      background-color: #f7fbfa;
      .use-dark-mode-query({
        color: var(--dark-font-color);
        background-color: #2a2a2a;
        border-color: #3a3a3a;
      });
      cursor: pointer;
    }

    &-subA {
      margin-left: 7px;
      padding: 7px 24px;
      text-align: center;
      font-size: 13px;
      color: #165dff;
      border-radius: 5px;
      border: 1px solid #165dff;
      background-color: #f7fbfa;
      .use-dark-mode-query({
        background-color: #2a2a2a;
      });
      cursor: pointer;
    }
  }
}

.input-section {
  margin-top: 15px;
  display: flex;
  flex-direction: column;

  .input-title {
    font-weight: bold;
    font-size: 15px;
    color: #000000;
    margin-bottom: 10px;
  }

  .input-container {
    display: flex;
    align-items: center;
    border-radius: 7px;
    background-color: #f7f5f5;
    padding: 10px;

    .price-input,
    .percent-input {
      flex: 1;
      border: none;
      background: transparent;
      font-size: 14px;
      color: #333333;
    }

    .input-unit {
      margin-left: 5px;
      font-size: 14px;
      color: #666666;
    }
  }
}

.holder {
  height: 60px;
}

.footerbtn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 10px 15px;
  border-top: 1px solid var(--line-color);
  .use-dark-mode-query({
    background-color: @dark-background-color;
    border-top: 1px solid @dark-line-color;
  });

  &-btn {
    width: 100%;
    height: 40px;
    background-color: var(--primary-color);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    text {
      color: #ffffff;
      font-size: 16px;
      font-weight: bold;
    }

    &:active {
      background-color: var(--primary-color);
      opacity: 0.8;
    }
  }
}

.clickOpacity {
  cursor: pointer;
  transition: opacity 0.2s;

  &:active {
    opacity: 0.7;
  }
}

.clickBg {
  cursor: pointer;
  transition: background-color 0.2s;
}

// 悬浮窗遮罩
.popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

// 标签悬浮窗
.tagpopup {
  position: absolute;
  right: 10px;
  width: 141px;

  &-bottomBox {
    width: 100%;
    overflow: hidden;
    background-color: #ffffff;
    border-radius: 6px;
    .use-dark-mode-query({
      background-color: @dark-background-color;
      border: 1px solid @dark-line-color;
    });

    &-line {
      padding: 0 10px;
      width: calc(100% - 20px);
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;

      &-text {
        font-size: 14px;
        color: #333333;
        .use-dark-mode-query({
          color: var(--dark-font-color);
        });
      }

      &-textC {
        font-size: 14px;
        color: #165dff;
      }

      &-img {
        width: 13px;
        height: 9px;
        display: block;
      }
    }

    &-divider {
      margin: 0 10px;
      border-bottom: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-bottom: 1px solid @dark-line-color;
    });
    }
  }
}

// 商品规格悬浮窗
.formatpopup {
  position: absolute;
  right: 10px;
  width: 141px;

  &-bottomBox {
    width: 100%;
    overflow: hidden;
    background-color: #ffffff;
    border-radius: 6px;
    .use-dark-mode-query({
      background-color: @dark-background-color;
      border: 1px solid @dark-line-color;
    });

    &-line {
      padding: 0 10px;
      width: calc(100% - 20px);
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;

      &-text {
        font-size: 14px;
        color: #333333;
        .use-dark-mode-query({
          color: var(--dark-font-color);
        });
      }

      &-textC {
        font-size: 14px;
        color: #165dff;
      }

      &-img {
        width: 13px;
        height: 9px;
        display: block;
      }
    }

    &-divider {
      margin: 0 10px;
      border-bottom: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-bottom: 1px solid @dark-line-color;
    });
    }
  }
}

// 去掉小数点选项
.xiaoshu {
  margin-top: 15px;
  display: flex;
  align-items: center;
  cursor: pointer;

  &-text {
    margin-left: 5px;
    font-size: 13px;
    color: #999999;
    .use-dark-mode-query({
      color: @dark-font-color;
    });
  }
}

// 金额输入框
.money {
  display: flex;
  align-items: center;

  &-unit {
    font-weight: bold;
    font-size: 22px;
    color: #000000;
    .use-dark-mode-query({
      color: var(--dark-font-color);
    });
  }

  &-input {
    font-weight: bold;
    font-size: 27px;
    color: #333333;
    flex: 1;
    .use-dark-mode-query({
      color: var(--dark-font-color);
      background-color: @dark-background-color;
    });
  }
}

// 比例输入框
.percent {
  display: flex;
  align-items: center;
  justify-content: space-between;

  &-input {
    padding: 0 !important;
    font-weight: bold;
    font-size: 27px;
    color: #333333;
    flex: 1;
    .use-dark-mode-query({
      color: var(--dark-font-color);
      background-color: @dark-background-color;
    });
  }

  &-unit {
    font-weight: bold;
    font-size: 22px;
    color: #000000;
    .use-dark-mode-query({
      color: var(--dark-font-color);
    });
  }
}

// 统一价提示和输入框
.same {
  margin-top: 15px;
  display: flex;
  flex-direction: column;

  &-hint {
    display: flex;
    align-items: center;

    &-text {
      font-size: 13px;
      color: #e35848;
    }

    &-text2 {
      font-size: 13px;
      color: #999999;
      .use-dark-mode-query({
        color: @dark-font-color;
      });
    }
  }

  &-content {
    display: flex;
    align-items: center;

    &-unit {
      font-weight: bold;
      font-size: 22px;
      color: #000000;
      .use-dark-mode-query({
        color: var(--dark-font-color);
      });
    }

    &-input {
      margin-left: 5px;
      font-weight: bold;
      font-size: 27px;
      color: #333333;
      flex: 1;
      .use-dark-mode-query({
        color: var(--dark-font-color);
        background-color: @dark-background-color;
      });
    }
  }
}

// 保留原价提示
.save {
  margin: 15px 25px 0 25px;
  display: flex;
  flex-direction: column;

  &-hint {
    display: flex;
    align-items: center;

    &-text {
      font-size: 13px;
      color: #e35848;
    }

    &-text2 {
      font-size: 13px;
      color: #999999;
      .use-dark-mode-query({
        color: @dark-font-color;
      });
    }
  }
}

// 去掉原价选项
.del {
  margin: 15px 25px 0 25px;
  display: flex;

  &-item1 {
    display: flex;
    align-items: center;
    cursor: pointer;

    &-text {
      margin-left: 5px;
      font-size: 13px;
      color: #999999;
      .use-dark-mode-query({
        color: @dark-font-color;
      });
    }
  }

  &-item2 {
    margin-left: 20px;
    display: flex;
    align-items: center;
    cursor: pointer;

    &-text {
      margin-left: 5px;
      font-size: 13px;
      color: #999999;
      .use-dark-mode-query({
        color: @dark-font-color;
      });
    }
  }
}

// 第二个 box
.box2 {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 7px;
  margin: 0 10px 10px 10px;
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });

  .content {
    height: 49px;
    width: calc(100% - 30px);
    padding: 0 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-title {
      font-size: 15px;
      color: #333333;
      .use-dark-mode-query({
        color: var(--dark-font-color);
      });
    }

    &-right {
      margin-left: 80px;
      flex: 1;
      display: flex;
      align-items: center;

      &-text {
        flex: 1;
        text-align: right;
        font-size: 15px;
        color: #999999;
        .use-dark-mode-query({
          color: @dark-font-color;
        });
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }

      &-img {
        margin-left: 8px;
        display: block;
        width: 6px;
        height: 11px;
      }
    }
  }

  .tag {
    height: 49px;
    width: calc(100% - 30px);
    padding: 0 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-title {
      font-size: 15px;
      color: #333333;
      .use-dark-mode-query({
        color: var(--dark-font-color);
      });
    }

    &-right {
      margin-left: 80px;
      flex: 1;
      display: flex;
      align-items: center;

      &-text {
        flex: 1;
        text-align: right;
        font-size: 15px;
        color: #999999;
        .use-dark-mode-query({
          color: @dark-font-color;
        });
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }

      &-img {
        margin-left: 8px;
        display: block;
        width: 6px;
        height: 11px;
      }
    }
  }

  .tagC {
    padding: 12px 10px;
    margin: 0 15px;
    margin-bottom: 15px;
    background-color: #fafafa;
    border-radius: 7px;
    display: flex;
    align-items: center;
    .use-dark-mode-query({
      background-color: #2a2a2a;
    });

    &-text {
      width: 268px;
      font-size: 13px;
      color: #666666;
      flex: 1;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      display: inline-block;
      .use-dark-mode-query({
        color: var(--dark-font-color);
      });
    }

    &-img {
      margin-left: 10px;
      margin-right: 10px;
      width: 11px;
      height: 11px;
      display: block;
    }

    &-add {
      padding: 5px 11px;
      border: 1px dashed #666666;
      border-radius: 50px;
      display: flex;
      align-items: center;
      .use-dark-mode-query({
        border-color: var(--dark-font-color);
      });

      &-text {
        font-size: 11px;
        color: #666666;
        .use-dark-mode-query({
          color: var(--dark-font-color);
        });
      }

      &-img {
        margin-left: 5px;
        width: 11px;
        height: 11px;
        display: block;
      }
    }
  }

  .format {
    height: 49px;
    width: calc(100% - 30px);
    padding: 0 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-title {
      font-size: 15px;
      color: #333333;
      .use-dark-mode-query({
        color: var(--dark-font-color);
      });
    }

    &-right {
      margin-left: 80px;
      flex: 1;
      display: flex;
      align-items: center;

      &-text {
        flex: 1;
        text-align: right;
        font-size: 15px;
        color: #999999;
        .use-dark-mode-query({
          color: @dark-font-color;
        });
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }

      &-img {
        margin-left: 8px;
        display: block;
        width: 6px;
        height: 11px;
      }
    }
  }

  .formatC {
    padding: 12px 10px;
    margin: 0 15px;
    margin-bottom: 15px;
    background-color: #fafafa;
    border-radius: 7px;
    display: flex;
    align-items: center;
    .use-dark-mode-query({
      background-color: #2a2a2a;
    });

    &-text {
      width: 268px;
      flex: 1;
      font-size: 13px;
      color: #666666;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      display: inline-block;
      .use-dark-mode-query({
        color: var(--dark-font-color);
      });
    }

    &-img {
      margin-left: 10px;
      margin-right: 10px;
      width: 11px;
      height: 11px;
      display: block;
    }

    &-add {
      padding: 5px 11px;
      border: 1px dashed #666666;
      border-radius: 50px;
      display: flex;
      align-items: center;
      .use-dark-mode-query({
        border-color: var(--dark-font-color);
      });

      &-text {
        font-size: 11px;
        color: #666666;
        .use-dark-mode-query({
          color: var(--dark-font-color);
        });
      }

      &-img {
        margin-left: 5px;
        width: 11px;
        height: 11px;
        display: block;
      }
    }
  }

  .line {
    height: 1px;
    background-color: #f0f0f0;
    margin: 0 15px;
    .use-dark-mode-query({
      background-color: @dark-line-color;
    });
  }
}

// 规则说明弹框样式
.rule-popup {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  border-radius: 16px 16px 0 0;
  z-index: 1001;
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });

  &-header {
    padding: 20px 20px 0 20px;
    text-align: center;
  }

  &-title {
    font-size: 18px;
    font-weight: 600;
    color: #1d2129;
    margin-bottom: 20px;
    .use-dark-mode-query({
      color: var(--dark-font-color);
    });
  }

  &-content {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
  }

  &-footer {
    padding: 0 20px 20px 20px;
  }

  &-btn {
    width: 100%;
    height: 44px;
    background-color: #165dff;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    &:active {
      background-color: #0e42d2;
    }

    &-text {
      color: #ffffff;
      font-size: 16px;
      font-weight: 500;
    }
  }
}

.rule-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  &-title {
    font-size: 16px;
    font-weight: 500;
    color: #1d2129;
    margin-bottom: 4px;
    .use-dark-mode-query({
      color: var(--dark-font-color);
    });
  }

  &-desc {
    margin-left: 20px;
    font-size: 14px;
    color: #86909c;
    line-height: 20px;
  }
}
