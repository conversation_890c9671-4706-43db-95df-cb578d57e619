import { View, Text, Image } from "@tarojs/components";
import React, { useState } from "react";
import { Ellipsis } from "@arco-design/mobile-react";
import { IconQrcode } from "@/components/YkIcons";
import { UserHomeTopData } from "./AlbumCore";

export interface AlbumHeaderProps {
  userInfo: UserHomeTopData;
  isOwnAlbum?: boolean;
  onQrcodeClick?: (payload: {
    userId?: string | number;
    nickname?: string;
    avatar?: string;
  }) => void;
}

const AlbumHeader: React.FC<AlbumHeaderProps> = ({
  userInfo,
  isOwnAlbum = false,
  onQrcodeClick,
}) => {
  const [isContentExpanded, setIsContentExpanded] = useState(false);

  return (
    <View className="user-header">
      {/* 头像昵称简介 */}
      <View className="user-header-info">
        <View className="user-header-avatar-wrap">
          {/* 头像 */}
          {userInfo.avatar && (
            <Image className="user-header-avatar" src={userInfo.avatar} />
          )}
          
          {/* 二维码图标 - 统一显示 */}
          <View
            className="user-header-qrcode"
            onClick={() =>
              onQrcodeClick?.({
                userId: userInfo.userId,
                nickname: userInfo.nickname,
                avatar: userInfo.avatar,
              })
            }
          >
            <IconQrcode size={20} />
          </View>
        </View>
        
        {/* 昵称 */}
        <Text className="user-header-nick">{userInfo.nickname}</Text>
        
        {/* 个人简介 */}
        {userInfo.personalProfile && (
          <View className="user-header-desc">
            <Ellipsis
              className="user-header-desc-text selectable-text"
              text={userInfo.personalProfile}
              maxLine={2}
              ellipsis={!isContentExpanded}
            />
          </View>
        )}
        
        {/* 统计数据区域 */}
        <View className="user-header-stats">
          <View className="user-header-stat">
            <Text className="user-header-stat-num">{userInfo.newNumbers || 0}</Text>
            <Text className="user-header-stat-label">上新</Text>
          </View>
          <View className="user-header-stat">
            <Text className="user-header-stat-num">{userInfo.total || 0}</Text>
            <Text className="user-header-stat-label">总数</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default AlbumHeader;



