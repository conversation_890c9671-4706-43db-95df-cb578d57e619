import React, { useState, useEffect } from "react";
import Taro, { useRouter } from "@tarojs/taro";
import { View, Text } from "@tarojs/components";
import { Cell, Toast, Input, Button } from "@arco-design/mobile-react";
import { IconCloseBold } from "@arco-design/mobile-react/esm/icon";
import {
  createAndFreightRules,
  updateFreightTemplate,
  createFreightRules,
  updateFreightRules,
  deleteFreightRules,
} from "@/utils/api/common/common_user";
import YkNavBar from "@/components/ykNavBar";
import "./editEmsTemplate.less";

const EditEmsTemplate: React.FC = () => {
  const router = useRouter();

  const [item, setItem] = useState<any>({});
  const [templateName, setTemplateName] = useState("");
  const [templateId, setTemplateId] = useState("");
  const [list, setList] = useState<any[]>([]);
  const [toastVisible, setToastVisible] = useState(false);
  const [toastText, setToastText] = useState("");
  const isEdit = Boolean(router.params.item);

  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  
  useEffect(() => {
    const { item } = router.params;

    if (item) {
      // console.log("param item: " + item);
      // 先对编码的参数进行解码，再进行 JSON 解析
      const decodedItem = decodeURIComponent(item);
      const itemObj = JSON.parse(decodedItem);
      // console.log("itemObj: " + JSON.stringify(itemObj));
      setItem(itemObj);
      setList(itemObj.rules || []);
      setTemplateId(itemObj.id);
      setTemplateName(itemObj.name);
    } else {
      // 新建模板时，初始化默认的全国规则
      setList([
        {
          area: "全国",
          type: "固定运费",
          price: "8",
          conditionMoney: "0",
        },
      ]);
    }

    // 添加事件监听
    setupEventListeners();

    return () => {
      // 移除事件监听
      removeEventListeners();
    };
  }, [router.params]);

  useEffect(() => {
    console.log("监测list变化【运费规则】更新: ", JSON.stringify(list));
  }, [list]);

  useEffect(() => {
    console.log("监测item变化【模版名称】更新: ", JSON.stringify(item));
  }, [item]);
  const setupEventListeners = () => {
    // Taro.eventCenter.on('editDefaultRule', (itemData) => {
    //   const itemTemp = JSON.parse(itemData);
    //   console.log("【editDefaultRule】 itemTemp: " + JSON.stringify(itemTemp));
    //   setItem(prev => ({
    //     ...prev,
    //     type: itemTemp.type,
    //     price: itemTemp.price,
    //     conditionMoney: itemTemp.conditionMoney
    //   }));
    // });

    Taro.eventCenter.on("editRule", (itemData) => {
      const itemTemp2 = JSON.parse(itemData);
      console.log("【editRule】 itemTemp: " + JSON.stringify(itemTemp2));

      setList((prevList) => {
        return prevList.map((listItem) => {
          if (listItem.id === itemTemp2.id) {
            return {
              ...listItem,
              type: itemTemp2.type,
              price: itemTemp2.price,
              conditionMoney: itemTemp2.conditionMoney,
            };
          }
          return listItem;
        });
      });
    });

    Taro.eventCenter.on("addRule", (itemData) => {
      const itemTemp2 = JSON.parse(itemData);
      setList((prevList) => [...prevList, itemTemp2]);
    });

    Taro.eventCenter.on("editAddress", (index, str) => {
      setList((prevList) => {
        const newList = [...prevList];
        if (newList[index]) {
          newList[index].area = str;
        }
        return newList;
      });
    });

    Taro.eventCenter.on("editNoAddress", (str) => {
      setItem((prev) => ({
        ...prev,
        nonDeliveryArea: str,
      }));
    });
  };

  const removeEventListeners = () => {
    // Taro.eventCenter.off('editDefaultRule');
    Taro.eventCenter.off("editRule");
    Taro.eventCenter.off("addRule");
    Taro.eventCenter.off("editAddress");
    Taro.eventCenter.off("editNoAddress");
  };

  const delEmsAddress = (index: number) => {
    console.log("删除指定地区运费规则 - 开始", { index, rule: list[index] });

    setList((prevList) => {
      const newList = [...prevList];
      console.log("当前规则列表:", newList);

      if (newList[index]) {
        if (newList[index].id) {
          console.log("已有规则，标记为删除:", newList[index]);
          // 对于已存在的规则，标记为删除状态
          newList[index] = { ...newList[index], _deleted: true };
          console.log("标记删除后的规则:", newList[index]);
        } else {
          console.log("新增规则，直接移除:", newList[index]);
          newList.splice(index, 1);
        }
      }

      console.log("更新后的规则列表:", newList);
      console.log(
        "被删除标记的规则:",
        newList.filter((rule) => rule._deleted)
      );
      return newList;
    });
  };

  const goToEditRule = (itemData?: any) => {
    let ruleData;

    if (itemData) {
      // 编辑现有规则（包括指定地区和全国默认规则）
      ruleData = itemData;
    } else {
      // 处理默认规则（全国）
      const defaultRule = list.find((rule) => rule.area === "全国");
      ruleData = defaultRule || {
        ...(!isEdit ? {} : item),
        type: "固定运费",
        price: "8",
        area: "全国",
        conditionMoney: "0",
      };
    }

    Taro.navigateTo({
      url: `/pageOrder/orderSetting/emsTemplate/emsRule?type=edit&item=${JSON.stringify(
        ruleData
      )}`,
    });
  };

  const goToEmsAddress = (addressType: number, index?: number) => {
    let noCheckList: string[] = [];
    let hasCheckList: string[] = [];

    index
      ? console.log("【goToEmsAddress】 index: " + index)
      : console.log("【goToEmsAddress】 index: undefined");
    console.log("【goToEmsAddress】 list: " + JSON.stringify(list));
    if (addressType === 1 && typeof index === "number") {
      // 编辑指定配送的地区
      if (list.length > 0 && list[index] && list[index].area) {
        // 确保不会包含"全国"规则
        hasCheckList = list[index].area
          .split(",")
          .filter((area) => area !== "全国");
      }

      let filteredArray = [...list];
      if (typeof index === "number") {
        filteredArray.splice(index, 1);
      }
      filteredArray = filteredArray.filter(
        (rule) => rule.type && rule.area !== "全国" && !rule._deleted
      );

      if (item.nonDeliveryArea && item.nonDeliveryArea !== "") {
        noCheckList = item.nonDeliveryArea.split(",").concat(
          filteredArray
            .map((rule) => rule.area)
            .join(",")
            .split(",")
        );
      } else {
        noCheckList = filteredArray
          .map((rule) => rule.area)
          .join(",")
          .split(",");
      }
      console.log(
        "【goToEmsAddress】 noCheckList: " + JSON.stringify(noCheckList)
      );
      console.log(
        "【goToEmsAddress】 hasCheckList: " + JSON.stringify(hasCheckList)
      );
      Taro.navigateTo({
        url: `/pageOrder/orderSetting/emsTemplate/emsArea?type=${addressType}&index=${index}&noCheckList=${JSON.stringify(
          noCheckList
        )}&hasCheckList=${JSON.stringify(hasCheckList)}`,
      });
    } else if (addressType === 2) {
      // 编辑不配送地区
      if (list.length > 0) {
        noCheckList = list
          .filter((item) => item.area !== "全国" && !item._deleted)
          .map((item) => item.area)
          .join(",")
          .split(",");
      }

      if (item.nonDeliveryArea && item.nonDeliveryArea !== "") {
        hasCheckList = item.nonDeliveryArea.split(",");
      }
      console.log(
        "【goToEmsAddress】 noCheckList: " + JSON.stringify(noCheckList)
      );
      console.log(
        "【goToEmsAddress】 hasCheckList: " + JSON.stringify(hasCheckList)
      );
      Taro.navigateTo({
        url: `/pageOrder/orderSetting/emsTemplate/emsArea?type=${addressType}&noCheckList=${JSON.stringify(
          noCheckList
        )}&hasCheckList=${JSON.stringify(hasCheckList)}`,
      });
    } else {
      // 新增配送地区
      if (item.nonDeliveryArea && item.nonDeliveryArea !== "") {
        noCheckList = item.nonDeliveryArea.split(",").concat(
          list
            .filter((item) => item.area !== "全国" && !item._deleted)
            .map((item) => item.area)
            .join(",")
            .split(",")
        );
      } else {
        noCheckList = list
          .filter((item) => item.area !== "全国" && !item._deleted)
          .map((item) => item.area)
          .join(",")
          .split(",");
      }
      console.log(
        "【goToEmsAddress】 noCheckList: " + JSON.stringify(noCheckList)
      );
      console.log(
        "【goToEmsAddress】 hasCheckList: " + JSON.stringify(hasCheckList)
      );
      Taro.navigateTo({
        url: `/pageOrder/orderSetting/emsTemplate/emsArea?type=${addressType}&noCheckList=${JSON.stringify(
          noCheckList
        )}&hasCheckList=${JSON.stringify(hasCheckList)}`,
      });
    }
  };

  const save = () => {
    if (templateName === "") {
      setToastText("请输入模版名称");
      setToastVisible(true);
      return;
    }

    saveTemplateRequest();
  };

  const saveTemplateRequest = () => {
    const userInfo = Taro.getStorageSync("userInfo");
    const userId = userInfo?.userInfo?.id || userInfo?.id;

    if (!userId) {
      setToastText("获取用户信息失败");
      setToastVisible(true);
      return;
    }

    // 编辑模板
    if (isEdit) {
      console.log("开始更新模板");
      console.log(
        "当前规则列表状态:",
        list.map((rule) => ({
          id: rule.id,
          area: rule.area,
          _deleted: rule._deleted,
          type: rule.type,
        }))
      );

      // 获取原始模板数据进行比对
      const originalItem = router.params.item
        ? JSON.parse(decodeURIComponent(router.params.item))
        : null;
      console.log(
        "【saveTemplateRequest】 originalItem: " + JSON.stringify(originalItem)
      );

      // 检查模板基础信息是否有变化（名称、不配送地区）
      const templateBasicInfoChanged =
        originalItem.name !== templateName ||
        (originalItem.nonDeliveryArea || "") !== (item.nonDeliveryArea || "");
      console.log(
        "【saveTemplateRequest】 templateBasicInfoChanged: " +
          templateBasicInfoChanged
      );
      console.log(
        "【saveTemplateRequest】 名称变化: " +
          (originalItem.name !== templateName)
      );
      console.log(
        "【saveTemplateRequest】 不配送地区变化: " +
          ((originalItem.nonDeliveryArea || "") !==
            (item.nonDeliveryArea || ""))
      );
      console.log(
        "【saveTemplateRequest】 原始不配送地区: " +
          (originalItem.nonDeliveryArea || "")
      );
      console.log(
        "【saveTemplateRequest】 当前不配送地区: " +
          (item.nonDeliveryArea || "")
      );

      // 改进规则变化检测逻辑
      const originalRules = originalItem?.rules || [];
      const hasRulesChanged = () => {
        const currentRules = list.filter((rule) => rule.type && !rule._deleted); // 过滤掉标记删除的规则

        console.log("规则变化检测:", {
          originalRulesCount: originalRules.length,
          currentRulesCount: currentRules.length,
          originalRules: originalRules.map((r) => ({ id: r.id, area: r.area })),
          currentRules: currentRules.map((r) => ({ id: r.id, area: r.area })),
          deletedRules: list
            .filter((rule) => rule._deleted)
            .map((r) => ({ id: r.id, area: r.area })),
        });

        // 检查是否有规则被删除
        const hasDeletedRules = list.some((rule) => rule._deleted);
        if (hasDeletedRules) {
          console.log("检测到有规则被删除");
          return true;
        }

        if (originalRules.length !== currentRules.length) {
          console.log("规则数量发生变化");
          return true;
        }

        // 比较每个规则的关键属性
        for (let i = 0; i < originalRules.length; i++) {
          const original = originalRules[i];
          const current = currentRules.find((r) => r.id === original.id);

          if (!current) {
            console.log("规则被删除:", original);
            return true; // 规则被删除
          }

          // 比较关键属性
          if (
            original.area !== current.area ||
            original.freightType !== (current.type === "包邮" ? 1 : 2) ||
            Math.abs(original.freightAmount - Number(current.price)) > 0.01 ||
            original.isFreeShippingConditions !==
              (Number(current.conditionMoney) > 0 ? 1 : 2) ||
            Math.abs(
              original.freeShippingAmount - Number(current.conditionMoney)
            ) > 0.01
          ) {
            console.log("规则内容发生变化:", { original, current });
            return true;
          }
        }

        console.log("未检测到规则变化");
        return false;
      };

      const rulesChanged = hasRulesChanged();

      // 如果模板基础信息和规则都没有变化，直接返回
      if (!templateBasicInfoChanged && !rulesChanged) {
        console.log("未检测到任何变更，跳过更新");
        setToastText("保存成功");
        setToastVisible(true);
        Taro.eventCenter.trigger("refreshTemplate", true);
        setTimeout(() => {
          Taro.navigateBack({ delta: 1 });
        }, 200);
        return;
      }

      // 更新规则的函数
      const updateRules = () => {
        console.log("=== 开始更新规则 ===");
        // 修复：从 list 中获取默认规则数据
        const defaultRuleFromList = list.find((rule) => rule.area === "全国");
        const defaultRule = {
          userId: userId,
          area: "全国",
          freightTemplateId: Number(templateId),
          freightType: defaultRuleFromList?.type === "包邮" ? 1 : 2,
          freightAmount: Number(defaultRuleFromList?.price) || 8,
          isFreeShippingConditions:
            Number(defaultRuleFromList?.conditionMoney) > 0 ? 1 : 2,
          freeShippingAmount: Number(defaultRuleFromList?.conditionMoney) || 0,
        };
        console.log("准备更新的默认规则数据:", defaultRule);

        // 找到原有的默认规则
        const existingDefaultRule = originalRules.find(
          (r) => r.area === "全国"
        );
        console.log("找到的现有默认规则:", existingDefaultRule);

        // 比较规则是否发生实质性变化
        const isRuleChanged = (oldRule, newRule) => {
          const changed =
            oldRule.freightType !== newRule.freightType ||
            Math.abs(oldRule.freightAmount - newRule.freightAmount) > 0.01 ||
            oldRule.isFreeShippingConditions !==
              newRule.isFreeShippingConditions ||
            Math.abs(oldRule.freeShippingAmount - newRule.freeShippingAmount) >
              0.01;

          console.log("规则比较:", {
            oldRule,
            newRule,
            changes: {
              freightType: oldRule.freightType !== newRule.freightType,
              freightAmount:
                Math.abs(oldRule.freightAmount - newRule.freightAmount) > 0.01,
              isFreeShippingConditions:
                oldRule.isFreeShippingConditions !==
                newRule.isFreeShippingConditions,
              freeShippingAmount:
                Math.abs(
                  oldRule.freeShippingAmount - newRule.freeShippingAmount
                ) > 0.01,
            },
            hasChanged: changed,
          });

          return changed;
        };

        // 只在规则实际发生变化时才更新
        console.log("处理默认规则更新...");
        const defaultRulePromise = existingDefaultRule
          ? isRuleChanged(existingDefaultRule, defaultRule)
            ? (console.log("默认规则需要更新"),
              updateFreightRules({
                ...defaultRule,
                id: existingDefaultRule.id,
              }))
            : (console.log("默认规则无变化，跳过更新"), Promise.resolve())
          : (console.log("未找到默认规则，创建新规则"),
            createFreightRules(defaultRule));

        return defaultRulePromise.then(() => {
          console.log("=== 默认规则处理完成，开始处理其他规则 ===");
          // 找出需要删除的规则（排除全国规则）
          const toDeleteRules = originalRules.filter((originalRule) => {
            if (originalRule.area === "全国") return false;
            const newRule = list.find(
              (newRule) => newRule.id === originalRule.id
            );
            const shouldDelete = !newRule || newRule._deleted;
            console.log("检查规则是否需要删除:", {
              rule: originalRule,
              shouldDelete,
              reason: shouldDelete
                ? !newRule
                  ? "规则不存在"
                  : "规则被标记删除"
                : "保留规则",
            });
            return shouldDelete;
          });
          console.log("需要删除的规则:", toDeleteRules);

          // 删除规则的 Promise 数组
          const deletePromises = toDeleteRules.map((rule) => {
            if (rule.id) {
              console.log("正在删除规则:", rule);
              return deleteFreightRules({ id: rule.id })
                .then((result) => {
                  console.log("删除规则成功:", rule.id, result);
                  return result;
                })
                .catch((error) => {
                  console.error("删除规则失败:", rule.id, error);
                  throw error;
                });
            }
            return Promise.resolve();
          });

          // 找出需要新增和更新的规则（排除全国规则和已删除的规则）
          const rulesToProcess = list.filter(
            (rule) => rule.type && rule.area !== "全国" && !rule._deleted
          );
          console.log("需要处理的非默认规则:", rulesToProcess);

          const rulePromises = rulesToProcess.map((rule) => {
            const ruleData = {
              userId: userId,
              area: rule.area || "",
              freightTemplateId: Number(templateId),
              freightType: rule.type === "包邮" ? 1 : 2,
              freightAmount: Number(rule.price) || 8,
              isFreeShippingConditions: Number(rule.conditionMoney) > 0 ? 1 : 2,
              freeShippingAmount: Number(rule.conditionMoney) || 0,
            };

            // 只有当规则发生变化时才更新
            const originalRule = originalRules.find((r) => r.id === rule.id);
            console.log("处理规则:", {
              rule,
              ruleData,
              originalRule,
              action: rule.id && originalRule ? "可能更新" : "新建",
            });

            if (rule.id && originalRule) {
              const needsUpdate = isRuleChanged(originalRule, ruleData);
              console.log("规则更新判断:", {
                ruleId: rule.id,
                needsUpdate,
              });
              return needsUpdate
                ? updateFreightRules({ ...ruleData, id: rule.id })
                : Promise.resolve();
            } else {
              console.log("创建新规则:", ruleData);
              return createFreightRules(ruleData);
            }
          });

          // 先执行删除操作，再执行新增和更新操作
          return Promise.all(deletePromises).then(() => {
            console.log("所有删除操作完成，开始执行新增和更新操作");
            return Promise.all(rulePromises);
          });
        });
      };

      // 根据变化情况选择更新流程
      const updateProcess = templateBasicInfoChanged
        ? updateFreightTemplate({
            id: Number(templateId),
            userId: userId,
            name: templateName,
            nonDeliveryArea: item.nonDeliveryArea || "",
            isDefault: originalItem.isDefault,
          }).then(() => {
            if (rulesChanged) {
              return updateRules().then(() => {});
            }
            return Promise.resolve();
          })
        : rulesChanged
        ? updateRules().then(() => {})
        : Promise.resolve();

      updateProcess
        .then(() => {
          console.log("所有更新处理完成");
          setToastText("保存成功");
          setToastVisible(true);
          Taro.eventCenter.trigger("refreshTemplate", true);
          setTimeout(() => {
            Taro.navigateBack({ delta: 1 });
          }, 1000);
        })
        .catch((error) => {
          console.error("更新失败:", error);
          setToastText("更新失败，请重试");
          setToastVisible(true);
        });
      return;
    }

    // 找到 list 里标为 全国 的项（如果有）
    const nationalItem = list.find((i) => i.area === "全国");

    // 生成除全国外的规则
    const rulesList = list
      .filter((i) => i.area !== "全国")
      .map((item) => ({
        userId,
        area: item.area || "",
        freightType: item.type === "包邮" ? 1 : 2,
        freightAmount: Number(item.price) || 8, // 或 parseInt(item.price || '8', 10)
        isFreeShippingConditions: Number(item.conditionMoney) > 0 ? 1 : 2,
        freeShippingAmount: Number(item.conditionMoney) || 0,
      }));

    // 用找到的 nationalItem（如果没有则用 list[0] 或默认值）作为全国的规则来源
    const source = nationalItem || list[0] || {};
    rulesList.push({
      userId,
      area: "全国",
      freightType: source.type === "包邮" ? 1 : 2,
      freightAmount: Number(source.price) || 8,
      isFreeShippingConditions: Number(source.conditionMoney) > 0 ? 1 : 2,
      freeShippingAmount: Number(source.conditionMoney) || 0,
    });

    const formData = {
      appFreightTemplateSaveReqVO: {
        userId: userId,
        name: templateName,
        isDefault: 0,
        nonDeliveryArea: item.nonDeliveryArea || "",
      },
      appFreightRulesSaveReqVOList: rulesList,
    };

    createAndFreightRules(formData)
      .then((res: any) => {
        if (res && res.code === 0) {
          setToastText("模版创建成功");
          setToastVisible(true);
          Taro.eventCenter.trigger("refreshTemplate", true);
          setTimeout(() => {
            Taro.navigateBack({ delta: 1 });
          }, 1000);
        } else {
          setToastText(res.msg || "创建失败");
          setToastVisible(true);
        }
      })
      .catch((error) => {
        console.error("创建运费模板失败:", error);
        setToastText("创建失败，请重试");
        setToastVisible(true);
      });
  };

  const renderRuleText = (ruleItem) => {
    console.log("【renderRuleText】 ruleItem: " + JSON.stringify(ruleItem));

    if (!ruleItem) {
      return ""; // 默认显示
    }

    if (ruleItem.type === "包邮") {
      return "包邮";
    } else if (ruleItem.conditionMoney > 0 && ruleItem.conditionMoney !== "") {
      return `固定运费${ruleItem.price}元; 满${ruleItem.conditionMoney}元包邮`;
    } else if (ruleItem.price > 0 && ruleItem.price !== "") {
      return `固定运费${ruleItem.price}元`;
    } else {
      return `固定运费8元`;
    }
  };

  return (
    <View className="ems-template">
      {platform !== "WX" &&<YkNavBar title={isEdit ? "编辑运费模板" : "新建运费模板"} />}

      <Toast
        visible={toastVisible}
        content={toastText}
        onClose={() => setToastVisible(false)}
      />
      <View className="ems-template-content">
        <Cell.Group className="name-group" bordered={false}>
          <Cell label="模版名称">
            <Input
              border="none"
              className="name-input"
              value={templateName}
              onChange={(_, value) => setTemplateName(value)}
              placeholder="请输入模版名称"
              maxLength={16}
            />
          </Cell>
        </Cell.Group>

        <View className="container-group">
          <View className="header-cell">
            <Text className="header-cell-label">运费规则</Text>
            <View className="header-flex">
              {/* <Text className="header-title">运费规则</Text> */}
              <View className="header-btn" onClick={() => goToEmsAddress(3)}>
                <span className="header-btn-text">+ 新增指定地区</span>
              </View>
            </View>
          </View>
          <Cell.Group className="default-group" bordered={false}>
            <Cell className="rule-cell" label="所在地区">
              <Text>默认全国（指定区域外）</Text>
            </Cell>
            <Cell
              className="rule-cell"
              label="运费规则"
              onClick={() => {
                if (!isEdit) {
                  goToEditRule();
                } else {
                  const defaultRule = list.find((rule) => rule.area === "全国");
                  console.log(
                    "【renderRuleText】 defaultRule: " +
                      JSON.stringify(defaultRule)
                  );
                  goToEditRule(defaultRule);
                }
              }}
              showArrow
            >
              <View className="cell-content">
                <Text>
                  {renderRuleText(list.find((rule) => rule.area === "全国"))}
                </Text>
                {/* <Image 
                className="cell-arrow" 
                src={require('@/assets/images/common/right_arrow.png')} 
              /> */}
              </View>
            </Cell>
          </Cell.Group>
        </View>

        {list.map(
          (listItem, idx) =>
            listItem.area !== "全国" &&
            !listItem._deleted && (
              <View className="rule-box" key={idx}>
                {/* <Image
                className="delete-icon"
                src={require('@/assets/images/common/uploadPic_del.png')}
                onClick={(e) => {
                  e.stopPropagation();
                  delEmsAddress(idx);
                }}
              /> */}
                <View className="delete-icon-box">
                  <IconCloseBold
                    className="delete-icon"
                    onClick={(e) => {
                      e.stopPropagation();
                      delEmsAddress(idx);
                    }}
                  />
                </View>
                <Cell.Group className="rule-group" key={idx} bordered={false}>
                  <Cell
                    className="rule-cell"
                    label="所在地区"
                    onClick={() => goToEmsAddress(1, idx)}
                    showArrow
                  >
                    <View className="cell-content">
                      <Text className="cell-content-text">{listItem.area}</Text>
                    </View>
                  </Cell>
                  <Cell
                    className="rule-cell"
                    label="运费规则"
                    onClick={() => goToEditRule(listItem)}
                    showArrow
                  >
                    <View className="cell-content">
                      <Text className="cell-content-text">
                        {renderRuleText(listItem)}
                      </Text>

                      {/* <Image 
                      className="cell-arrow" 
                      src={require('@/assets/images/common/right_arrow.png')} 
                    /> */}
                    </View>
                  </Cell>
                </Cell.Group>
              </View>
            )
        )}

        <Cell.Group className="no-address-group" bordered={false}>
          <Cell label="不配送地区" onClick={() => goToEmsAddress(2)} showArrow>
            <View className="cell-content">
              <Text className="cell-content-text">
                {item.nonDeliveryArea || ""}
              </Text>
            </View>
          </Cell>
        </Cell.Group>
      </View>

      <View className="holder"></View>

      <View className="footerbtn">
        <Button type="primary" className="footerbtn-btn" onClick={save}>
          <Text className="footerbtn-btn-text">保存</Text>
        </Button>
      </View>
    </View>
  );
};

export default EditEmsTemplate;
