import { View, Text } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import { NavBar } from "@arco-design/mobile-react";
import React from "react";
import Taro from "@tarojs/taro";
import { IconArrowBack } from "@arco-design/mobile-react/esm/icon";

interface YkNavBarProps {
  title: string;
  rightContent?: React.ReactNode;
  [key: string]: any;
}

export default function YkNavBar({title, rightContent, ...props}: YkNavBarProps) {
  const [httpPlatform, setHttpPlatform] = React.useState("H5");

  useLoad(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setHttpPlatform("Android");
    } else if (isIos) {
      setHttpPlatform("IOS");
    } else if (isHM) {
      setHttpPlatform("HM");
    } else {
      setHttpPlatform("H5");
    }
  });
  const onPageBack = () => {
    console.log("YkNavBar PAGES -------: " + JSON.stringify(Taro.getCurrentPages()));
    Taro.navigateBack({
      delta: 1,
    });
  };

  return (
    <NavBar
      leftContent={props.switchTab ? null : <IconArrowBack />}
      title={title}
      rightContent={rightContent}
      hasBottomLine={false}
      onClickLeft={props.onClickLeft ? props.onClickLeft : onPageBack}
      className={
        httpPlatform == "H5"
          ? "setPageContent-navbar"
          : "setPageContent-navbar navbarPaddingTop"
      }
      style={
        httpPlatform == "H5"
          ? { height: '2.2rem' }
          : { paddingTop: "2rem", height: "2.2rem" }
      }
    />
  );
}
