import { View, Text } from "@tarojs/components";
import React, { useEffect, useState, useRef } from "react";
import "./index.less";
import {
  Cell,
  Switch,
  ContextProvider,
  Radio,
} from "@arco-design/mobile-react";
import Taro from "@tarojs/taro";
import wx from "weixin-webview-jssdk";

// 组件
import YkNavBar from "@/components/ykNavBar/index";

// 暗黑模式类型
type DarkModeType = "system" | "manual" | "off";

export default function DarkModeSettings() {
  const [platform, setPlatform] = useState("H5");
  const platformRef = useRef("H5");
  const [darkModeType, setDarkModeType] = useState<DarkModeType>("off"); // 暗黑模式类型
  const [manualDarkMode, setManualDarkMode] = useState(false); // 手动选择的暗黑模式状态
  const [systemDarkMode, setSystemDarkMode] = useState(false); // 系统暗黑模式状态

  useEffect(() => {
    // 检测平台
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;

    // 设置回调函数
    (window as any).onSystemThemeResult = (isDark: boolean) => {
      setSystemDarkMode(isDark);
    };

    if (isAndroid) {
      setPlatform("Android");
      platformRef.current = "Android";
    } else if (isIos) {
      setPlatform("IOS");
      platformRef.current = "IOS";
    } else if (isHM) {
      setPlatform("HM");
      platformRef.current = "HM";
    } else {
      setPlatform("H5");
      platformRef.current = "H5";
    }

    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
      platformRef.current = "WX";
    }

    // 初始化暗黑模式设置
    initializeDarkModeSettings();

    // 检测系统主题
    detectSystemTheme();
  }, []);

  // 初始化暗黑模式设置
  const initializeDarkModeSettings = () => {
    const savedDarkModeType = Taro.getStorageSync("darkModeType") || "off";
    const savedManualDarkMode = !!Taro.getStorageSync("manualDarkMode");

    setDarkModeType(savedDarkModeType);
    setManualDarkMode(savedManualDarkMode);
  };

  // 检测系统主题
  const detectSystemTheme = () => {
    let _window: any = window;
    // App环境下通过原生方法检测系统主题
    try {
      if (platformRef.current === "Android") {
        if (_window.getSystemTheme) {
          const isDark = _window.getSystemTheme.getSystemTheme();
          setSystemDarkMode(isDark);
        }
      } else if (platformRef.current === "IOS") {
        if (_window.webkit?.messageHandlers?.getSystemTheme) {
          _window.webkit.messageHandlers.getSystemTheme.postMessage();
        }
      } else if (platformRef.current === "HM") {
        if (_window.harmony?.getSystemTheme) {
          _window.harmony.getSystemTheme();
        }
      }
    } catch (error) {
      console.log("检测系统主题失败:", error);
    }
  };

  // 应用主题
  const applyTheme = (isDark: boolean) => {
    // 保存到本地存储
    Taro.setStorageSync("darkMode", isDark);
  };

  // 处理暗黑模式类型变化
  const handleDarkModeTypeChange = (type: DarkModeType) => {
    setDarkModeType(type);
    Taro.setStorageSync("darkModeType", type);

    if (type === "system") {
      // 跟随系统
      applyTheme(systemDarkMode);
      if (platformRef.current === "Android") {
        const isDark = window.getSystemTheme.getSystemTheme();
        setSystemDarkMode(isDark);
      } else if (platformRef.current === "IOS") {
        window.webkit.messageHandlers.getSystemTheme.postMessage();
      } else if (platformRef.current === "HM") {
        window.harmony.getSystemTheme();
      }
    } else if (type === "manual") {
      // 手动选择 - 如果是从跟随系统切换过来，默认选中当前系统主题
      if (darkModeType === "system") {
        setManualDarkMode(systemDarkMode);
        Taro.setStorageSync("manualDarkMode", systemDarkMode);
        applyTheme(systemDarkMode);
      } else {
        applyTheme(manualDarkMode);
      }
    } else {
      // 关闭
      applyTheme(false);
    }
  };

  // 处理手动暗黑模式切换
  const handleManualDarkModeChange = (value: boolean) => {
    setManualDarkMode(value);
    Taro.setStorageSync("manualDarkMode", value);

    if (darkModeType === "manual") {
      applyTheme(value);
    }
  };

  // 获取当前生效的暗黑模式状态
  const getCurrentDarkMode = () => {
    if (darkModeType === "system") {
      return systemDarkMode;
    } else if (darkModeType === "manual") {
      return manualDarkMode;
    }
    return false;
  };

  return (
    <View className="darkModeSettingsPage">
      {platform !== "WX" && <YkNavBar title="深色模式" />}

      <ContextProvider
        isDarkMode={getCurrentDarkMode()}
        darkModeSelector="arco-theme-dark"
      >
        <View className="settings-content">
          {/* 跟随系统 */}
          <Cell.Group bordered={false}>
            <Cell
              className="padding-item setting-item"
              label={
                <View>
                  <Text>跟随系统</Text>
                  <Text className="description">
                    开启后，将跟随系统打开或关闭深色模式
                  </Text>
                </View>
              }
            >
              <Switch
                checked={darkModeType === "system"}
                platform={platform === "IOS" ? "ios" : "android"}
                onChange={(value) => {
                  if (value) {
                    handleDarkModeTypeChange("system");
                  } else {
                    handleDarkModeTypeChange("off");
                  }
                }}
              />
            </Cell>
          </Cell.Group>

          {/* 手动选择 - 只在非跟随系统模式下显示 */}
          {darkModeType !== "system" && (
            <View className="manual-section">
              <View className="section-title">手动选择</View>
              <Cell.Group bordered={false}>
                <Cell
                  className="setting-item"
                  label="普通模式"
                  onClick={() => {
                    handleDarkModeTypeChange("manual");
                    handleManualDarkModeChange(false);
                  }}
                >
                  <Radio
                    checked={darkModeType === "manual" && !manualDarkMode}
                    onChange={() => {
                      handleDarkModeTypeChange("manual");
                      handleManualDarkModeChange(false);
                    }}
                  />
                </Cell>

                <Cell
                  className="setting-item"
                  label="深色模式"
                  onClick={() => {
                    handleDarkModeTypeChange("manual");
                    handleManualDarkModeChange(true);
                  }}
                >
                  <Radio
                    checked={darkModeType === "manual" && manualDarkMode}
                    onChange={() => {
                      handleDarkModeTypeChange("manual");
                      handleManualDarkModeChange(true);
                    }}
                  />
                </Cell>
              </Cell.Group>
            </View>
          )}
        </View>
      </ContextProvider>
    </View>
  );
}
