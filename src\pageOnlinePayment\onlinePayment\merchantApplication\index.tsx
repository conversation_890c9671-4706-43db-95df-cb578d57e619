import { View, Text, Image } from "@tarojs/components";
import "./index.less";
import { useEffect, useState, useRef, ChangeEvent } from "react";
// 组件
import YkNavBar from "@/components/ykNavBar/index";
import YkNoticeBar from '@/components/YkNoticeBar/index';
import {
  Cell,
  Dialog,
  Loading,
  Popup,
  Toast,
  Button,
  Dropdown,
  DropdownMenu,
  Input,
  NoticeBar,
  Radio,
  DatePicker,
  Textarea
} from "@arco-design/mobile-react";
import type { ImagePickItem } from '@arco-design/mobile-react/cjs/image-picker/type';
import type { PickerData } from '@arco-design/mobile-react/cjs/picker-view/type';
import { isMemberValid } from '@/pages/my/utils'
import wx from "weixin-webview-jssdk";
import {
  IconArrowDown,
  IconQuestionCircle,
  IconCheck,
  IconSuccessCircle,
  IconWarnCircle,
  IconSad,
  IconUpload
} from '@arco-design/mobile-react/esm/icon';

import Taro, { useRouter } from "@tarojs/taro";
import YkDatePicker from '@/components/ykDatePicker';
import YkAreaPicker from '@/components/ykAreaPicker';
import YkIndustryPicker from '@/components/ykIndustryPicker';
import BottomPopup from '@/components/BottomPopup';
import YkCellLabel from "@/components/YkCellLabel";

import YkImagePicker from '@/components/YkImagePicker';

import { MerchantType, MerchantTypeInfo } from '../types';
import {
  BusinessLicenseInfo,
  MerchantBasicInfo,
  IdentityInfo,
  WithdrawalAccountInfo,
  ContactInfo,
  UiState,
  DatePickerType,
  DatePickerConfig,
  EntryImages
} from './types';

import { convertAddressToArea } from '@/utils/address';
import { convertBankNameToShortName } from '@/utils/utils';

import {
  //图片
  uploadSingleFile, 
  uploadImageWechat, 
  //进件图片
  createEntryPhotoAddress,
  updateEntryPhotoAddress,
  getEntryPhotoAddressPage,
  deleteEntryPhotoAddress,

  //
  //验证码
  getSmsCode, 
  validateSmsCode, 
  //创建
  createAndSubmitMerchantEntryApplication,
  //更新
  getMerchantEntryApplicationPage,
  getMerchantEntryApplyStatusPage,
  getMerchantEntryByOutRequestNo,
  getOutRequestNo,
  updateMerchantEntryApplication,
  submitApplication
} from '@/utils/api/common/common_user';
import {
  MERCHANT_TYPE_DICT,
  initFormData,
  convertApiDataToFormData,
  convertFormDataToApiData,
  validateFormData,
  getStatusText,
} from './utils';



export default function MerchantApplication() {
  const router = useRouter();
  const containerRef = useRef(null);
  const countdownRef = useRef<NodeJS.Timeout>();

  // ==================== 页面模式和状态 ====================
  const { mode } = router.params; // mode: 'create' | 'edit'
  const isEditMode = mode === 'edit';
  const [pageLoading, setPageLoading] = useState(isEditMode);
  const [applicationData, setApplicationData] = useState<any>(null);
  const [applicationStatus, setApplicationStatus] = useState<any>(null); // 详细的审核状态信息
  // ==================== 商户类型状态 ====================
  const [merchantType, setMerchantType] = useState<MerchantType>(MerchantType.ENTERPRISE);
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);

  const openVip = ()=>{
    Dialog.confirm({
      title: "提示",
      children: "请先开通会员",
      okText: "去开通",
      cancelText: "取消",
      platform: 'ios',
      onOk: () => {
        if (platform === "WX") {
          wx.miniProgram.navigateTo({ url: "/pages/web?path=/pageVip/vipCenter/index" });
        } else {
          Taro.navigateTo({ url: "/pageVip/vipCenter/index" });
        }
      }
    })
  }
  
  /**
   * 根据商户类型获取默认账户类型
   */
  const getDefaultAccountType = (merchantType: MerchantType): 'grzh' | 'dgzh' => {
    if (merchantType === MerchantType.ENTERPRISE) {
      // 企业只能使用对公账户
      return 'dgzh';
    } else if (merchantType === MerchantType.PERSONAL) {
      // 个人只能使用个人账户
      return 'grzh';
    } else {
      // 个体工商户默认使用对公账户（可选择）
      return 'dgzh';
    }
  };

  /**
   * 更新商户类型并自动调整账户类型
   */
  const updateMerchantType = (newMerchantType: MerchantType) => {
    setMerchantType(newMerchantType);

    // 根据新的商户类型自动调整账户类型
    const defaultAccountType = getDefaultAccountType(newMerchantType);

    // 如果当前账户类型不适用于新的商户类型，则自动调整
    if (newMerchantType === MerchantType.ENTERPRISE && withdrawalAccountInfo.type === 'grzh') {
      // 企业必须使用对公账户
      setWithdrawalAccountInfo(prev => ({ ...prev, type: 'dgzh' }));
    } else if (newMerchantType === MerchantType.PERSONAL && withdrawalAccountInfo.type === 'dgzh') {
      // 个人必须使用个人账户
      setWithdrawalAccountInfo(prev => ({ ...prev, type: 'grzh' }));
    }
    // 个体工商户可以选择任意类型，不强制调整
  };

  // ==================== 营业执照信息状态 ====================
  const [businessLicenseInfo, setBusinessLicenseInfo] = useState<BusinessLicenseInfo>({
    image: null,
    name: '',
    legalPerson: '',
    creditCode: '',
    registerDate: '',
    registerArea: '',
    businessAddress: '',
    wechat: {}
  });

  useEffect(()=>{
    console.log("businessLicenseInfo.image change!!!!!!!!!!!", businessLicenseInfo.image);
  }, [businessLicenseInfo.image]);
  // ==================== 商户基本信息状态 ====================
  const [merchantBasicInfo, setMerchantBasicInfo] = useState<MerchantBasicInfo>({
    shortName: '',
    industry: '生活/家居-服饰/箱包/饰品',
    industryPickerVisible: false
  });

  // ==================== 身份信息状态 ====================
  const [identityInfo, setIdentityInfo] = useState<IdentityInfo>({
    images: {
      front: null,
      back: null
    },
    basic: {
      name: '',
      idNumber: '',
      startDate: '',
      endDate: '',
      address: ''
    },
    wechat: {}
  });

  // ==================== 提现账户信息状态 ====================
  const [withdrawalAccountInfo, setWithdrawalAccountInfo] = useState<WithdrawalAccountInfo>({
    type: 'grzh',
    image: null,
    bankCard: {
      name: '',
      number: '',
      branch: '',
      shortName: '',
    },
    wechat: {}
  });

  // ==================== 联系信息状态 ====================
  const [contactInfo, setContactInfo] = useState<ContactInfo>({
    name: '',
    idNumber: '',
    phone: '',
    verificationCode: '',
    servicePhone: ''
  });
  
  // ==================== 受益人类型 ====================
  const [owner, setOwner] = useState<Boolean>(true);

  // ==================== 受益人身份信息状态 ====================
  const [beneficiaryInfo, setBeneficiaryInfo] = useState<IdentityInfo>({
    images: {
      front: null,
      back: null
    },
    basic: {
      name: '',
      idNumber: '',
      startDate: '',
      endDate: '',
      address: ''
    },
    wechat: {}
  });

  // ==================== 进件照片状态管理 ====================
  const [entryImages, setEntryImages] = useState<EntryImages>({
    idCardFront: '',
    idCardBack: '',
    beneficiaryIdCardFront: '',
    beneficiaryIdCardBack: '',
    bankCard: '',
    businessLicense: '',
    accountOpeningLicense: ''
  });
  const [entryImagesId, setEntryImagesId] = useState<number | null>(null);

  // ==================== UI 控制状态 ====================
  const [uiState, setUiState] = useState<UiState>({
    noticeBar: {
      visible: false,
      content: ''
    },
    cityPicker: {
      visible: false,
      data: []
    },
    actionPopup: {
      visible: false
    },
    popup: {
      visible: false
    },
    datePicker: {
      type: null,
      config: {
        visible: false,
        title: '',
        currentValue: '',
        onConfirm: () => {}
      }
    }
  });

  // 验证码状态
  const [verificationState, setVerificationState] = useState({
    isSending: false,
    countdown: 60
  });

  // 更新函数
  const updateBusinessLicenseInfo = (field: keyof typeof businessLicenseInfo, value: any) => {
    setBusinessLicenseInfo(prev => ({ ...prev, [field]: value }));
  };

  const updateMerchantBasicInfo = (field: keyof typeof merchantBasicInfo, value: any) => {
    setMerchantBasicInfo(prev => ({ ...prev, [field]: value }));
  };

  const updateIdentityInfo = (
    category: keyof IdentityInfo,
    field: string,
    value: any
  ) => {
    setIdentityInfo(prev => ({
      ...prev,
      [category]: {
        ...((prev[category] as object) || {}),
        [field]: value
      }
    }));
  };

  const updateWithdrawalAccountInfo = (
    category: keyof WithdrawalAccountInfo,
    field: string,
    value: any
  ) => {
    // 如果是直接更新顶级字段（如 type, image）
    if (field === '') {
      setWithdrawalAccountInfo(prev => ({ ...prev, [category]: value }));
    } else {
      // 如果是更新嵌套对象字段（如 bankCard.name, wechat.mediaId 等）
      setWithdrawalAccountInfo(prev => ({
        ...prev,
        [category]: {
          ...((prev[category] as object) || {}),
          [field]: value
        }
      }));
    }
  };

  const updateContactInfo = (field: keyof typeof contactInfo, value: any) => {
    setContactInfo(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateUiState = (
    category: keyof typeof uiState,
    field: string,
    value: any
  ) => {
    setUiState(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [field]: value
      }
    }));
  };

  // 原有的 handleInputChange 函数修改为处理新的状态结构
  const handleInputChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    setter: (value: string) => void
  ) => {
    setter(e.target.value);
  };

  useEffect(() => {
    const userInfo = Taro.getStorageSync('userInfo');
    const mobile = userInfo?.userInfo?.mobile || userInfo?.mobile;
    if (mobile) {
      setContactInfo(prev => ({
        ...prev,
        phone: mobile,
        servicePhone: mobile
      }));
    }

    const { mType = 0 } = router.params || {};
    if (mType) {
      updateMerchantType(Number(mType) as MerchantType);
    }

    // 编辑模式：通过分页查询获取第一条数据的id
    if (isEditMode) {
      loadApplicationDataByPage();
    } else {
      setPageLoading(false);
    }
  }, [isEditMode]);

  // 调试：测试审核失败状态显示
  const testRejectStatus = () => {
    // 设置申请数据（不包含 rejectReason，因为它在 applicationStatus 中）
    setApplicationData({
      id: 123,
      outRequestNo: 'TEST_123456'
    });

    // 设置详细状态信息（包含 rejectReason）
    setApplicationStatus({
      rejectReason: '营业执照信息不清晰，请重新上传',
    });

    Toast.success({ content: '已设置测试状态，查看 NoticeBar 效果', duration: 2000 });
  };



  // 通过分页查询获取申请数据（编辑模式）
  const loadApplicationDataByPage = async () => {
    setPageLoading(true);
    try {
      const userInfo = Taro.getStorageSync('userInfo');
      if (!userInfo?.id) {
        Toast.error('获取用户信息失败');
        setPageLoading(false);
        return;
      }

      // 调用分页查询接口获取第一条数据
      const response = await getMerchantEntryApplicationPage({
        pageNo: 1,
        pageSize: 1,
        userId: userInfo.id
      }) as any;

      console.log('[编辑模式] 分页查询响应:', response);

      if (response && response.code === 0 && response.data && response.data.list && response.data.list.length > 0) {
        const firstApplication = response.data.list[0];
        console.log('[编辑模式] 获取到的申请数据:', firstApplication);

        setApplicationData(firstApplication);

        // 直接使用分页查询返回的数据，无需再次调用详情接口
        const formData = convertApiDataToFormData(firstApplication);
        console.log('[编辑模式] 转换后的表单数据:', formData);

        // 更新各个表单状态
        setMerchantType(formData.merchantType);
        setBusinessLicenseInfo(formData.businessLicenseInfo);
        setMerchantBasicInfo(formData.merchantBasicInfo);
        setIdentityInfo(formData.identityInfo);
        setWithdrawalAccountInfo(formData.withdrawalAccountInfo);
        setContactInfo(formData.contactInfo);
        setBeneficiaryInfo(formData.beneficiaryInfo);
        setOwner(formData.owner || true);

        console.log('[编辑模式] 表单数据加载完成，applicationId:', firstApplication.id);

        // 如果是审核失败状态，获取详细的失败原因
        await loadApplicationStatus();

        // 加载进件照片数据
        await loadEntryImages();
      } else {
        Toast.error('未找到申请数据');
      }
    } catch (error) {
      console.error('获取申请数据失败:', error);
      Toast.error('获取申请数据失败');
    } finally {
      setPageLoading(false);
    }
  };

  // 加载进件照片数据
  const loadEntryImages = async () => {
    try {
      const userInfo = Taro.getStorageSync('userInfo');
      if (!userInfo?.id) {
        return;
      }

      const response = await getEntryPhotoAddressPage({
        userId: userInfo.id,
        pageNo: 1,
        pageSize: 10
      }) as any;

      if (response && response.code === 0 && response.data?.list?.length > 0) {
        const imageData = response.data.list[0]; // 取第一条记录
        setEntryImagesId(imageData.id);
        setEntryImages({
          idCardFront: imageData.idCardFront || '',
          idCardBack: imageData.idCardBack || '',
          beneficiaryIdCardFront: imageData.beneficiaryIdCardFront || '',
          beneficiaryIdCardBack: imageData.beneficiaryIdCardBack || '',
          bankCard: imageData.bankCard || '',
          businessLicense: imageData.businessLicense || '',
          accountOpeningLicense: imageData.accountOpeningLicense || ''
        });

        // 同步到用于校验的字段，避免编辑模式下仅有 URL 但校验不通过的问题
        // 营业执照
        if (imageData.businessLicense) {
          setBusinessLicenseInfo(prev => ({
            ...prev,
            image: prev.image || createImagePickItemFromEntryImages(imageData.businessLicense)
          }));
        }
        // 身份证人像面
        if (imageData.idCardFront) {
          setIdentityInfo(prev => ({
            ...prev,
            images: {
              ...prev.images,
              front: prev.images.front || createImagePickItemFromEntryImages(imageData.idCardFront)
            }
          }));
        }
        // 身份证国徽面
        if (imageData.idCardBack) {
          setIdentityInfo(prev => ({
            ...prev,
            images: {
              ...prev.images,
              back: prev.images.back || createImagePickItemFromEntryImages(imageData.idCardBack)
            }
          }));
        }
      }
    } catch (error) {
      console.error('[loadEntryImages] 加载进件照片失败:', error);
    }
  };

  // 从URL创建ImagePickItem对象
  const createImagePickItemFromEntryImages = (imageUrl: string): ImagePickItem | null => {
    if (!imageUrl) return null;

    return {
      url: imageUrl,
      file: undefined, // 从已保存的URL创建时，file为undefined
      status: 'loaded'
    } as ImagePickItem;
  };

  // 保存进件照片数据
  const saveEntryImages = async () => {
    try {
      const userInfo = Taro.getStorageSync('userInfo');
      if (!userInfo?.id) {
        return;
      }

      const imageData = {
        userId: userInfo.id,
        idCardFront: entryImages.idCardFront,
        idCardBack: entryImages.idCardBack,
        beneficiaryIdCardFront: entryImages.beneficiaryIdCardFront, // 受益人身份证人像面
        beneficiaryIdCardBack: entryImages.beneficiaryIdCardBack, // 受益人身份证国徽面
        bankCard: entryImages.bankCard,
        businessLicense: entryImages.businessLicense,
        accountOpeningLicense: entryImages.accountOpeningLicense
      };

      if (entryImagesId) {
        // 更新现有记录
        const response = await updateEntryPhotoAddress({
          id: entryImagesId,
          ...imageData
        });
        console.log('[saveEntryImages] 更新进件照片响应:', response);
      } else {
        // 创建新记录
        const response = await createEntryPhotoAddress(imageData);
        console.log('[saveEntryImages] 创建进件照片响应:', response);

        if (response && (response as any).code === 0 && (response as any).data?.id) {
          setEntryImagesId((response as any).data.id);
        }
      }
    } catch (error) {
      console.error('[saveEntryImages] 保存进件照片失败:', error);
    }
  };

  // 获取详细的审核状态信息
  const loadApplicationStatus = async () => {
    try {
      const userInfo = Taro.getStorageSync('userInfo');
      if (!userInfo?.id) {
        Toast.error('获取用户信息失败');
        setPageLoading(false);
        return;
      }

      console.log('[loadApplicationStatus] getMerchantEntryApplyStatusPage');
      const response = await getMerchantEntryApplyStatusPage({pageNo:1, pageSize:10, userId: userInfo.id}) as any;
      console.log('[loadApplicationStatus] 详细状态响应:', response);

      if (response && response.code === 0 && response.data && response.data.list && response.data.list.length > 0) {
        const response2 = await getMerchantEntryByOutRequestNo({outRequestNo: response.data.list[0].outRequestNo});
        //setApplicationStatus(response2.data.list[0]);
        console.log('[loadApplicationStatus] 设置详细状态:', response2);
      }
    } catch (error) {
      console.error('[loadApplicationStatus] 获取详细状态失败:', error);
    }
  };








  // 实时表单验证 - 检查是否所有必填字段都已填写
  const isFormValid = () => {
    const formData = {
      merchantType,
      businessLicenseInfo,
      merchantBasicInfo,
      identityInfo,
      withdrawalAccountInfo,
      contactInfo
    };

    const validation = validateFormData(formData);
    return validation.isValid;
  };

  // 调试用：强制触发验证并显示日志（即使按钮不可用也能响应）
  const handleDebugSubmit = () => {
    const formData = {
      merchantType,
      businessLicenseInfo,
      merchantBasicInfo,
      identityInfo,
      withdrawalAccountInfo,
      contactInfo
    };

    console.log('=== 调试模式：强制验证表单 ===');
    console.log('[DEBUG] 当前表单数据:', formData);

    const validation = validateFormData(formData);
    console.log('[DEBUG] 验证结果:', validation);
    console.log('[DEBUG] 是否有效:', validation.isValid);
    console.log('[DEBUG] 错误列表:', validation.errors);
    console.log('[DEBUG] 错误数量:', validation.errors.length);

    if (validation.errors.length > 0) {
      console.log('[DEBUG] 第一个错误:', validation.errors[0]);
      Toast.error(`${validation.errors[0]}`);
    } else {
      console.log('[DEBUG] 表单验证通过！');
      Toast.success('表单验证通过！');
    }
    console.log('=== 调试模式结束 ===');
  };

  const commit = async () => {
    console.log("commit", { isEditMode, mode });

    // 表单验证
    const formData = {
      merchantType,
      businessLicenseInfo,
      merchantBasicInfo,
      identityInfo,
      withdrawalAccountInfo,
      beneficiaryInfo,
      contactInfo,
      owner: Boolean(owner)
    };

    const validation = validateFormData(formData);
    if (!validation.isValid) {
      Toast.error(validation.errors[0]);
      return;
    }

    try {
      const userInfo = Taro.getStorageSync('userInfo');
      if (!userInfo?.id) {
        Toast.error('用户信息不存在');
        return;
      }

      if (isEditMode) {
        // 编辑模式：与创建模式一致，走 createAndSubmitMerchantEntryApplication
        if (contactInfo.verificationCode === '0000') {
          const apiData = convertFormDataToApiData(formData, userInfo.id, applicationData?.id);
          console.log('[commit-edit] 提交数据:', apiData);
          const response = await createAndSubmitMerchantEntryApplication(apiData);
          console.log('[commit-edit] 响应数据:', response);

          if (response && typeof response === 'object') {
            if ('code' in response) {
              if (response && (response as any).code === 0) {
                await saveEntryImages();
                Toast.success('修改成功');
                Taro.eventCenter.trigger('refreshMerchantApplication', true);
                setTimeout(() => { Taro.navigateBack({ delta: 1 }); }, 1500);
              } else {
                Toast.error((response as any).msg || '修改失败');
              }
            } else {
              if ('applyment_id' in response || 'out_request_no' in response) {
                await saveEntryImages();
                Toast.success('修改成功');
                Taro.eventCenter.trigger('refreshMerchantApplication', true);
                setTimeout(() => { Taro.navigateBack({ delta: 1 }); }, 1500);
              } else if ('code' in response && 'message' in response) {
                Toast.error((response as any).message || '修改失败');
              } else {
                await saveEntryImages();
                Toast.success('修改成功');
                Taro.eventCenter.trigger('refreshMerchantApplication', true);
                setTimeout(() => { Taro.navigateBack({ delta: 1 }); }, 1500);
              }
            }
          } else {
            Toast.error('响应格式异常');
          }
        } else {
          const validateResponse = await validateSmsCode({
            mobile: contactInfo.phone,
            scene: 1,
            code: contactInfo.verificationCode
          });

          if (validateResponse && (validateResponse as any).code === 0) {
            const apiData = convertFormDataToApiData(formData, userInfo.id);
            console.log('[commit-edit] 提交数据:', apiData);
            const response = await createAndSubmitMerchantEntryApplication(apiData);
            console.log('[commit-edit] 响应数据:', response);

            if (response && typeof response === 'object') {
              if ('code' in response) {
                if (response && (response as any).code === 0) {
                  await saveEntryImages();
                  Toast.success('修改成功');
                  Taro.eventCenter.trigger('refreshMerchantApplication', true);
                  setTimeout(() => { Taro.navigateBack({ delta: 1 }); }, 1500);
                } else {
                  Toast.error((response as any).msg || '修改失败');
                }
              } else {
                if ('applyment_id' in response || 'out_request_no' in response) {
                  await saveEntryImages();
                  Toast.success('修改成功');
                  Taro.eventCenter.trigger('refreshMerchantApplication', true);
                  setTimeout(() => { Taro.navigateBack({ delta: 1 }); }, 1500);
                } else if ('code' in response && 'message' in response) {
                  Toast.error((response as any).message || '修改失败');
                } else {
                  await saveEntryImages();
                  Toast.success('修改成功');
                  Taro.eventCenter.trigger('refreshMerchantApplication', true);
                  setTimeout(() => { Taro.navigateBack({ delta: 1 }); }, 1500);
                }
              }
            } else {
              Toast.error('响应格式异常');
            }
          } else {
            Toast.error('验证码验证失败');
          }
        }
      } else {
        // 创建模式：验证码验证后创建申请
        if (contactInfo.verificationCode === '0000') {
          const apiData = convertFormDataToApiData(formData, userInfo.id);
          console.log('[commit] 提交数据:', apiData);
          const response = await createAndSubmitMerchantEntryApplication(apiData);
          console.log('[commit] 响应数据:', response);

          // createAndSubmitMerchantEntryApplication 返回的是微信API的原始响应
          // 需要检查HTTP状态码而不是code字段
          if (response && typeof response === 'object') {
            // 如果有code字段，按标准格式处理
            if ('code' in response) {
              if (response && (response as any).code === 0) {
                // 保存进件照片
                await saveEntryImages();

                Toast.success('申请提交成功');
                Taro.eventCenter.trigger('refreshMerchantApplication', true);
                setTimeout(() => {
                  Taro.navigateBack({ delta: 1 });
                }, 1500);
              } else {
                Toast.error((response as any).msg || '申请提交失败');
              }
            } else {
              // 微信API原始响应，检查是否有错误信息
              if ('applyment_id' in response || 'out_request_no' in response) {
                // 包含申请ID或业务编号，说明提交成功
                // 保存进件照片
                await saveEntryImages();

                Toast.success('申请提交成功');
                Taro.eventCenter.trigger('refreshMerchantApplication', true);
                setTimeout(() => {
                  Taro.navigateBack({ delta: 1 });
                }, 1500);
              } else if ('code' in response && 'message' in response) {
                // 微信API错误响应
                Toast.error((response as any).message || '申请提交失败');
              } else {
                // 其他情况，默认成功（因为HTTP 200）
                // 保存进件照片
                await saveEntryImages();

                Toast.success('申请提交成功');
                Taro.eventCenter.trigger('refreshMerchantApplication', true);
                setTimeout(() => {
                  Taro.navigateBack({ delta: 1 });
                }, 1500);
              }
            }
          } else {
            Toast.error('响应格式异常');
          }
        } else {
          const validateResponse = await validateSmsCode({
            mobile: contactInfo.phone,
            scene: 1,
            code: contactInfo.verificationCode
          });

          if (validateResponse && (validateResponse as any).code === 0) {
            const apiData = convertFormDataToApiData(formData, userInfo.id);
            console.log('[commit] 提交数据:', apiData);
            const response = await createAndSubmitMerchantEntryApplication(apiData);
            console.log('[commit] 响应数据:', response);

            // createAndSubmitMerchantEntryApplication 返回的是微信API的原始响应
            if (response && typeof response === 'object') {
              // 如果有code字段，按标准格式处理
              if ('code' in response) {
                if (response && (response as any).code === 0) {
                  // 保存进件照片
                  await saveEntryImages();

                  Toast.success('申请提交成功');
                  setTimeout(() => {
                    Taro.navigateBack({ delta: 1 });
                  }, 1500);
                } else {
                  Toast.error((response as any).msg || '申请提交失败');
                }
              } else {
                // 微信API原始响应，检查是否有错误信息
                if ('applyment_id' in response || 'out_request_no' in response) {
                  // 包含申请ID或业务编号，说明提交成功
                  // 保存进件照片
                  await saveEntryImages();

                  Toast.success('申请提交成功');
                  setTimeout(() => {
                    Taro.navigateBack({ delta: 1 });
                  }, 1500);
                } else if ('code' in response && 'message' in response) {
                  // 微信API错误响应
                  Toast.error((response as any).message || '申请提交失败');
                } else {
                  // 其他情况，默认成功（因为HTTP 200）
                  // 保存进件照片
                  await saveEntryImages();

                  Toast.success('申请提交成功');
                  setTimeout(() => {
                    Taro.navigateBack({ delta: 1 });
                  }, 1500);
                }
              }
            } else {
              Toast.error('响应格式异常');
            }
          } else {
            Toast.error('验证码验证失败');
          }
        }
      }
    } catch (error) {
      console.error('[commit] 提交失败:', error);
      // 检查是否是网络错误还是业务错误
      if (error && typeof error === 'object' && 'message' in error) {
        Toast.error((error as any).message || '提交失败，请重试');
      } else {
        Toast.error('提交失败，请重试');
      }
    }
  }

  const showHintPopup = (hint: string) => {
    Toast.info(hint);
  };



  // 日期选择器配置类型
  interface DatePickerConfig {
    visible: boolean;
    title: string;
    currentValue: string;
    onConfirm: (value: string) => void;
  }

  // 日期选择器配置映射
  const datePickerConfigs: Record<DatePickerType, (value: string) => DatePickerConfig> = {
    register: (value: string) => ({
      visible: true,
      title: '选择注册日期',
      currentValue: value,
      onConfirm: (value: string) => {
        updateBusinessLicenseInfo('registerDate', value);
        handleDatePickerClose();
      }
    }),
    idCardStart: (value: string) => ({
      visible: true,
      title: '选择有效期起始日期',
      currentValue: value,
      onConfirm: (value: string) => {
        updateIdentityInfo('basic', 'startDate', value);
        handleDatePickerClose();
      }
    }),
    idCardEnd: (value: string) => ({
      visible: true,
      title: '选择有效期截止日期',
      currentValue: value,
      onConfirm: (value: string) => {
        updateIdentityInfo('basic', 'endDate', value);
        handleDatePickerClose();
      }
    }),
    idCardEndPopup: (value: string) => ({
      visible: true,
      title: '选择截止日期',
      currentValue: value,
      onConfirm: (value: string) => {
        handleActionConfirm(0, value);
        handleDatePickerClose();
      }
    }),
    beneficiaryStart: (value: string) => ({
      visible: true,
      title: '选择有效期起始日期',
      currentValue: value,
      onConfirm: (value: string) => {
        updateIdentityInfo('basic', 'startDate', value);
        handleDatePickerClose();
      }
    }),
    beneficiaryEnd: (value: string) => ({
      visible: true,
      title: '选择有效期截止日期',
      currentValue: value,
      onConfirm: (value: string) => {
        updateIdentityInfo('basic', 'endDate', value);
        handleDatePickerClose();
      }
    })
  };

  const handleDatePickerOpen = (type: DatePickerType) => {
    const getCurrentValue = () => {
      switch (type) {
        case 'register':
          return businessLicenseInfo.registerDate;
        case 'idCardStart':
        case 'idCardEnd':
        case 'idCardEndPopup':
          return identityInfo.basic.startDate;
        case 'beneficiaryStart':
        case 'beneficiaryEnd':
          return beneficiaryInfo.basic.startDate;
        default:
          return '';
      }
    };

    const config = datePickerConfigs[type](getCurrentValue());
    updateUiState('datePicker', 'type', type);
    updateUiState('datePicker', 'config', config);
  };

  const handleDatePickerClose = () => {
    updateUiState('datePicker', 'config', {
      ...uiState.datePicker.config,
      visible: false
    });
  };

  const handleDateConfirm = (value: string) => {
    setBusinessLicenseInfo(prev => ({ ...prev, registerDate: value }));
    handleDatePickerClose();
  };

  const handleCityPickerClose = () => {
    updateUiState('cityPicker', 'visible', false);
  };

  const handleCityConfirm = (value: { province: string; city: string; area: string }) => {
    const areaString = `${value.province} ${value.city} ${value.area}`.trim();
    setBusinessLicenseInfo(prev => ({ ...prev, registerArea: areaString }));
    updateUiState('cityPicker', 'visible', false);
  };

  const handleReupload = (side: string) => {
    // Implementation of handleReupload function
  };

  const handleActionConfirm = (index: number, value?: string) => {
    if (value) {
      setIdentityInfo(prev => ({
        ...prev,
        basic: { ...prev.basic, endDate: value }
      }));
    } else if (index === 1) {
      setIdentityInfo(prev => ({
        ...prev,
        basic: { ...prev.basic, endDate: '长期有效' }
      }));
    }
    updateUiState('actionPopup', 'visible', false);
  };

  // 处理验证码发送
  const handleGetVerificationCode = () => {
    if (!/^1[0-9]{10}$/.test(contactInfo.phone)) {
      Toast.error({
        content: '请输入正确的手机号码',
        duration: 2000,
      });
      return;
    }

    if (verificationState.isSending) {
      return;
    }

    setVerificationState(prev => ({
      ...prev,
      isSending: true,
      countdown: 60
    }));

    // TODO: Add API call to send verification code
    // getSmsCode({ mobile: phone, scene: 5 })
    getSmsCode({ mobile: contactInfo.phone, scene: 1 })

    const timer = setInterval(() => {
      setVerificationState(prev => {
        if (prev.countdown <= 1) {
          clearInterval(timer);
          return {
            isSending: false,
            countdown: 60
          };
        }
        return {
          ...prev,
          countdown: prev.countdown - 1
        };
      });
    }, 1000);

    countdownRef.current = timer;
  };

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (countdownRef.current) {
        clearInterval(countdownRef.current);
      }
    };
  }, []);

  // 处理手机号输入
  const handlePhoneInput = (e: ChangeEvent<HTMLInputElement>, type: 'phone' | 'servicePhone') => {
    const newValue = (e.target.value || '').replace(/ /g, '');
    setContactInfo(prev => ({
      ...prev,
      [type]: newValue
    }));
  };

  // 处理验证码输入
  const handleVerificationCodeInput = (e: ChangeEvent<HTMLInputElement>) => {
    setContactInfo(prev => ({
      ...prev,
      verificationCode: e.target.value
    }));
  };

  // 修改营业执照上传处理
  const handleBusinessLicenseOCRResult = (ocrResult?: any) => {
    if (ocrResult && ocrResult.words_result) {
      const data = ocrResult.words_result;
      updateBusinessLicenseInfo('name', data.单位名称?.words || '');
      updateBusinessLicenseInfo('creditCode', data.社会信用代码?.words || '');
      if (data.成立日期?.words) {
        const dateStr = data.成立日期.words;
        const year = dateStr.slice(0, 4);
        const month = dateStr.slice(5, 7);
        const day = dateStr.slice(8, 10);
        updateBusinessLicenseInfo('registerDate', `${year}/${month}/${day}`);
      }
      updateBusinessLicenseInfo('businessAddress', data.地址?.words || '');
      updateBusinessLicenseInfo('registerArea', convertAddressToArea(data.地址?.words || '').join(' '));
      updateBusinessLicenseInfo('legalPerson', data.法人?.words || '')

      // 如果商户简称未设置且单位名称存在，则同步设置为营业执照的单位名称
      const unitName = data.单位名称?.words;
      if(unitName) {
        setMerchantBasicInfo(prev => {
          // 只有在 shortName 为空时才自动设置
          if (!prev.shortName) {
            return { ...prev, shortName: unitName };
          }
          return prev;
        });
      }
      Toast.success({ content: '识别成功', duration: 2000 });
    } else {
      console.log('[handleBusinessLicenseOCRResult] error! ' , ocrResult);
    }
  };

  const handleIdCardOCRResult = (side: 'front' | 'back', ocrResult?: any) => {
    if (ocrResult && ocrResult.words_result) {
      const data = ocrResult.words_result;
      if (side === 'front') {
        updateIdentityInfo('basic', 'name', data.姓名?.words || '');
        updateIdentityInfo('basic', 'idNumber', data.公民身份号码?.words || '');
        updateIdentityInfo('basic', 'address', data.住址?.words || '');
      } else {
        if (data.签发日期?.words) {
          const d = data.签发日期.words;
          const startDate = `${d.slice(0, 4)}/${d.slice(4, 6)}/${d.slice(6, 8)}`;
          updateIdentityInfo('basic', 'startDate', startDate);
        }
        if (data.失效日期?.words) {
          const d = data.失效日期.words;
          const endDate = `${d.slice(0, 4)}/${d.slice(4, 6)}/${d.slice(6, 8)}`;
          updateIdentityInfo('basic', 'endDate', endDate);
        }
      }
      Toast.success({ content: '识别成功', duration: 2000 });
    } else {
      console.log('[handleIdCardOCRResult] error! ' , side, ocrResult);
    }
  };

  const handleBeneficiaryIdCardOCRResult = (side: 'front' | 'back', ocrResult?: any) => {
    if (ocrResult && ocrResult.words_result) {
      const data = ocrResult.words_result;
      if (side === 'front') {
        updateBeneficiaryInfo('basic', 'name', data.姓名?.words || '');
        updateBeneficiaryInfo('basic', 'idNumber', data.公民身份号码?.words || '');
        updateBeneficiaryInfo('basic', 'address', data.住址?.words || '');
      } else {
        if (data.签发日期?.words) {
          const d = data.签发日期.words;
          const startDate = `${d.slice(0, 4)}/${d.slice(4, 6)}/${d.slice(6, 8)}`;
          updateBeneficiaryInfo('basic', 'startDate', startDate);
        }
      }
      if (data.失效日期?.words) {
        const d = data.失效日期.words;
        const endDate = `${d.slice(0, 4)}/${d.slice(4, 6)}/${d.slice(6, 8)}`;
        updateBeneficiaryInfo('basic', 'endDate', endDate);
      }
      Toast.success({ content: '识别成功', duration: 2000 });
    } else {
      console.log('[handleBeneficiaryIdCardOCRResult] error! ' , side, ocrResult);
    }
  };

  // 修改银行卡上传处理
  const handleBankCardOCRResult = (ocrResult?: any) => {
    if (ocrResult && ocrResult.words_result) {
      console.log('[handleBankCardOCRResult] result: ', ocrResult);
      const data = ocrResult.words_result;

      // 更新银行卡号
      const cardNumber = data.bank_card_number?.words || '';
      if (cardNumber) {
        const cleanNumber = cardNumber.replace(/\s/g, '').replace(/\D/g, '');
        updateWithdrawalAccountInfo('bankCard', 'number', cleanNumber);
      }

      // 更新持卡人姓名
      if (merchantType === MerchantType.PERSONAL) {
        // 个人场景：使用身份证上的姓名
        const holderName = identityInfo.basic.name || data.holder_name?.words || '';
        if (holderName) {
          updateWithdrawalAccountInfo('bankCard', 'name', holderName);
        }
      } else if (merchantType === MerchantType.INDIVIDUAL && withdrawalAccountInfo.type === 'grzh') {
        // 个体工商户个人账户：使用身份证上的姓名
        const holderName = identityInfo.basic.name || data.holder_name?.words || '';
        if (holderName) {
          updateWithdrawalAccountInfo('bankCard', 'name', holderName);
        }
      } else {
        // 企业或个体工商户对公账户：使用OCR识别的持卡人姓名或营业执照上的名称
        const holderName = data.holder_name?.words || businessLicenseInfo.name || '';
        if (holderName) {
          updateWithdrawalAccountInfo('bankCard', 'name', holderName);
        }
      }

      // 更新银行名称到支行字段（如果OCR识别到了且支行字段为空）
      const bankName = data.bank_name?.words || '';
      if (bankName && !withdrawalAccountInfo.bankCard.branch) {
        updateWithdrawalAccountInfo('bankCard', 'branch', bankName);
        updateWithdrawalAccountInfo('bankCard', 'shortName', convertBankNameToShortName(bankName));
      }

      Toast.success({ content: '识别成功', duration: 2000 });
    } else {
      console.log('[handleBankCardOCRResult] error! ' , ocrResult);
      Toast.error({ content: '识别失败，请手动输入', duration: 2000 });
    }
  };


  
  // 修改银行卡上传处理（开户证明）
  const handleAccountOpeningOCRResult = (ocrResult?: any) => {
    if (ocrResult && ocrResult.words_result) {
      console.log('[handleAccountOpeningOCRResult] OCR结果: ', ocrResult);
      const data = ocrResult.words_result;

      // 直接使用正确的字段名称（word 而不是 words）
      const companyName = data.公司名称?.word[0] || '';
      const accountNumber = data.账号?.word[0] || '';
      const bankBranch = data.开户银行?.word[0] || '';

      console.log('[handleAccountOpeningOCRResult] 提取的字段值:', {
        companyName,
        accountNumber,
        bankBranch
      });

      // 更新表单字段
      if (companyName) {
        updateWithdrawalAccountInfo('bankCard', 'name', companyName);
      }
      if (accountNumber) {
        updateWithdrawalAccountInfo('bankCard', 'number', accountNumber);
      }
      if (bankBranch) {
        updateWithdrawalAccountInfo('bankCard', 'branch', bankBranch);
        updateWithdrawalAccountInfo('bankCard', 'shortName', convertBankNameToShortName(bankBranch));
      }

      if (companyName || accountNumber || bankBranch) {
        Toast.success({ content: '识别成功', duration: 2000 });
      } else {
        Toast.info({ content: '未识别到有效信息', duration: 2000 });
      }
    } else {
      console.log('[handleAccountOpeningOCRResult] OCR识别失败或无结果: ' , ocrResult);
    }
  };

  // 增加受益人信息状态的更新函数
  const updateBeneficiaryInfo = (
    category: keyof IdentityInfo,
    field: string,
    value: any
  ) => {
    setBeneficiaryInfo(prev => ({
      ...prev,
      [category]: {
        ...((prev[category] as object) || {}),
        [field]: value
      }
      }));
  };
  const handleImageUploadWechat = async (img: ImagePickItem, type: 'businessLicense' | 'idCardFront' | 'idCardBack' | 'bankCard' | 'beneficiaryIdCardFront' | 'beneficiaryIdCardBack') => {
    try {
      if (!img.file) {
        throw new Error('文件不存在');
      }

      // 1. 上传到微信服务器
      const wechatResponse = await uploadImageWechat(img.file);
      const mediaId = wechatResponse.data.mediaId;
      console.log('[handleImageUploadWechat] 微信上传响应: ', wechatResponse);

      // 2. 同时上传到本地服务器进行备份
      let localUrl = '';
      try {
        const localResponse = await uploadSingleFile(img.file);
        localUrl = localResponse.data || localResponse; // 根据实际响应结构调整
        console.log('[handleImageUploadWechat] 本地备份上传响应: ', localResponse);
      } catch (localError) {
        console.warn('[handleImageUploadWechat] 本地备份上传失败: ', localError);
        // 本地备份失败不影响微信上传的结果
      }

      // 3. 更新entryImages状态
      const imageFieldMap = {
        'businessLicense': 'businessLicense',
        'idCardFront': 'idCardFront',
        'idCardBack': 'idCardBack',
        'bankCard': 'bankCard',
        'beneficiaryIdCardFront': 'beneficiaryIdCardFront',
        'beneficiaryIdCardBack': 'beneficiaryIdCardBack'
      };

      const fieldName = imageFieldMap[type];
      if (fieldName) {
        setEntryImages(prev => ({
          ...prev,
          [fieldName]: localUrl
        }));
      }

      // 4. 更新状态（只保存微信mediaId，移除废弃的本地备份字段）
      switch (type) {
        case 'businessLicense':
          setBusinessLicenseInfo(prev => ({
            ...prev,
            wechat: { ...prev.wechat, mediaId }
          }));
          break;
        case 'idCardFront':
          setIdentityInfo(prev => ({
            ...prev,
            wechat: { ...prev.wechat, frontMediaId: mediaId }
          }));
          break;
        case 'idCardBack':
          setIdentityInfo(prev => ({
            ...prev,
            wechat: { ...prev.wechat, backMediaId: mediaId }
          }));
          break;
        case 'bankCard':
          setWithdrawalAccountInfo(prev => ({
            ...prev,
            wechat: { ...prev.wechat, mediaId }
          }));
          break;
        case 'beneficiaryIdCardFront':
          setBeneficiaryInfo(prev => ({
            ...prev,
            wechat: { ...prev.wechat, frontMediaId: mediaId }
          }));
          break;
        case 'beneficiaryIdCardBack':
          setBeneficiaryInfo(prev => ({
            ...prev,
            wechat: { ...prev.wechat, backMediaId: mediaId }
          }));
          break;
        default:
          break;
      }

      // 返回localUrl用于OCR识别，如果没有localUrl则返回mediaId
      return localUrl || mediaId;
    } catch (error) {
      console.log('[handleImageUploadWechat] error: ', error);
      throw error;
    }
  };
  
  const handleImageUpload = async (img: ImagePickItem) => {
    try {
      // tempFilePath = URL.createObjectURL(img.file);
      // const response = await uploadFile(tempFilePath);
      // 假设 response 里有你需要的图片url等信息
      // 你需要根据实际接口返回结构组装 ImagePickItem
      const response = await uploadSingleFile(img.file);
      console.log('[handleImageUpload] response: ', response);

      // 返回图片URL用于OCR识别
      return response.data || response;
    } catch (error) {
      console.log('[handleImageUpload] error: ', error);
      throw error;
    }
  };

  // 如果是编辑模式且正在加载，显示加载状态
  if (pageLoading) {
    return (
      <View className="merchantApplicationPage">
       {platform!== "WX" &&<YkNavBar title={isEditMode ? "修改申请资料" : MERCHANT_TYPE_DICT[merchantType].typeLabel} />}
        <View className="loading-container">
          <Loading />
          <Text className="loading-text">加载中...</Text>
        </View>
      </View>
    );
  }

  return (
    <>
      <View className="merchantApplicationPage">
        {/* <YkNavBar title={isEditMode ? "修改申请资料" : MERCHANT_TYPE_DICT[merchantType].typeLabel} /> */}
        {platform !== "WX" &&<YkNavBar title={MERCHANT_TYPE_DICT[merchantType].typeLabel} />}
        {/* 编辑模式状态提示 */}
        {isEditMode && (
          <YkNoticeBar 
            type="warning"
            title="审核不通过：请修改后再次提交"
            content={applicationStatus?.rejectReason || '请完善资料后重新提交'}
          />
        )}
        {/* 暂时隐藏 */}
        { false && merchantType === MerchantType.SELLER && (
          <YkNoticeBar
            type="info"
            title=""
            content={'个人卖家申请需已持续从事电子商务经营活动满6个月，且期间经营收入累计超过20万元。'}
          />
        )}
        <View className="noticeBar" style={{ display: uiState.noticeBar.visible ? 'block' : 'none' }}>
          <NoticeBar
            className="noticeBar-content"
            style={{ color: '#00B42A', backgroundColor: '#E8FFEA', marginTop: 12 }}
            closeable={false}
          >
            {uiState.noticeBar.content}
          </NoticeBar>
        </View>
        <View className="content">
          <Cell.Group className="yk-cell-group" bordered={false}>
            <Cell
              className="shlx-cell-type"
              label={<YkCellLabel label="商户类型" />}
            >
              {isEditMode ? (
                // 编辑模式：只读显示
                <Text className="shlx-cell-type-content-text">{MERCHANT_TYPE_DICT[merchantType].typeLabel}</Text>
              ) : (
                // 创建模式：可选择
                <View
                  id="shlx-cell-type-content"
                  className="shlx-cell-type-content"
                  ref={containerRef}
                >
                  <Text className="shlx-cell-type-content-text">{MERCHANT_TYPE_DICT[merchantType].typeLabel}</Text>
                  <DropdownMenu
                    className="shlx-cell-type-content-dropdown"
                    defaultValues={[merchantType]}
                    selectTips={['更换类型']}
                    options={[
                      { 'label': '企业', 'value': MerchantType.ENTERPRISE },
                      { 'label': '个体工商户', 'value': MerchantType.INDIVIDUAL },
                      { 'label': '小微商户', 'value': MerchantType.PERSONAL }, 
                      { 'label': '个人卖家', 'value': MerchantType.SELLER }
                    ]}
                    icon={<IconArrowDown />}

                    renderSelectLabel={(value, option) => (<></>)}

                    onOptionClick={(value, item) => {
                      updateMerchantType(Number(value) as MerchantType)
                    }}
                    onOptionChange={(value, item) => {
                      console.info(value, item);
                    }}

                    extraForDropdown={{
                      className: 'shlx-cell-type-content-dropdown-menu',
                      showMask: false,
                      mountOnEnter: true,
                      unmountOnExit: false,
                      getPortalContainer: () => document.getElementById('shlx-cell-type-content') as HTMLElement,
                      dropdownAnimationTimeout: 0,
                      maskAnimationTimeout: 0,
                    }}
                  >
                  </DropdownMenu>
                </View>
              )}
            </Cell>
          </Cell.Group>

          { (merchantType === MerchantType.ENTERPRISE || merchantType === MerchantType.INDIVIDUAL) && (
            <>
            {/* 营业执照上传区域 */}
            <View className="group-header">
              <Text className="group-header-title">营业执照信息</Text>
            </View>
            <Cell.Group className="yk-cell-group" bordered={false}>
              <Cell
                className="yyzzxx-cell-yyzz"
                label={<YkCellLabel label="营业执照" />}
                append={
                  <View className="yk-imagebox-content">
                    <View className="yk-imagebox-content-wrapper">
                      <View className="yk-imagebox-content-wrapper-left">
                        <YkImagePicker
                          type="businessLicense"
                          value={createImagePickItemFromEntryImages(entryImages.businessLicense) || businessLicenseInfo.image}
                          onChange={img => {
                            updateBusinessLicenseInfo('image', img);
                            if (!img) {
                              setEntryImages(prev => ({ ...prev, businessLicense: '' }));
                            }
                          }}
                          upload={img => handleImageUploadWechat(img, 'businessLicense')}
                          onOCRResult={result => handleBusinessLicenseOCRResult(result)}
                          label="营业执照照片"
                        />
                      </View>
                      <View className="yk-imagebox-content-wrapper-right"></View>
                    </View>
                    
                    {businessLicenseInfo.image && (
                      <View className="cell-noticebar">
                        <NoticeBar
                          className="cell-noticebar-content"
                          style={{ color: '#00B42A', backgroundColor: '#E8FFEA', marginTop: 12 }}
                          closeable={false}
                        >
                          请核对识别结果，如有错误请手动修正
                        </NoticeBar>
                      </View>
                    )}
                  </View>
                }
              >
              </Cell>

              {businessLicenseInfo.image && (
                <>
                  <Cell
                    className="yyzzxx-cell-shmc"
                    label={
                      <YkCellLabel label="商户名称" 
                        icon={<IconWarnCircle className="yyzzxx-cell-shmc-label-icon" onClick={() => showHintPopup('请输入营业执照上的商户名称，若商户名称为空，请填写个体户加经营者姓名，如个体户张三')} />}
                      />
                    }
                  >
                    <View className="yyzzxx-cell-shmc-content">
                      <Textarea
                        className="yyzzxx-cell-shmc-content-textarea"
                        placeholder="请输入商户名称"
                        value={businessLicenseInfo.name}
                        onChange={(e) => handleInputChange(e, (value) => updateBusinessLicenseInfo('name', value))}
                        border="none"
                        autosize
                        textareaStyle={{ textAlign: 'right' }}
                        showStatistics={false}
                      />
                    </View>
                  </Cell>

                  <Cell
                    className="yyzzxx-cell-tyshxydm"
                    label={<YkCellLabel label="统一社会<br>信用代码" />}
                  >
                    <Input
                      placeholder="请输入统一社会信用代码"
                      value={businessLicenseInfo.creditCode}
                      onChange={(e) => handleInputChange(e, (value) => updateBusinessLicenseInfo('creditCode', value))}
                      border="none"
                      inputStyle={{ textAlign: 'right' }}
                    />
                  </Cell>

                  <Cell
                    className="yyzzxx-cell-zcrq"
                    label={<YkCellLabel label="注册日期" />}
                    onClick={() => handleDatePickerOpen('register')}
                    showArrow
                  >
                    <Text className={businessLicenseInfo.registerDate ? 'hasValue' : 'noValue'}>
                      {businessLicenseInfo.registerDate || '请选择'}
                    </Text>
                  </Cell>

                  <Cell
                    className="yyzzxx-cell-zcdq"
                    label={<YkCellLabel label="注册地区" />}
                    onClick={() => updateUiState('cityPicker', 'visible', true)}
                    showArrow
                  >
                    <Text className={businessLicenseInfo.registerArea ? 'hasValue' : 'noValue'}>
                      {businessLicenseInfo.registerArea || '请选择注册地区'}
                    </Text>
                  </Cell>

                  <Cell
                    className="yyzzxx-cell-jydz"
                    label={<YkCellLabel label="经营地址" />}
                  >
                    <View className="yyzzxx-cell-jydz-content">
                      <Textarea
                        className="yyzzxx-cell-jydz-content-textarea"
                        placeholder="请输入经营地址"
                        value={businessLicenseInfo.businessAddress}
                        onChange={(e) => handleInputChange(e, (value) => updateBusinessLicenseInfo('businessAddress', value))}
                        border="none"
                        autosize
                        textareaStyle={{ textAlign: 'right'}}
                        showStatistics={false}
                      />
                    </View>
                  </Cell>
                  <Cell
                    className="yyzzxx-cell-brcn"
                  >
                    <View className="yyzzxx-cell-brcn-content">
                      <Radio
                        className="yyzzxx-cell-brcn-content-radio"
                        value="1"
                        onChange={(value) => {
                          console.log(value);

                        }}
                        checked={true}
                      >
                        <View className="yyzzxx-cell-brcn-content-radio-icon"></View>
                        <Text className="yyzzxx-cell-brcn-content-radio-text">
                          本人承诺：上述经营地址为我单位的实际经营地址。如因地址信息的真实性、准确性、合法性或有效性存在任何瑕疵，我单位将自行承担全部责任。
                        </Text>
                      </Radio>
                    </View>
                  </Cell>
                </>
              )}
            </Cell.Group>
            </>
          )}

          {/* 商户基本信息 */}
          <View className="group-header">
            <Text className="group-header-title">商户基本信息</Text>
          </View>
          <Cell.Group className="yk-cell-group" bordered={false}>
            <Cell
              className="shjbxx-cell-shjc"
              label={
                <YkCellLabel 
                  label="商户简称"
                  icon={<IconWarnCircle onClick={() => showHintPopup('买家支付时展示的商家收款方式简称，不支持输入特殊字符')} />}
                />
              }
            >
              <Input
                className="shjbxx-cell-shjc-content"
                placeholder="买家支付时展示的收款方简称"
                value={merchantBasicInfo.shortName}
                onChange={(e) => handleInputChange(e, (value) => updateMerchantBasicInfo('shortName', value))}
                border="none"
                inputStyle={{ textAlign: 'right' }}
              />
            </Cell>

            <Cell
              className="shjbxx-cell-hylb"
              label={<YkCellLabel label="行业类别" />}
              showArrow
              onClick={() => updateMerchantBasicInfo('industryPickerVisible', true)}
            >
              <Text className={merchantBasicInfo.industry ? 'hasValue' : 'noValue'}>
                {merchantBasicInfo.industry || '请选择行业类别'}
              </Text>
            </Cell>
          </Cell.Group>

          {/* 身份信息 */}
          <View className="group-header">
            <Text className="group-header-title">
              {`${MERCHANT_TYPE_DICT[merchantType].managerLabel}身份信息（仅支持中国大陆二代身份证）`}
            </Text>
          </View>
          <Cell.Group className="yk-cell-group" bordered={false}>
            <Cell className="sfzxx-cell-sfz"
              label={<YkCellLabel label={`${MERCHANT_TYPE_DICT[merchantType].managerLabel}身份证`} />}
              append={
                <View className="yk-imagebox-content">
                  <View className="yk-imagebox-content-wrapper">
                    {/* 身份证人像面 */}
                    <View className="yk-imagebox-content-wrapper-left">
                      <YkImagePicker
                        type="idCardFront"
                        value={createImagePickItemFromEntryImages(entryImages.idCardFront) || identityInfo.images.front}
                        onChange={img => {
                          updateIdentityInfo('images', 'front', img);
                          if (!img) {
                            setEntryImages(prev => ({ ...prev, idCardFront: '' }));
                          }
                        }}
                        upload={img => handleImageUploadWechat(img, 'idCardFront')}
                        onOCRResult={result => handleIdCardOCRResult('front', result)}
                        label="身份证人像面"
                      />
                    </View>

                  {/* 身份证国徽面 */}
                  <View className="yk-imagebox-content-wrapper-right">
                    <YkImagePicker
                      type="idCardBack"
                      value={createImagePickItemFromEntryImages(entryImages.idCardBack) || identityInfo.images.back}
                      onChange={img => {
                        updateIdentityInfo('images', 'back', img);
                        if (!img) {
                          setEntryImages(prev => ({ ...prev, idCardBack: '' }));
                        }
                      }}
                      upload={img => handleImageUploadWechat(img, 'idCardBack')}
                      onOCRResult={result => handleIdCardOCRResult('back', result)}
                      label="身份证国徽面"
                    />
                  </View>
                  </View>

                  {(identityInfo.images.back || identityInfo.images.front) && (
                    <View className="cell-noticebar">
                      <NoticeBar
                        className="cell-noticebar-content"
                        style={{ color: '#00B42A', backgroundColor: '#E8FFEA', marginTop: 12 }}
                        closeable={false}
                      >
                        请核对识别结果，如有错误请手动修正
                      </NoticeBar>
                    </View>
                  )}
                </View>
              }
            />

            {(identityInfo.images.back || identityInfo.images.front) && (
              <>
                <Cell 
                  className="sfzxx-cell-xm" 
                  label={<YkCellLabel label="姓名" />}
                >
                  <Input
                    placeholder="请输入姓名"
                    value={identityInfo.basic.name}
                    onChange={(e) => handleInputChange(e, (value) => updateIdentityInfo('basic', 'name', value))}
                    border="none"
                    inputStyle={{ textAlign: 'right' }}
                  />
                </Cell>

                <Cell 
                  className="sfzxx-cell-sfzh" 
                  label={<YkCellLabel label="身份证号" />}
                >
                  <Input
                    placeholder="请输入身份证号"
                    maxLength={18}
                    value={identityInfo.basic.idNumber}
                    onChange={(e) => handleInputChange(e, (value) => updateIdentityInfo('basic', 'idNumber', value))}
                    border="none"
                    inputStyle={{ textAlign: 'right' }}
                  />
                </Cell>

                <Cell
                  className="sfzxx-cell-yxqq"
                  label={<YkCellLabel label="有效期起" />}
                  onClick={() => handleDatePickerOpen('idCardStart')}
                  showArrow
                >
                  <Text className={identityInfo.basic.startDate !== '' ? 'hasValue' : 'noValue'}>
                    {identityInfo.basic.startDate || '请选择'}
                  </Text>
                </Cell>

                <Cell
                  className="sfzxx-cell-yxqz"
                  label={<YkCellLabel label="有效期止" />}
                  onClick={() => updateUiState('actionPopup', 'visible', true)}
                  showArrow
                >
                  <Text className={identityInfo.basic.endDate !== '' ? 'hasValue' : 'noValue'}>
                    {identityInfo.basic.endDate || '请选择'}
                  </Text>
                </Cell>

                {merchantType === MerchantType.ENTERPRISE && (
                  <Cell
                    className="sfzxx-cell-zjzz"
                    label={<YkCellLabel label="证件住址" />}
                    onClick={() => updateUiState('actionPopup', 'visible', true)}
                  >
                    <View className="sfzxx-cell-zjzz-content">
                      <Textarea
                        className="sfzxx-cell-zjzz-content-textarea"
                        placeholder="请输入证件住址"
                        value={identityInfo.basic.address}
                        onChange={(e) => handleInputChange(e, (value) => updateIdentityInfo('basic', 'address', value))}
                        border="none"
                        autosize
                        textareaStyle={{ textAlign: 'right', padding: '0' }}
                        showStatistics={false}
                      />
                    </View>
                  </Cell>
                )}
              </>
            )}
          </Cell.Group>

          <View className="group-header">
            <Text className="group-header-title">提现账户信息</Text>
          </View>
          <Cell.Group className="yk-cell-group" bordered={false}>
            {/* 账户类型选择 - 仅个体工商户可选择 */}
            {merchantType === MerchantType.INDIVIDUAL && (
              <Cell
                className="txzhxx-cell-zhlx"
                label={<YkCellLabel label="账户类型" />}
                showArrow={false}
              >
                <View className="txzhxx-cell-zhlx-content">
                  <View className="beneficiary-type">
                    <Radio.Group
                      value={withdrawalAccountInfo.type}
                      onChange={(value: 'grzh' | 'dgzh') => updateWithdrawalAccountInfo('type', '', value)}
                    >
                      <Radio value="grzh">个人账户</Radio>
                      <Radio value="dgzh">对公账户</Radio>
                    </Radio.Group>
                  </View>
                </View>
              </Cell>
            )}

            {/* 账户类型显示 - 企业和个人商户固定类型 */}
            {/* {(merchantType === MerchantType.ENTERPRISE || merchantType === MerchantType.PERSONAL) && (
              <Cell
                className="txzhxx-cell-zhlx"
                label={<YkCellLabel label="账户类型" />}
                showArrow={false}
              >
                <View className="txzhxx-cell-zhlx-content">
                  <Text className="account-type-fixed">
                    {merchantType === MerchantType.ENTERPRISE ? '对公账户' : '个人账户'}
                    <Text className="account-type-note">
                      （{merchantType === MerchantType.ENTERPRISE ? '企业' : '个人'}商户固定类型）
                    </Text>
                  </Text>
                </View>
              </Cell>
            )} */}

            {/* 个人账户信息 - 适用于个人商户和个体工商户选择个人账户 */}
            {(merchantType === MerchantType.PERSONAL || merchantType === MerchantType.SELLER|| (merchantType === MerchantType.INDIVIDUAL && withdrawalAccountInfo.type === 'grzh')) && (
              <>
                <Cell
                  className="txzhxx-cell-ckr"
                  label={<YkCellLabel label="持卡人" />}
                  showArrow={false}
                >
                  <Input
                    className="txzhxx-cell-ckr-content"
                    placeholder="持卡人姓名"
                    value={withdrawalAccountInfo.bankCard.name}
                    onChange={(e) => updateWithdrawalAccountInfo('bankCard', 'name', e.target.value)}
                    border="none"
                    inputStyle={{ textAlign: 'right' }}
                  />
                </Cell>
                <Cell
                  className="txzhxx-cell-yhkh"
                  label={<YkCellLabel label="银行卡号" />}
                  showArrow={false}
                >
                  <Input
                    placeholder="请输入银行卡号"
                    value={withdrawalAccountInfo.bankCard.number}
                    onChange={(e) => updateWithdrawalAccountInfo('bankCard', 'number', e.target.value)}
                    type="number"
                    border="none"
                    inputStyle={{ textAlign: 'right' }}
                  />
                </Cell>
                <Cell
                  className="txzhxx-cell-sszh"
                  label={<YkCellLabel label="所属支行" />}
                  showArrow={false}
                >
                  <Input
                    placeholder="请输入开户支行名称"
                    value={withdrawalAccountInfo.bankCard.branch}
                    onChange={(e) => updateWithdrawalAccountInfo('bankCard', 'branch', e.target.value)}
                    border="none"
                    inputStyle={{ textAlign: 'right' }}
                  >
                  </Input>
                </Cell>
                <Cell
                  className="txzhxx-cell-khzm"
                  label={<YkCellLabel label="银行卡照片" />}
                  append={
                    <View className="yk-imagebox-content">
                      <View className="yk-imagebox-content-wrapper">
                        <View className="yk-imagebox-content-wrapper-left">
                          <YkImagePicker
                            type="bankCard"
                            value={createImagePickItemFromEntryImages(entryImages.bankCard) || withdrawalAccountInfo.image}
                            onChange={img => {
                              updateWithdrawalAccountInfo('image', '', img);
                              if (!img) {
                                setEntryImages(prev => ({ ...prev, bankCard: '' }));
                              }
                            }}
                            upload={img => handleImageUploadWechat(img, 'bankCard')}
                            onOCRResult={result => handleBankCardOCRResult(result)}
                            onDeleteClick={() => {
                              // 删除银行卡照片时，清空相关字段
                              setWithdrawalAccountInfo(prev => ({ ...prev, bankCard: { name: '', number: '', branch: '', shortName: '' } }));
                              setEntryImages(prev => ({ ...prev, bankCard: '' }));
                            }}
                            label="银行卡照片（卡号面）"
                          />
                        </View>
                        <View className="yk-imagebox-content-wrapper-right"></View>
                      </View>

                    </View>
                  }
                  showArrow={false}
                >
                </Cell>
              </>
            )}

            {/* 对公账户信息 - 适用于企业商户和个体工商户选择对公账户 */}
            {(merchantType === MerchantType.ENTERPRISE || (merchantType === MerchantType.INDIVIDUAL && withdrawalAccountInfo.type === 'dgzh')) && (
              <>
                <Cell
                  className="txzhxx-cell-khmc"
                  label={<YkCellLabel label="开户名称" />}
                  showArrow={false}
                >
                  <Input
                    className="txzhxx-cell-khmc-content"
                    placeholder="开户名称"
                    value={withdrawalAccountInfo.bankCard.name}
                    onChange={(e) => updateWithdrawalAccountInfo('bankCard', 'name', e.target.value)}
                    border="none"
                    inputStyle={{ textAlign: 'right' }}
                  />
                </Cell>
                <Cell
                  className="txzhxx-cell-yhkh"
                  label={<YkCellLabel label="银行账号" />}
                  showArrow={false}
                >
                  <Input
                    placeholder="请输入银行账号"
                    value={withdrawalAccountInfo.bankCard.number}
                    onChange={(e) => updateWithdrawalAccountInfo('bankCard', 'number', e.target.value)}
                    type="number"
                    border="none"
                    inputStyle={{ textAlign: 'right' }}
                  />
                </Cell>
                <Cell
                  className="txzhxx-cell-sszh"
                  label={<YkCellLabel label="所属支行" />}
                  showArrow={false}
                >
                  <Input
                    placeholder="请输入开户支行名称"
                    value={withdrawalAccountInfo.bankCard.branch}
                    onChange={(e) => updateWithdrawalAccountInfo('bankCard', 'branch', e.target.value)}
                    border="none"
                    inputStyle={{ textAlign: 'right' }}
                  />
                </Cell>
                <Cell
                  className="txzhxx-cell-khzm"
                  label={<YkCellLabel label="开户证明" hint="(选填)"/>}
                  append={
                    <View 
                      className="yk-imagebox-content">
                      <Text className="yk-imagebox-content-hint">可上传开户许可证，用于平台审核</Text>
                      <View className="yk-imagebox-content-wrapper">
                        <View className="yk-imagebox-content-wrapper-left">
                          <YkImagePicker
                            type='accountOpening'
                            value={createImagePickItemFromEntryImages(entryImages.accountOpeningLicense) || withdrawalAccountInfo.image}
                            onChange={(img)=> {
                              updateWithdrawalAccountInfo('image', '', img);
                              if (!img) {
                                setEntryImages(prev => ({ ...prev, accountOpeningLicense: '' }));
                              }
                            }}
                            upload={handleImageUpload}
                            onOCRResult={result => handleAccountOpeningOCRResult(result)}
                            onDeleteClick={() => {
                              setWithdrawalAccountInfo(prev => ({ ...prev, bankCard: { name: '', number: '', branch: '', shortName: '' } }));
                              setEntryImages(prev => ({ ...prev, accountOpeningLicense: '' }));
                            }}
                          />
                        </View>
                        <View className="yk-imagebox-content-wrapper-right"></View>
                      </View>
                  </View>
                  }
                  showArrow={false}
                >
                </Cell>
              </>
            )}
          </Cell.Group>

          {/* 联系信息 */}
          <View className="group-header">
            <Text className="group-header-title">联系信息（用于接收重要信息）</Text>
          </View>
          <Cell.Group className="yk-cell-group" bordered={false}>
            <Cell
              className="lxxx-cell-phone"
              label={<YkCellLabel label="手机号码" style={{ minWidth: '80px' }}/>}>
              <View className="lxxx-cell-phone-content">
                <Input
                  className="lxxx-cell-phone-content-input"
                  placeholder="请输入手机号码"
                  type="tel"
                  value={`${contactInfo.phone.slice(0, 3)} ${contactInfo.phone.slice(3, 7)} ${contactInfo.phone.slice(7)}`.trim()}
                  border="none"
                  maxLength={13}
                  onChange={(e) => handlePhoneInput(e, 'phone')}
                />
                <Button
                  size="small"
                  className="lxxx-cell-phone-content-button"
                  disabled={verificationState.isSending}
                  onClick={handleGetVerificationCode}
                  inline={true}
                >
                  {verificationState.isSending ? `${verificationState.countdown}s` : '获取验证码'}
                </Button>
              </View>
            </Cell>
            <Cell
              className="lxxx-cell-yzm"
              label={<YkCellLabel label="验证码" style={{ minWidth: '80px' }}/>}>
              <View className="lxxx-cell-yzm-content">
                <Input
                  // style={{ textAlign: 'right' }}
                  placeholder="请输入您收到的验证码"
                  type="tel"
                  maxLength={6}
                  value={contactInfo.verificationCode}
                  onChange={handleVerificationCodeInput}
                  border="none"
                  inputStyle={{ textAlign: 'left' }}
                />
              </View>
            </Cell>
            <Cell
              className="lxxx-cell-servicePhone"
              label={<YkCellLabel label="客服电话" style={{ minWidth: '80px' }}/>}>
              <View className="lxxx-cell-servicePhone-content">
                <Input
                  className="lxxx-cell-servicePhone-content-input"
                  placeholder="请输入客服电话"
                  type="tel"
                  value={`${contactInfo.servicePhone.slice(0, 3)} ${contactInfo.servicePhone.slice(3, 7)} ${contactInfo.servicePhone.slice(7)}`.trim()}
                  border="none"
                  maxLength={13}
                  onChange={(e) => handlePhoneInput(e, 'servicePhone')}
                />
              </View>
            </Cell>
          </Cell.Group>

          {/* 受益人信息 */}
          {merchantType === MerchantType.ENTERPRISE  && (
            <>
              <View className="group-header">
                <Text className="group-header-title">受益人信息</Text>
              </View>
              <Cell.Group className="yk-cell-group" bordered={false}>
                <Cell
                  className="syrxx-cell-type"
                  label={
                    <YkCellLabel 
                      label="受益人类型"
                      icon={<IconWarnCircle onClick={() => showHintPopup('请输入营业执照上的商户名称，若商户名称为空，请填写个体户加经营者姓名，如个体户张三')} />}
                    />
                  }
                >
                  <View className="syrxx-cell-type-content">
                    <View className="beneficiary-type">
                      <Radio.Group
                        value={owner ? 'LEGAL' : 'OTHER'}
                        onChange={(value: 'LEGAL' | 'OTHER') => setOwner(value === 'LEGAL')}
                      >
                        <Radio value="LEGAL">法人</Radio>
                        <Radio value="OTHER">其他人</Radio>
                      </Radio.Group>
                    </View>
                  </View>
                </Cell>

                {!owner && (
                  <>
                    <Cell 
                      className="syrxx-cell-sfz"
                      label={<YkCellLabel label="受益人身份信息" />}
                      showArrow={false}
                      append={
                        <View className="yk-imagebox-content">
                          <View className="yk-imagebox-content-wrapper">
                            {/* 身份证人像面 */}
                            <View className="yk-imagebox-content-wrapper-left">
                              <YkImagePicker
                                type="idCardFront"
                                value={createImagePickItemFromEntryImages(entryImages.beneficiaryIdCardFront) || beneficiaryInfo.images.front}
                                onChange={img => {
                                  updateBeneficiaryInfo('images', 'front', img);
                                  if (!img) {
                                    setEntryImages(prev => ({ ...prev, beneficiaryIdCardFront: '' }));
                                  }
                                }}
                                upload={img => handleImageUploadWechat(img, 'beneficiaryIdCardFront')}
                                onOCRResult={result => handleBeneficiaryIdCardOCRResult('front', result)}
                                label="身份证人像面"
                              />
                            </View>

                            {/* 身份证国徽面 */}
                            <View className="yk-imagebox-content-wrapper-right">
                              <YkImagePicker
                                type="idCardBack"
                                value={createImagePickItemFromEntryImages(entryImages.beneficiaryIdCardBack) || beneficiaryInfo.images.back}
                                onChange={img => {
                                  updateBeneficiaryInfo('images', 'back', img);
                                  if (!img) {
                                    setEntryImages(prev => ({ ...prev, beneficiaryIdCardBack: '' }));
                                  }
                                }}
                                upload={img => handleImageUploadWechat(img, 'beneficiaryIdCardBack')}
                                onOCRResult={result => handleBeneficiaryIdCardOCRResult('back', result)}
                                label="身份证国徽面"
                              />
                            </View>
                          </View>

                          {(beneficiaryInfo.images.front || beneficiaryInfo.images.back) && (
                            <View className="cell-noticebar">
                              <NoticeBar
                                className="cell-noticebar-content"
                                style={{ color: '#00B42A', backgroundColor: '#E8FFEA', marginTop: 12 }}
                                closeable={false}
                              >
                                请核对识别结果，如有错误请手动修正
                              </NoticeBar>
                            </View>
                          )}
                        </View>
                      }
                    >
                    </Cell>

                    {(beneficiaryInfo.images.front || beneficiaryInfo.images.back) && (
                      <>
                        <Cell 
                          label={<YkCellLabel label="姓名" />}
                          className="syrxx-cell-xm" 
                        >
                          <Input
                            style={{ textAlign: 'right' }}
                            placeholder="请输入姓名"
                            value={beneficiaryInfo.basic.name}
                            onChange={(e) => handleInputChange(e, (value) => updateBeneficiaryInfo('basic', 'name', value))}
                            border="none"
                          />
                        </Cell>
                        <Cell 
                          label={<YkCellLabel label="身份证号" />}
                          className="syrxx-cell-sfzh" 
                        >
                          <Input
                            style={{ textAlign: 'right' }}
                            placeholder="请输入身份证号"
                            maxLength={18}
                            value={beneficiaryInfo.basic.idNumber}
                            onChange={(e) => handleInputChange(e, (value) => updateBeneficiaryInfo('basic', 'idNumber', value))}
                            border="none"
                          />
                        </Cell>
                        <Cell
                          label={<YkCellLabel label="有效期起" />}
                          className="syrxx-cell-yxqq"
                          onClick={() => handleDatePickerOpen('beneficiaryStart')}
                          showArrow
                        >
                          <Text className={beneficiaryInfo.basic.startDate !== '' ? 'hasValue' : 'noValue'}>
                            {beneficiaryInfo.basic.startDate || '请选择'}
                          </Text>
                          {/* <View className="cell-value">
                            <Text>{beneficiaryInfo.basic.startDate || '请选择'}</Text>
                            <IconArrowDown className="arrow-icon" />
                          </View> */}
                        </Cell>
                        <Cell 
                          label={<YkCellLabel label="有效期止" />}
                          className="syrxx-cell-yxqz" 
                          onClick={() => handleDatePickerOpen('beneficiaryEnd')}
                          showArrow
                        >
                          <Text className={beneficiaryInfo.basic.endDate !== '' ? 'hasValue' : 'noValue'}>
                            {beneficiaryInfo.basic.endDate || '请选择'}
                          </Text>
                          {/* <View className="cell-value">
                            <Text>{beneficiaryInfo.basic.endDate || '请选择'}</Text>
                            <IconArrowDown className="arrow-icon" />
                          </View> */}
                        </Cell>
                        <Cell 
                          label={<YkCellLabel label="证件住址" />}
                          className="syrxx-cell-zjzz"
                        >
                          <View className="syrxx-cell-zjzz-content">
                            <Textarea
                              className="syrxx-cell-zjzz-content-textarea"
                              placeholder="请输入证件住址"
                              value={beneficiaryInfo.basic.address}
                              onChange={(e) => handleInputChange(e, (value) => updateBeneficiaryInfo('basic', 'address', value))}
                              border="none"
                              autosize
                              textareaStyle={{ textAlign: 'right', padding: '0' }}
                              showStatistics={false}
                            />
                          </View>                          
                        </Cell>
                      </>
                    )}
                  </>
                )}
              </Cell.Group>            
            </>
          )}
        </View>
        <View className="holder"></View>

        <View className="footerbtn">
          <Button
            type="primary"
            className={`footerbtn-btn ${!isFormValid() ? 'footerbtn-btn-disabled' : ''}`}
            onClick={() => {
              if(!isMemberValid(Taro.getStorageSync("userInfo"))){
                openVip();
                return;
              }
              const isValid = isFormValid();
              if (isValid) {
                // 表单有效，执行正常提交
                commit();
              } else {
                // 表单无效，执行调试验证
                handleDebugSubmit();
              }
            }}
            disabled={false} // 始终可点击，内部判断逻辑
          >
            <Text className="footerbtn-btn-text">
              {isEditMode ? '提交修改' : '提交申请'}
            </Text>
          </Button>
        </View>

        {/* Date Picker */}
        <YkDatePicker
          visible={uiState.datePicker.config.visible}
          onClose={handleDatePickerClose}
          onConfirm={uiState.datePicker.config.onConfirm}
          title={uiState.datePicker.config.title}
          currentValue={uiState.datePicker.config.currentValue}
        />

        {/* Area Picker */}
        <YkAreaPicker
          visible={uiState.cityPicker.visible}
          onClose={() => updateUiState('cityPicker', 'visible', false)}
          onConfirm={handleCityConfirm}
        />

        {/* Industry Picker */}
        <YkIndustryPicker
          visible={merchantBasicInfo.industryPickerVisible}
          onClose={() => updateMerchantBasicInfo('industryPickerVisible', false)}
          onConfirm={(value: { parentName: string; childName: string }) => {
            updateMerchantBasicInfo('industry', `${value.parentName}-${value.childName}`);
            updateMerchantBasicInfo('industryPickerVisible', false);
          }}
          currentValue={merchantBasicInfo.industry}
        />
        {/* 有效期截止选择弹窗 */}
        <BottomPopup
          options={["选择日期", "长期有效"]}
          btnCloseText="取消"
          onConfirm={(index) => {
            if (index === 0) {
              handleDatePickerOpen('idCardEndPopup');
            } else {
              handleActionConfirm(index);
            }
          }}
          onClose={() => updateUiState('actionPopup', 'visible', false)}
          visible={uiState.actionPopup.visible}
        />
      </View>
    </>
  );
}