import { useState, useEffect, useRef, useCallback } from "react";
import Taro from "@tarojs/taro";
import { View, Text, Image, ScrollView } from "@tarojs/components";
import { IconPicture, IconCheck } from "@arco-design/mobile-react/esm/icon";
import { toast } from "@/utils/yk-common";
import "./selectClassify.less";

interface Tag {
  id: number;
  userId: number | null;
  parentId: number | null;
  dynamicsId: string | null;
  name: string;
  coverImage: string | null;
  type: number;
  sort: number | null;
  isTop: number | null;
  sortType: number | null;
  children: Tag[];
}

interface SelectClassifyProps {
  data: Tag[];
  onConfirm: (selectedTags: Tag[]) => void;
  onCancel: () => void;
  maxSelect?: number;
  selectedIds?: number[];
}

export default function SelectClassify({
  data,
  onConfirm,
  onCancel,
  maxSelect = 3,
  selectedIds = [],
}: SelectClassifyProps) {
  const [selectedTags, setSelectedTags] = useState<Tag[]>([]);
  const [activeCategory, setActiveCategory] = useState<number | null>(null);
  const [isMultiSelect, setIsMultiSelect] = useState(false); // 是否为多选模式
  const scrollViewRef = useRef<any>(null);
  const categoryRefs = useRef<{ [key: number]: any }>({});
  const isScrollingToCategory = useRef(false);

  useEffect(() => {
    // 初始化已选择的标签
    if (selectedIds.length > 0) {
      const initialSelected: Tag[] = [];
      const findTagsById = (tags: Tag[]) => {
        tags.forEach((tag) => {
          if (selectedIds.includes(tag.id) && tag.type === 1) {
            initialSelected.push(tag);
          }
          if (tag.children && tag.children.length > 0) {
            findTagsById(tag.children);
          }
        });
      };
      findTagsById(data);
      setSelectedTags(initialSelected);
    }

    // 默认选中第一个分类
    const firstCategory = data.find((item) => item.type === 2);
    if (firstCategory) {
      setActiveCategory(firstCategory.id);
    }
  }, [selectedIds, data]);

  // 节流函数
  const throttle = (func: Function, delay: number) => {
    let timeoutId: NodeJS.Timeout | null = null;
    let lastExecTime = 0;
    return (...args: any[]) => {
      const currentTime = Date.now();

      if (currentTime - lastExecTime > delay) {
        func(...args);
        lastExecTime = currentTime;
      } else {
        if (timeoutId) clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          func(...args);
          lastExecTime = Date.now();
        }, delay - (currentTime - lastExecTime));
      }
    };
  };

  // 处理滚动事件，检测当前可见的分类
  const handleScroll = useCallback(() => {
    const throttledHandler = throttle(() => {
      if (isScrollingToCategory.current) return;

      const categories = data.filter((item) => item.type === 2);

      // 查找当前滚动位置对应的分类
      let currentCategory: number | null = null;
      let minDistance = Infinity;

      categories.forEach((category, index) => {
        Taro.createSelectorQuery()
          .select(`#category-${category.id}`)
          .boundingClientRect((rect: any) => {
            if (rect) {
              // 计算元素顶部到视窗顶部的距离
              const distance = Math.abs(rect.top);

              // 如果元素在视窗中且距离最小，则选中它
              if (
                rect.top <= 150 &&
                rect.bottom > 0 &&
                distance < minDistance
              ) {
                minDistance = distance;
                currentCategory = category.id;
              }
            }

            // 在最后一个元素处理完后更新状态
            if (
              index === categories.length - 1 &&
              currentCategory &&
              currentCategory !== activeCategory
            ) {
              setTimeout(() => {
                setActiveCategory(currentCategory);
              }, 0);
            }
          })
          .exec();
      });
    }, 150);

    throttledHandler();
  }, [data, activeCategory]);

  // 选择分类并滚动到对应位置
  const selectCategory = (categoryId: number) => {
    if (activeCategory === categoryId) return;

    setActiveCategory(categoryId);
    isScrollingToCategory.current = true;

    // 滚动到对应分类，使用scrollIntoView
    if (scrollViewRef.current) {
      setTimeout(() => {
        try {
          scrollViewRef.current.scrollIntoView(`category-${categoryId}`, {
            duration: 300,
            offsetTop: 0,
          });
        } catch (error) {
          console.log("滚动失败:", error);
        }

        // 滚动完成后重置标志
        setTimeout(() => {
          isScrollingToCategory.current = false;
        }, 400);
      }, 50);
    }
  };

  // 选择/取消选择标签
  const toggleTag = (tag: Tag) => {
    if (tag.type !== 1) return; // 只能选择标签，不能选择目录

    const isSelected = selectedTags.some((t) => t.id === tag.id);

    if (!isMultiSelect) {
      // 单选模式：直接选择并确认
      if (!isSelected) {
        setSelectedTags([tag]);
        onConfirm([tag]);
      }
      return;
    }

    // 多选模式
    if (isSelected) {
      // 取消选择
      setSelectedTags((prev) => prev.filter((t) => t.id !== tag.id));
    } else {
      // 选择标签
      if (selectedTags.length >= maxSelect) {
        toast("info", {
          content: `最多只能选择${maxSelect}个标签`,
          duration: 2000,
        });
        return;
      }
      setSelectedTags((prev) => [...prev, tag]);
    }
  };

  const handleAddTag = () => {
      Taro.navigateTo({ url: "/pageDynamic/tagAndCategoryManage/index" });
  };

  // 确认选择
  const handleConfirm = () => {
    onConfirm(selectedTags);
  };

  // 切换选择模式
  const toggleSelectMode = () => {
    setIsMultiSelect(!isMultiSelect);
    if (!isMultiSelect) {
      // 切换到多选模式时，清空当前选择
      setSelectedTags([]);
    }
  };

  // 取消多选模式
  const cancelMultiSelect = () => {
    setIsMultiSelect(false);
    setSelectedTags([]);
  };

  // 渲染标签项
  const renderTag = (tag: Tag) => {
    const isSelected = selectedTags.some((t) => t.id === tag.id);

    return (
      <View
        key={tag.id}
        className={`tag-item ${isSelected ? "selected" : ""}`}
        onClick={() => toggleTag(tag)}
      >
        <View className='tag-image-container'>
          {tag.coverImage ? (
            <Image
              src={tag.coverImage}
              className='tag-image'
              mode='aspectFill'
            />
          ) : (
            <View className='tag-placeholder'>
              <IconPicture style={{ fontSize: "24px", color: "#C9CDD4" }} />
            </View>
          )}
          {isSelected && (
            <View className='selected-mark'>
              <IconCheck style={{ fontSize: "12px", color: "#fff" }} />
            </View>
          )}
        </View>
        <Text className='tag-name'>{tag.name}</Text>
      </View>
    );
  };

  // 渲染分类区块
  const renderCategorySection = (category: Tag) => {
    if (category.type !== 2) return null;

    return (
      <View
        key={category.id}
        id={`category-${category.id}`}
        className='category-section'
        ref={(ref) => {
          if (ref) {
            categoryRefs.current[category.id] = ref;
          }
        }}
      >
        {/* 分类标题 */}
        <View className='category-header'>
          <View className='header-info'>
            {/* <View className='avatar-placeholder' /> */}
            <View className='info-text'>
              <Text className='list-content'>{category.name}</Text>
            </View>
          </View>
        </View>

        {/* 标签网格 */}
        <View className='tags-grid'>
          {category.children.map((tag) => renderTag(tag))}
        </View>
      </View>
    );
  };

  return (
    <View className='select-classify'>
      {/* 背景遮罩 - 点击关闭 */}
      <View className='mask-overlay' onClick={onCancel} />

      {/* 顶部空白区域 - 点击关闭 */}
      <View className='top-blank-area' onClick={onCancel} />

      {/* 顶部导航栏 */}
      <View className='top-header' onClick={(e) => e.stopPropagation()}>
        <View className='cancel-button' >
        </View>
        {isMultiSelect ? (
          <View className='multi-select-button' onClick={cancelMultiSelect}>
            <Text className='multi-select-text'>取消</Text>
          </View>
        ) : (
          <View className='multi-select-button' onClick={toggleSelectMode}>
            <Text className='multi-select-text'>多选</Text>
          </View>
        )}
      </View>

      {/* 主体内容 */}
      <View className='main-content' onClick={(e) => e.stopPropagation()}>
        {/* 左侧目录导航 */}
        <View className='left-sidebar'>
          <ScrollView className='category-list' scrollY>
            {data.map((category) => {
              if (category.type === 2) {
                return (
                  <View
                    key={category.id}
                    className={`category-item ${
                      activeCategory === category.id ? "active" : ""
                    }`}
                    onClick={() => selectCategory(category.id)}
                  >
                    <Text className='category-name'>{category.name}</Text>
                  </View>
                );
              }
              return null;
            })}
          </ScrollView>
        </View>

        {/* 右侧内容区域 */}
        <View className='right-content'>
          <ScrollView
            className='content-scroll'
            scrollY
            ref={scrollViewRef}
            onScroll={handleScroll}
            scrollWithAnimation
          >
            {data
              .filter((item) => item.type === 2)
              .map((category) => renderCategorySection(category))}
          </ScrollView>
        </View>
      </View>

      {/* 底部确定按钮 - 只在多选模式下显示 */}
      {isMultiSelect && (
        <View className='bottom-section' onClick={(e) => e.stopPropagation()}>
          <View className='confirm-button' onClick={handleConfirm}>
            <Text className='confirm-text'>
              确定{selectedTags.length > 0 ? `(${selectedTags.length})` : ""}
            </Text>
          </View>
        </View>
      )}
    </View>
  );
}
