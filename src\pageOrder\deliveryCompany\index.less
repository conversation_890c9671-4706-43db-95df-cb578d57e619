@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageOrder/deliveryCompany/index"] {
  .delivery-company-page {
    min-height: 100vh;
    background-color: #f8f9fa;
    .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });

    .company-list {
      background-color: #ffffff;
      .use-dark-mode-query({
      background-color: @dark-background-color !important;
    });

      .company-item {
        padding: 10px 20px;
        cursor: pointer;

        border-bottom: 1px solid var(--line-color);
        .use-dark-mode-query({
        border-bottom: 1px solid @dark-line-color;
      });

        &:last-child {
          border-bottom: none;
        }

        .company-name {
          font-size: 16px;
          color: #333;
          .use-dark-mode-query({
          color: @dark-font-color !important;
        });
        }
      }

      .empty-message {
        padding: 40px 20px;
        text-align: center;
        color: #999;
        font-size: 14px;
        .use-dark-mode-query({
        color: @dark-font-color !important;
      });
      }
    }
  }
}
