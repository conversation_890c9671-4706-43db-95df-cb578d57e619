import { View, Text } from '@tarojs/components'
import { useEffect, useState } from 'react'
import Taro, { useDidShow } from '@tarojs/taro'
import { getMerchantEntryApplicationPage, getMerchantEntryApplyStatusPage, getMerchantEntryByOutRequestNo } from '@/utils/api/common/common_wechat'
import { Toast, Steps } from '@arco-design/mobile-react'
import YkNavBar from "@/components/ykNavBar/index"
import { MerchantApplicationStatus } from './types'
import './index.less'

interface ApiResponse<T> {
  code: number;
  data: {
    list: T[];
    total: number;
  };
  msg: string;
}

// 微信支付商户进件申请状态响应接口
interface MerchantEntryApplyStatus {
    id: number;
    userId: number;
    applymentId: number;
    outRequestNo: string;
    applymentState: string;  // 微信支付申请状态
    applymentStateDesc: string;
    signUrl: string;
    subMchid: string;
    accountName: string;
    accountNo: string;
    payAmount: number;
    destinationAccountNumber: string;
    destinationAccountName: string;
    destinationAccountBank: string;
    city: string;
    remark: string;
    deadline: string;
    paramName: string;
    rejectReason: string;
    legalValidationUrl: string;
    signState: string;
    createTime: string;
}

// 微信支付申请状态枚举（根据微信支付文档）
enum WechatApplymentState {
  CHECKING = 'CHECKING',                    // 资料校验中
  ACCOUNT_NEED_VERIFY = 'ACCOUNT_NEED_VERIFY', // 待账户验证
  AUDITING = 'AUDITING',                    // 审核中
  REJECTED = 'REJECTED',                    // 已驳回
  NEED_SIGN = 'NEED_SIGN',                  // 待签约
  FINISH = 'FINISH',                        // 完成
  FROZEN = 'FROZEN',                        // 已冻结
  CANCELED = 'CANCELED'                     // 已作废
}



interface PaymentInfo {
  id: number;
  status: MerchantApplicationStatus;
  rejectReason?: string;
  rejectReasons?: string[];
  reviewTips?: string[];
  verifyUrl?: string;
  signUrl?: string;
  merchantName?: string;
  subMchid?: string; // 二级商户号
}

interface UserInfo {
  id: number;
  accessToken?: string;
  token?: string;
}

/**
 * 将微信支付申请状态映射到系统内部状态
 * @param wechatState 微信支付申请状态
 * @param _rejectReason 驳回原因（暂未使用，预留用于后续业务逻辑扩展）
 * @returns 系统内部状态
 */
const mapWechatStateToInternalStatus = (
  wechatState: string,
  _rejectReason?: string
): MerchantApplicationStatus => {
  switch (wechatState) {
    case WechatApplymentState.CHECKING:
    case WechatApplymentState.AUDITING:
      return MerchantApplicationStatus.PENDING_REVIEW;

    case WechatApplymentState.REJECTED:
      // 根据是否有驳回原因判断是首次提交驳回还是修改后驳回
      // 这里可以根据业务逻辑进一步细化判断
      return MerchantApplicationStatus.REVIEW_SUBMIT_REJECTED;

    case WechatApplymentState.ACCOUNT_NEED_VERIFY:
      return MerchantApplicationStatus.PENDING_VERIFY;

    case WechatApplymentState.NEED_SIGN:
      return MerchantApplicationStatus.PENDING_SIGN;

    case WechatApplymentState.FINISH:
      return MerchantApplicationStatus.COMPLETED;

    case WechatApplymentState.FROZEN:
    case WechatApplymentState.CANCELED:
      // 冻结和作废状态可以根据业务需要处理，这里暂时映射为驳回
      return MerchantApplicationStatus.REVIEW_SUBMIT_REJECTED;

    default:
      // 未知状态或空状态，默认为待提交
      return MerchantApplicationStatus.PENDING_SUBMIT;
  }
};

// 用户信息解析工具函数
const parseUserInfo = (userInfoStorage: any): UserInfo | null => {
  if (!userInfoStorage) {
    return null;
  }

  // 方式1: 直接是用户对象
  if (userInfoStorage.id) {
    return userInfoStorage;
  }
  // 方式2: 包装在data字段中
  else if (userInfoStorage.data && userInfoStorage.data.id) {
    return userInfoStorage.data;
  }
  // 方式3: 包装在userInfo字段中
  else if (userInfoStorage.userInfo && userInfoStorage.userInfo.id) {
    return userInfoStorage.userInfo;
  }

  return null;
};

const OnlinePayment = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [currentApplication, setCurrentApplication] = useState<PaymentInfo | null>(null);
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  useDidShow(() => {
    // 检查是否有URL参数用于测试
    const router = Taro.getCurrentInstance().router;
    const testStatus = router?.params?.status;

    if (testStatus !== undefined) {
      // 测试模式：使用URL参数模拟状态
      simulateTestStatus(parseInt(testStatus));
    } else {
      // 正常模式：获取真实数据
      getOnlinePaymentData();
    }
  })

  // 监听申请状态变化，当状态为开通成功时自动跳转到信息页面
  useEffect(() => {
    if (currentApplication?.status === MerchantApplicationStatus.COMPLETED) {
      console.log('[在线收款] 检测到开通成功状态，立即跳转到信息页面');
            // 构建跳转URL，如果有subMchid则添加参数
      let url = '/pageOnlinePayment/onlinePayment/information/index';
      if (currentApplication.subMchid) {
        url += `?subMchid=${encodeURIComponent(currentApplication.subMchid)}`;
        console.log('[在线收款] 携带二级商户号参数:', currentApplication.subMchid);
      }
      Taro.redirectTo({
        url: url
      });
    }
  }, [currentApplication?.status])

  // 监听商户申请刷新事件
  useEffect(() => {
    const refreshHandler = () => {
      console.log('[在线收款] 收到商户申请刷新事件，开始刷新数据');
      getOnlinePaymentData();
    };
    
    // 监听刷新事件
    Taro.eventCenter.on('refreshMerchantApplication', refreshHandler);
    
    // 组件卸载时移除监听
    return () => {
      Taro.eventCenter.off('refreshMerchantApplication', refreshHandler);
    };
  }, [])

  const getOnlinePaymentData = () => {
    console.log("[在线收款] 开始获取商户申请状态信息");

    // 获取用户信息 - 兼容多种存储格式
    const userInfoStorage = Taro.getStorageSync('userInfo');
    console.log('[在线收款] 原始用户信息存储:', userInfoStorage);

    const userInfo = parseUserInfo(userInfoStorage);

    if (!userInfo || !userInfo.id) {
      console.error('[在线收款] 获取用户信息失败，用户未登录', {
        userInfoStorage,
        parsedUserInfo: userInfo
      });
      Toast.error('账号未登录，请先登录');

      // 跳转到登录页面
      setTimeout(() => {
        Taro.navigateTo({
          url: '/pages/login/index'
        });
      }, 1500);
      return;
    }

    console.log('[在线收款] 解析后的用户信息:', userInfo);

    // 优先查询微信支付商户进件申请状态
    getWechatEntryFormStatus();
  };

  // 查询微信支付商户进件申请状态
  const getWechatEntryFormStatus = async () => {
    try {
      console.log("[在线收款] 开始查询微信支付商户进件申请状态");
      const userInfo = Taro.getStorageSync('userInfo');
      if (!userInfo?.id) {
        console.log('获取用户信息失败');
        return;
      }

      const response = await getMerchantEntryApplyStatusPage({pageNo:1, pageSize:10, userId: userInfo.id}) as ApiResponse<MerchantEntryApplyStatus>;
      console.log("[在线收款] 微信支付商户进件申请状态返回:", response);

      if (response && response.code === 0 && response.data && response.data.list && response.data.list.length > 0) {
        const wechatData = response.data.list[0];
        console.log("[在线收款] 获取到第一项数据，outRequestNo:", wechatData.outRequestNo);

        // 如果有 outRequestNo，请求最新状态
        if (wechatData.outRequestNo) {
          console.log("[在线收款] 通过 outRequestNo 查询最新状态:", wechatData.outRequestNo);
          try {
            const latestStatusResponse = await getMerchantEntryByOutRequestNo({ outRequestNo: wechatData.outRequestNo });
            console.log("[在线收款] 最新状态查询返回:", latestStatusResponse);
            
            // 如果获取到最新状态，使用最新数据
            if (latestStatusResponse && latestStatusResponse.code === 0 && latestStatusResponse.data) {
              const latestData = latestStatusResponse.data;
              console.log("[在线收款] 使用最新状态数据");
              
              // 将微信状态映射到系统内部状态
              const internalStatus = mapWechatStateToInternalStatus(
                latestData.applymentState,
                latestData.rejectReason
              );

              console.log("[在线收款] 最新状态映射结果:", {
                wechatState: latestData.applymentState,
                internalStatus,
                rejectReason: latestData.rejectReason
              });

              // 构建PaymentInfo对象
              const paymentInfo: PaymentInfo = {
                id: latestData.id || wechatData.id,
                status: internalStatus,
                rejectReason: latestData.rejectReason || undefined,
                rejectReasons: latestData.rejectReason ? [latestData.rejectReason] : undefined,
                verifyUrl: latestData.legalValidationUrl || undefined,
                signUrl: latestData.signUrl || undefined,
                merchantName: latestData.accountName || wechatData.accountName || undefined,
                subMchid: latestData.subMchid || wechatData.subMchid || undefined
              };

              setCurrentApplication(paymentInfo);
              setLoading(false);
              return;
            }
          } catch (latestStatusError) {
            console.error('[在线收款] 查询最新状态出错:', latestStatusError);
            // 继续使用原有数据
          }
        }

        // 使用原有数据（如果没有最新状态或查询失败）
        console.log("[在线收款] 使用原有数据");
        const internalStatus = mapWechatStateToInternalStatus(
          wechatData.applymentState,
          wechatData.rejectReason
        );

        console.log("[在线收款] 微信状态映射结果:", {
          wechatState: wechatData.applymentState,
          internalStatus,
          rejectReason: wechatData.rejectReason
        });

        // 构建PaymentInfo对象
        const paymentInfo: PaymentInfo = {
          id: wechatData.id,
          status: internalStatus,
          rejectReason: wechatData.rejectReason || undefined,
          rejectReasons: wechatData.rejectReason ? [wechatData.rejectReason] : undefined,
          verifyUrl: wechatData.legalValidationUrl || undefined,
          signUrl: wechatData.signUrl || undefined,
          merchantName: wechatData.accountName || undefined,
          subMchid: wechatData.subMchid || undefined
        };

        setCurrentApplication(paymentInfo);
        setLoading(false);
        return;
      } else {
        console.log("[在线收款] 微信支付商户进件申请状态查询失败或无数据，尝试查询本地商户申请数据");
        // 如果微信接口查询失败，回退到商户申请数据查询
        getLocalOnlinePaymentData();
      }
    } catch (error) {
      console.error('[在线收款] 查询微信支付商户进件申请状态出错:', error);
      // 出错时回退到商户申请数据查询
      getLocalOnlinePaymentData();
    }
  };

  // 获取商户申请信息列表（替代原有的online-payment-information逻辑）
  const getLocalOnlinePaymentData = () => {
    console.log("[在线收款] 开始获取商户申请信息列表");

    const userInfoStorage = Taro.getStorageSync('userInfo');
    const userInfo = parseUserInfo(userInfoStorage);

    if (!userInfo || !userInfo.id) {
      console.error('[在线收款] 获取用户信息失败');
      return;
    }

    // 获取商户申请信息列表
    getMerchantEntryApplicationPage({
      pageNo: 1,
      pageSize: 10,
      userId: userInfo.id
    })
      .then((response: any) => {
        console.log("[在线收款] 获取商户申请信息列表返回:", response);
        const paymentData = response as ApiResponse<PaymentInfo>;

        if (paymentData && paymentData.code === 0 && paymentData.data && paymentData.data.list) {
          console.log("[在线收款] 商户申请信息列表数据:", paymentData.data.list);

          if (paymentData.data.list.length === 0) {
            // 没有商户申请记录，设置为待提交状态
            console.log("[在线收款] 无商户申请信息，显示待提交状态");
            setCurrentApplication({
              id: 0,
              status: MerchantApplicationStatus.PENDING_SUBMIT
            });
          } else {
            // 有记录，设置当前申请状态
            const application = paymentData.data.list[0];
            setCurrentApplication(application);
          }
        } else {
          console.error("[在线收款] 获取商户申请信息列表失败:", paymentData.msg || '未知错误');
          // 如果本地数据也获取失败，设置为待提交状态
          setCurrentApplication({
            id: 0,
            status: MerchantApplicationStatus.PENDING_SUBMIT
          });
        }
      })
      .catch((error: any) => {
        console.error('[在线收款] 获取商户申请信息出错:', error);
        // 出错时设置为待提交状态
        setCurrentApplication({
          id: 0,
          status: MerchantApplicationStatus.PENDING_SUBMIT
        });
      })
      .finally(() => {
        console.log("[在线收款] 本地数据加载完成，显示页面");
        setLoading(false);
      });
  };

  // 模拟测试状态（用于开发和测试）
  const simulateTestStatus = (status: number) => {
    console.log(`[测试模式] 模拟状态: ${status}`);

    let mockApplication: PaymentInfo;

    switch (status) {
      case MerchantApplicationStatus.PENDING_SUBMIT:
        mockApplication = {
          id: 999,
          status: MerchantApplicationStatus.PENDING_SUBMIT
        };
        break;

      case MerchantApplicationStatus.PENDING_REVIEW:
        // 已提交-待审核状态 - 显示"资料审核中"
        mockApplication = {
          id: 999,
          status: MerchantApplicationStatus.PENDING_REVIEW,
          merchantName: "测试商户名称"
        };
        break;

      case MerchantApplicationStatus.REVIEW_SUBMIT_REJECTED:
        // 提交信息-审核不通过
        mockApplication = {
          id: 999,
          status: MerchantApplicationStatus.REVIEW_SUBMIT_REJECTED,
          rejectReasons: [
            "商户名称填写有误，请填写与上传的营业执照匹配的信息",
            "工商暂未查询到该营业执照注册号，请检查营业执照注册号是否填写正确",
            "身份证信息校验失败，请检查身份证姓名和号码是否填写正确",
            "请填写与影印件一致的身份证号码和姓名。如填写正确仍被驳回，请上传更清晰的身份证人像面照片，并确保关键信息不被遮挡",
            "法人姓名填写有误，请填写与上传的营业执照匹配的信息",
            "您好，您输入的卡号长度有误，请重新核对",
            "手机号码取值有误，请检查后再试",
            "法人姓名填写有误，请填写与上传的营业执照匹配的信息"
          ],
          merchantName: "测试商户名称"
        };
        break;

      case MerchantApplicationStatus.REVIEW_MODIFY_REJECTED:
        // 修改信息-审核不通过
        mockApplication = {
          id: 999,
          status: MerchantApplicationStatus.REVIEW_MODIFY_REJECTED,
          rejectReasons: [
            "修改的营业执照信息与原始信息不符",
            "新提交的身份证照片不清晰",
            "银行卡信息变更需要提供相关证明",
            "联系人信息变更需要法人确认",
            "经营地址变更需要提供新的证明材料"
          ],
          merchantName: "测试商户名称"
        };
        break;

      case MerchantApplicationStatus.PENDING_VERIFY:
        mockApplication = {
          id: 999,
          status: MerchantApplicationStatus.PENDING_VERIFY,
          merchantName: "测试商户名称",
          verifyUrl: "https://example.com/verify?token=test123"
        };
        break;

      case MerchantApplicationStatus.PENDING_SIGN:
        mockApplication = {
          id: 999,
          status: MerchantApplicationStatus.PENDING_SIGN,
          merchantName: "测试商户名称",
          signUrl: "https://example.com/sign?token=test123"
        };
        break;

      case MerchantApplicationStatus.COMPLETED:
        mockApplication = {
          id: 999,
          status: MerchantApplicationStatus.COMPLETED,
          merchantName: "测试商户名称",
          subMchid: "1234567890" // 测试用的二级商户号
        };
        break;

      default:
        mockApplication = {
          id: 999,
          status: MerchantApplicationStatus.PENDING_SUBMIT
        };
        break;
    }

    setCurrentApplication(mockApplication);
    setLoading(false);

    // 显示测试提示
    Toast.info(`测试模式：${getStatusName(status)}`);
  };

  // 获取状态名称（用于测试提示）
  const getStatusName = (status: number): string => {
    switch (status) {
      case MerchantApplicationStatus.PENDING_SUBMIT:
        return "待提交";
      case MerchantApplicationStatus.PENDING_REVIEW:
        return "待审核 (微信: CHECKING/AUDITING)";
      case MerchantApplicationStatus.REVIEW_SUBMIT_REJECTED:
        return "提交信息-审核不通过 (微信: REJECTED)";
      case MerchantApplicationStatus.REVIEW_MODIFY_REJECTED:
        return "修改信息-审核不通过 (微信: REJECTED)";
      case MerchantApplicationStatus.PENDING_VERIFY:
        return "待验证 (微信: ACCOUNT_NEED_VERIFY)";
      case MerchantApplicationStatus.PENDING_SIGN:
        return "待签约 (微信: NEED_SIGN)";
      case MerchantApplicationStatus.COMPLETED:
        return "开通成功 (微信: FINISH)";
      default:
        return "未知状态";
    }
  };

  // 获取步骤信息
  const getStepInfo = (status: MerchantApplicationStatus) => {
    switch (status) {
      case MerchantApplicationStatus.PENDING_SUBMIT:
        return {
          current: 0,
          actionButton: {
            text: '去提交',
            type: 'primary' as const,
            color: 'green',
            handler: handleSubmit
          }
        };
      case MerchantApplicationStatus.PENDING_REVIEW:
        return {
          current: 1,
          actionButton: null
        };
      case MerchantApplicationStatus.REVIEW_SUBMIT_REJECTED:
        return {
          current: 1,
          actionButton: {
            text: '去修改',
            type: 'primary' as const,
            color: 'green',
            handler: handleModify
          }
        };
      case MerchantApplicationStatus.REVIEW_MODIFY_REJECTED:
        return {
          current: 1,
          actionButton: {
            text: '去修改',
            type: 'primary' as const,
            color: 'green',
            handler: handleModify
          }
        };
      case MerchantApplicationStatus.PENDING_VERIFY:
        return {
          current: 2,
          actionButton: {
            text: '去验证',
            type: 'primary' as const,
            color: 'green',
            handler: handleVerify
          }
        };
      case MerchantApplicationStatus.PENDING_SIGN:
        return {
          current: 3,
          actionButton: {
            text: '去签约',
            type: 'primary' as const,
            color: 'green',
            handler: handleSign
          }
        };
      case MerchantApplicationStatus.COMPLETED:
        return {
          current: 4,
          actionButton: null
        };
      default:
        return {
          current: 0,
          actionButton: null
        };
    }
  };

  // 1. 去提交 - 跳转到商户申请页面
  const handleSubmit = () => {
    Taro.navigateTo({
      url: '/pageOnlinePayment/onlinePayment/merchantApplication/index'
    });
  };

  // 2. 去修改 - 跳转到商户申请页面（编辑模式）
  const handleModify = () => {
    Taro.navigateTo({
      url: '/pageOnlinePayment/onlinePayment/merchantApplication/index?mode=edit'
    });
  };

  // 3. 去验证 - 跳转到账户验证页面
  const handleVerify = () => {
    if (currentApplication?.verifyUrl) {
      const encodedUrl = encodeURIComponent(currentApplication.verifyUrl);
      Taro.navigateTo({
        url: `/pageOnlinePayment/onlinePayment/verifyAccount?url=${encodedUrl}`
      });
    } else {
      // 使用模拟URL
      const mockVerifyUrl = 'https://example.com/verify?token=' + Date.now();
      const encodedUrl = encodeURIComponent(mockVerifyUrl);
      Taro.navigateTo({
        url: `/pageOnlinePayment/onlinePayment/verifyAccount?url=${encodedUrl}`
      });
    }
  };

  // 4. 去签约 - 跳转到协议签署页面
  const handleSign = () => {
    if (currentApplication?.signUrl) {
      const encodedUrl = encodeURIComponent(currentApplication.signUrl);
      Taro.navigateTo({
        url: `/pageOnlinePayment/onlinePayment/signAgreement?url=${encodedUrl}`
      });
    } else {
      // 使用模拟URL
      const mockSignUrl = 'https://example.com/sign?token=' + Date.now();
      const encodedUrl = encodeURIComponent(mockSignUrl);
      Taro.navigateTo({
        url: `/pageOnlinePayment/onlinePayment/signAgreement?url=${encodedUrl}`
      });
    }
  };

  // 渲染驳回原因
  const renderRejectReasons = () => {
    if (!currentApplication ||
        (currentApplication.status !== MerchantApplicationStatus.REVIEW_SUBMIT_REJECTED &&
         currentApplication.status !== MerchantApplicationStatus.REVIEW_MODIFY_REJECTED)) {
      return null;
    }

    const reasons = currentApplication.rejectReasons ||
      (currentApplication.rejectReason ? [currentApplication.rejectReason] : []);

    if (reasons.length === 0) {
      return null;
    }

    // 根据不同的驳回类型显示不同的标题
    const title = currentApplication.status === MerchantApplicationStatus.REVIEW_SUBMIT_REJECTED
      ? "提交信息审核不通过：以下信息有误，请修改后再次提交"
      : "修改信息审核不通过：以下信息有误，请修改后再次提交";

    return (
      <View className="reject-reasons">
        <Text className="reject-title">{title}</Text>
        {reasons.map((reason, index) => (
          <Text key={index} className="reject-item">
            {index + 1}. {reason}
          </Text>
        ))}
      </View>
    );
  };





  // 渲染步骤描述
  const renderStepDescription = (stepIndex: number) => {
    if (!currentApplication) return null;

    const { status } = currentApplication;
    const stepInfo = getStepInfo(status);

    switch (stepIndex) {
      case 0: // 资料提交
        if (status === MerchantApplicationStatus.PENDING_SUBMIT) {
          return (
            <View>
              <Text className="step-desc">请提交资料，用于微信支付平台审核</Text>
              {stepInfo.actionButton && (
                <Text
                  className={`step-action ${stepInfo.actionButton.color}`}
                  onClick={stepInfo.actionButton.handler}
                >
                  {stepInfo.actionButton.text}
                </Text>
              )}
            </View>
          );
        }
        return null;

      case 1: // 资料审核
        if (status === MerchantApplicationStatus.PENDING_REVIEW) {
          return (
            <View>
              <Text className="step-desc">资料审核中：微信支付平台正在审核，请耐候</Text>
              {/* <Text className="step-desc-detail">状态说明：资料校验中(CHECKING)或审核中(AUDITING)</Text> */}
            </View>
          );
        } else if (status === MerchantApplicationStatus.REVIEW_SUBMIT_REJECTED ||
                   status === MerchantApplicationStatus.REVIEW_MODIFY_REJECTED) {
          return (
            <View>
              {/* <Text className="step-desc-detail">状态说明：已驳回(REJECTED)</Text> */}
              {renderRejectReasons()}
              {stepInfo.actionButton && (
                <Text
                  className={`step-action ${stepInfo.actionButton.color}`}
                  onClick={stepInfo.actionButton.handler}
                >
                  {stepInfo.actionButton.text}
                </Text>
              )}
            </View>
          );
        }
        return null;

      case 2: // 账户验证
        if (status === MerchantApplicationStatus.PENDING_VERIFY) {
          return (
            <View>
              <Text className="step-desc">等待验证：法人扫码验证</Text>
              {/* <Text className="step-desc-detail">状态说明：待账户验证(ACCOUNT_NEED_VERIFY)</Text> */}
              {stepInfo.actionButton && (
                <Text
                  className={`step-action ${stepInfo.actionButton.color}`}
                  onClick={stepInfo.actionButton.handler}
                >
                  {stepInfo.actionButton.text}
                </Text>
              )}
            </View>
          );
        }
        return null;

      case 3: // 签约
        if (status === MerchantApplicationStatus.PENDING_SIGN) {
          return (
            <View>
              <Text className="step-desc">等待签约：完成签约即可开通在线收款</Text>
              {/* <Text className="step-desc-detail">状态说明：待签约(NEED_SIGN)</Text> */}
              {stepInfo.actionButton && (
                <Text
                  className={`step-action ${stepInfo.actionButton.color}`}
                  onClick={stepInfo.actionButton.handler}
                >
                  {stepInfo.actionButton.text}
                </Text>
              )}
            </View>
          );
        }
        return null;

      case 4: // 开通成功
        // 开通成功状态不显示描述，直接跳转
        return null;

      default:
        return null;
    }
  };

  // 渲染测试面板（仅在测试模式下显示）
  const renderTestPanel = () => {
    // 只在明确指定showTest=1时显示测试面板
    const router = Taro.getCurrentInstance().router;
    const showTestPanel = router?.params?.showTest === '1';

    if (!showTestPanel) return null;

    const testButtons = [
      { status: 0, label: '待提交', color: '#165DFF' },
      { status: 1, label: '待审核', color: '#FF7D00' },
      { status: 2, label: '提交信息-审核不通过', color: '#F53F3F' },
      { status: 3, label: '修改信息-审核不通过', color: '#F53F3F' },
      { status: 4, label: '待验证', color: '#165DFF' },
      { status: 5, label: '待签约', color: '#165DFF' },
      { status: 6, label: '开通成功', color: '#00B42A' }
    ];

    return (
      <View className="test-panel">
        <Text className="test-panel-title">🧪 调试面板 - 在线收款开通流程</Text>
        <View className="test-buttons">
          {testButtons.map((btn, index) => (
            <Text
              key={index}
              className="test-button"
              style={{ backgroundColor: btn.color }}
              onClick={() => simulateTestStatus(btn.status)}
            >
              {btn.label}
            </Text>
          ))}
        </View>
        <Text className="test-panel-tip">
          💡 URL参数测试: ?status=0-6&showTest=1
        </Text>
        <Text className="test-panel-tip">
          📋 状态说明: 0=待提交, 1=待审核, 2=提交驳回, 3=修改驳回, 4=待验证, 5=待签约, 6=开通成功
        </Text>
        <View className="test-buttons">
          <Text
            className="test-button"
            style={{ backgroundColor: '#722ED1' }}
            onClick={() => {
              console.log('[测试] 手动调用微信接口');
              getWechatEntryFormStatus();
            }}
          >
            🔄 测试微信接口
          </Text>
          <Text
            className="test-button"
            style={{ backgroundColor: '#13C2C2' }}
            onClick={() => {
              console.log('[测试] 状态映射测试');
              const testStates = ['CHECKING', 'AUDITING', 'REJECTED', 'ACCOUNT_NEED_VERIFY', 'NEED_SIGN', 'FINISH', 'FROZEN', 'CANCELED', ''];
              testStates.forEach(state => {
                const mapped = mapWechatStateToInternalStatus(state);
                console.log(`微信状态: ${state || '空'} -> 系统状态: ${mapped} (${getStatusName(mapped)})`);
              });
            }}
          >
            🧪 测试状态映射
          </Text>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View className="online-payment">
        {platform !== "WX" &&<YkNavBar title="在线收款开通流程" />}
        <View className="loading-container">
          <Text className="loading-text">加载中...</Text>
        </View>
      </View>
    );
  }

  if (!currentApplication) {
    return (
      <View className="online-payment">
        {platform !== "WX" &&<YkNavBar title="在线收款开通流程" />}
        <View className="error-container">
          <Text className="error-text">获取数据失败</Text>
        </View>
      </View>
    );
  }

  const stepInfo = getStepInfo(currentApplication.status);

  return (
    <View className="online-payment">
     {platform !== "WX" && <YkNavBar title="在线收款开通流程" />}
      {renderTestPanel()}
      <View className="content">
        <Steps current={stepInfo.current} direction="vertical" className="custom-steps">
          <Steps.Step
            title="资料提交"
            description={renderStepDescription(0)}
          />
          <Steps.Step
            title="资料审核"
            description={renderStepDescription(1)}
          />
          <Steps.Step
            title="账户验证"
            description={renderStepDescription(2)}
          />
          <Steps.Step
            title="签约"
            description={renderStepDescription(3)}
          />
          <Steps.Step
            title="开通成功"
            description={renderStepDescription(4)}
          />
        </Steps>
      </View>
    </View>
  );
}

export default OnlinePayment
