import { View, Text } from "@tarojs/components";
import { useLoad, useReachBottom, useDidShow, useDidHide } from "@tarojs/taro";
import "./index.less";
import { Toast, TabBar, Grid, Image, Dialog } from "@arco-design/mobile-react";
import Taro from "@tarojs/taro";
import wx from "weixin-webview-jssdk";
import { getOrderNum } from "@/utils/api/common/common_user";
import { toast } from "@/utils/yk-common";
import { useEffect, useRef, useState } from "react";
import {
  IconApps,
  IconUser,
  IconInteraction,
  IconSettings,
  IconCommand,
  IconNotification,
  IconCamera,
  IconFile,
  IconQrcode,
  IconArrowRight,
  IconRight,
} from "@arco-iconbox/react-yk-arco";

// 组件
import YkNavBar from "@/components/ykNavBar/index";
import YkSwitchTabBar from "@/components/ykSwitchTabBar/index";
import React from "react";
export default function Control() {
  const [platform, setPlatform] = useState<string>("H5");
  const platformRef = useRef<string>("H5");

  // 添加顶部功能区数据
  const topGridData = [
    {
      // img: <IconCamera className="gridModeBoxContent-grid-icon" />,
      img: (
        <Image
          src={require("@/assets/images/common/fabudongtai.png")}
          className="gridModeBoxContent-grid-icon"
          bottomOverlap={null}
        />
      ),
      title: <Text className="gridModeBoxContent-grid-text">发布动态</Text>,
      onClick: () => {
        if (!checkLogin()) return;
          if (platform === "WX") {
            wx.miniProgram.navigateTo({
              url:
                "/pages/web?path=" +
                encodeURIComponent(`/pageDynamic/releaseDynamic/index`),
            });
          } else {
            Taro.navigateTo({
              url: `/pageDynamic/releaseDynamic/index`,
            });
          }

        // if (this.userInfo.is_vip == 1) {
        // navigateToVue('/pages/pagesA/releaseDynamic/index?type=1')
        // } else {
        //     // toast('warning', {
        //     //     content: '请先开通会员',
        //     //     duration: 2000
        //     // })
        //     navigateToVue('/pages/pagesA/vipCenter/index')
        // }
      },
      className: "gridModeBoxContent-grid-item",
    },
    {
      // img: <IconFile className="gridModeBoxContent-grid-icon" />,
      img: (
        <Image
          src={require("@/assets/images/common/sucaibanjia.png")}
          className="gridModeBoxContent-grid-icon"
          bottomOverlap={null}
        />
      ),
      title: <Text className="gridModeBoxContent-grid-text">素材搬家</Text>,
      onClick: () => {
        if (!checkLogin()) return;
          if (platform === "WX") {
            wx.miniProgram.navigateTo({
              url:
                "/pages/web?path=" +
                encodeURIComponent(
                  `/pageDynamic/moveMaterials/collectLink/index`
                ),
            });
          } else {
            Taro.navigateTo({
              // url: '/pageDynamic/moveMaterials/selectMaterials/index'
              url: "/pageDynamic/moveMaterials/collectLink/index",
            });
          }
      },
      className: "gridModeBoxContent-grid-item",
    },
    {
      // img: <IconQrcode className="gridModeBoxContent-grid-icon" />,
      img: (
        <Image
          src={require("@/assets/images/common/erweima.png")}
          className="gridModeBoxContent-grid-icon"
          bottomOverlap={null}
        />
      ),
      title: <Text className="gridModeBoxContent-grid-text">二维码</Text>,
      // onClick: () => {
      //     navigateToVue(`/pages/pagesA/qrcode/index?user_id=${userInfo?.user_id}`)
      // },
      onClick: () => {
        if (!checkLogin()) return;
        if (platform === "WX") {
          // wx.miniProgram.navigateTo({
          //   url:
          //     "/pages/web?path=" +
          //     encodeURIComponent(
          //       `/pageUserInfo/qrcode/index`
          //     ),
          // });

          let userInfoTemp = {
            userId: userInfo?.id,
            nickname: userInfo?.nickname,
            avatar: userInfo?.avatar,
          };
          wx.miniProgram.navigateTo({
            url:
              "/pages/qrcode/index?userInfo=" +
              encodeURIComponent(JSON.stringify(userInfoTemp)),
          });
        } else {
          Taro.navigateTo({ url: "/pageUserInfo/qrcode/index" });
        }
      },
      className: "gridModeBoxContent-grid-item",
    },
    {
      // img: <IconSettings className="gridModeBoxContent-grid-icon" />,
      img: (
        <Image
          src={require("@/assets/images/common/shezhi.png")}
          className="gridModeBoxContent-grid-icon"
          bottomOverlap={null}
        />
      ),
      title: <Text className="gridModeBoxContent-grid-text">设置</Text>,
      onClick: () => {
        if (!checkLogin()) return;
        if (platform === "WX") {
          wx.miniProgram.navigateTo({
            url:
              "/pages/web?path=" +
              encodeURIComponent(`/pageOrder/orderSetting/index`),
          });
        } else {
          Taro.navigateTo({ url: "/pageOrder/orderSetting/index" });
        }
      },
      className: "gridModeBoxContent-grid-item",
    },
  ];

  // 修改订单统计数据，改为从接口获取
  const [orderStats, setOrderStats] = React.useState([
    { number: 0, label: "待收款" },
    { number: 0, label: "待发货" },
    { number: 0, label: "已发货" },
    { number: 0, label: "已完成" },
    { number: 0, label: "退款" },
  ]);

  // useEffect(() => {
  
  // }, []);

  useDidShow(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
      platformRef.current = "Android";
    } else if (isIos) {
      setPlatform("IOS");
      platformRef.current = "IOS";
    } else if (isHM) {
      setPlatform("HM");
      platformRef.current = "HM";
    } else {
      setPlatform("H5");
      platformRef.current = "H5";
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
      platformRef.current = "WX";
    }
    if (platformRef.current == "IOS") {
      window.webkit.messageHandlers.configStatusBarStyle.postMessage("white");
    } else if (platformRef.current == "Android") {
      window.StatusBarLightMode.StatusBarLightMode();
      console.log("useDidShow window.StatusBarLightMode", window.StatusBarLightMode);
    } else if (platformRef.current == "HM") {
      window.harmony.StatusBarLightMode();
    }
  });

  useDidHide(() => {
    if (platformRef.current == "IOS") {
      window.webkit.messageHandlers.configStatusBarStyle.postMessage("black");
    } else if (platformRef.current == "Android") {
      window.StatusBarDarkMode.StatusBarDarkMode();
      console.log("useDidHide window.StatusBarDarkMode", window.StatusBarDarkMode);
    } else if (platformRef.current == "HM") {
      window.harmony.StatusBarDarkMode();
    }
  });

  // 移除本地 toast 函数定义，使用导入的 toast 函数

  // 获取订单数据
  const fetchOrderNumApi = async () => {
    console.log("[工作台] 获取用户信息:", Taro.getStorageSync("userInfo"));
    try {
      const res: any = await getOrderNum({
        userId: Taro.getStorageSync("userInfo")?.id,
        orderTypeFlag: 1,
      });
      console.log("[工作台] 获取订单数量返回:", res);

      // 确保res不为null且有code属性
      if (res && res.code === 0 && res.data) {
        setOrderStats([
          { number: res.data.pendingPaymentCount || 0, label: "待收款" },
          { number: res.data.pendingShipmentCount || 0, label: "待发货" },
          { number: res.data.shippedCount || 0, label: "已发货" },
          { number: res.data.complete_count || 0, label: "已完成" },
          { number: res.data.refundCount || 0, label: "退款" },
        ]);
      } else {
        console.error("[工作台] 获取订单数量失败:", res?.msg || "未知错误");
        // toast("error", {
        //   content: res?.msg || '获取订单数据失败',
        //   duration: 2000,
        // });
      }
    } catch (error) {
      console.error("[工作台] 获取订单数量出错:", error);
      toast("error", {
        content: "网络异常，请重试",
        duration: 2000,
      });
    }
  };
  // 用户资料
  const [userInfo, setUserInfo] = React.useState<any>(false);

  const checkLogin = () => {
    const userInfo = Taro.getStorageSync("userInfo");
    if (!userInfo || !userInfo.id) {
      if (platform === "WX") {
        wx.miniProgram.navigateTo({ url: "/pages/index/index" });
      } else {
        Taro.navigateTo({ url: "/pages/login/index" });
      }
      return false
    } else {
      return true
    }
  };

  useLoad(() => {
    console.log("[工作台] 获取用户信息:", Taro.getStorageSync("userInfo"));
    setUserInfo(Taro.getStorageSync("userInfo"));
    fetchOrderNumApi();
  });
  // 添加相册功能区数据
  const albumGridData = [
    {
      // img: <IconFile className="albumBox-grid-icon" />,
      img: (
        <Image
          src={require("@/assets/images/common/work_bqgl.png")}
          className="albumBox-grid-icon"
          bottomOverlap={null}
        />
      ),
      title: <Text className="albumBox-grid-text">标签管理</Text>,
      onClick: () => {
        if (!checkLogin()) return;
        if (platform === "WX") {
          wx.miniProgram.navigateTo({
            url:
              "/pages/web?path=" +
              encodeURIComponent(`/pageDynamic/tagAndCategoryManage/index`),
          });
        } else {
          Taro.navigateTo({ url: "/pageDynamic/tagAndCategoryManage/index" });
        }
      },
      className: "albumBox-grid-item",
    },
    {
      // img: <IconFile className="albumBox-grid-icon" />,
      img: (
        <Image
          src={require("@/assets/images/common/work_plbj.png")}
          className="albumBox-grid-icon"
          bottomOverlap={null}
        />
      ),
      title: <Text className="albumBox-grid-text">批量编辑</Text>,
      onClick: () => {
        if (!checkLogin()) return;
          if (platform === "WX") {
            wx.miniProgram.navigateTo({
              url:
                "/pages/web?path=" +
                encodeURIComponent(`/pageDynamic/album/batchEdit/index`),
            });
          } else {
            Taro.navigateTo({ url: `/pageDynamic/album/batchEdit/index` });
          }
      },
      className: "albumBox-grid-item",
    },
    {
      // img: <IconFile className="albumBox-grid-icon" />,
      img: (
        <Image
          src={require("@/assets/images/common/work_sxjtw.png")}
          className="albumBox-grid-icon"
          bottomOverlap={null}
        />
      ),
      title: <Text className="albumBox-grid-text">上下架图文</Text>,
      onClick: () => {
        if (!checkLogin()) return;
        if (platform === "WX") {
          wx.miniProgram.navigateTo({
            url:
              "/pages/web?path=" +
              encodeURIComponent(`/pageDynamic/album/upAndDownShelves/index`),
          });
        } else {
          //判断vip
          Taro.navigateTo({
            url: "/pageDynamic/album/upAndDownShelves/index",
          });
        }
        // navigateToVue('/pages/pagesA/albumManage/batchOnOffShelf')
      },
      className: "albumBox-grid-item",
    },

    // {
    //   // img: <IconFile className="albumBox-grid-icon" />,
    //   img: <Image src={require('@/assets/images/common/work_sxjtw.png')} className="albumBox-grid-icon" bottomOverlap={null}/>,
    //   title: <Text className="albumBox-grid-text">价格管理</Text>,
    //   onClick: () => {
    //       //判断vip
    //       Taro.navigateTo({
    //         url: '/pages/albumManage/index'
    //       })
    //       // navigateToVue('/pages/pagesA/albumManage/batchOnOffShelf')
    //   },
    //   className: "albumBox-grid-item",
    // },
    // ... 其他相册功能按钮
  ];

  return (
    <View className="indexBox">
      {platform !== "WX" && <YkNavBar switchTab title="工作台" />}

      {/* 顶部功能区 */}
      <View className="gridModeBoxContent">
        <Grid
          data={topGridData}
          columns={4}
          gutter={{ x: 0, y: 16 }}
          className="gridModeBoxContent-grid"
        />
      </View>

      {/* 订单统计区域 */}
      <View className="orderStatsBox">
        <View className="orderStatsTitle">
          <View className="orderStatsTitleItem">
            <Text>我卖的</Text>
            <View
              className="orderStatsTitleItemRight"
              onClick={() => {
                if (!checkLogin()) return;
                if (platform === "WX") {
                  wx.miniProgram.navigateTo({
                    url:
                      "/pages/web?path=" +
                      encodeURIComponent(
                        `/pageOrder/sellOrder/index?current=0`
                      ),
                  });
                } else {
                  Taro.navigateTo({
                    url: "/pageOrder/sellOrder/index?current=0",
                  });
                }
              }}
            >
              {/* <Text>交易明细 &gt;</Text> */}
            </View>
          </View>

          <View className="viewAll-container">
            <Text
              className="viewAll"
              onClick={() => {
                if (!checkLogin()) return;
                if (platform === "WX") {
                  wx.miniProgram.navigateTo({
                    url:
                      "/pages/web?path=" +
                      encodeURIComponent(
                        `/pageOrder/sellOrder/index?current=0`
                      ),
                  });
                } else {
                  Taro.navigateTo({
                    url: "/pageOrder/sellOrder/index?current=0",
                  });
                }
              }}
            >
              全部订单
            </Text>
            <IconRight className="viewAll-icon" />
          </View>
        </View>
        <View className="orderStatsContent">
          {orderStats.map((item, index) => (
            <View
              key={index}
              className="statsItem"
              onClick={() => {
                if (!checkLogin()) return;
                if (platform === "WX") {
                  wx.miniProgram.navigateTo({
                    url:
                      "/pages/web?path=" +
                      encodeURIComponent(
                        `/pageOrder/sellOrder/index?current=${index + 1}`
                      ),
                  });
                } else {
                  Taro.navigateTo({
                    url: `/pageOrder/sellOrder/index?current=${index + 1}`,
                  });
                }
                // navigateToVue(`/pages/pagesA/order/saleOrder?current=${index + 1}`)
              }}
            >
              <Text className="number">{item.number}</Text>
              <Text className="label">{item.label}</Text>
            </View>
          ))}
        </View>
      </View>

      {/* 相册功能区 */}
      <View className="albumBox">
        <View className="albumBoxTitle">相册</View>
        <View className="">
          <Grid data={albumGridData} columns={3} className="albumBox-grid" />
        </View>
      </View>

      <YkSwitchTabBar activeTab={2} />
    </View>
  );
}
