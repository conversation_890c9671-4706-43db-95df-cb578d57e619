import React, { useState, useEffect } from 'react';
import './inedx.less';
import { View } from '@tarojs/components';
import YkNavBar from '@/components/ykNavBar'
import {  getTreeCatalog } from "@/utils/api/common/common_user";
import { Tag } from "@arco-design/mobile-react";
import Taro,{ useLoad } from "@tarojs/taro";
import { useRef } from "react";



export default function SelectTag() {
  // 只允许单选
  const [selectedTags, setSelectedTags] = useState<Array<{id: string, name: string}>>([]);
  
  const [catalog, setCatalog]= useState<any[]>([]); 
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  const handleInput = (e: any) => {
    console.log(e.target.value);
  }

  //获取目录列表
  const getTagListData = async (type=1) => {
    const data = {
      userId: Taro.getStorageSync('userInfo').id,
    }
    const tags: any = await getTreeCatalog(data);
    if (tags && tags.code == 0) {
      if (tags.data.length > 0) {
        // 过滤掉 children 为空的项
        const filtered = tags.data.filter((item: any) => Array.isArray(item.children) && item.children.length > 0);
        setCatalog(filtered);

        // 在获取到标签列表后，进行回显匹配
        const savedTags = Taro.getStorageSync('selectedArticles');
        if (savedTags && savedTags.length > 0) {
          // 从服务器返回的标签列表中找到匹配的标签
          const allTags: any[] = [];
          filtered.forEach((group: any) => {
            if (group.children && group.children.length > 0) {
              allTags.push(...group.children);
            }
          });

          // 根据ID匹配已选择的标签
          const matchedTags = allTags.filter(tag =>
            savedTags.some((saved: any) => saved.id == tag.id)
          );

          setSelectedTags(matchedTags);
        }
      } else {
        setCatalog([]);
      }
    }
  }
  useEffect(() => {
    getTagListData();

    Taro.eventCenter.on('refreshTagList', getTagListData);

    return () => {
      Taro.eventCenter.off('refreshTagList', getTagListData);
    };
  }, []); // 添加 sortType 作为依赖



  return (
    <View className="select-tag-page">
      {platform !== "WX" &&<YkNavBar title="标签" />}
      {selectedTags.length > 0 && (<View className="tags-row">
        {selectedTags.map((item, index) => (
          <Tag
            size="small"
            key={item.id}
            filleted 
            type="solid" 
            closeable
            onClose={() => {
              setSelectedTags(prev => prev.filter((_, i) => i !== index));
            }}
            style={{fontSize:'12px',height:'25px'}} 
            bgColor={ "var(--primary-color)" }
          >
            {item.name}
          </Tag>
        ))}
      </View>)}
      <input type="text" className="add-tag" placeholder="添加标签" onInput={handleInput} />
      <View className="select-title">选择标签</View>
      <View className="tag-group-list">
        {catalog.map((group, i) => (
          <View className="tag-group" key={group.id}>
            <View className="group-title">{group.name}</View>
            <View className="tags-row">
              {group.children.map(tag => {
                const isSelected = selectedTags.some(t => t.id === tag.id);
                return (
                  <View
                    key={tag.name}
                    onClick={() => {
                      setSelectedTags(prev =>
                        isSelected
                          ? prev.filter(t => t.id !== tag.id)
                          : [...prev, { id: tag.id, name: tag.name }]
                      );
                    }}
                  >
                    <Tag 
                      size="small"
                      filleted 
                      type="hollow" 
                      halfBorder={false}
                      style={{fontSize:'12px',height:'25px'}} 
                      bgColor="#fff"
                      borderColor={isSelected ? "var(--primary-color)" : "#86909C"}
                      color={isSelected ? "var(--primary-color)" : "#86909C"}
                    >
                      {tag.name}
                    </Tag>
                  </View>
                );
              })}
            </View>
          </View>
        ))}
      </View>
      <View className="footer-btns">
        <button className="btn btn-grey" onClick={()=>{
         Taro.navigateTo({ url: "/pageDynamic/tagAndCategoryManage/index" });
        }}>目录/标签管理</button>
        <button className="btn btn-primary" onClick={()=>{
          Taro.setStorageSync('selectedArticles', selectedTags);
          Taro.navigateBack();
        }}>完成</button>
      </View>
    </View>
  );
}
