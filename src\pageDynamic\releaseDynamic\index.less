@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pageDynamic/releaseDynamic/index"] {
  background-color: #f8f9fa !important;
  //暗黑模式
  .use-dark-mode-query({
    background-color: @dark-background-color;
});
  .release-dynamic {
    min-height: 100vh;
    background-color: #f8f9fa;
    .use-dark-mode-query({
      background-color: @dark-background-color;
    });
    padding-bottom: 50px;

    // 自定义运费模板选择弹窗样式
    .freight-template-modal {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1000;
      display: flex;
      align-items: flex-end;

      .modal-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
      }

      .modal-content {
        position: relative;
        width: 100%;
        max-height: 70vh;
        background-color: #ffffff;
        .use-dark-mode-query({
          background-color: @dark-background-color;
        });
        border-radius: 16px 16px 0 0;
        display: flex;
        flex-direction: column;

        .modal-header {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 16px 20px;
          border-bottom: 1px solid var(--line-color);
          .use-dark-mode-query({
          border-bottom: 1px solid @dark-line-color;
        });

          .modal-title {
            font-size: 16px;
            font-weight: 500;
            color: #333333;
            .use-dark-mode-query({
              color: @dark-font-color;
            });
          }
        }

        .modal-body {
          flex: 1;
          overflow-y: auto;
          padding: 0;

          .template-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid var(--line-color);
            .use-dark-mode-query({
            border-bottom: 1px solid @dark-line-color;
          });
            cursor: pointer;

            &:last-child {
              border-bottom: none;
            }

            .template-content {
              display: flex;
              flex-direction: column;
              justify-content: center;
              flex: 1;
              padding-right: 12px;

              .template-name {
                font-size: 15px;
                color: #333333;
                .use-dark-mode-query({
                  color: @dark-font-color;
                });
                margin-bottom: 6px;
                line-height: 1.4;
                word-wrap: break-word;
                word-break: break-all;

                &.selected {
                  color: var(--primary-color);
                  font-weight: 500;
                }
              }

              .template-rules {
                margin-bottom: 4px;

                .freight-rule-text {
                  font-size: 12px;
                  color: #666666;
                  .use-dark-mode-query({
                    color: @dark-font-color;
                  });
                  line-height: 1.4;

                  &.selected {
                    color: var(--primary-color);
                  }
                }

                .freight-rule-container {
                  .freight-rule-main {
                    font-size: 12px;
                    color: #666666;
                    line-height: 1.4;
                    margin-bottom: 2px;

                    &.selected {
                      color: var(--primary-color);
                    }
                  }

                  .freight-rule-sub {
                    font-size: 11px;
                    color: #999999;
                    line-height: 1.4;

                    &.selected {
                      color: var(--primary-color);
                    }
                  }
                }
              }

              .template-area {
                font-size: 11px;
                color: #666666;
                .use-dark-mode-query({
                  color: @dark-font-color;
                });
                line-height: 1.4;
                word-wrap: break-word;
                word-break: break-all;

                &.selected {
                  color: var(--primary-color);
                }
              }
            }

            .template-check {
              color: var(--primary-color);
              font-size: 18px;
              font-weight: bold;
            }
          }
        }

        .modal-footer {
          padding: 16px 20px;
          border-top: 1px solid var(--line-color);
          .use-dark-mode-query({
          border-top: 1px solid @dark-line-color;
        });

          .cancel-btn {
            width: 100%;
            height: 44px;
            background-color: #f5f5f5;
            .use-dark-mode-query({
              background-color: @dark-background-color;
            });
            color: #333333;
            .use-dark-mode-query({
              color: @dark-font-color;
            });
            border: none;
            border-radius: 8px;
            font-size: 16px;
          }
        }
      }
    }

    .container {
      padding: 10px 0;
    }

    .textarea {
      width: 100%;
      .rem(height, 150);
      padding: 10px 15px;
      background-color: #ffffff;
      .use-dark-mode-query({
        background-color: @dark-background-color;
      });
      .rem(font-size, 12);
    }

    .image-tips {
      // margin-top: 20px;
      padding: 0 15px;
      padding-top: 20px;
      .rem(height, 20);
      line-height: 20px;
      .rem(font-size, 12);
      color: #999999;
      background-color: #ffffff;
      .use-dark-mode-query({
        background-color: @dark-background-color;
      });
    }

    .imagelist {
      padding: 10px 15px;
      background-color: #ffffff;
      .use-dark-mode-query({
        background-color: @dark-background-color;
      });

      // 防止拖拽时页面滚动
      touch-action: none;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      -khtml-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;

      .image-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
      }

      .image-item {
        position: relative;
        width: calc(
          (100vw - 30px - 12px) / 3
        ); // 屏幕宽度减去左右padding(30px)和间距(12px)后三等分
        aspect-ratio: 1; // 使用aspect-ratio保持1:1比例，更可靠
        border-radius: 8px;
        overflow: hidden; // 恢复hidden，删除按钮在图片内侧
        transition: all 0.3s ease;
        cursor: move;
        user-select: none;
        -webkit-user-select: none;
        -webkit-touch-callout: none;

        // 拖拽中的状态
        &.dragging {
          opacity: 0.7;
          transform: scale(1.05);
          box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
          z-index: 1000;
          pointer-events: none; // 防止拖拽时触发其他事件
        }

        // 拖拽目标位置
        &.drop-target {
          box-shadow: inset 0 0 0 2px #165dff;
          background: rgba(22, 93, 255, 0.1);
          transform: scale(0.98);

          &::after {
            content: "";
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border: 2px solid #165dff;
            border-radius: 10px;
            animation: pulse 1s infinite;
          }
        }

        @keyframes pulse {
          0% {
            opacity: 1;
            transform: scale(1);
          }
          50% {
            opacity: 0.7;
            transform: scale(1.02);
          }
          100% {
            opacity: 1;
            transform: scale(1);
          }
        }

        // 拖拽指示器
        .drag-indicator {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: rgba(22, 93, 255, 0.9);
          color: #fff;
          text-align: center;
          font-size: 10px;
          padding: 2px 0;
          z-index: 1001;
        }

        .image-preview {
          width: 100%;
          height: 100%;
          object-fit: cover;
          background: #f7f8fa;
        }

        .image-delete {
          position: absolute;
          top: 0;
          right: 0;
          width: 20px;
          height: 20px;
          background: rgba(0, 0, 0, 0.6);
          color: #fff;
          border-radius: 0 8px 0 8px; // 与图片右上角圆角一致
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          font-weight: bold;
          cursor: pointer;
          z-index: 10;

          &:active {
            background: rgba(255, 0, 0, 0.8);
            transform: scale(0.95);
          }
        }
      }

      .image-add {
        width: calc((100vw - 30px - 12px) / 3); // 与图片项相同尺寸
        aspect-ratio: 1; // 使用aspect-ratio保持1:1比例
        // border: 2px dashed #d9d9d9;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        background: #fafafa;

        &:hover {
          border-color: #165dff;
          background: #f0f5ff;
        }

        .add-icon {
          font-size: 24px;
          color: #999;
          margin-bottom: 4px;
        }

        .add-text {
          font-size: 12px;
          color: #999;
        }
      }
    }

    .image-list {
      padding: 10px 15px;
      background-color: #ffffff;
      .use-dark-mode-query({
        background-color: @dark-background-color;
      });
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-gap: 10px;

      .image-item {
        position: relative;
        .rem(width, 82);
        .rem(height, 82);
        border-radius: 8px;
        background-color: #f6f6f6;
        .use-dark-mode-query({
          background-color: @dark-card-background-color;
        });

        &.add {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .delete-icon {
          position: absolute;
          top: 0;
          right: 0;
          .rem(width, 30);
          .rem(height, 30);
        }
      }
    }

    .price-section,
    .tag-section,
    .format-section,
    .short-text-section,
    .source-section,
    .ems-section {
      .rem(height, 50);
      background-color: #ffffff;
      .use-dark-mode-query({
        background-color: @dark-background-color;
      });
      padding: 0 15px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .label {
        .rem(width, 120);
        .rem(font-size, 15);
        color: #000000;
        .use-dark-mode-query({
          color: @dark-font-color;
        });
      }

      .input-wrapper {
        display: flex;
        align-items: center;

        .currency {
          .rem(font-size, 14);
          color: #000000;
          .use-dark-mode-query({
            color: @dark-font-color;
          });
        }

        .price-input {
          width: 110px; // 设置固定宽度，避免间隔太远
          align-items: flex-end;
          text-align: right;
          height: 100%;
          margin-right: 10px;

          .arco-input{
            padding: 0 !important;
            color: #000000;
          .use-dark-mode-query({
            color: @dark-font-color;
          });
            .rem(font-size, 14) !important;
          }
        }
      }

      .value {
        color: #000000;
        .use-dark-mode-query({
          color: @dark-font-color;
        });
        .rem(font-size, 14);
      }

      .right {
        flex: 1;
        margin-left: 9px;
        display: flex;
        align-items: center;
        justify-content: flex-end;

        .placeholder {
          color: #000000;
          .use-dark-mode-query({
            color: @dark-font-color;
          });
          .rem(font-size, 14);
          // 单行显示，超出省略号
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 200px;
        }

        .value {
          color: #000000;
          .use-dark-mode-query({
            color: @dark-font-color;
          });
          .rem(font-size, 14);
          // 单行显示，超出省略号
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 200px;
        }

        .arrow {
          margin-left: 16px;
          .rem(width, 12);
          .rem(height, 22);
        }

        .input {
          .rem(font-size, 14);
          height: 100%;
          display: flex;
          align-items: center;
        }
      }
    }

    .short-text-section {
      .right {
        .input {
          flex: 1;
          .rem(height, 21);
          color: #000000;
          .use-dark-mode-query({
            color: @dark-font-color;
          });
          .rem(font-size, 14);
        }

        .get-from-title {
          margin-left: 18px;
          color: #355a95;
          .rem(font-size, 14);
        }
      }
    }

    .divider {
      margin: 0 16px;
      border-bottom: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-bottom: 1px solid @dark-line-color;
    });
    }

    .value {
      .rem(font-size, 14);
    }

    .footer {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      .rem(height, 50);
      background-color: #ffffff;
      .use-dark-mode-query({
        background-color: @dark-background-color;
      });
      display: flex;
      align-items: center;
      justify-content: center;

      .save-button {
        .rem(width, 345);
        .rem(height, 36);
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ffffff;
        .rem(font-size, 13);
        font-weight: bold;
        background-color: var(--primary-color);
        opacity: 0.3;

        &.active {
          background-color: var(--primary-color);
          opacity: 1;
        }
      }
    }
  }
}
