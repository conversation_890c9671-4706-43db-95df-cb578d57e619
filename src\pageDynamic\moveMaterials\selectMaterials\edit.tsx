import { View, Text, Image, Button } from "@tarojs/components";
import { useState,useEffect,useRef } from "react";
import Taro, { useDidShow } from "@tarojs/taro";
import { Input, Textarea } from "@arco-design/mobile-react";
import { IconTriDown } from "@arco-design/mobile-react/esm/icon";
import YkNavBar from "@/components/ykNavBar/index";
import { moveHouseDynamic } from "@/utils/api/common/common_user";
import { toast } from "@/utils/yk-common";
import "./edit.less";

// 定义 tab 类型

export default function SelectMaterialsEdit2() {
  const [expandedId, setExpandedId] = useState<number | null>(null);
  const [dynamic, setDynamic] = useState<any[]>([]);
  const userInfo = Taro.getStorageSync('userInfo');

  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  const handlePriceInput = (item: any) => (_e: any, value: string) => {
    // 移除非数字字符和多余的小数点
    let sanitizedValue = value.replace(/[^\d.]/g, '').replace(/^(\d*\.\d{2}).*$/, '$1');

    // 将输入值限制在 6 位整数，两位小数点
    let [integerPart, decimalPart] = sanitizedValue.split('.');
    if (integerPart && integerPart.length > 6) {
      integerPart = integerPart.slice(0, 6);
    }
    if (decimalPart && decimalPart.length > 2) {
      decimalPart = decimalPart.slice(0, 2);
    }

    // 重新组装处理后的值
    const finalValue = decimalPart !== undefined ? `${integerPart}.${decimalPart}` : integerPart;

    const updatedDynamic = dynamic.map(dItem => {
      if (dItem.digitalWatermark === item.digitalWatermark) {
        return { ...dItem, optimaPrice: finalValue };
      }
      return dItem;
    });
    setDynamic(updatedDynamic);
    Taro.setStorageSync("selectedDynamicItems", updatedDynamic);
  }

  const getDynamicListData = async () => {
    const data=Taro.getStorageSync("selectedDynamicItems")
    const itemsWithSkus = data.map(item => ({
        ...item,
        color: []
      })) || [];
      setDynamic(itemsWithSkus);
  }


  const moveHouse = async (dynamicItems) => {
    const updatedDynamic = dynamicItems.map(item => ({
      ...item,
      userId: userInfo.id,
      linkId: Taro.getCurrentInstance().router?.params?.linkId || '',
    }));
    const data={
      content: JSON.stringify(updatedDynamic),
    }
    console.log(data)
    let res: any = await moveHouseDynamic(data);
    if (res && res.code == 0) {
      Taro.setStorageSync("selectedDynamicItems", []);
      // 跳转搬家成功结果页，传入数量
      Taro.redirectTo({
        url: `/pageDynamic/moveMaterials/selectMaterials/result?count=${dynamicItems.length}`,
      });
      Taro.eventCenter.trigger('refreshAlbumList');
    }else{
      toast("error", {
        content: "搬家失败",
        duration: 2000
      });
    }
  }


  useDidShow(() => {
    getDynamicListData();
  });





  // 新增状态控制描述编辑弹窗
  const [showDescriptionEditDialog, setShowDescriptionEditDialog] = useState(false);
  const [editingItemId, setEditingItemId] = useState<number | string | null>(null);
  const [editingItemDescription, setEditingItemDescription] = useState('');



  // 处理描述修改
  const handleDescriptionChange = (itemId: number | string | null, newDescription: string) => {
    const updatedDynamic = dynamic.map(item => {
      if (item.digitalWatermark === itemId) {
        return { ...item, title: newDescription };
      }
      return item;
    });
    setDynamic(updatedDynamic);
    Taro.setStorageSync("selectedDynamicItems", updatedDynamic);
  };

  // 渲染商品列表
  const renderProductList = () => {
    return (
      <View className="move-list-edit" >
          {dynamic.map(item => (
            <View className="move-item-wrap-edit" key={item.digitalWatermark}>
                    <View className="move-item-s">
                        <Image className="move-img" src={item.imgsSrc[0]} />
                        <View className="move-info-edit">
                            <View className="move-title">
                                <Text className="move-title-text-s" >{item.title}</Text>
                                <Text className="move-title-edit"
                                 onClick={() => {
                                   setEditingItemId(item.digitalWatermark);
                                   setEditingItemDescription(item.title);
                                   setShowDescriptionEditDialog(true);
                                 }}
                                 >编辑</Text>
                            </View>

                            <View className="move-price-wrap">
                              <View className="move-price-wrap-left">
                                <Text  className="move-price-edit" >售价</Text>
                                <View className="move-price-input-c">
                                <Input
                                className="move-price-input"
                                value={item.optimaPrice || ''}
                                placeholder="0"
                                type="number"
                                border="none"
                                onChange={handlePriceInput(item)}
                                />
                                </View>
</View>

                              </View>
                        </View>
                    </View>
                    <View className="move-attrs-wrap-s">
                        {(item.skus.length > 0 || item.color.length > 0) ? (
                        <>
                            <Text
                            className="move-attrs-edit"
                            onClick={() => setExpandedId(expandedId === item.digitalWatermark ? null : item.digitalWatermark)}
                            >
                            商品属性
                            {expandedId === item.digitalWatermark ?
                                // <Text className="move-attrs-arrow">▼</Text> :
                                <IconTriDown className="move-attrs-arrow" style={{ transform: "rotate(0deg)" }} />
                                :
                                // <Text className="move-attrs-arrow">▶</Text>
                                <IconTriDown className="move-attrs-arrow" style={{ transform: "rotate(-90deg)" }} />

                                // <Text className="move-attrs-arrow">▶</Text>
                            }
                            </Text>
                            {expandedId === item.digitalWatermark && (
                            <View className="attr-content card-attr-content">
                                {item.skus.length ?
                                (<View className="attr-row" key="规格">
                                <Text className="attr-label">规格：</Text>
                                <View className="attr-values">
                                    {item.skus.map(val => (
                                    <Text
                                        className='attr-value'
                                        key={val}
                                    >
                                        {val}
                                    </Text>
                                    ))}
                                </View>
                                </View>):(<></>)
                                }
                                {item.color.length ?
                                (<View className="attr-row" key="颜色">
                                    <Text className="attr-label">颜色：</Text>
                                    <View className="attr-values">
                                    {item.color.map(val => (
                                        <Text
                                        className='attr-value'
                                        key={val}
                                        >
                                        {val}
                                        </Text>
                                    ))}
                                    </View>
                                </View>):(<></>)
                                }
                            </View>
                            )}
                        </>
                        ):(<Text className="move-attrs"></Text>)
                        }
                    </View>
            </View>
          ))}
      </View>
    );
  };

  return (
    <View >
     {platform !== "WX" && <YkNavBar title="商品设置"   />}
      <View className="move-content-page">
        {renderProductList()}
        <View className="move-footer-s">
            <Button
              className="move-footer-btn"
              onClick={() => {
                moveHouse(dynamic);
              }}
            >
              开始搬家({Taro.getStorageSync("selectedDynamicItems").length})
            </Button>
          </View>
      </View>

      {/* 修改描述弹窗 */}
      {showDescriptionEditDialog  && (
        <View className="description-edit-mask">
          <View className="description-edit-dialog">
            <View className="description-edit-header">
              <Text className="description-edit-title">修改描述</Text>
              <Text className="description-edit-close" onClick={() => setShowDescriptionEditDialog(false)}>×</Text>
            </View>
            <View className="description-edit-content">
              <Textarea

                className="description-edit-textarea"
                value={editingItemDescription}
                onChange={(val) => setEditingItemDescription(val.target.value)}
                placeholder="请输入描述"
                border="none"
                autosize
              />
            </View>
            <View className="description-edit-footer">
              <Button className="description-edit-confirm-button" onClick={() => {

                handleDescriptionChange(editingItemId, editingItemDescription);
                setShowDescriptionEditDialog(false);

              }}>确认修改</Button>
            </View>
          </View>
        </View>
      )}

    </View>
  );
}

