import { View, Text, Image } from "@tarojs/components";
import "./index.less";
import {
  Dialog,
  Toast,
  Popup,
  Cell,
  Switch,
  Button,
  Textarea,
  Loading,
} from "@arco-design/mobile-react";
import Taro, { useLoad } from "@tarojs/taro";
import { useState, useRef, useEffect } from "react";

import IconArrowBack from "@arco-design/mobile-react/esm/icon/IconArrowBack";

// 导入示例图片
import deliveryHintImg from "@/assets/images/common/delivery_hint.png";

// 组件
import YkNavBar from "@/components/ykNavBar/index";

// 接口
import {
  getDeliveryTemplateList,
  getDeliveryList,
  createDelivery,
  updateDelivery,
} from "@/utils/api/common/common_user";

interface UserInfo {
  user_id: number;
  [key: string]: any;
}

interface ApiResponse<T> {
  code: number;
  data: {
    list: T[];
  };
  msg: string;
}

interface DeliveryTemplate {
  id: number;
  name: string;
  createTime: number;
}

interface DeliveryConfig {
  id?: number; // 用户配置ID
  userId?: number;
  deliveryTemplateId: number; // 模板ID
  name?: string; // 模板名称
  orderReminder?: string;
  isEnable?: number;
  createTime?: number;
}

export default function DeliveryManager() {
  // 状态
  const [showPage, setShowPage] = useState(false);
  const [popupBottomShow, setPopupBottomShow] = useState(false);
  const [popHint, setPopHint] = useState("");
  const [popDgHint, setPopDgHint] = useState("");
  const [deliveryList, setDeliveryList] = useState<DeliveryConfig[]>([]);
  const [curItem, setCurItem] = useState<DeliveryConfig | null>(null);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [exampleDialogVisible, setExampleDialogVisible] = useState(false); // 示例对话框显示状态

  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);

  // 页面加载
  useLoad(() => {
    getDeliveryData();
  });

  // 获取配送方式数据（并行请求优化）
  const getDeliveryData = () => {
    console.log("[配送管理] 开始获取配送方式数据");

    // 获取用户信息
    const userInfoStorage = Taro.getStorageSync("userInfo");
    const userInfo: UserInfo =
      userInfoStorage && userInfoStorage.data
        ? userInfoStorage.data
        : userInfoStorage;
    if (!userInfo || !userInfo.id) {
      console.error("[配送管理] 获取用户信息失败", userInfoStorage);
      Toast.error("获取用户信息失败，请重新登录");
      return;
    }

    // 并行请求配送模板列表和用户配送配置列表
    Promise.all([
      getDeliveryTemplateList({ pageNo: 1, pageSize: 100 }),
      getDeliveryList({ pageNo: 1, pageSize: 100, userId: userInfo.id }),
    ])
      .then(([templateRes, deliveryRes]) => {
        console.log("[配送管理] 并行请求完成");
        console.log("[配送管理] 获取配送模板列表返回:", templateRes);
        console.log("[配送管理] 获取配送方式列表返回:", deliveryRes);

        const templateData = templateRes as ApiResponse<DeliveryTemplate>;
        const deliveryData = deliveryRes as ApiResponse<DeliveryConfig>;

        // 处理配送模板数据
        if (
          templateData&&
          templateData.code !== 0 ||
          !templateData.data ||
          !templateData.data.list
        ) {
          console.error(
            "[配送管理] 获取配送模板列表失败:",
            templateData.msg || "未知错误"
          );
          Toast.error("获取配送模板失败，请稍后重试");
          return;
        }

        const templateList = templateData.data.list;
        console.log("[配送管理] 配送模板列表数据:", templateList);

        // 处理用户配送配置数据
        let userConfigList: DeliveryConfig[] = [];
        if (
          deliveryData&&
          deliveryData.code === 0 &&
          deliveryData.data &&
          deliveryData.data.list
        ) {
          console.log("[配送管理] 配送方式列表数据:", deliveryData.data.list);
          userConfigList = deliveryData.data.list;
        }

        // 合并数据
        const mergedList: DeliveryConfig[] = templateList.map((template) => {
          const userConfig = userConfigList.find(
            (item) => item.deliveryTemplateId === template.id
          );
          if (!userConfig) {
            return {
              deliveryTemplateId: template.id,
              name: template.name,
              isEnable: 0, //未配置之前，默认关闭
              createTime: template.createTime,
            };
          } else {
            return {
              id: userConfig.id,
              deliveryTemplateId: template.id,
              name: template.name,
              isEnable: userConfig.isEnable,
              orderReminder: userConfig.orderReminder,
              createTime: userConfig.createTime,
              userId: userConfig.userId,
            };
          }
        });

        // 排序后再设置状态
        console.log("[配送管理] 合并后的配送方式数据:", mergedList);
        const sortedList = [...mergedList].sort(
          (a, b) => a.deliveryTemplateId - b.deliveryTemplateId
        );
        console.log("[配送管理] 排序后的配送方式数据:", sortedList);
        setDeliveryList(sortedList);
      })
      .catch((error) => {
        console.error("[配送管理] 获取配送方式数据出错:", error);
        Toast.error("获取数据失败，请稍后重试");
      })
      .finally(() => {
        console.log("[配送管理] 数据加载完成，显示页面");
        setTimeout(() => {
          setShowPage(true);
        }, 500);
      });
  };

  // 保存提示
  const saveHint = () => {
    if (!popHint) {
      Toast.warn("请输入下单提示");
      return;
    }

    if (curItem) {
      const updatedList = deliveryList.map((item) => {
        if (item.deliveryTemplateId === curItem.deliveryTemplateId) {
          return { ...item, orderReminder: popHint };
        }
        return item;
      });

      setDeliveryList(updatedList);

      // 更新当前修改的配送方式
      const updatedItem = updatedList.find(
        (item) => item.deliveryTemplateId === curItem.deliveryTemplateId
      );
      if (updatedItem) {
        saveDeliveryConfig(updatedItem);
      }

      closeBottomPop();
    }
  };

  // 保存配送方式配置（区分创建和更新）
  const saveDeliveryConfig = (item: DeliveryConfig) => {
    const userInfoStorage = Taro.getStorageSync("userInfo");
    const userInfo: UserInfo =
      userInfoStorage && userInfoStorage.data
        ? userInfoStorage.data
        : userInfoStorage;
    if (!userInfo || !userInfo.id) {
      console.error("[配送管理] 保存配送方式配置失败: 未获取到用户ID");
      Toast.error("操作失败，请重新登录");
      return;
    }

    console.log("[配送管理] 准备保存配送方式配置:", item);

    const baseData: DeliveryConfig = {
      userId: userInfo.id,
      deliveryTemplateId: item.deliveryTemplateId, // API需要字符串类型
      orderReminder: item.orderReminder,
      isEnable: item.isEnable,
    };

    // 根据是否已有用户配置决定创建还是更新
    if (item.id) {
      // 已有配置，执行更新
      baseData.id = item.id;

      console.log("[配送管理] 更新配送方式请求参数:", baseData);
      updateDelivery(baseData).then(handleApiResponse).catch(handleApiError);
    } else {
      // 无配置，执行创建
      console.log("[配送管理] 创建配送方式请求参数:", baseData);
      createDelivery(baseData)
        .then((res: any) => {
          handleApiResponse(res);

          // 创建成功后，更新item的状态，标记为已有配置
          if (res && res.code === 0 && res.data) {
            const newUserConfigId = res.data; // data字段直接是ID
            setDeliveryList((prevList) =>
              prevList.map((listItem) =>
                listItem.deliveryTemplateId === item.deliveryTemplateId
                  ? {
                      ...listItem,
                      id: Number(newUserConfigId),
                      userId: userInfo.id,
                    }
                  : listItem
              )
            );
          }
        })
        .catch(handleApiError);
    }
  };

  // 处理API响应
  const handleApiResponse = (res: any) => {
    console.log("[配送管理] API响应:", res);
    if (res && res.code === 0) {
      console.log("[配送管理] 操作成功");
    } else {
      console.error("[配送管理] 操作失败:", res);
    }
  };

  // 处理API错误
  const handleApiError = (error: any) => {
    console.error("[配送管理] API错误:", error);
    Toast.error("网络异常，请重试");
  };

  // 显示底部弹窗
  const showBottomPop = (item: DeliveryConfig) => {
    setCurItem(item);
    setPopHint(item.orderReminder || "");
    setPopupBottomShow(true);
  };

  // 关闭底部弹窗
  const closeBottomPop = () => {
    setPopupBottomShow(false);
  };

  // 切换选中状态
  const check = (item: DeliveryConfig, checked: boolean) => {
    const updatedList = deliveryList.map((delivery) => {
      if (delivery.deliveryTemplateId === item.deliveryTemplateId) {
        return { ...delivery, isEnable: checked ? 1 : 0 };
      }
      return delivery;
    });

    // 检查是否至少保留一种配送方式
    const hasEnabled = updatedList.some((delivery) => delivery.isEnable === 1);
    if (!hasEnabled) {
      Toast.warn("至少保留一种配送方式");
      return;
    }

    setDeliveryList(updatedList);

    // 更新当前修改的配送方式
    const updatedItem = updatedList.find(
      (delivery) => delivery.deliveryTemplateId === item.deliveryTemplateId
    );
    if (updatedItem) {
      saveDeliveryConfig(updatedItem);
    }

    Toast.success(
      checked
        ? "开启成功，买家下单时会显示该配送方式"
        : "关闭成功，买家下单时不显示该配送方式"
    );
  };

  // 显示提示框
  const openHintPop = (str: string) => {
    setPopDgHint(str);
    setDialogVisible(true);
  };

  // 显示示例对话框
  const showExampleDialog = () => {
    setExampleDialogVisible(true);
    // 临时关闭填写下单提示弹窗，避免层级冲突
    setPopupBottomShow(false);
  };

  // 关闭示例对话框
  const closeExampleDialog = () => {
    setExampleDialogVisible(false);
    // 重新显示填写下单提示弹窗
    setPopupBottomShow(true);
  };

  // 返回上一页
  const handleBack = () => {
    Taro.navigateBack({
      delta: 1,
    });
  };

  return (
    <View className={`deliveryManagerPage ${popupBottomShow ? "cover" : ""}`}>
     {platform !== "WX" && <YkNavBar title="配送管理" onClickLeft={handleBack} />}

      {!showPage && (
        <View className="loading-container">
          <Loading />
        </View>
      )}

      {showPage && (
        <>
          <View className="container">
            <Text className="container-title">管理店铺启用的配送方式：</Text>

            <View className="container-box">
              {deliveryList.map((item) => (
                <Cell.Group
                  key={item.deliveryTemplateId}
                  className="container-box-cellgroup"
                  bordered={false}
                >
                  <Cell label={item.name}>
                    <Switch
                      platform={platform}
                      checked={item.isEnable === 1}
                      onChange={(checked) => check(item, checked)}
                    />
                  </Cell>
                  <Cell
                    label="下单提示"
                    showArrow
                    onClick={() => showBottomPop(item)}
                  >
                    <Text className="delivery-item-hint-right-text">
                      {item.orderReminder || "去设置"}
                    </Text>
                  </Cell>
                </Cell.Group>
              ))}
            </View>
          </View>

          <Popup
            visible={popupBottomShow}
            onClose={closeBottomPop}
            maskClosable
            close={closeBottomPop}
          >
            <View className="popupBottomShow">
              <View className="popupBottomShow-head">
                <View className="popupBottomShow-head-left">
                  <IconArrowBack
                    className="popupBottomShow-head-left-icon"
                    onClick={closeBottomPop}
                  />
                </View>
                <Text className="popupBottomShow-head-title">下单提示</Text>
                <Text
                  className="popupBottomShow-head-right"
                  onClick={showExampleDialog}
                >
                  查看示例
                </Text>
              </View>
              <View className="popupBottomShow-content">
                <Textarea
                  className="popupBottomShow-content-textarea"
                  placeholder="备注的内容将在买家下单时高亮展示"
                  value={popHint}
                  rows={5}
                  onChange={(_, value) => setPopHint(value)}
                  maxLength={200}
                  style={{ minHeight: "140px" }}
                  border="none"
                />
              </View>

              <View className="popupBottomShow-bottom">
                <Button
                  className="popupBottomShow-bottom-btn"
                  type="primary"
                  onClick={saveHint}
                >
                  保存
                </Button>
              </View>
            </View>
          </Popup>

          <Dialog
            visible={dialogVisible}
            title="下单提示"
            onClose={() => setDialogVisible(false)}
            close={() => setDialogVisible(false)}
            footer={[
              { content: "知道了", onClick: () => setDialogVisible(false) },
            ]}
            platform={platform}
          >
            {popDgHint}
          </Dialog>


        </>
      )}

      {/* 示例对话框 - 自定义弹窗 */}
      {exampleDialogVisible && (
        <View
          className="custom-dialog-mask"
          onClick={closeExampleDialog}
        >
          <View className="custom-dialog" onClick={(e) => e.stopPropagation()}>
            <View className="custom-dialog-header">
              <Text className="custom-dialog-title">下单提示</Text>
            </View>
            <View className="custom-dialog-content">
              <Image
                src={deliveryHintImg}
                className="custom-dialog-image"
                mode="aspectFit"
              />
            </View>
            <View className="custom-dialog-footer">
              <Text className="custom-dialog-btn" onClick={closeExampleDialog}>
                知道了
              </Text>
            </View>
          </View>
        </View>
      )}
    </View>
  );
}
