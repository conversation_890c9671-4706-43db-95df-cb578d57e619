import React, { useState } from 'react';
import { View, Text, Image } from '@tarojs/components';
import { Popup } from '@arco-design/mobile-react';
import Taro from '@tarojs/taro';
import { IconDownload, IconCopy, IconPhone } from "@arco-iconbox/react-yk-arco";
import './index.less';
import wx from "weixin-webview-jssdk";
import { useRef, useEffect } from 'react';
import { useSetPermission } from "@/stores/permissionStore";
import {
  AuthTypes,
  AuthStatusAndroid,
  AuthStatusIos,
} from "@/utils/config/authTypes";
import PermissionPopup from "@/components/PermissionPopup";
import { usePermission } from "@/hooks/usePermission";
import { useGlobalCallbacks } from "@/utils/globalCallbackManager";
// import { toast } from '@arco-design/mobile-react/cjs/toast';
export interface ContactModalProps {
  visible: boolean;
  onClose: () => void;
  contactInfo: {
    wechatQrCode?: string;
    wechatNumber?: string;
    contactMobile?: string;
    nickname?: string;
  };
  onCopyWechat?: (wechatNumber: string) => void;
  onCopyPhone?: (contactMobile: string) => void;
  onCallPhone?: (contactMobile: string) => void;
  onDownloadQrCode?: (qrCodeUrl: string) => void;
  onChat?: () => void;
}

const ContactModal: React.FC<ContactModalProps> = ({
  visible,
  onClose,
  contactInfo,
  onCopyWechat,
  onCopyPhone,
  onCallPhone,
  onDownloadQrCode,
  onChat,
}) => {
  const [showCallPhonePopup, setShowCallPhonePopup] = useState(false);
// 自定义权限同意处理，处理首页特有的逻辑
const customWebPermissonConsent = () => {
    // 下载操作
    if (platformRef.current === "Android") {
      downloadWxCode();
    } else if (platformRef.current === "IOS") {
      window.webkit.messageHandlers.checkPermission.postMessage("");
    } 
  return true;
};

// 使用全局权限管理
const {
  initPermissions,
  hasPermission,
  requestPermission,
  webPermissonConsent,
  webPermissonDeny,
  permissionPopupProps,
  platformRef,
  authConfirmType,
} = usePermission(customWebPermissonConsent);
  useEffect(() => {
     // 初始化权限管理
     const cleanup = initPermissions();
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      platformRef.current = "Android";
    } else if (isIos) {
      platformRef.current = "IOS";
    } else if (isHM) {
      platformRef.current = "HM";
    } else {
      platformRef.current = "H5";
    }

    // 使用全局回调管理器注册下载回调
    // const callbackCleanup = useGlobalCallbacks('contactModal', {
    //   webDownloadSuc: webDownloadSuc,
    //   webDownloadFail: webDownloadFail,
    // });

    return () => {
      cleanup && cleanup();
      // callbackCleanup && callbackCleanup();
    };
  }, []);

  // const webDownloadSuc = () => {
  //   Taro.showToast({
  //     title: "下载成功",
  //     icon: "success",
  //     duration: 2000,
  //   });
  // };
  // const webDownloadFail = () => {
  //   Taro.showToast({
  //     title: "下载失败",
  //     icon: "error",
  //     duration: 2000,
  //   });
  // };

  // 显示拨打电话确认弹框
  const showCallPhonePop = () => {
    setShowCallPhonePopup(true);
  };

  // 关闭拨打电话确认弹框
  const closeCallPhonePop = () => {
    setShowCallPhonePopup(false);
  };


  const handleDownload = () => {
      if (platformRef.current === "HM"  || platformRef.current === "WX") {
        downloadWxCode();
      }else{
      // 检查存储权限
      if (!hasPermission(AuthTypes.STORAGE)) {
        console.log("没有权限");
        // 如果没有权限，请求权限
        requestPermission(AuthTypes.STORAGE);
        return;
      }
      // 有权限则执行下载
      downloadWxCode();
      Taro.showToast({
        title: "下载成功",
        duration: 2000,
      });
    }
  };

  // 下载微信二维码
  const downloadWxCode = () => {
    if (onDownloadQrCode && contactInfo.wechatQrCode) {
      onDownloadQrCode(contactInfo.wechatQrCode);
    } else if (contactInfo.wechatQrCode) {
      // 默认下载实现 - 照搬订单详情页的逻辑
      console.log("下载微信二维码", contactInfo.wechatQrCode);
      if(platformRef.current === "Android"){
        window.downloadImg.downloadImg(contactInfo.wechatQrCode);
      } else if(platformRef.current === "IOS"){
        window.webkit.messageHandlers.saveImgWithUrlStr.postMessage(contactInfo.wechatQrCode);
      } else if(platformRef.current === "HM"){
        window.harmony.downloadImg(contactInfo.wechatQrCode);
      }  else if(platformRef.current === "WX"){
        wx.miniProgram.navigateTo({ url: "/pages/downloadImgs/index?imgs=" + encodeURIComponent(contactInfo.wechatQrCode) });
      }
    }
  };

  // 复制微信号
  const getCopyWechat = () => {
    if (onCopyWechat && contactInfo.wechatNumber) {
      console.log("复制微信号", contactInfo.wechatNumber);
      onCopyWechat(contactInfo.wechatNumber);
    } else if (contactInfo.wechatNumber) {
      // 默认复制实现 - 照搬订单详情页的逻辑
      Taro.setClipboardData({
        data: contactInfo.wechatNumber,
        success: () => {
          Taro.hideToast();
          Taro.showToast({
            title: "微信号已复制",
            duration: 2000,
          });
        },
        fail: () => {
          Taro.hideToast();
          Taro.showToast({
            title: "复制失败",
            duration: 2000,
          });
        },
      });
    }
  };

  // 复制手机号
  const getCopyPhone = () => {
    if (onCopyPhone && contactInfo.contactMobile) {
      onCopyPhone(contactInfo.contactMobile);
    } else if (contactInfo.contactMobile) {
      // 默认复制实现 - 照搬订单详情页的逻辑
      Taro.setClipboardData({
        data: contactInfo.contactMobile,
        success: () => {
          Taro.hideToast();
          Taro.showToast({
            title: "手机号已复制",
            duration: 2000,
          });
        },
        fail: () => {
          Taro.hideToast();
          Taro.showToast({
            title: "复制失败",
            icon: "error",
            duration: 2000,
          });
        },
      });
    }
  };

  // 拨打电话
  const callPhone = () => {
    if (onCallPhone && contactInfo.contactMobile) {
      onCallPhone(contactInfo.contactMobile);
      setShowCallPhonePopup(false);
      return;
    }

    if (!contactInfo.contactMobile) {
      Taro.showToast({
        title: "暂无手机号",
        duration: 2000,
      });
      return;
    }

    // 默认拨打电话实现 - 照搬订单详情页的逻辑
    window.callPhone.callPhone(contactInfo.contactMobile);
    setShowCallPhonePopup(false);
  };

  // 在线聊天
  const handleChat = () => {
    if (onChat) {
      onChat();
      onClose();
    } else {
      Taro.showToast({
        title: "在线聊天功能开发中",
        duration: 2000,
      });
    }
    onClose();
  };

  return (
    <>
      {/* 联系商家弹框 */}
      <Popup
        visible={visible}
        close={onClose}
        direction="bottom"
        className="contact-popup"
      >
        <View className="popup-bottom-contact-show">
          {/* 微信二维码 */}
          {contactInfo?.wechatQrCode && (
            <View className="popup-bottom-contact-show-qrcode">
              <Image
                className="popup-bottom-contact-show-qrcode-img"
                src={contactInfo.wechatQrCode}
              />
            </View>
          )}

          {/* 下载按钮 */}
          {contactInfo?.wechatQrCode && (
            <View className="popup-bottom-contact-show-download">
              <IconDownload
                className="popup-bottom-contact-show-download-img"
                onClick={handleDownload}
              />
            </View>
          )}

          {/* 微信号 */}
          {contactInfo?.wechatNumber && (
            <View className="popup-bottom-contact-show-num">
              <View className="popup-bottom-contact-show-num-left">
                <Text className="popup-bottom-contact-show-num-left-title">
                  微信号
                </Text>
                <Text className="popup-bottom-contact-show-num-left-text">
                  {contactInfo.wechatNumber}
                </Text>
              </View>
              <View className="popup-bottom-contact-show-num-right">
                <IconCopy
                  className="popup-bottom-contact-show-num-right-img"
                  onClick={getCopyWechat}
                />
              </View>
            </View>
          )}

          {/* 手机号 */}
          {contactInfo?.contactMobile && (
            <View className="popup-bottom-contact-show-num">
              <View className="popup-bottom-contact-show-num-left">
                <Text className="popup-bottom-contact-show-num-left-title">
                  手机号
                </Text>
                <Text className="popup-bottom-contact-show-num-left-text">
                  {contactInfo.contactMobile}
                </Text>
              </View>
              <View className="popup-bottom-contact-show-num-right">
                <IconPhone
                  className="popup-bottom-contact-show-num-right-img"
                  onClick={showCallPhonePop}
                />
                <IconCopy
                  className="popup-bottom-contact-show-num-right-img"
                  onClick={getCopyPhone}
                />
              </View>
            </View>
          )}

          {/* 在线聊天 */}
          {/* <View className="popup-bottom-contact-show-chat" onClick={handleChat}>
            <Text className="popup-bottom-contact-show-chat-text">在线聊天</Text>
          </View> */}

          <View className="popup-bottom-contact-show-line"></View>

          {/* 取消按钮 */}
          <View
            className="popup-bottom-contact-show-cancel"
            onClick={onClose}
          >
            取消
          </View>
        </View>

        {/* 拨打电话确认弹框 */}
        <Popup
          visible={showCallPhonePopup}
          close={closeCallPhonePop}
          direction="bottom"
          className="call-phone-popup"
        >
          <View className="popup-bottom-phone-show">
            <View className="popup-bottom-phone-show-phone" onClick={callPhone}>
              <IconPhone
                className="popup-bottom-phone-show-phone-img"
              />
              <Text className="popup-bottom-phone-show-phone-text">
                {contactInfo?.contactMobile}
              </Text>
            </View>
            <View
              className="popup-bottom-phone-show-cancel"
              onClick={closeCallPhonePop}
            >
              取消
            </View>
          </View>
        </Popup>
      </Popup>
    </>
  );
};

export default ContactModal;
