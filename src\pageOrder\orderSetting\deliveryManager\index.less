@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageOrder/orderSetting/deliveryManager/index"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });

  .deliveryManagerPage {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow-y: auto;

    &.cover {
      overflow: hidden;
      position: fixed;
      height: 100%;
      width: 100%;
    }

    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 50vh;
    }
    
    // .loading-page {
    //   position: fixed;
    //   top: 0;
    //   left: 0;
    //   width: 100%;
    //   height: 100%;
    //   display: flex;
    //   justify-content: center;
    //   align-items: center;
    //   z-index: 99999;
    //   background-color: #ffffff;
    //   .use-var(background-color, background-color);
    //   .use-dark-mode-query({
    //   background-color: @dark-background-color !important;
    // });
    // }

    .loading-icon {
      width: 40px;
      height: 40px;
      border: 2px solid #999999;
      border-top-color: transparent;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .container {
      display: flex;
      flex-direction: column;
      padding: 0 10px;

      &-title {
        margin-left: 10px;
        margin-top: 15px;
        font-size: 14px;
        color: #999999;
      }

      &-box {
        margin-top: 10px;

        &-cellgroup {
          margin-bottom: 10px;
          overflow: hidden;
          background-color: #ffffff;
          border-radius: 10px;
          .use-var(background-color, background-color);
          .use-dark-mode-query({
          background-color: var(--dark-background-color) !important;
        });
        }
      }
    }

    .delivery-item-hint-right-text {
      color: #666666;
      font-size: 13px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      max-width: 200px;
      display: inline-block;
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

 
}

.popupBottomShow {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  border-radius: 7px 7px 0 0;
  background-color: #f8f9fa;
  .use-var(background-color, background-color);
  .use-dark-mode-query({
  background-color: @dark-background-color !important;
});
  display: flex;
  flex-direction: column;

  &-head {
    margin: 10px 0;
    display: flex;

    &-left {
      display: flex;
      align-items: center;

      &-icon {
        margin-left: 16px;
        width: 18px;
        height: 18px;
        .use-var(color, font-color);
        .use-dark-mode-query({
        color: @dark-font-color;
      });
      }
    }
    &-title {
      font-weight: bold;
      padding-left: 52px;
      text-align: center;
      flex: 1;
      font-size: 15px;
      color: #333333;
      .use-var(color, font-color);
      .use-dark-mode-query({
      color: @dark-font-color;
    });
    }

    &-right {
      width: 52px;
      margin-right: 15px;
      font-size: 13px;
      .use-var(color, primary-color);
      .use-dark-mode-query({
      color: var(--dark-primary-color) !important;
    });
    }
  }

  &-content {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
    background-color: #f7f7f7;
    .use-var(background-color, background-color);
    .use-dark-mode-query({
    background-color: var(--dark-cell-background-color) !important;
  });
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    max-height: 250px;
    overflow: auto;

    &-textarea {
      border: none;
      min-height: 140px;
      margin: 0px;
      width: 100%;
      display: block;
      padding: 0px;
      font-size: 13px;
      color: #333333;
      .use-var(color, font-color);
      .use-dark-mode-query({
      color: @dark-font-color;
    });
      background-color: var(--page-primary-background-color);
      .use-dark-mode-query({
      background-color: var(--dark-cell-background-color) !important;
    });
      word-break: break-all;
      white-space: pre-wrap;
    }
  }

  &-bottom {
    // margin: 7px 15px 15px 15px;
    padding: 16px;
    align-items: center;
    display: flex;
    justify-content: center;

    &-btn {
      width: 100%;
      height: 41px;
      border-radius: 6px;
      //background-color: #6cbe70;
      color: #ffffff;
      font-size: 16px;
      border: none;

      &-text {
        font-size: 16px;
        color: #ffffff;
      }
    }
  }
}

// 自定义对话框样式
.custom-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999999;

  .custom-dialog {
    background-color: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    max-width: 320px;
    width: 90%;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    .use-var(background-color, background-color);
    .use-dark-mode-query({
      background-color: @dark-background-color !important;
    });

    .custom-dialog-header {
      padding: 20px 20px 10px 20px;
      text-align: center;

      .custom-dialog-title {
        font-size: 18px;
        font-weight: 600;
        color: #333333;
        .use-var(color, font-color);
        .use-dark-mode-query({
          color: @dark-font-color;
        });
      }
    }

    .custom-dialog-content {
      padding: 10px 20px 20px 20px;
      display: flex;
      justify-content: center;
      align-items: center;

      .custom-dialog-image {
        width: 100%;
        max-width: 280px;
        height: auto;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }

    .custom-dialog-footer {
      border-top: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-top: 1px solid @dark-line-color;
    });

      .custom-dialog-btn {
        display: block;
        padding: 16px;
        text-align: center;
        color: var(--primary-color);
        font-size: 16px;
        font-weight: 500;
        background: none;
        border: none;
        cursor: pointer;

        &:active {
          background-color: rgba(24, 144, 255, 0.1);
        }
      }
    }
  }
}
