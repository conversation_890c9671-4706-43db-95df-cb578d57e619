import React from 'react';
import { View, Text } from "@tarojs/components";
import './index.less';

interface SelectQuantityPopupProps {
  /** 控制弹窗显示/隐藏 */
  visible: boolean;
  /** 关闭弹窗的回调 */
  onClose: () => void;
  /** 选择自定义数量的回调 */
  onSelectCustom: () => void;
  /** 选择全部的回调 */
  onSelectAll: () => void;
  /** 总数量，用于显示提示信息（可选） */
  totalCount?: number;
}

const SelectQuantityPopup: React.FC<SelectQuantityPopupProps> = ({
  visible,
  onClose,
  onSelectCustom,
  onSelectAll,
  totalCount
}) => {
  if (!visible) return null;

  return (
    <View
      className="select-quantity-popup-overlay"
      onClick={onClose}
    >
      <View
        className="select-quantity-popup-container"
        onClick={(e) => e.stopPropagation()}
      >
        <View className="select-quantity-popup-content">
          <View
            className="select-quantity-option"
            onClick={() => {
              onClose();
              onSelectCustom();
            }}
          >
            <Text className="select-quantity-option-text">自定义数量</Text>
          </View>
          <View
            className="select-quantity-option"
            onClick={() => {
              onClose();
              onSelectAll();
            }}
          >
            <Text className="select-quantity-option-text">全部</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default SelectQuantityPopup;
