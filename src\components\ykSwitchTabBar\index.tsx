import { View, Text } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import { TabBar } from "@arco-design/mobile-react";
import React from "react";
import {
    IconUser,
    IconImage,
    IconUserGroup,
    IconDesktop,
    IconNotification
  } from "@arco-iconbox/react-yk-arco";
import Taro from "@tarojs/taro";
export default function YkSwitchTabBar({ activeTab, ...props }) {
  const tabs = [
    {
      title: "相册",
      icon: <IconImage className={activeTab == 0 ? "activeTab" : "ykTabsIconPrimary"} />,
      url: "/pages/index/index",
    },
    {
      title: "好友",
      icon: <IconUserGroup className={activeTab == 1 ? "activeTab" : "ykTabsIconPrimary"} />,
      url: "/pages/friends/index",
    },
    {
      title: "工作台",
      icon: <IconDesktop  className={activeTab == 2 ? "activeTab" : "ykTabsIconPrimary"} />,
      url: "/pages/control/index",
    },
    // {
    //   title: "消息",
    //   icon: <IconMessage className={activeTab == 3 ? "activeTab" : "ykTabsIconPrimary"} />,
    //   url: "/pages/message/index",
    // },
    {
      title: "我的",
      icon: <IconUser className={activeTab == 3 ? "activeTab" : "ykTabsIconPrimary"} />,
      url: "/pages/my/index",
    },
  ];
  const [httpPlatform, setHttpPlatform] = React.useState("H5");

  useLoad(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setHttpPlatform("Android");
    } else if (isIos) {
      setHttpPlatform("IOS");
    } else if (isHM) {
      setHttpPlatform("HM");
    } else {
      setHttpPlatform("H5");
    }
  });
   const onTabBarClick = (tab: any) => {
      Taro.switchTab({
        url: tab.url,
      });
    };
    
  return (
    <TabBar fixed activeIndex={activeTab} className={httpPlatform == 'H5' ? 'tabBarBox' : 'tabBarBox tabBarPaddingbottom'}>
      {tabs.map((tab, index) => (
        <TabBar.Item
          title={tab.title}
          icon={tab.icon}
          key={index}
          onClick={() => onTabBarClick(tab)}
        />
      ))}
    </TabBar>
  );
}
