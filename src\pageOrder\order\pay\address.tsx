import React, { useState, useEffect } from 'react';
import { View, Text, Input } from '@tarojs/components';
import { Button, Cell } from '@arco-design/mobile-react';
import YkNavBar from '@/components/ykNavBar';
import YkAreaPicker from '@/components/ykAreaPicker';
import './address.less';
import { addAddress } from '@/utils/api/common/common_user';
import { toast } from "@/utils/yk-common";
import Taro from "@tarojs/taro";
import { useRef } from "react";
const AddressPage = () => {
  const [name, setName] = useState('');
  const [mobile, setMobile] = useState('');
  const [detailAddress, setDetailAddress] = useState('');
  const [areaPickerVisible, setAreaPickerVisible] = React.useState(false);
  const [pickerValue, setPickerValue] = React.useState<string[]>([]);
  const [areaId, setAreaId] = useState('');
  const [areaName, setAreaName] = useState('');
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  // 使用 YkAreaPicker 组件，不再需要单独的地区数据

  // 初始化地址信息
  useEffect(() => {
    // 从URL参数获取地址信息
    const router = Taro.getCurrentInstance().router;
    const params = router?.params || {};

    console.log('添加地址页面获取到的URL参数:', params);
    console.log('添加地址页面获取到的URL参数:', params.areaName);
    console.log('添加地址页面获取到的URL参数:', decodeURIComponent(params.areaName || ''));

    if (params.name || params.mobile || params.detailAddress || params.areaId || params.areaName) {
      setName(decodeURIComponent(params.name || ''));
      setMobile(decodeURIComponent(params.mobile || ''));
      setDetailAddress(decodeURIComponent(params.detailAddress || ''));
      setAreaId(params.areaId || '');
      setAreaName(decodeURIComponent(params.areaName || ''));
      console.log("params.areaName",params.areaName);

      // 处理地区信息
        // const areaString = decodeURIComponent(params.area);
        // const areaArray = areaString.split('-');
        if(params.areaName){
          setPickerValue(decodeURIComponent(params.areaName || '').split(' '));
        }
        // console.log('设置地区选择器值:', areaArray);
    }
  }, []);

  const addAddressData = () => {
    // 必填字段验证
    if (!name || name.trim() === '') {
      toast("error", {
        content: '请输入姓名',
        duration: 2000,
      });
      return;
    }

    if (!mobile || mobile.trim() === '') {
      toast("error", {
        content: '请输入手机号',
        duration: 2000,
      });
      return;
    }

    // 手机号格式验证
    const phoneReg = /^1\d{10}$/;
    if (!phoneReg.test(mobile)) {
      toast("error", {
        content: '请输入正确的手机号格式',
        duration: 2000,
      });
      return;
    }

    if (!pickerValue || pickerValue.length === 0 || pickerValue.some(item => !item)) {
      toast("error", {
        content: '请选择所在地区',
        duration: 2000,
      });
      return;
    }

    if (!detailAddress || detailAddress.trim() === '') {
      toast("error", {
        content: '请输入详细地址',
        duration: 2000,
      });
      return;
    }

    const data = {
      userId:Taro.getStorageSync('userInfo').id,
      name: name.trim(),
      mobile: mobile.trim(),
      areaId:areaId,
      // area: pickerValue.join('-'),
      areaName: areaName,
      defaultStatus: true,
      detailAddress: detailAddress.trim(),
    }
    console.log('准备保存的地址数据:', data);
    addAddress(data).then(res => {
      console.log('添加地址接口响应:', res);
      if(res && res.code == 0){
        console.log('地址添加成功，ID:', res.data);

        // 触发确认订单页面更新地址
        Taro.eventCenter.trigger('addressUpdated', {
          id: res.data,
          name: data.name,
          mobile: data.mobile, // 修改为 mobile 以匹配接收端
          areaId: data.areaId,
          areaName: data.areaName, // 使用兼容性更好的 replace 方法
          // area: data.area,
          detailAddress: data.detailAddress
        });

        toast("success", {
          content: '添加成功',
          duration: 500,
        })
        setTimeout(() => {
          Taro.navigateBack();
        }, 500);
      }
    })
  }

  // 处理地区选择确认
  const handleAreaConfirm = (value: { province: string; city: string; area: string; areaId: string }) => {
    const areaArray = [value.province, value.city, value.area];
    setPickerValue(areaArray);

    // 设置显示的地区名称
    const areaDisplayName = areaArray.filter(item => item && item.trim() !== '').join(' ');
    setAreaName(areaDisplayName);

    // 设置地区 ID
    setAreaId(value.areaId);

    console.log('地区选择确认:', areaArray, '显示名称:', areaDisplayName, '地区ID:', value.areaId);
  };

  return (
    <View className="address-page">
      {platform !== "WX" &&<YkNavBar title="添加地址" />}
      <View className="address-section-title">收货信息</View>
      <View className="address-form">
        <Cell label={<Text className="address-label" style={{paddingRight: '26px'}}>姓名</Text>} bordered={false}>
          <Input
            className="address-input"
            placeholder="请输入姓名"
            value={name}
            onInput={e => setName(e.detail.value)}
            maxlength={20}
          />
        </Cell>
        <Cell label={<Text className="address-label">手机号码</Text>} bordered={false}>
          <Input
            className="address-input"
            placeholder="请输入手机号"
            value={mobile}
            onInput={e => setMobile(e.detail.value)}
            maxlength={11}
          />
        </Cell>
        <Cell label="所在地区" showArrow onClick={() => setAreaPickerVisible(true)}>
            <View className="address-input address-region-value">
                {areaName || <Text className="address-placeholder">省/市/区/县</Text>}
            </View>
        </Cell>
        
        <Cell label={<Text className="address-label">详细地址</Text>} bordered={false}>
          <Input
            className="address-input"
            placeholder="小区楼栋/乡村名称"
            value={detailAddress}
            onInput={e => setDetailAddress(e.detail.value)}
            maxlength={50}
          />
        </Cell>
      </View>
      <View className="address-bottom-bar">
        <Button className="address-btn" type="primary"
        onClick={() => {
          addAddressData()
        }}
        >确定</Button>
      </View>

      {/* 地区选择器 */}
      <YkAreaPicker
        visible={areaPickerVisible}
        onClose={() => setAreaPickerVisible(false)}
        onConfirm={handleAreaConfirm}
        currentValue={pickerValue}
      />
    </View>
  );
};

export default AddressPage;
