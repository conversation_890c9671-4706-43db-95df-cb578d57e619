@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageDynamic/moveMaterials/collectLink/index"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;  
  });

  .collect-link-page {
    min-height: 100vh;
    background: #fff;
    display: flex;
    flex-direction: column;
    position: relative;
    .use-dark-mode-query({
      background-color: @dark-background-color;     //黑色背景
    });
  }
  .collect-link-navbar {
    display: flex;
    align-items: center;
    background: #fff;
    border-bottom: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-bottom: 1px solid @dark-line-color;
  });
    .use-dark-mode-query({
      background-color: @dark-background-color;     //黑色背景
    });
  }
  .collect-link-back {
    font-size: 22px;
    color: #222;
    margin-left: 12px;
    cursor: pointer;
  }
  .collect-link-input-wrap {
    padding: 16px;
    background: #fff;
    .use-dark-mode-query({
      background-color: @dark-background-color;     //黑色背景
    });
  }
  
  .collect-link-textarea-container {
    position: relative;
    background: #f7f8fa;
    border-radius: 8px;
    padding: 12px;
    padding-bottom: 40px;
    display: flex;
    flex-direction: column;
    .use-dark-mode-query({
      background: @dark-container-background-color;
    });
  }
  
  .collect-link-textarea {
    width: 100%;
    background: transparent;
    border: none;
    font-size: 15px;
    color: #222;
    padding: 0;
    box-sizing: border-box;
    min-height: 72px; // 约3行高度
    overflow-y: auto; // 超出时显示滚动条
    word-wrap: break-word; // 长单词自动换行
    word-break: break-all; // 允许在任意字符间断行
    resize: none;
    .use-dark-mode-query({
      color: var(--dark-font-color);
    });
  }
  
  .collect-link-action-btn {
    position: absolute;
    bottom: 12px;
    right: 12px;
    font-size: 15px;
    color: #165dff;
    cursor: pointer;
    padding: 4px 8px;
    background: transparent;
    white-space: nowrap; // 防止文字换行
    z-index: 10; // 确保按钮在最上层
    .use-dark-mode-query({
      color: #165dff;
    });
  }
  
  .collect-link-hint {
    text-align: center;
    padding: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .collect-link-hint-text {
    font-size: 13px;
    color: #999;
    line-height: 1.6;
    .use-dark-mode-query({
      color: #666;
    });
  }
  .collect-link-input {
    width: 78%;
    height: 40px;
    display: flex;
    align-items: center;
    background: #f7f8fa;
    border-radius: 8px;
    border: none;
    font-size: 15px;
    color: #222;
    padding: 0 12px;
    box-sizing: border-box;
    float: left;
    .use-dark-mode-query({
      background: @dark-container-background-color;
      color: var(--dark-font-color);
    });
  }
  .collect-link-list {
    margin-top: 8px;
    .use-dark-mode-query({
      background-color: @dark-background-color;     //黑色背景
    });
  }
  .collect-link-item {
    display: flex;
    align-items: flex-start;
    padding: 16px;
    background: #fff;
    border-bottom: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-bottom: 1px solid @dark-line-color;
  });
    cursor: pointer;
    transition: background 0.2s;
    &.selected {
      background: #f5faff;
    }
    .use-dark-mode-query({
      background-color: @dark-background-color;     //黑色背景
    });
  }
  .collect-link-icon {
    font-size: 20px;
    color: #165dff;
    margin-right: 10px;
    margin-top: 2px;
  }
  .collect-link-info {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  .collect-link-title {
    font-size: 16px;
    color: #222;
    margin-bottom: 2px;
    .use-dark-mode-query({
      color: var(--dark-font-color);
    });
  }
  .collect-link-url {
    font-size: 13px;
    color: #8c8c8c;
    word-break: break-all;
  }
  .collect-link-more {
    font-size: 22px;
    color: #c9cdd4;
    margin-left: 8px;
  }
  .collect-link-tip {
    color: #bfc3c9;
    font-size: 14px;
    text-align: center;
    display: block;
  }
  .collect-link-btn {
    width: 20%;

    height: 40px;
    background: #165dff;
    color: #fff;
    border-radius: 8px;
    font-size: 15px;
    border: none;
    padding: 0;
    display: block;
    //position: fixed;
    //left: 4vw;
    //bottom: 24px;
    //opacity: 0.7;
    cursor: pointer;
    &.enabled,
    &:not([disabled]) {
      opacity: 1;
    }
  }

 


  .collect-link-confirm-mask {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .collect-link-confirm-modal {
    background: #fff;
    border-radius: 12px;
    width: 80vw;
    max-width: 320px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
    padding: 24px 0 0 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    .use-dark-mode-query({
      background: @dark-container-background-color;
      color: var(--dark-font-color);
    });
  }
  .collect-link-confirm-title {
    font-size: 18px;
    font-weight: bold;
    color: #222;
    text-align: center;
    margin-bottom: 12px;
    .use-dark-mode-query({
      color: var(--dark-font-color);
    });
  }
  .collect-link-confirm-content {
    font-size: 15px;
    color: #666;
    text-align: center;
    margin-bottom: 18px;
    line-height: 1.5;
    .use-dark-mode-query({
      color: var(--dark-font-color);
    });
  }
  .collect-link-confirm-actions {
    display: flex;
    width: 100%;
    border-top: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-top: 1px solid @dark-line-color;
  });
  }
  .collect-link-confirm-cancel {
    flex: 1;
    text-align: center;
    padding: 16px 0;
    color: var(--primary-color);
    font-size: 17px;
    border-right: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-right: 1px solid @dark-line-color;
  });
    cursor: pointer;
  }
  .collect-link-confirm-delete {
    flex: 1;
    text-align: center;
    padding: 16px 0;
    color: var(--primary-color);
    font-size: 17px;
    cursor: pointer;
  }

  .user-header-avatar {
    width: 32px;
    height: 32px;
    border-radius: 12px;
    margin-right: 12px;
    margin-top: 8px;
  }

  .user-header-send {
    color: var(--primary-color);
    font-size: 15px;
    margin-left: 8px;
    cursor: pointer;
  }
}

