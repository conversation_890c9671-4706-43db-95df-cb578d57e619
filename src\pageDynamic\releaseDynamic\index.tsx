import { View, Text } from '@tarojs/components'
import { Button, Image, Input, Textarea, Dialog } from '@arco-design/mobile-react'
import wx from "weixin-webview-jssdk";
import './index.less'
import Taro,{useDidShow} from '@tarojs/taro'
import YkNavBar from '@/components/ykNavBar'
import { useState, useRef, useEffect } from 'react'
import { getFreightTemplateAll, releaseAlbum, dynamicDetails, updateDynamic, uploadFile, getUserHomeTopData } from '@/utils/api/common/common_user'
import { toast } from "@/utils/yk-common"
import BottomPopup from "@/components/BottomPopup"
import PermissionPopup from "@/components/PermissionPopup"
import { AuthTypes } from "@/utils/config/authTypes"
import { usePermission } from "@/hooks/usePermission";
import { IconRight } from '@arco-iconbox/react-yk-arco'
import { isMemberValid } from '@/pages/my/utils'
export default function ReleaseDynamic() {
  const [dynamicTitle, setDynamicTitle] = useState('')
  const [price, setPrice] = useState('')
  const [tag, setTag] = useState('通过标签给图文分类')
  const [formatColor, setFormatColor] = useState('')
  const [ems, setEms] = useState('无')
  const [shortText, setShortText] = useState('')
  const [imageList, setImageList] = useState<any[]>([])
  const [dragIndex, setDragIndex] = useState<number | null>(null)
  const [dropIndex, setDropIndex] = useState<number | null>(null)
  const [, setIsDragging] = useState(false)
  const [type, setType] = useState(1)
  const [checkTagList] = useState<any[]>([])
  const [checkFormatList] = useState<any[]>([])
  const [checkColorList] = useState<any[]>([])
  const [sourceName, setSourceName] = useState('')
  const [sourceId, setSourceId] = useState('')
  const [dynamicId, setDynamicId] = useState('')
  const [isShowFooter] = useState(true)
  const [paddingBottom] = useState('0rpx')
  const [tags, setTags] = useState<any[]>([])
  const [formats, setFormats] = useState<any[]>([])
  const [colors, setColors] = useState<any[]>([])
  const [freightTemplateList, setFreightTemplateList] = useState<any[]>([])
  const [selectedFreightTemplate, setSelectedFreightTemplate] = useState<any>(null)
  const [showFreightTemplateModal, setShowFreightTemplateModal] = useState(false)
  const [editFreightTemplateId, setEditFreightTemplateId] = useState<number | null>(null)

  // 弹框相关状态
  const [isPopupVisible, setPopupVisible] = useState(false)
  // 拖拽相关的ref
  const dragItem = useRef<number | null>(null)
  const dragOverItem = useRef<number | null>(null)

  const openVip = ()=>{
    Dialog.confirm({
      title: "提示",
      children: "请先开通会员",
      okText: "去开通",
      cancelText: "取消",
      platform: 'ios',
      onOk: () => {
        if (platformRef.current === "WX") {
          wx.miniProgram.navigateTo({ url: "/pages/web?path=/pageVip/vipCenter/index" });
        } else {
          Taro.navigateTo({ url: "/pageVip/vipCenter/index" });
        }
      }
    })
  }

    // 选择图片的方法
    const chooseImage = (sourceType: "album" | "camera") => {
      const count = 9 - imageList.length;
      if(platformRef.current === "Android"){
        window.setPhotoNum?.setPhotoNum(count);
      }
      if(platformRef.current === "HM"){
        window.harmony.setPhotoNum(count);
      }
      Taro.chooseImage({
        count,
        sizeType: ['original', 'compressed'],
        sourceType: [sourceType], // 'album' 为从相册选择，'camera' 为拍照
        success: async (res) => {
          console.log("选择图片成功", res.tempFiles);
  
          // 显示上传进度
          Taro.showLoading({
            title: '上传中...',
            mask: true
          });
  
          // 逐个上传图片，成功的保留，失败的跳过
          const uploadedImages: any[] = [];
  
          for (let i = 0; i < res.tempFiles.length; i++) {
            const file = res.tempFiles[i];
            try {
              const uploadRes: any = await uploadFile(file.path);
              if (uploadRes && uploadRes.code === 0) {
                const imageData = {
                  url: uploadRes.data, // 服务器返回的URL
                  path: file.path,     // 本地路径用于预览
                  size: file.size,
                  type: file.type || 'image/jpeg'
                };
                uploadedImages.push(imageData);
                console.log(`图片 ${i + 1} 上传成功:`, imageData);
              } else {
                console.error(`图片 ${i + 1} 上传失败:`, uploadRes);
              }
            } catch (error) {
              console.error(`图片 ${i + 1} 上传异常:`, error);
            }
          }
  
          // 添加成功上传的图片到列表
          if (uploadedImages.length > 0) {
            setImageList(prevList => [...prevList, ...uploadedImages]);
          }
  
          Taro.hideLoading();
        },
        fail: (err) => {
          console.error("选择图片失败", err);
        },
      });
    };

  // 自定义权限同意处理
  const customWebPermissonConsent = () => {
    console.log("releaseDynamic customWebPermissonConsent");
    console.log("authType:", authConfirmType.current);

    // 根据权限类型执行不同的后续处理
    if (authConfirmType.current === AuthTypes.CAMERA) {
      // 拍照
      chooseImage("camera");
    } else if (authConfirmType.current === AuthTypes.GALLERY_PHOTO) {
      // 从相册选择
      chooseImage("album");
    }

    return true;
  };

  const {
    initPermissions,
    hasPermission,
    requestPermission,
    permissionPopupProps,
    platformRef,
    authConfirmType,
  } = usePermission(customWebPermissonConsent);

  useDidShow(() => {
    const selectedArticles = Taro.getStorageSync('selectedArticles')
    const selectedFormats = Taro.getStorageSync('selectedFormats')
    const selectedColors = Taro.getStorageSync('selectedColors')
    if (selectedArticles && selectedArticles.length > 0) {
      setTags(selectedArticles)
    }else{
      setTags([])
    }
    if (selectedFormats && selectedFormats.length > 0) {
      setFormats(selectedFormats)
    }else{
      setFormats([])
    }
    if (selectedColors && selectedColors.length > 0) {
      setColors(selectedColors)
    }else{
      setColors([])
    }
  });

  useEffect(() => {
    // 初始化权限管理
    const cleanup = initPermissions();

    // 初始化页面
    // setTimeout(() => {
    //   setShowPage(true)
    // }, 200)
    // 获取页面参数
    Taro.setStorageSync('selectedArticles',[])
    Taro.setStorageSync('selectedFormats',[])
    Taro.setStorageSync('selectedColors',[])
    setTags([])
    setFormats([])
    setColors([])
    const params = Taro.getCurrentInstance().router?.params
    console.log('params', params)

    if (params?.type) {
      setType(Number(params.type))
      if (params.type === '2' || params.type === '4') {
        const releaseDynamicList = Taro.getStorageSync('releaseDynamicList')

        // 原有的动态详情获取逻辑
        dynamicDetails({ id: releaseDynamicList.id, userId: releaseDynamicList.userId }).then(async (res: any) => {
          if (res && res.code === 0) {
            setDynamicTitle(res.data.content)
            setDynamicId(res.data.id)
            
            // 设置来源信息
            // 对于转发操作(type=4)，sourceId应该是被转发动态的作者userId
            // 对于编辑操作(type=2)，保持原有的source信息
            if (params.type === '4') {
              // 转发：设置为被转发动态的作者userId
              setSourceId(res.data.userId);
            } else {
              // 编辑：保持原有的source信息
              setSourceId(res.data.source || res.data.userId);
            }
            
            // 获取来源用户昵称
            const currentUserInfo = Taro.getStorageSync("userInfo");
            const targetUserId = params.type === '4' ? res.data.userId : (res.data.source || res.data.userId);
            
            if (targetUserId && targetUserId != currentUserInfo.id) {
              // 如果是他人动态，通过接口获取用户昵称
              try {
                const sourceUserRes = await getUserHomeTopData({ userId: targetUserId, homePageCountType: 2 });
                if (sourceUserRes && sourceUserRes.code === 0 && sourceUserRes.data) {
                  setSourceName(sourceUserRes.data.nickname || '未知用户');
                  console.log('获取来源用户信息:', {
                    userId: targetUserId,
                    nickname: sourceUserRes.data.nickname,
                    type: params.type
                  });
                } else {
                  setSourceName(res.data.nickname || '未知用户');
                }
              } catch (error) {
                console.error('获取来源用户信息失败:', error);
                setSourceName(res.data.nickname || '未知用户');
              }
            } else {
              // 自己的动态
              setSourceName('我的相册');
            }
            
            setPrice((res.data.price / 100).toString() || '')

            // 保存运费模板ID，用于后续回显
            if (res.data.freightTemplateId) {
              setEditFreightTemplateId(res.data.freightTemplateId)
            }

            setTag(res.data.labelAndCatalogueNames	 || '通过标签给图文分类')
            setFormatColor( [res.data.productSpecificationsNames,res.data.productColorNames].join(',').replace(/^,|,$/g, '') || '无'	)

            // 处理已选择的标签，用于回显
            if (res.data.labelAndCatalogueIds && res.data.labelAndCatalogueNames) {
              const tagIds = res.data.labelAndCatalogueIds.split(',');
              const tagNames = res.data.labelAndCatalogueNames.split(',');
              const selectedTags = tagIds.map((id, index) => ({
                id: id,
                name: tagNames[index] || ''
              })).filter(item => item.id && item.name);
              Taro.setStorageSync('selectedArticles', selectedTags);
              setTags(selectedTags);
            }

            // 处理已选择的规格和颜色，用于回显
            if (res.data.productSpecificationsIds && res.data.productSpecificationsNames) {
              const formatIds = res.data.productSpecificationsIds.split(',');
              const formatNames = res.data.productSpecificationsNames.split(',');
              const selectedFormats = formatIds.map((id, index) => ({
                id: id,
                name: formatNames[index] || ''
              })).filter(item => item.id && item.name);
              Taro.setStorageSync('selectedFormats', selectedFormats);
              setFormats(selectedFormats);
            }

            if (res.data.productColorIds && res.data.productColorNames) {
              const colorIds = res.data.productColorIds.split(',');
              const colorNames = res.data.productColorNames.split(',');
              const selectedColors = colorIds.map((id, index) => ({
                id: id,
                name: colorNames[index] || ''
              })).filter(item => item.id && item.name);
              Taro.setStorageSync('selectedColors', selectedColors);
              setColors(selectedColors);
            }

            // 只有当 pictures 不为空时才处理图片列表
            if (res.data.pictures && res.data.pictures.trim()) {
              setImageList(res.data.pictures.split(',').map(item => ({ url: item })))
            } else {
              setImageList([])
            }
          }
        })
      }
    }

    if (params?.isFirstShare === 'true') {
      const releaseDynamicList = Taro.getStorageSync('releaseDynamicList')
       // 检查是否是首图分享
       if (params?.isFirstShare === 'true' && releaseDynamicList?.items?.[0]) {
        const combinedItem = releaseDynamicList.items[0];
        setDynamicTitle(combinedItem.content || '');

        // 处理合成的图片
        if (combinedItem.pictures) {
          // const imageUrls = combinedItem.pictures.split(',').filter((img: string) => img.trim() !== '');
          setImageList(combinedItem.pictures.split(',').map(item => ({ url: item })))
          // setImageList(imageUrls);
        }

            return;
          }
    }

    // 获取运费模板列表
    const userInfo = Taro.getStorageSync('userInfo')
    if (userInfo?.id) {
      getFreightTemplateAll({ userId: userInfo.id }).then((res: any) => {
        if (res && res.code === 0) {
          let templateList = res.data?.list || []
          setFreightTemplateList(templateList)

          // 如果是编辑模式且有运费模板ID，优先设置编辑的模板
          if ((params?.type === '2' || params?.type === '4') && editFreightTemplateId) {
            const editTemplate = templateList.find(item => item.id === editFreightTemplateId)
            if (editTemplate) {
              setSelectedFreightTemplate(editTemplate)
              setEms(editTemplate.name)
              return // 直接返回，不执行默认设置逻辑
            }
          }

          // 设置默认选中的模板（仅在非编辑模式或编辑模式但没有找到对应模板时）
          const defaultTemplate = templateList.find(item => item.isDefault === 1)
          if (defaultTemplate) {
            setSelectedFreightTemplate(defaultTemplate)
            setEms(defaultTemplate.name)
          } else if (templateList.length > 0) {
            setSelectedFreightTemplate(templateList[0])
            setEms(templateList[0].name)
          }
        }
      }).catch((error) => {
        console.error('获取运费模板失败', error)
        setFreightTemplateList([])
      })
    }

    
    return () => {
      cleanup && cleanup();
    };
  }, [editFreightTemplateId])

//   const recordPositionalParameter = (list, type) => {
//     for (let i = 0; i < list.length; i++) {
//       const x = (165 + (i % 4 === 0 ? 0 : 10)) * (i % 4)
//       const y = (165 + (i < 4 ? 0 : 10)) * Math.floor(i / 4)
//       list[i].xy = [Taro.pxTransform(x), Taro.pxTransform(y)]
//     }
//     setImageList(list)
//     if (type === 1) {
//       setTimeout(() => {
//         getdownloadFile()
//       }, 800)
//     }
//   }

//   const getdownloadFile = () => {
//     const list = [...imageList]
//     for (let i = 0; i < list.length; i++) {
//       Taro.downloadFile({
//         url: list[i].path,
//         success: (res) => {
//           if (res.statusCode === 200) {
//             list[i].path = res.tempFilePath
//             setImageList(list)
//           }
//         }
//       })
//     }
//   }

  const handlePriceInput = (_e: any, value: string) => {
    // 移除非数字字符和多余的小数点
    let sanitizedValue = value.replace(/[^\d.]/g, '').replace(/^(\d*\.\d{2}).*$/, '$1')
    
    // 将输入值限制在 6 位整数，两位小数点
    let [integerPart, decimalPart] = sanitizedValue.split('.')
    if (integerPart && integerPart.length > 6) {
      integerPart = integerPart.slice(0, 6)
    }
    if (decimalPart && decimalPart.length > 2) {
      decimalPart = decimalPart.slice(0, 2)
    }
    setPrice(decimalPart ? `${integerPart}.${decimalPart}` : integerPart)
  }

  const handleSave = async () => {
    if(!isMemberValid(Taro.getStorageSync("userInfo"))){
      openVip();
      return;
    }
    if (dynamicTitle.trim().length === 0 && imageList.length === 0) {
      toast("info", {
        content: "图片跟描述至少填写一个",
        duration: 2000,
      });
      return
    }

    if (price !== '' && Number(price) > 999999.99) {
      toast("info", {
        content: "最多输入六位整数（包括两位小数）",
        duration: 2000,
      });
      return
    }

    const formData: any = {
      content: dynamicTitle,
      price: price === '' ? '0' : Number(price) * 100,
      freightTemplateId: selectedFreightTemplate?.id || null,
      goodsShortName: shortText || dynamicTitle
    }

    console.log('111',imageList)


    if(tags.length > 0) {
      formData.labelAndCatalogueIds = tags.map(item => item.id).join(',')
    }else{
      formData.labelAndCatalogueIds = ''
    }
    if(formats.length > 0) {
      formData.productSpecificationsIds = formats.map(item => item.id).join(',')
    }else{
      formData.productSpecificationsIds = ''
    }
    if(colors.length > 0) {
      formData.productColorIds = colors.map(item => item.id).join(',')
    }else{
      formData.productColorIds = ''
    } 

    if (type === 2) {
      formData.id = dynamicId
    }
    const userInfo = Taro.getStorageSync('userInfo')

    formData.userId = userInfo.id
    
    // 处理转发他人动态的来源信息
    if (type === 4) {
      // 转发：如果是他人动态，设置source为被转发动态的userId
      if (sourceId && sourceId != userInfo.id) {
        formData.source = sourceId; // 被转发动态的userId（上一个转发人或原作者）
        
        console.log('设置转发来源信息:', {
          source: formData.source,
          sourceName: sourceName
        });
      }
      // 如果是转发自己的动态，不设置source字段（表示来源自"我的相册"）
    }

    if (checkFormatList.length > 0) {
      //wait fix
      formData.size = checkFormatList.map(item => item.name).join(',')
    }

    if (checkColorList.length > 0) {
      //wait fix
      formData.color = checkColorList.map(item => item.name).join(',')
    }

    if (checkTagList.length > 0) {
      const tagIdArray = checkTagList.filter(item => item.tId !== '-1').map(item => item.tId)
      if (tagIdArray.length > 0) {
        //wait fix
        formData.label_ids = tagIdArray.join(',')
      }

      const tagNameArray = checkTagList.filter(item => item.tId === '-1').map(item => item.name)
      if (tagNameArray.length > 0) {
        //wait fix
        formData.label_names = tagNameArray.join(',')
      }
    }
    if(imageList.length>0){
      let pictures=''
      imageList.map(item => {
             const  url = item.data ?  item.data : item.url 
             pictures=pictures+url+',';
      })
      formData.pictures = pictures.replace(/^,+|,+$/g, '');
    }else{
      formData.pictures=''
    }
    
    
    // loadingRef.current?.show()
    
    if (type === 4) {
      releaseAlbum(formData).then((res: any) => {
        console.log('type', type)
        // loadingRef.current?.hide()
        if (res && res.code === 0) {
          toast("success", {
            content: "转发成功",
            duration: 800
          });

          // 通知首页刷新列表
          Taro.eventCenter.trigger('refreshAlbumList');

          Taro.removeStorageSync('releaseDynamicList')
          setTimeout(() => {
            Taro.navigateBack({
              delta: 1
            })
          }, 800) // 进一步减少延迟时间到800ms
        } else {
          toast("error", {
            content: res.message || "转发失败",
            duration: 2000,
          });
        }
      }).catch((error) => {
        console.error('转发失败:', error);
        toast("error", {
          content: "转发失败，请重试",
          duration: 2000
        });
      });
    } else if (type == 2) {
      updateDynamic(formData).then((res: any) => {
        // loadingRef.current?.hide()
        if (res && res.code === 0) {
          toast("success", {
        content: "编辑成功",
        duration: 800
      });

          // 通知首页刷新列表
          Taro.eventCenter.trigger('refreshAlbumList');

          if (type === 2 || type === 3) {
            Taro.removeStorageSync('releaseDynamicList')
            setTimeout(() => {
              Taro.navigateBack({
                delta: 1
              })
            }, 800) // 进一步减少延迟时间到800ms
          }
          //  else {
          //   setTimeout(() => {
          //     Taro.switchTab({
          //       url: '/pages/album/index'
          //     })
          //   }, 800) // 进一步减少延迟时间到800ms
          // }
        } else {
          toast("error", {
            content: res.message,
            duration: 2000,
          });
        }
      })

    } else {
      releaseAlbum(formData).then((res: any) => {
        console.log('type', type)
        // loadingRef.current?.hide()
        if (res && res.code === 0) {
          toast("success", {
        content: "发布成功",
        duration: 800
      });

          // 通知首页刷新列表
          Taro.eventCenter.trigger('refreshAlbumList');

          // if (type === 2 || type === 3) {
            Taro.removeStorageSync('releaseDynamicList')
            setTimeout(() => {
              Taro.navigateBack({
                delta: 1
              })
            }, 800) // 进一步减少延迟时间到800ms
          // } else {
          //   setTimeout(() => {
          //     Taro.switchTab({
          //       url: '/pages/album/index'
          //     })
          //   }, 800) // 进一步减少延迟时间到800ms
          // }
        } else {
          toast("error", {
            content: res.message,
            duration: 2000,
          });
        }
      })
    }
  }

  // 打开选择图片弹框
  const handleChooseImage = () => {
    if (platformRef.current === "WX") {
      chooseImage("album");
      return;
    }
    setPopupVisible(true);
  };

  // 关闭弹框
  const handleClose = () => {
    setPopupVisible(false);
  };

  // 弹框确认选择
  const handleConfirm = (index: number) => {
    if (index === 0) {
      if(platformRef.current === "HM"){
        chooseImage("camera");
      }else{
      // 请求相机权限
      if (!hasPermission(AuthTypes.CAMERA)) {
        // 如果没有权限，请求权限
        requestPermission(AuthTypes.CAMERA);
        return;
      }
      chooseImage("camera");
      }
    } else if (index === 1) {
      if(platformRef.current === "HM"){
        chooseImage("album");
      }else{
      // 请求相册权限
      if (!hasPermission(AuthTypes.GALLERY_PHOTO)) {
        // 如果没有权限，请求权限
        requestPermission(AuthTypes.GALLERY_PHOTO);
        return;
      }
      chooseImage("album");
      }
    }
  };



  const handleDeleteImage = (index) => {
    const newList = [...imageList]
    newList.splice(index, 1)
    setImageList(newList)
  }

  // 拖拽开始（桌面端）
  const handleDragStart = (e: any, index: number) => {
    console.log('桌面端拖拽开始:', index)
    dragItem.current = index
    setDragIndex(index)
    setIsDragging(true)
    // 设置拖拽效果
    e.dataTransfer.effectAllowed = 'move'
    // 设置拖拽时的图片
    const dragImage = e.currentTarget.querySelector('.image-preview') as HTMLElement
    if (dragImage) {
      e.dataTransfer.setDragImage(dragImage, 50, 50)
    }
  }

  // 拖拽经过
  const handleDragOver = (e: any) => {
    e.preventDefault()
    e.stopPropagation()
    e.dataTransfer.dropEffect = 'move'
  }

  // 进入拖拽区域
  const handleDragEnter = (e: any, index: number) => {
    e.preventDefault()
    e.stopPropagation()
    if (dragItem.current !== null && dragItem.current !== index) {
      dragOverItem.current = index
      setDropIndex(index)
    }
  }

  // 离开拖拽区域
  const handleDragLeave = (e: any) => {
    e.preventDefault()
    e.stopPropagation()
  }

  // 拖拽放下
  const handleDrop = (e: any, targetIndex: number) => {
    e.preventDefault()
    e.stopPropagation()
    
    console.log('桌面端拖拽放下:', { dragItem: dragItem.current, targetIndex })
    
    if (dragItem.current !== null && dragItem.current !== targetIndex) {
      const draggedIndex = dragItem.current
      
      console.log('桌面端执行交换:', { draggedIndex, targetIndex })
      
      const newList = [...imageList]
      const draggedItem = newList[draggedIndex]
      
      // 移除拖拽的元素
      newList.splice(draggedIndex, 1)
      // 在目标位置插入
      newList.splice(targetIndex, 0, draggedItem)
      
      setImageList(newList)
      console.log(`将第${draggedIndex}张图片移动到第${targetIndex}位`)
    } else {
      console.log('桌面端未执行交换')
    }
  }

  // 拖拽结束
  const handleDragEnd = (e: any) => {
    e.preventDefault()
    e.stopPropagation()
    
    // 重置所有状态
    setDragIndex(null)
    setDropIndex(null)
    setIsDragging(false)
    dragItem.current = null
    dragOverItem.current = null
  }

  // 触摸事件处理（移动端）
  const [touchStartPos, setTouchStartPos] = useState<{ x: number; y: number } | null>(null)
  const [currentTouchIndex, setCurrentTouchIndex] = useState<number | null>(null)
  const [touchOffset, setTouchOffset] = useState<{ x: number; y: number }>({ x: 0, y: 0 })
  const [isDragStarted, setIsDragStarted] = useState(false)
  const touchStartTime = useRef<number>(0)
  const imageGridRef = useRef<HTMLDivElement>(null)

  const handleTouchStart = (e: any, index: number) => {
    console.log('移动端触摸开始:', index)
    const touch = e.touches[0]
    touchStartTime.current = Date.now()
    setTouchStartPos({ x: touch.clientX, y: touch.clientY })
    setCurrentTouchIndex(index)
    setIsDragStarted(false)
    dragItem.current = index
  }

  const handleTouchMove = (e: any) => {
    if (!touchStartPos || currentTouchIndex === null) return
    
    const touch = e.touches[0]
    const offsetX = touch.clientX - touchStartPos.x
    const offsetY = touch.clientY - touchStartPos.y
    
    // 计算移动距离
    const moveDistance = Math.sqrt(offsetX * offsetX + offsetY * offsetY)
    const touchDuration = Date.now() - touchStartTime.current
    
    // 只有当移动距离超过10px或按住时间超过200ms时才开始拖拽
    if ((moveDistance > 10 || touchDuration > 200) && !isDragStarted) {
      console.log('开始拖拽:', { moveDistance, touchDuration, index: currentTouchIndex })
      setIsDragStarted(true)
      setDragIndex(currentTouchIndex)
      setIsDragging(true)
      // 确保 dragItem.current 不会被重置
      dragItem.current = currentTouchIndex
      
      // 震动反馈
      if (window.navigator?.vibrate) {
        window.navigator.vibrate(50)
      }
      
      // 阻止页面滚动
      document.body.style.overflow = 'hidden'
      // 移除 preventDefault 调用，因为是 passive listener
    }
    
    // 只有在拖拽状态下才处理移动
    if (isDragStarted) {
      setTouchOffset({ x: offsetX, y: offsetY })
      
      // 查找当前触摸位置下的元素
      const element = document.elementFromPoint(touch.clientX, touch.clientY)
      if (element) {
        const imageItem = element.closest('.image-item')
        if (imageItem) {
          const index = parseInt(imageItem.getAttribute('data-index') || '-1')
          if (index !== -1) {
            // 如果拖回原位置，清除目标位置
            if (index === currentTouchIndex) {
              console.log('拖回原位置:', index)
              dragOverItem.current = null
              setDropIndex(null)
            } else {
              console.log('拖拽到目标位置:', index)
              dragOverItem.current = index
              setDropIndex(index)
            }
          }
        } else {
          // 如果没有找到图片元素，也清除目标位置
          dragOverItem.current = null
          setDropIndex(null)
        }
      } else {
        // 如果没有找到任何元素，清除目标位置
        dragOverItem.current = null
        setDropIndex(null)
      }
    }
  }

  const handleTouchEnd = () => {
    console.log('触摸结束:', { 
      isDragStarted, 
      dragItem: dragItem.current, 
      dragOverItem: dragOverItem.current,
      currentTouchIndex 
    })
    
    // 恢复页面滚动
    document.body.style.overflow = ''
    
    // 只有在拖拽状态下才处理放下操作
    if (isDragStarted) {
      // 在触摸结束时，使用 currentTouchIndex 作为 dragItem
      const draggedIndex = currentTouchIndex
      const targetIndex = dragOverItem.current
      
      if (draggedIndex !== null && targetIndex !== null && draggedIndex !== targetIndex) {
        console.log('执行交换:', { draggedIndex, targetIndex })
        
        const newList = [...imageList]
        const draggedItem = newList[draggedIndex]
        
        newList.splice(draggedIndex, 1)
        newList.splice(targetIndex, 0, draggedItem)
        
        setImageList(newList)
        console.log(`将第${draggedIndex}张图片移动到第${targetIndex}位`)
      } else {
        console.log('未执行交换:', { 
          draggedIndex, 
          targetIndex,
          isDragStarted 
        })
      }
    }
    
    // 重置状态
    setTouchStartPos(null)
    setCurrentTouchIndex(null)
    setTouchOffset({ x: 0, y: 0 })
    setDragIndex(null)
    setDropIndex(null)
    setIsDragging(false)
    setIsDragStarted(false)
    dragItem.current = null
    dragOverItem.current = null
  }


  const selectTag = () => {
    Taro.navigateTo({
      url: '/pageDynamic/releaseDynamic/selectTag/index'
    })
  }

  const selectProductColor = () => {
    Taro.navigateTo({
      url: '/pageDynamic/releaseDynamic/selectProductColor/index'
    })
  }


  // 打开运费模板选择弹窗
  const openFreightTemplateActionSheet = () => {
    setShowFreightTemplateModal(true)
  }

  // 渲染运费规则描述
  const renderFreightRuleDescription = (template, isSelected) => {
    if (!template || !template.freightRulesRespVOList || template.freightRulesRespVOList.length === 0) {
      return (
        <Text className={`freight-rule-text ${isSelected ? 'selected' : ''}`}>
          固定运费8元
        </Text>
      )
    }

    const rule = template.freightRulesRespVOList[0] // 取第一个规则作为默认显示

    if (rule.freightType === 1) { // 包邮
      return (
        <Text className={`freight-rule-text ${isSelected ? 'selected' : ''}`}>
          包邮
        </Text>
      )
    } else if (rule.freightType === 2) { // 固定运费
      if (rule.isFreeShippingConditions === 1 && rule.freeShippingAmount > 0) {
        return (
          <View className='freight-rule-container'>
            <Text className={`freight-rule-main ${isSelected ? 'selected' : ''}`}>
              固定运费{rule.freightAmount}元
            </Text>
            <Text className={`freight-rule-sub ${isSelected ? 'selected' : ''}`}>
              满{rule.freeShippingAmount}元包邮
            </Text>
          </View>
        )
      } else {
        return (
          <Text className={`freight-rule-text ${isSelected ? 'selected' : ''}`}>
            固定运费{rule.freightAmount}元
          </Text>
        )
      }
    }

    return (
      <Text className={`freight-rule-text ${isSelected ? 'selected' : ''}`}>
        固定运费8元
      </Text>
    )
  }
  

  return (
    <View className='release-dynamic'>
      {platformRef.current !== "WX" &&<YkNavBar title={type === 2 ? '编辑动态' : type === 4 ? '转发动态' : '发布动态'} />}
      
      <View className='container'>
        <Textarea
          border='none'
          className='textarea'
          value={dynamicTitle}
          onChange={(_,value) => setDynamicTitle(value)}
          placeholder='发布我的新宝贝...'
          maxLength={3000}
        />

        <View className='image-tips'>
          <Text>长按并拖动图片可调整顺序</Text>
        </View>

        <View className='imagelist'>
          <div className={`image-grid ${isDragStarted ? 'dragging-active' : ''}`} ref={imageGridRef}>
            {imageList.map((item, index) => (
              <div
                key={index}
                data-index={index}
                className={`image-item ${
                  dragIndex === index ? 'dragging' : ''
                } ${
                  dropIndex === index && dragIndex !== null && dragIndex !== index ? 'drop-target' : ''
                }`}
                draggable
                onDragStart={(e) => handleDragStart(e, index)}
                onDragOver={handleDragOver}
                onDragEnter={(e) => handleDragEnter(e, index)}
                onDragLeave={handleDragLeave}
                onDrop={(e) => handleDrop(e, index)}
                onDragEnd={handleDragEnd}
                onTouchStart={(e) => handleTouchStart(e, index)}
                onTouchMove={handleTouchMove}
                onTouchEnd={handleTouchEnd}
                onTouchCancel={handleTouchEnd}
                style={{
                  transform: (currentTouchIndex === index && isDragStarted) ? `translate(${touchOffset.x}px, ${touchOffset.y}px)` : 'none',
                  opacity: dragIndex === index ? 0.7 : 1,
                  transition: (currentTouchIndex === index && isDragStarted) ? 'none' : 'all 0.3s ease',
                  zIndex: (currentTouchIndex === index && isDragStarted) ? 1000 : 'auto',
                  position: 'relative'
                }}
              >
                <Image
                  className='image-preview'
                  src={item.url}
                />
                <View
                  className='image-delete'
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteImage(index);
                  }}
                  onTouchStart={(e) => e.stopPropagation()}
                >
                  ×
                </View>
                {dragIndex === index && (
                  <View className='drag-indicator'>
                    <Text>移动中</Text>
                  </View>
                )}
              </div>
            ))}
            {imageList.length < 9 && (
              <View className='image-add' onClick={handleChooseImage}>
                <Text className='add-icon'>+</Text>
                <Text className='add-text'>添加图片</Text>
              </View>
            )}
          </div>
        </View>

        <View className='price-section'>
          <Text className='label'>售价</Text>
          <View className='input-wrapper'>
            {price !== '' && <Text className='currency'>￥</Text>}
            <Input
              className='price-input'
              value={price}
              onChange={handlePriceInput}
              type='number'
              placeholder='请输入售价'
              border='none'
            />
          </View>
        </View>

        <View className='divider' />

        <View className='tag-section' onClick={() => {selectTag()}}>
          <Text className='label'>标签</Text>
          <View className='right'>
            <Text className={tag === '' || tag === '通过标签给图文分类' ? 'placeholder' : 'value'}>
              {
                tags.length > 0 ? (
                  tags.map(item => item.name).join(', ')
                ) : tag
              }
            </Text>
            <IconRight className='arrow'/>
            {/* <Image className='arrow' src={require('@/static/image/common/right_arrow.png')} /> */}
          </View>
        </View>

        <View className='divider' />

        <View className='format-section' onClick={() => {selectProductColor()}}>
          <Text className='label'>规格</Text>
          <View className='right'>
            <Text className={formatColor === '' || formatColor === '无' ? 'placeholder' : 'value'}>
            {/* {
                formats.length > 0 ? (
                  [...formats, ...colors].map(item => item.name).join(', ')
                ) : formatColor
              } */}
      
     { [...formats, ...colors].map(item => item.name).join(', ')}
            </Text>
            <IconRight className='arrow'/>
            {/* <Image className='arrow' src={require('@/static/image/common/right_arrow.png')} /> */}
          </View>
        </View>

        <View className='divider' />

        {/* next-todo: 需要智能识别，暂时关闭 */}
        { false && 
        <>
        <View className='short-text-section'>
          <Text className='label'>商品简称</Text>
          <View className='right'>
            <Input
              border='none'
              className='input'
              value={shortText}
              onChange={(_,value) => setShortText(value)}
              placeholder='请输入简称'
              maxLength={16}
            />
            {dynamicTitle !== '' && (
              <Text className='get-from-title' onClick={() => {/* 从标题获取 */}}>
                从标题获取
              </Text>
            )}
          </View>
        </View>

        <View className='divider' />
        </>
        }


        <View className='source-section'>
          <Text className='label'>来源(仅自己可见)</Text>
          <Text className='value'>{sourceName}</Text>
        </View>

        <View className='divider' />

        <View className='ems-section' onClick={() => openFreightTemplateActionSheet()}>
          <Text className='label'>运费模版</Text>
          <View className='right'>
            <Text className={ems === '' || ems === '无' ? 'placeholder' : 'value'}>
              {ems}
            </Text>
            <IconRight className='arrow'/>
            {/* <Image className='arrow' src={require('@/static/image/common/right_arrow.png')} /> */}
          </View>
        </View>
      </View>

      {isShowFooter && (
        <View className='footer' style={{ paddingBottom }}>
          <Button
            className={`save-button ${dynamicTitle.length > 0 || imageList.length > 0 ? 'active' : ''}`}
            onClick={handleSave}
          >
            {type === 4 ? '转发至相册' : '保存'}
          </Button>
        </View>
      )}



      {/* <Toast ref={toastRef} /> */}
      {/* <Loading ref={loadingRef} /> */}
      {/* <Popup
        ref={popupRef}
        title='放弃发布'
        content='确定要放弃发布动态吗？'
        confirmText='放弃'
        cancelText='继续编辑'
        onConfirm={() => {
          Taro.navigateBack({
            delta: 1
          })
        }}
      /> */}

      {/* 自定义运费模板选择弹窗 */}
      {showFreightTemplateModal && (
        <View className='freight-template-modal'>
          <View className='modal-overlay' onClick={() => setShowFreightTemplateModal(false)} />
          <View className='modal-content'>
            <View className='modal-header'>
              <Text className='modal-title'>运费模板</Text>
            </View>
            <View className='modal-body'>
              {freightTemplateList.map((template) => (
                <View
                  key={template.id}
                  className='template-item'
                  onClick={() => {
                    setSelectedFreightTemplate(template)
                    setEms(template.name)
                    setShowFreightTemplateModal(false)
                  }}
                >
                  <View className='template-content'>
                    <Text className={`template-name ${template.id === selectedFreightTemplate?.id ? 'selected' : ''}`}>
                      {template.name}
                      {template.isDefault === 1 && ' (默认)'}
                    </Text>
                    <View className='template-rules'>
                      {renderFreightRuleDescription(template, template.id === selectedFreightTemplate?.id)}
                    </View>
                    {template.nonDeliveryArea && (
                      <Text className={`template-area ${template.id === selectedFreightTemplate?.id ? 'selected' : ''}`}>
                        不配送区域: {template.nonDeliveryArea}
                      </Text>
                    )}
                  </View>
                  {template.id === selectedFreightTemplate?.id && (
                    <Text className='template-check'>✓</Text>
                  )}
                </View>
              ))}
            </View>
            <View className='modal-footer'>
              <Button
                className='cancel-btn'
                onClick={() => setShowFreightTemplateModal(false)}
              >
                取消
              </Button>
            </View>
          </View>
        </View>
      )}

      {/* 底部弹出对话框 */}
      <BottomPopup
        options={['拍照', '从相册选择']}
        btnCloseText='取消'
        onConfirm={handleConfirm}
        onClose={handleClose}
        visible={isPopupVisible}
      />

      <PermissionPopup {...permissionPopupProps} />
    </View>
  )
}