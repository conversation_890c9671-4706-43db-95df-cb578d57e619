@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageOrder/orderSetting/emsTemplate/emsArea"] {
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });

  .ems-area {
    min-height: 100vh;

    .address {
      padding-bottom: 40px;
      display: flex;
      flex-direction: column;

      &-item {
        margin: 0px 16px;
        padding: 15px 0px;
        display: flex;
        flex-direction: column;

        &-head {
          display: flex;
          align-items: center;

          &-img {
            width: 20px;
            height: 20px;
            display: block;
          }

          &-text {
            margin-left: 16px;
            font-size: 15px;
            color: #333333;
            .use-var(color, font-color);
            .use-dark-mode-query({
            color: @dark-font-color !important;
          });
          }
        }

        &-list {
          margin-left: 25px;
          display: flex;
          flex-wrap: wrap;

          &-item {
            width: 50%;
            margin-top: 20px;
            display: flex;
            align-items: center;

            &-img {
              width: 20px;
              height: 20px;
              display: block;
            }

            &-text {
              margin-left: 16px;
              font-size: 14px;
              color: #333333;
              .use-var(color, font-color);
              .use-dark-mode-query({
              color: @dark-font-color !important;
            });
            }
          }
        }
      }
    }

    .foot-holder {
      height: 168px;
    }

    .footerbtn {
      .use-var(background-color, background-color);
      .use-dark-mode-query({
      background-color: @dark-cell-background-color !important;
    });
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 64px;
      .use-var(background-color, background-color);
      .use-dark-mode-query({
      background-color: @dark-cell-background-color !important;
    });
      z-index: 3090;
      display: flex;
      align-items: center;
      justify-content: center;

      &-all {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20%;

        &-icon {
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        &-text {
          font-size: 12px;
          margin-left: 4px;
          color: #333333;
          .use-var(color, font-color);
          .use-dark-mode-query({
          color: @dark-font-color !important;
        });
        }
      }

      &-next {
        margin: 0;
        padding: 0 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80%;

        &-btn {
          margin: 0;
          flex: 1;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #ffffff;
          font-size: 14px;
          background-color: var(--primary-color);
        }

        &-notbtn {
          margin: 0;
          flex: 1;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #ffffff;
          font-size: 14px;
          background-color: var(--primary-disabled-color);
          cursor: not-allowed;
          opacity: 0.5;
        }
      }
    }
  }

  .ems-area-icon-checked {
    .use-var(color, primary-color) !important;
    .use-dark-mode-query({
    color: var(--dark-primary-color) !important;
  });
  }

  .ems-area-icon-unchecked {
    .use-var(color, primary-color) !important;
    .use-dark-mode-query({
    color: var(--dark-primary-color) !important;
  });
  }

  .ems-area-icon-disabled {
    color: var(--primary-disabled-color);
    .use-var(color, primary-disabled-color) !important;
    .use-dark-mode-query({
    color: var(--dark-primary-disabled-color) !important;
  });
  }
}
