import { IconProps } from './types';

const IconCopy: React.FC<IconProps> = ({
  color = 'var(--primary-color)',
  size = 11,
  className
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    fill="none"
    version="1.1"
    width={size}
    height={size}
    viewBox="0 0 11 11"
    className={className}
  >
    <g opacity="0.9">
      <g>
        <path
          d="M8.93856,0L4.03698,0C2.91043,0,1.99187,0.89733,1.97625,2.00707C0.878807,2.05124,0,2.94647,0,4.04079L0,8.96418C0,10.0865,0.924948,11,2.06144,11L6.96302,11C8.08957,11,9.00813,10.1027,9.02375,8.99293C10.1212,8.94876,11,8.05353,11,6.95921L11,2.03582C11,0.913454,10.0758,-1.33713e-7,8.93856,0ZM8.17334,8.96418C8.17334,9.62316,7.63029,10.1588,6.96373,10.1588L2.06143,10.1588C1.39417,10.1588,0.851832,9.62246,0.851832,8.96418L0.851832,4.04009C0.851832,3.38111,1.39488,2.84552,2.06143,2.84552L6.96302,2.84552C7.63029,2.84552,8.17262,3.38181,8.17262,4.04009L8.17262,8.96418L8.17334,8.96418ZM10.1482,6.95921C10.1482,7.58945,9.65127,8.10541,9.02446,8.14958L9.02446,4.04009C9.02446,2.91772,8.09951,2.00427,6.96302,2.00427L2.82879,2.00427C2.84583,1.36002,3.38036,0.841246,4.03698,0.841246L8.93927,0.841246C9.60654,0.841246,10.1489,1.37754,10.1489,2.03582L10.1489,6.95921L10.1482,6.95921Z"
          fill={color}
          fillOpacity="1"
        />
      </g>
    </g>
  </svg>
);

export default IconCopy;