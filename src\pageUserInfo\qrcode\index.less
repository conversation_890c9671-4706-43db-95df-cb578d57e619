@import "@arco-design/mobile-react/style/mixin.less";
@import "@/utils/css/variables.less";

[id^="/pageUserInfo/qrcode/index"] {
  // 直接使用Arco Design和项目预定义的设计token变量
  // 基于DSL映射关系，无需重新定义变量

  // DSL paint_3:9718 - Logo绿色 (DSL特定品牌色)
  @qr-logo-green: #6cbe70;
  // DSL paint_3:07600 - 微信绿色 (DSL特定功能色)
  @qr-wechat-green: #54b736;
  // DSL paint_3:07811 - 图片预览蒙层背景 (DSL特定样式)
  @qr-preview-mask-bg: rgba(0, 0, 0, 0.9);
  // DSL effect_3:00287 - popover阴影 (DSL特定效果)
  @qr-popover-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);

  .qrcode-page {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
    background-color: #FFFFFF;
    .use-var(background-color, background-color);
    .use-dark-mode-query({
    background-color: @--text-5;
  });
  }

  // 顶部切换按钮区域 - 匹配DSL组1862
  .qrcode-tab-buttons {
    width: 100%;
    height: 64px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 14px;
    padding: 14px 0px;
    background-color: #FFFFFF;
    border-radius: 0px 0px 14px 14px;
    .use-var(background-color, background-color);
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
  }

  .qrcode-tab-button {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 4px;
    padding: 7.5px 16px;
    border-radius: 50px;
    border: 1px solid;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &.active {
      background-color: #165DFF;
      border-color: #165DFF;
      
      .qrcode-icon svg {
        color: #FFFFFF;
      }
      
      .qrcode-tab-text {
        color: #FFFFFF;
      }
    }
    
    &:not(.active) {
      background-color: #F7F8FA;
      border-color: #F7F8FA;
      
      .qrcode-icon svg {
        color: #86909C;
      }
      
      .qrcode-tab-text {
        color: #86909C;
      }
    }
  }

  .qrcode-icon {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    svg {
      width: 100%;
      height: 100%;
    }
  }

  .qrcode-tab-text {
    font-family: "PingFangSC-Regular", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
    font-size: ~`pxtorem(15)`;
    line-height: ~`pxtorem(21)`;
    white-space: nowrap;
  }

  // 页面内容区域 - 动态列表
  .qrcode-page-content {
    flex: 1;
    flex-shrink: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    overflow-y: auto;
    padding: 0px 0px 0px 16px;
    background-color: #FFFFFF;
    .use-var(background-color, background-color);
    .use-dark-mode-query({
    background-color: @--text-5;
  });
  }

  // 动态卡片容器
  .dynamic-card {
    width: 100%;
    display: flex;
    flex-direction: row;
    gap: 8px;
    padding: 15px 16px 15px 0px;
  }

  // 用户头像区域
  .dynamic-user {
    width: 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex-shrink: 0;
    
  }

  .dynamic-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    flex-shrink: 0;
  }

  // 动态内容区域
  .dynamic-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  // 商家名称
  .dynamic-header {
    width: 100%;
    height: 20px;
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .merchant-name {
    font-family: "PingFangSC-Medium", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
    font-size: ~`pxtorem(14)`;
    font-weight: 500;
    color: #002C8C;
    line-height: ~`pxtorem(20)`;
  }

  // 动态文案区域
  .dynamic-text {
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .dynamic-desc {
    font-family: "PingFangSC-Medium", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
    font-size: ~`pxtorem(12)`;
    color: #86909C;
    line-height: 260%;
    white-space: pre-line;
  }

  // 页面内容包裹器 - 带装饰背景
  .page-content-wrapper {
    position: relative;
    width: 167.54px;
    height: 298px;
    background-color: #F2F3F5;
    border-radius: 4px;
    overflow: hidden;
    
    // 装饰性圆形
    .decoration-circle {
      position: absolute;
      border-radius: 50%;
      pointer-events: none;
      
      &.circle-1 {
        width: 23.68px;
        height: 23.68px;
        background-color: #DCF3E1;
        filter: blur(1.79px);
        left: 11.17px;
        top: 270.75px;
      }
      
      &.circle-2 {
        width: 15.64px;
        height: 15.64px;
        background-color: #FAD8B1;
        filter: blur(4.47px);
        left: -8.49px;
        top: 243.05px;
      }
      
      &.circle-3 {
        width: 16.08px;
        height: 16.08px;
        background-color: #D2E2F3;
        filter: blur(4.47px);
        left: 44.23px;
        top: 253.32px;
      }
      
      &.circle-4 {
        width: 27.25px;
        height: 27.25px;
        background-color: #D4D2F3;
        filter: blur(4.47px);
        left: 137.16px;
        top: 274.77px;
      }
      
      &.circle-5 {
        width: 23.68px;
        height: 23.68px;
        background-color: #FAD8B1;
        filter: blur(8.94px);
        left: -12.51px;
        top: 15.19px;
      }
      
      &.circle-6 {
        width: 19.66px;
        height: 19.66px;
        background-color: #B8E1B7;
        filter: blur(8.94px);
        left: 32.61px;
        top: -9.83px;
      }
      
      &.circle-7 {
        width: 13.85px;
        height: 13.85px;
        background-color: #D2E9F3;
        filter: blur(4.47px);
        left: 143.86px;
        top: 226.52px;
      }
      
      &.circle-8 {
        width: 11.17px;
        height: 11.17px;
        background-color: #D2F3EA;
        filter: blur(4.47px);
        left: 6.7px;
        top: 197.03px;
      }
      
      &.circle-9 {
        width: 11.17px;
        height: 11.17px;
        background-color: #FAD8B1;
        filter: blur(4.47px);
        left: 107.23px;
        top: 274.77px;
      }
      
      &.circle-10 {
        width: 4.91px;
        height: 4.91px;
        background-color: #FAB1B1;
        filter: blur(4.47px);
        left: 79.08px;
        top: 285.94px;
      }
    }
  }

  // 主容器 - 匹配DSL容器616 (220x499)
  .qrcode-container {
    position: relative;
    width: 98.29px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 26.81px;
    padding: 34.99px 34.4px 0px;
    z-index: 1;
  }

  // 头部区域 - 匹配DSL容器12
  .qrcode-header {
    width: 98.29px;
    flex-shrink: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8.94px;
  }

  // Logo容器 - 匹配DSL LOGO样式
  .qrcode-logo {
    width: 31.95px;
    height: 31.95px;
    background: #FFFFFF;
    box-shadow: 0px 2.28px 6.85px 0px rgba(0, 0, 0, 0.1);
    border-radius: 3.65px;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;

    &-img {
      width: 50px;
      height: 50px;
      object-fit: cover;
      object-position: center;
    }
  }

  // 标题容器 - 匹配DSL容器615
  .qrcode-title-container {
    width: 72.38px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .qrcode-title {
    width: 100%;
    font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
    font-size: ~`pxtorem(8.04)`;
    font-weight: bold;
    color: @font-color;
    .use-var(color, font-color);
    .use-dark-mode-query({
    color: @--text-1;
  });
    line-height: 140%;
    text-align: center;
    white-space: nowrap;
  }

  .qrcode-subtitle {
    width: 100%;
    font-family: "PingFangSC-Medium", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
    font-size: ~`pxtorem(8.04)`;
    color: @sub-info-font-color;
    .use-var(color, sub-info-font-color);
    .use-dark-mode-query({
    color: @--text-2;
  });
    line-height: 140%;
    text-align: center;
    white-space: nowrap;
  }

  // 二维码内容区域 - 匹配DSL容器16
  .qrcode-content {
    width: 98.29px;
    background: #FFFFFF;
    box-shadow: 0px 0.89px 3.57px 0px rgba(0, 0, 0, 0.1);
    border-radius: 3.57px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 7.15px;
    padding: 6.7px 0px 3.57px;
    .use-var(background-color, background-color);
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
  }

  // 用户信息区域 - 匹配DSL容器13
  .user-info {
    width: 98.29px;
    flex-shrink: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4.47px;
  }

  .user-avatar {
    width: 17.87px;
    height: 17.87px;
    border-radius: 50%;
  }

  .user-name {
    font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
    font-size: ~`pxtorem(6.7)`;
    font-weight: bold;
    color: @font-color;
    .use-var(color, font-color);
    .use-dark-mode-query({
    color: @--text-1;
  });
    line-height: 140%;
    text-align: center;
    white-space: nowrap;
  }

  // 二维码框 - 匹配DSL二维码区域
  .qrcode-box {
    width: 71.48px;
    height: 71.48px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 0.22px solid #E5E6EB;
    border-radius: 0.89px;
    .use-var(border-color, line-color);
    .use-dark-mode-query({
    border-color: @dark-line-color;
  });
    padding: 0;
    background-color: #FFFFFF;
    
    svg {
      width: 65px !important;
      height: 65px !important;
    }
  }

  .qrcode-canvas {
    width: 65px;
    height: 65px;
    display: block;
    margin: auto;
  }

  // 底部提示区域 - 匹配DSL容器14
  .qrcode-tip {
    width: 98.29px;
    flex-shrink: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2.23px;
  }

  .wechat-icon {
    width: 8.94px;
    height: 8.94px;
    flex-shrink: 0;
    color: #54B736;
  }

  .tip-text {
    font-family: "PingFangSC-Regular", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
    font-size: ~`pxtorem(5.36)`;
    color: @sub-info-font-color;
    .use-var(color, sub-info-font-color);
    .use-dark-mode-query({
    color: @--text-2;
  });
    line-height: 140%;
    text-align: center;
    white-space: nowrap;
  }

  // 一键转发按钮包裹器
  .forward-button-wrapper {
    width: 100%;
  }

  .forward-button {
    width: calc(100% - 32px);
    height: 36px;
    // margin-right: 120px;
    background-color: #E8F3FF;
    border: 1px solid #E8F3FF;
    border-radius: 4px;
    font-family: "PingFangSC-Regular", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
    font-size: ~`pxtorem(15)`;
    color: #165DFF;
    line-height: ~`pxtorem(21)`;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 7.5px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:active {
      opacity: 0.8;
    }
  }

  // 底部横条 - iOS Home Indicator
  .home-indicator {
    width: 100%;
    height: 34px;
    background-color: #FFFFFF;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    align-items: center;
    padding: 21px 120px 6px;
    .use-var(background-color, background-color);
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
  }

  .indicator-bar {
    width: 134px;
    height: 5px;
    background-color: rgba(0, 0, 0, 0.9);
    border-radius: 100px;
  }

  // 图片预览样式已移除，现在使用 Arco Design 的 ImagePreview 组件
}
