@import "@arco-design/mobile-react/style/mixin.less";

/* 购物车弹框样式 - 完全照搬详情页样式 */
.demo-action-sheet {
  .move-item-my {
    display: flex;
    align-items: flex-start;
    margin-top: 10px; // 列表项之间顶部间距
    // border-bottom: 1px solid #f0f0f0; // 列表项之间不需要底部边线
    background: #fff;
    .use-dark-mode-query({
        background-color: transparent;   //黑色背景
    });
  }

  .move-info-my-s {
    flex: 1;
    min-width: 0;
    display: inline-grid;
    justify-items: flex-start;
    margin-top: 10px;
  }

  .move-title-text-my {
    // width: 200px; // 移除固定宽度
    padding-bottom: 3px;
    height: 42px;
    font-size: 14px; // 根据图片调整字体大小
    color: #222;
    display: -webkit-box; // 多行文本溢出省略
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; // 限制最多显示 2 行
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
    .use-dark-mode-query({
        color: var(--dark-font-color);    //白色字体
    });
  }

  .move-price {
    padding-bottom: 3px;
    height: 21px;
    color: #f53f3f;
    font-size: 14px; // 根据图片调整字体大小
  }

  .move-img {
    width: 85px; // 根据图片调整图片尺寸
    height: 85px;
    border-radius: 6px;
    object-fit: cover;
    background: transparent; // 修改为透明背景，避免暗黑模式下的白边
    margin-right: 12px; // 调整图片与信息区域间距
  }

  .sku-container {
    // padding: 16px;

    .color-tabs {
      margin: 0 16px 16px 16px; // 左右16px间距，下方16px间距，与详情页保持一致

      .color-tab-containerOut {
        // margin-bottom: 16px;
      padding: 0 16px;
      background: #fff!important;
      .use-dark-mode-query({
        background-color: @dark-background-color!important;
      });
      }

      .color-tab-content {
        .spec-list {
          padding: 0;
          max-height: 200px; // 减小最大高度为200px
          overflow-y: scroll; // 强制显示垂直滚动
          overflow-x: hidden; // 禁用水平滚动
          -webkit-overflow-scrolling: touch; // iOS平滑滚动
          .no-border-cell {
            border-bottom: none !important;
            // padding: 8px 0;

            &::after {
              display: none !important;
            }

            &::before {
              display: none !important;
            }
          }
        }
      }
    }

    .batch-only-container {
      // margin: 0 16px; // 为批量操作容器也添加左右16px间距

      .no-border-cell {
        border-bottom: none !important;
        // padding: 8px 0;

        &::after {
          display: none !important;
        }

        &::before {
          display: none !important;
        }
      }
    }
  }

  // 去掉Cell的下划线 - 使用更强的选择器
  .no-border-cell {
    border-bottom: none !important;

    &::after {
      display: none !important;
    }

    &::before {
      display: none !important;
    }
  }

  // 底部容器样式
  .bottom-container {
    width: 100%;
    padding: 0 0 15px 0;
  }

  // 按钮容器样式
  .button-container-out {
    display: flex;
    width: 100%;
    justify-content: space-between;
    gap: 16px;
    padding: 0 16px 16px 16px;
  }
}

// 购物车弹框样式（照搬动态详情页）
.move-item-my {
  display: flex;
  align-items: flex-start;
  margin-top: 10px; // 列表项之间顶部间距
  background: #fff;
  .use-dark-mode-query({
    background-color: transparent;   //黑色背景
  });
}

.move-info-my-s {
  flex: 1;
  min-width: 0;
  display: inline-grid;
  justify-items: flex-start;
  margin-top: 10px;
}

.move-title-text-my {
  padding-bottom: 3px;
  height: 42px;
  font-size: 14px; // 根据图片调整字体大小
  color: #222;
  .use-dark-mode-query({
    color: @dark-font-color;
  });
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.move-price {
  padding-bottom: 3px;
  height: 21px;
  color: #f53f3f;
  font-size: 14px; // 根据图片调整字体大小
}

.move-img {
  width: 85px; // 根据图片调整图片尺寸
  height: 85px;
  border-radius: 6px;
  object-fit: cover;
  background: transparent; // 修改为透明背景，避免暗黑模式下的白边
  margin-right: 12px; // 调整图片与信息区域间距
}

.bottom-container {
  padding: 16px;

  .total-info-container {
    margin-bottom: 16px;
    text-align: center;
  }

  .button-container-out {
    display: flex;
    gap: 12px;
  }
}

// SKU选择器样式（照搬动态详情页）
.sku-container {
  .color-tabs {
    margin: 0 16px 16px 16px;
  }

  .color-tab-content {
    .spec-list {
      padding: 0;
      max-height: 200px;
      overflow-y: scroll;
      overflow-x: hidden;
      -webkit-overflow-scrolling: touch;
    }
  }

  // 为没有颜色时的规格列表也添加相同的限制
  .spec-list {
    padding: 0;
    max-height: 200px;
    overflow-y: scroll;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
  }

  // 去掉Cell的下划线 - 使用更强的选择器
  .no-border-cell {
    border-bottom: none !important;

    &::after {
      display: none !important;
    }

    &::before {
      display: none !important;
    }
  }

}

// 批量操作容器样式
.batch-only-container {
  // padding: 10px 0;
  background: #fff;

  .no-border-cell {
    border-bottom: none !important;
  }
}

// 无边框单元格样式
.no-border-cell {
  border-bottom: none !important;
}

// 步进器样式
.stepper-style {
  margin-left: 150px;
}

// 购物车总计样式
.cart-total-summary {
  margin-right: 16px;
  font-size: 16px !important;
  color: #222 !important;
  .use-dark-mode-query({
    color: @dark-font-color !important;
  });
  font-weight: 500 !important;

  .highlight-text {
    color: #e35848 !important;
  }
}

// 总计信息容器样式
.total-info-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 12px;
  margin-right: 16px;
}

// 底部容器样式
.bottom-container {
  width: 100%;
  padding: 0 0 15px 0;
}

// 按钮容器样式
.button-container-out {
  display: flex;
  width: 100%;
  justify-content: space-between;
  gap: 16px;
  padding: 0 16px 16px 16px;
  margin-top: 30px;
}

.cart-modal-buy {
  flex: 1;
  height: 44px!important;
  background: var(--primary-color);
  opacity: 0.3;
  color: #fff!important;
  border-radius: 8px!important;
  border: none;
  font-size: 14px!important;
}

.cart-modal-buy-active {
  background: var(--primary-color);
  opacity: 1;
}

// 加入购物车按钮样式
.cart-modal-add-btn {
  flex: 1;
  height: 44px!important;
  background: #f7f8fa!important;
  color: #bcbcbc!important;
  border-radius: 8px!important;
  border: none;
  font-size: 14px!important;

  &.active {
    background: #e6f3ff!important;
    color: #1890ff!important;
  }
}