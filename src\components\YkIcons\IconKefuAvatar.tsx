import { IconProps } from './types';

const IconKefuAvatar: React.FC<IconProps> = ({
  color = 'var(--primary-color)',
  size = 38,
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    version="1.1"
    width={size}
    height={size}
    viewBox="0 0 56 38"
    className={className}
  >
    <g>
      <g>
        <path
          d="M11.20000057220459,0L44.80000057220459,0Q47.60000057220459,0,47.60000057220459,2.71429L47.60000057220459,35.2857Q47.60000057220459,38,44.80000057220459,38L11.20000057220459,38Q8.40000057220459,38,8.40000057220459,35.2857L8.40000057220459,2.71429Q8.40000057220459,0,11.20000057220459,0Z"
          fill={color}
          fillOpacity="0.7"
        />
      </g>
      <g>
        <path
          d="M44.80000057220459,0C46.34640057220459,0,47.60000057220459,1.21523,47.60000057220459,2.71429L47.60000057220459,5.42857C47.60000057220459,3.92952,46.34640057220459,2.71429,44.80000057220459,2.71429L11.20000057220459,2.71429C9.65360057220459,2.71428,8.39999929046459,3.92951,8.40000057220459,5.42857L8.40000057220459,2.71429C8.39999929046459,1.21523,9.65360057220459,0,11.20000057220459,0L44.80000057220459,0Z"
          fill={color}
          fillOpacity="0.5"
        />
      </g>
      <g>
        <path
          d="M2.8,10.857117652893066Q5.6,10.857117652893066,5.6,13.571407652893066L5.6,24.428517652893067Q5.6,27.142817652893065,2.8,27.142817652893065Q0,27.142817652893065,0,24.428517652893067L0,13.571407652893066Q0,10.857120137893066,2.8,10.857117652893066Z"
          fill={color}
          fillOpacity="0.5"
        />
      </g>
      <g>
        <path
          d="M53.2000015258789,10.857117652893066Q56.00000152587891,10.857117652893066,56.00000152587891,13.571407652893066L56.00000152587891,24.428517652893067Q56.00000152587891,27.142817652893065,53.2000015258789,27.142817652893065Q50.400001525878906,27.142817652893065,50.400001525878906,24.428517652893067L50.400001525878906,13.571407652893066Q50.400001525878906,10.857120137893066,53.2000015258789,10.857117652893066Z"
          fill={color}
          fillOpacity="0.5"
        />
      </g>
      <g>
        <path
          d="M19.60000114440918,10.857117652893066C21.01953114440918,10.857299057893066,22.21432114440918,11.887207652893066,22.38040114440918,13.253837652893067L22.40000114440918,13.571407652893066L22.40000114440918,18.999977652893065C22.39836114440918,20.436087652893065,21.24301114440918,21.622417652893066,19.76410114440918,21.706617652893065C18.28518114440918,21.790717652893065,16.99457914440918,20.74360765289307,16.81960404440918,19.31754765289307L16.80000114440918,18.999977652893065L16.80000114440918,13.571407652893066C16.79999858092918,12.072347652893066,18.05360114440918,10.857117652893066,19.60000114440918,10.857117652893066ZM36.40000114440918,10.857117652893066C37.81950114440918,10.857301542893067,39.01430114440918,11.887207652893066,39.18040114440918,13.253837652893067L39.20000114440918,13.571407652893066L39.20000114440918,18.999977652893065C39.198401144409175,20.436097652893068,38.043001144409175,21.622417652893066,36.56410114440918,21.706617652893065C35.08520114440918,21.790817652893068,33.79460114440918,20.74360765289307,33.61960114440918,19.31754765289307L33.60000114440918,18.999977652893065L33.60000114440918,13.571407652893066C33.60000114440918,12.072347652893066,34.85360114440918,10.857117652893066,36.40000114440918,10.857117652893066Z"
          fill="#FFFFFF"
          fillOpacity="1"
        />
      </g>
    </g>
  </svg>
);

export default IconKefuAvatar;
