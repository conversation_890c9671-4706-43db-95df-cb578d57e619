@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pageDynamic/report/reportType/index"] {
  .report-type-page {
    min-height: 100vh;
    .use-var(background-color, background-color);
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });

    .report-type-header {
      display: flex;
      align-items: center;
      justify-content: center;
      .rem(padding, 16);
      .use-var(background-color, card-background-color);
      .use-dark-mode-query({
      background-color: @dark-card-background-color;
    });
      // .rem(margin-bottom, 8);

      .report-type-header-text {
        .rem(font-size, 16);
        color: #1d2129;
        .use-dark-mode-query({
        color: @dark-font-color;
      });
        text-align: center;
      }
    }

    .report-type-list {
      .use-var(background-color, card-background-color);
      .use-dark-mode-query({
      background-color: @dark-card-background-color;
    });

      .report-type-item {
        // .rem(padding, 16);
        // border-bottom: 1px solid;
        // .use-var(border-bottom-color, line-color);
        // .use-dark-mode-query({
        //   border-bottom-color: @dark-line-color;
        // });

        &:last-child {
          border-bottom: none;
        }

        .arco-cell-label {
          .rem(font-size, 16);
          .use-var(color, font-color);
          .use-dark-mode-query({
          color: @dark-font-color;
        });
        }
      }
    }
  }
}
