import areaDataType from '@/constants/area.json';
// 直辖市列表
const DIRECT_CITIES = ['北京市', '上海市', '天津市', '重庆市'];

/**
 * 将经营地址字符串转换为省市区/市市区数组，适配直辖市
 * @param address 经营地址字符串
 * @param areaData 地区数据（省市区三级结构），默认使用@/constants/area.json
 * @returns string[] 省市区/市市区数组
 */
export function convertAddressToArea(address: string, areaData: any[] = areaDataType): string[] {
  for (const province of areaData) {
    // 直辖市特殊处理
    if (DIRECT_CITIES.includes(province.name)) {
      if (address.includes(province.name)) {
        const city = province.children?.[0];
        if (city) {
          for (const area of city.children || []) {
            if (address.includes(area.name)) {
              return [province.name, province.name, area.name];
            }
          }
        }
      }
    } else {
      // 普通省份
      if (address.includes(province.name)) {
        for (const city of province.children || []) {
          if (address.includes(city.name)) {
            for (const area of city.children || []) {
              if (address.includes(area.name)) {
                return [province.name, city.name, area.name];
              }
            }
            // 只匹配到市
            return [province.name, city.name, ''];
          }
        }
        // 只匹配到省
        return [province.name, '', ''];
      }
    }
  }
  // 没有匹配到，返回空
  return [];
}
