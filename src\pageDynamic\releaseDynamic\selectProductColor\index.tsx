import React, { useState, useEffect } from 'react';
import { <PERSON>, Button, Text } from '@tarojs/components';
import { Dialog, SearchBar, Popover, Input } from "@arco-design/mobile-react";
import YkNavBar from "@/components/ykNavBar/index";
import Taro from "@tarojs/taro";
import {  getFormat, getColor, createFormat, createColor, delFormat, editFormat, delColor, editColor  } from "@/utils/api/common/common_user";
import { toast } from "@/utils/yk-common";
import './index.less';
import  { useRef } from "react";

export default function SelectProductColor() {
  const [search, setSearch] = useState('');
  const [formats, setFormats] = useState<any[]>([]);
  const [colors, setColors] = useState<any[]>([]);
  const [selectedFormats, setSelectedFormats] = useState<any[]>([]);
  const [selectedColors, setSelectedColors] = useState<any[]>([]);
  const [popoverFormat, setPopoverFormat] = useState<{[key: string]: boolean}>({});
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  // 通用弹框状态
  const [dialogVisible, setDialogVisible] = useState(false);
  const [dialogConfig, setDialogConfig] = useState({
    type: '', // 'addFormat', 'editFormat', 'addColor', 'editColor'
    title: '',
    placeholder: '',
    value: '',
    itemId: null // 用于编辑操作
  });
  

    const getFormatList = async () => {
      const data = {
        pageNo: 1,
        pageSize: 100,
        userId: Taro.getStorageSync('userInfo').id,
      }
      const res: any = await getFormat(data);
      if (res && res.code == 0) {
        if (res.data.list.length > 0) {
          setFormats(res.data.list);

          // 在获取到规格列表后，进行回显匹配
          const savedFormats = Taro.getStorageSync('selectedFormats');
          if (savedFormats && savedFormats.length > 0) {
            // 根据ID匹配已选择的规格
            const matchedFormats = res.data.list.filter(format =>
              savedFormats.some(saved => saved.id == format.id)
            );
            setSelectedFormats(matchedFormats);
          }
        } else {
          setFormats([]);
        }
      }
    }

    const getColorList = async () => {
      const data = {
        pageNo: 1,
        pageSize: 100,
        userId: Taro.getStorageSync('userInfo').id,
      }
      const res: any = await getColor(data);
      if (res && res.code == 0) {
        if (res.data.list.length > 0) {
          setColors(res.data.list);

          // 在获取到颜色列表后，进行回显匹配
          const savedColors = Taro.getStorageSync('selectedColors');
          if (savedColors && savedColors.length > 0) {
            // 根据ID匹配已选择的颜色
            const matchedColors = res.data.list.filter(color =>
              savedColors.some(saved => saved.id == color.id)
            );
            setSelectedColors(matchedColors);
          }
        } else {
          setColors([]);
        }
      }
    }

  // 获取规格
  useEffect(() => {
    getColorList()
    getFormatList()
  }, []);


  // 规格选择
  const toggleFormat = (item: any) => {
    setSelectedFormats(prev =>
      prev.some(f => f.id === item.id) ? prev.filter(f => f.id !== item.id) : [...prev, item]
    );
  };

  // 颜色选择
  const toggleColor = (item: any) => {
    setSelectedColors(prev =>
      prev.some(c => c.id === item.id) ? prev.filter(c => c.id !== item.id) : [...prev, item]
    );
  };

  // 通用弹框操作函数
  const openDialog = (type: string, itemId?: any) => {
    let config = {
      type,
      title: '',
      placeholder: '',
      value: '',
      itemId: itemId || null
    };

    switch (type) {
      case 'addFormat':
        config.title = '添加规格';
        config.placeholder = '请输入新规格';
        config.value = '';
        break;
      case 'editFormat':
        const formatItem = formats.find(f => f.id === itemId);
        config.title = '修改规格';
        config.placeholder = '请输入规格';
        config.value = formatItem?.name || '';
        break;
      case 'addColor':
        config.title = '添加颜色';
        config.placeholder = '请输入新颜色';
        config.value = '';
        break;
      case 'editColor':
        const colorItem = colors.find(c => c.id === itemId);
        config.title = '修改颜色';
        config.placeholder = '请输入颜色';
        config.value = colorItem?.name || '';
        break;
    }

    setDialogConfig(config);
    setDialogVisible(true);
  };

  const closeDialog = () => {
    setDialogVisible(false);
    setDialogConfig({
      type: '',
      title: '',
      placeholder: '',
      value: '',
      itemId: null
    });
  };

  // 通用确定按钮处理
  const handleDialogConfirm = async () => {
    const currentValue = dialogConfig.value.trim();
    const { type, itemId } = dialogConfig;

    // 验证输入内容
    if (!currentValue) {
      const errorMsg = type.includes('Format') ? '请输入规格名称' : '请输入颜色名称';
      toast("info", {
        content: errorMsg,
        duration: 2000
      });
      return;
    }

    // 判断是否已存在（编辑时排除自己）
    if (type === 'addFormat' || type === 'editFormat') {
      const exists = formats.some(f => f.name === currentValue && f.id !== itemId);
      if (exists) {
        toast("info", {
          content: "规格已存在",
          duration: 2000
        });
        return;
      }
    } else if (type === 'addColor' || type === 'editColor') {
      const exists = colors.some(c => c.name === currentValue && c.id !== itemId);
      if (exists) {
        toast("info", {
          content: "颜色已存在",
          duration: 2000
        });
        return;
      }
    }

    // 显示加载状态
    const loadingTitle = type.includes('add') ? '添加中...' : '编辑中...';
    Taro.showLoading({
      title: loadingTitle,
      mask: true
    });

    try {
      const data = {
        userId: Taro.getStorageSync('userInfo').id,
        name: currentValue,
        ...(type.includes('edit') && { id: itemId })
      };

      let apiCall;
      let successMsg = '';
      let updateState;

      switch (type) {
        case 'addFormat':
          apiCall = createFormat(data);
          successMsg = '添加成功';
          updateState = (res: any) => {
            const format = { id: res.data, name: currentValue };
            setFormats([...formats, format]);
          };
          break;
        case 'editFormat':
          apiCall = editFormat(data);
          successMsg = '编辑成功';
          updateState = () => {
            setFormats(prev => prev.map(f =>
              f.id === itemId ? { ...f, name: currentValue } : f
            ));
          };
          break;
        case 'addColor':
          apiCall = createColor(data);
          successMsg = '添加成功';
          updateState = (res: any) => {
            const color = { id: res.data, name: currentValue };
            setColors([...colors, color]);
          };
          break;
        case 'editColor':
          apiCall = editColor(data);
          successMsg = '编辑成功';
          updateState = () => {
            setColors(prev => prev.map(c =>
              c.id === itemId ? { ...c, name: currentValue } : c
            ));
          };
          break;
      }

      apiCall.then((res: any) => {
        if (res && res.code === 0) {
          updateState(res);
          closeDialog(); // 关闭弹窗

          setTimeout(() => {
            Taro.hideLoading();
            toast("success", {
              content: successMsg,
              duration: type.includes('add') ? 1500 : 2000
            });
          }, 1000);
        } else {
          Taro.hideLoading();
          toast("error", {
            content: type.includes('add') ? "添加失败，请重试" : "编辑失败，请重试",
            duration: 2000
          });
        }
      });
    } catch (error) {
      Taro.hideLoading();
      toast("error", {
        content: type.includes('add') ? "添加失败，请重试" : "编辑失败，请重试",
        duration: 2000
      });
      console.error('操作失败:', error);
    }
  };

  // 修改后的函数调用
  const addFormat = () => openDialog('addFormat');
  const addColor = () => openDialog('addColor');



  const editorFormat = (id, type) => {
    const dialogType = type === 'format' ? 'editFormat' : 'editColor';
    openDialog(dialogType, id);
  };

  const deleteFormat=(id,type)=>{

    let title = '';
    title=type=='format' ? '规格' : '颜色'
    Dialog.confirm({
      platform: 'ios',
      className: 'dialog-input-demo',
      title: '温馨提示',
      contentAlign: 'left',
      children: (<>
          <div className="dialog-input-demo-hint">删除{title}后不可恢复，确认删除？</div>
      </>),
      onOk: async () => {
        Taro.showLoading({
          title: '删除中...',
          mask: true
        });
        try {
          if(type=='format'){
            delFormat({"id":id}).then((res: any) => {  
              if(res && res.code == 0) {  
              setTimeout(() => {
                Taro.hideLoading();
                toast("info", {
        content: "删除成功",
        duration: 2000
      });
              }, 1000);
                setFormats(formats.filter(f => f.id !== id));
              }
            })
          }else{
            delColor({"id":id}).then((res: any) => {  
              if(res && res.code == 0) {  
              setTimeout(() => {
                Taro.hideLoading();
                toast("info", {
        content: "删除成功",
        duration: 2000
      });
              }, 1000);
                setColors(colors.filter(f => f.id !== id));
              }
            })
          }
       } catch (error) {
         Taro.hideLoading();
         toast("info", {
        content: "删除失败，请重试",
        duration: 2000
      });
         console.error('删除失败:', error);
       }
      },
      onCancel: () => {
        // 用户点击取消时执行
        console.log('用户取消了操作');
      },
    })  

  }
  // 搜索过滤

  const filteredFormats = formats.filter(f => f.name.includes(search));
  const filteredColors = colors.filter(c => c.name.includes(search));

  return (
    <View className="product-color-page"
      onClick={()=>{
      const hasTrue = Object.values(popoverFormat).some(Boolean);
          if (hasTrue) {
            // 把所有的 popoverFormat 都设为 false
            setPopoverFormat(
              Object.keys(popoverFormat).reduce((acc, key) => {
                acc[key] = false;
                return acc;
              }, {})
            );
          }    
    }} >
      {platform !== "WX" &&<YkNavBar  title="设置规格" />}
      <SearchBar shape="round" placeholder="搜索" clearable={false}   />
      <View className="section" >
        <View className="section-title">
          选择规格
          <Text className="section-tip">[长按] 支持修改和删除</Text>
        </View>
        <View className="tag-list">
          <View className="tag add-tag" onClick={addFormat}>＋添加</View>
          {filteredFormats.map(item => (
                <Popover
                    visible={popoverFormat[item.id] || false}
                    className="popover-with-margin"
                    content={
                      <View
                        className='popover'
                        onClick={()=>{
                          setPopoverFormat(prev => ({...prev, [item.id]: false}));
                        }}
                      >
                        <span className='content' >
                          <span onClick={()=>{editorFormat(item.id,'format')}}  style={{marginRight:"8px",cursor:"pointer"}}>编辑</span>  |  <span onClick={()=>{deleteFormat(item.id,'format')}} style={{marginLeft:"8px",cursor:"pointer"}}>删除</span>
                        </span>                      
                      </View>
                    }
                    direction="topLeft"
                >
                    <View
                      className={`tag${selectedFormats.some(f => f.id === item.id) ? ' selected' : ''}`}
                      key={item.id}
                      onClick={() => {
                        toggleFormat(item);
                        // setPopoverFormat(prev => {
                        //   const newState = Object.keys(prev).reduce((acc, key) => {
                        //     acc[key] = false;
                        //     return acc;
                        //   }, {} as {[key: string]: boolean});
                        //   newState[item.id] = true;
                        //   return newState;
                        // });
                      }}
                      onLongPress={()=>{
                        setPopoverFormat(prev => {
                          const newState = Object.keys(prev).reduce((acc, key) => {
                            acc[key] = false;
                            return acc;
                          }, {} as {[key: string]: boolean});
                          newState[item.id] = true;
                          return newState;
                        });
                      }} 
                    >
                      {item.name}
                    </View>
                </Popover>
          ))}
        </View>
      </View>

      <View className="section">
        <View className="section-title">选择颜色</View>
        <View className="tag-list">
          <View className="tag add-tag" onClick={addColor}>＋添加</View>
          {filteredColors.map(item => (
                <Popover
                    visible={popoverFormat[item.id] || false}
                    content={
                      <View
                        className='popover'
                        onClick={()=>{
                          setPopoverFormat(prev => ({...prev, [item.id]: false}));
                        }}
                      >
                        <span className='content' >
                          <span onClick={()=>{editorFormat(item.id,'color')}}  style={{marginRight:"8px",cursor:"pointer"}}>编辑</span>  |  <span onClick={()=>{deleteFormat(item.id,'color')}} style={{marginLeft:"8px",cursor:"pointer"}}>删除</span>
                        </span>
                        
                      </View>
                    }
                    direction="topCenter"       
                >
                    <View
                      className={`tag${selectedColors.some(c => c.id === item.id) ? ' selected' : ''}`}
                      key={item.id}
                      onClick={() => {
                        toggleColor(item);
                      }}
                      onLongPress={()=>{
                        setPopoverFormat(prev => {
                          const newState = Object.keys(prev).reduce((acc, key) => {
                            acc[key] = false;
                            return acc;
                          }, {} as {[key: string]: boolean});
                          newState[item.id] = true;
                          return newState;
                        });
                      }} 
                    >
                      {item.name}
                    </View>
                </Popover>
          ))}
        </View>
      </View>

      <Button className="confirm-btn"
      onClick={()=>{
        Taro.setStorageSync('selectedFormats', selectedFormats);
        Taro.setStorageSync('selectedColors', selectedColors);
        Taro.navigateBack();
      }}>确定</Button>

      {/* 通用弹框 */}
      <Dialog
        visible={dialogVisible}
        title={dialogConfig.title}
        platform="ios"
        close={closeDialog}
        className="custom-dialog"
        footer={[
          {
            content: '取消',
            onClick: closeDialog
          },
          {
            content: '确定',
            onClick: handleDialogConfirm
          }
        ]}
      >
        <Input
          border='none'
          type="text"
          maxLength={10}
          placeholder={dialogConfig.placeholder}
          value={dialogConfig.value}
          onChange={(e, value: string) => setDialogConfig(prev => ({ ...prev, value }))}
          className="dialog-input"
        />
      </Dialog>
    </View>
  );
}