import Taro from '@tarojs/taro';

/**
 * URL处理工具：将哈希路由前的查询参数正确地附加到哈希路由路径中
 * 解决微信授权回调URL格式问题
 */
class RouterHelper {
  /**
   * 修复URL格式，将# 前面的查询参数移动到哈希路由部分
   * 例如: https://domain.com?code=xxx&state=yyy#/pages/abc?uid=123
   * 转换为: https://domain.com#/pages/abc?uid=123&code=xxx&state=yyy
   */
  static fixUrlFormat() {
    // 只在 H5 环境下执行
    if (Taro.getEnv() !== Taro.ENV_TYPE.WEB) return;

    const url = window.location.href;
    const hashIndex = url.indexOf('#');
    
    // 如果没有哈希或哈希在URL最开始，则不需要处理
    if (hashIndex <= 0) return;
    
    // 检查哈希前是否有查询参数
    const questionMarkIndex = url.indexOf('?');
    if (questionMarkIndex === -1 || questionMarkIndex > hashIndex) return;
    
    // 分离URL各部分
    const baseUrl = url.substring(0, questionMarkIndex);
    const queryString = url.substring(questionMarkIndex + 1, hashIndex);
    
    const hashPart = url.substring(hashIndex);
    const [hashPath, hashQuery = ''] = hashPart.split('?');
    
    // 构建新的URL
    let newUrl = baseUrl + hashPath;
    
    // 合并查询参数
    const combinedQuery = this.combineQueryParams(queryString, hashQuery);
    if (combinedQuery) {
      newUrl += '?' + combinedQuery;
    }
    
    console.log('[RouterHelper] 原始URL:', url);
    console.log('[RouterHelper] 修正后URL:', newUrl);
    
    // 使用 replaceState 修改URL，不触发页面刷新
    if (url !== newUrl) {
      window.history.replaceState(null, '', newUrl);
      console.log('[RouterHelper] URL已修正');
    }
    
    return newUrl;
  }
  
  /**
   * 合并两个查询字符串，去除重复参数
   * @param {string} queryBefore 哈希前的查询参数
   * @param {string} queryAfter 哈希后的查询参数
   * @returns {string} 合并后的查询字符串
   */
  static combineQueryParams(queryBefore, queryAfter) {
    if (!queryBefore) return queryAfter;
    if (!queryAfter) return queryBefore;
    
    // 解析查询参数
    const paramsBefore = new URLSearchParams(queryBefore);
    const paramsAfter = new URLSearchParams(queryAfter);
    
    // 合并参数，哈希后的参数优先
    const combinedParams = new URLSearchParams(queryAfter);
    
    // 添加哈希前的参数（如果哈希后没有同名参数）
    for (const [key, value] of paramsBefore.entries()) {
      if (!paramsAfter.has(key)) {
        combinedParams.append(key, value);
      }
    }
    
    return combinedParams.toString();
  }
  
  /**
   * 初始化路由拦截器
   */
  static init() {
    // 在页面加载时执行一次URL修正
    if (typeof window !== 'undefined') {
      // 使用setTimeout确保在React应用初始化后执行
      setTimeout(() => {
        this.fixUrlFormat();
      }, 0);
      
      // 监听hashchange事件，处理页面内导航
      window.addEventListener('hashchange', () => {
        setTimeout(() => {
          this.fixUrlFormat();
        }, 0);
      });
    }
  }
}

// 自动初始化
RouterHelper.init();

export default RouterHelper; 