import { useState, useRef, useEffect } from "react";
import { View, Text } from "@tarojs/components";
import { Cell, Switch, Dialog } from "@arco-design/mobile-react";
import YkNavBar from "@/components/ykNavBar/index";
import { useSetPermission, permissionsAtomArray } from "@/stores/permissionStore";
import { useRecoilValue } from "recoil";
import {
  AuthTypes,
  AuthStatusAndroid,
  AuthStatusIos,
} from "@/utils/config/authTypes";
import PermissionPopup from "@/components/PermissionPopup";
import { usePermission } from "@/hooks/usePermission";
// import * as Icons from '@arco-design/mobile-react/esm/icon';
import * as Icons from "@arco-iconbox/react-yk-arco";
import "./index.less";
import { useDidShow } from "@tarojs/taro";
export default function PermissionPage() {
  const clickItem = useRef({});
  const setPermission = useSetPermission();
  const permissions = useRecoilValue(permissionsAtomArray); // 从 Recoil 获取权限数据
  const [permissionsState, setPermissionsState] = useState([]); // 用于存储并渲染权限数据
  const allPermissions = useRef([]); // 权限数据引用
  const popupTitle = useRef("");
  const popupText = useRef("");
  const isRequestPermission = useRef(false); // 添加权限请求标志，初始为 false

  // 使用 usePermission hook，就像首页一样
  const {
    initPermissions,
    requestPermission,
    permissionPopupProps,
    platformRef,
    authConfirmType,
  } = usePermission();
  // 初始化权限数据
  useEffect(() => {
    allPermissions.current = permissions;
    setPermissionsState([...permissions]);
  }, [permissions]);

  useEffect(() => {
    // 使用 usePermission hook 的初始化方法
    const cleanup = initPermissions();

    // 初始化权限检查
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;

    if (isAndroid) {
      window.checkPermission.checkPermission();
    } else if (isIos) {
      window.webkit.messageHandlers.checkPermission.postMessage("");
    }

    return cleanup;
  }, []); 


  // 权限状态更新处理
  const updatePermissionsState = async (e) => {
    console.log("Updating permissions state:", JSON.stringify(e));
    console.log("Current authConfirmType in updatePermissionsState:", authConfirmType.current);
    console.log("Before update - allPermissions.current:", allPermissions.current);
    console.log("Before update - permissionsState:", permissionsState);

    // 更新权限状态
    allPermissions.current = await setPermission(e); // 确保更新后赋值
    setPermissionsState([...allPermissions.current]); // 更新状态

    console.log("After update - allPermissions.current:", allPermissions.current);
    console.log("After update - new permissionsState will be:", [...allPermissions.current]);
  };


  useDidShow(() => {
    // 延迟包装权限回调，确保 usePermission hook 已经设置了回调
    const timer = setTimeout(() => {
      // 保存原始的权限回调处理
      const originalCallback = window.checkPermissionCallBack;
      console.log("Original callback found:", !!originalCallback);

      // 包装权限回调，先更新状态，再调用原始回调
      window.checkPermissionCallBack = async (e) => {
        console.log("Wrapped checkPermissionCallBack called with:", JSON.stringify(e));
        // 先更新权限状态
        await updatePermissionsState(e);
        // 然后调用 usePermission hook 的原始回调处理
        if (originalCallback) {
          console.log("Calling original callback");
          originalCallback(e);
        } else {
          console.log("No original callback to call");
        }
      };

      console.log("Wrapped callback set");
    }, 100);

    return () => {
      clearTimeout(timer);
    };
  });

  const openCloseSetPopup = (item) => {
    clickItem.current = item;
    authConfirmType.current = item.authType;
    popupTitle.current = `${item.name}权限`;
    if (platformRef.current === "Android") {
      popupText.current = `你可以前往「设置＞应用＞${APP_NAME_CN}＞权限中」关闭${item.name}权限。`;
    } else {
      popupText.current = `你可以前往「设置＞${APP_NAME_CN}」关闭${item.name}权限。`;
    }
    showDialog();
  };

  const openSetPopup = (item) => {
    clickItem.current = item;
    authConfirmType.current = item.authType;
    console.log("openSetPopup - Setting authConfirmType to:", item.authType, "Item:", item);
    if (
      item.authType === AuthTypes.FLOATWIN ||
      item.authType === AuthTypes.AUTOSTART ||
      item.authType === AuthTypes.ACCESS
    ) {
      popupTitle.current = `${item.name}权限`;
      if (platformRef.current === "Android") {
        popupText.current = `无法使用${item.name}，前往「设置＞应用＞${APP_NAME_CN}＞权限中」打开${item.name}权限。」`;
      } else {
        popupText.current = `无法使用${item.name}，前往「设置＞${APP_NAME_CN}」打开${item.name}权限。」`;
      }
      showDialog();
    } else {
      if (item.currentStatus === AuthStatusAndroid.DENIEDANDNOTASK) {
        openSetting();
      } else {
        // 设置权限请求标志，然后请求权限
        isRequestPermission.current = true;
        console.log("openSetPopup - About to request permission with authConfirmType:", authConfirmType.current);
        requestPermission(authConfirmType.current);

        // 延迟重新检查权限状态，以防权限申请后状态没有立即更新
        setTimeout(() => {
          console.log("Rechecking permissions after 2 seconds...");
          let uaAll = window.navigator.userAgent;
          let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
          let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;

          if (isAndroid) {
            window.checkPermission.checkPermission();
          } else if (isIos) {
            window.webkit.messageHandlers.checkPermission.postMessage("");
          }
        }, 2000);
      }
    }
  };

  const showDialog = () => {
    Dialog.confirm({
      title: popupTitle.current ? popupTitle.current : "",
      children: popupText.current ? popupText.current : "",
      okText: "前往设置",
      cancelText: "取消",
      onOk: () => {
        handleSettingConfirm();
      },
      platform: platformRef.current === "Android" ? "android" : "ios",
    });
  };

  // 打开设置
  const openSetting = () => {
    if (platformRef.current === "Android") {
      window.openSetting.openSetting();
    } else {
      window.webkit.messageHandlers.openSetting.postMessage("");
    }
  };

  const handleSettingConfirm = () => {
    console.log(clickItem.current.authType);
    if (platformRef.current === "Android") {
      if (
        clickItem.current.authType === AuthTypes.FLOATWIN ||
        clickItem.current.authType === AuthTypes.AUTOSTART ||
        clickItem.current.authType === AuthTypes.ACCESS
      ) {
        // 设置权限请求标志，然后请求权限
        isRequestPermission.current = true;
        requestPermission(clickItem.current.authType);
      } else {
        openSetting();
      }
    } else {
      window.webkit.messageHandlers.openSetting.postMessage("");
    }
  };



  const getIcon = (iconName) => {
    const IconComponent = Icons[iconName]; // 从 Icons 对象中获取对应图标组件
    return IconComponent ? <IconComponent className="primary-icon" /> : null; // 确保存在对应图标
  };

  return (
    <View className="permission">
      {platformRef.current !== "WX" &&<YkNavBar title="系统权限管理" />}

      {/* 权限管理列表 */}
      <View className="cell-container">
        <Cell.Group className="cell-group" bordered={false}>
          {permissionsState.map((item) => (
            <Cell
              key={item.authType}
              className="cell-label"
              label={item.name}
              icon={
                getIcon(item.imgPath)
                // <img
                //   src={require(`../../assets/images/permission/${item.imgPath}`)}
                // />
              }
            >
              <Switch
                platform={
                  platformRef.current === "Android" ? "android" : "ios"
                }
                checked={item.state}
                onChange={(checked) => {}}
                onClick={() => {
                  if (item.state) {
                    openCloseSetPopup(item);
                  } else {
                    openSetPopup(item);
                  }
                }}
              />
            </Cell>
          ))}
        </Cell.Group>

        {/* 前往系统设置 */}
        <Text className="setting-text" onClick={() => openSetting()}>
          前往系统设置
        </Text>
      </View>

      <PermissionPopup
        {...permissionPopupProps}
      />
    </View>
  );
}
