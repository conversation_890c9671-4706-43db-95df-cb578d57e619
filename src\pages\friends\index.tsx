import { View, Text, Image } from "@tarojs/components";
import { useLoad, useDidShow } from "@tarojs/taro";
import "./index.less";
import { Toast, Avatar, SearchBar, LoadMore, Cell, PullRefresh } from "@arco-design/mobile-react";
import Taro from "@tarojs/taro";
import { useState, useCallback, useRef, useEffect } from "react";
import YkNavBar from "@/components/ykNavBar/index";
import { getFriendsList } from "@/utils/api/common/common_user";
import YkSwitchTabBar from "@/components/ykSwitchTabBar/index";
import wx from "weixin-webview-jssdk" ;
import { useDebounce } from "@/utils/yk-common";
import { usePermission } from "@/hooks/usePermission";

import { IconFans, IconUserStarred, IconPayment } from "@/components/YkIcons";

// 好友项数据接口
interface FriendItem {
  id: number;
  userId: number;
  followUserId: number;
  followNickname: string;
  followAvatar: string;
  total: number;
  newNumbers: number;
  createTime: number | null;
  hasOpenOnlinePayment: number;
  isMutual: number;
  starred: number;
}

// 好友列表响应接口
interface FriendsListResponse {
  code: number;
  data: {
    list: FriendItem[];
    total: number;
  };
  msg: string;
}

// 页面状态接口
interface FriendsPageState {
  loading: boolean;
  data: FriendItem[];
  hasMore: boolean;
  page: number;
  pageSize: number;
  error: string | null;
  keyword: string;
}


export default function Friends() {
  // ==================== 状态管理 ====================
  const [pageState, setPageState] = useState<FriendsPageState>({
    loading: false,
    data: [],
    hasMore: true,
    page: 1,
    pageSize: 100,
    error: null,
    keyword: ""
  });

  // Debug状态
  const [debugScenario] = useState('');

  const [platform,setPlatform] = useState<string>("H5");
  const platformRef = useRef<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
      platformRef.current = "Android";
    } else if (isIos) {
      setPlatform("IOS");
      platformRef.current = "IOS";
    } else if (isHM) {
      setPlatform("HM");  
      platformRef.current = "HM";
    } else {
      setPlatform("H5");
      platformRef.current = "H5";
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
      platformRef.current = "WX";
    }
  }, []);

  useDidShow(() => {
    if (platformRef.current == "IOS") {
      window.webkit.messageHandlers.configStatusBarStyle.postMessage("black");
    } else if (platformRef.current == "Android") {
      window.StatusBarDarkMode.StatusBarDarkMode();
    } else if (platformRef.current == "HM") {
      window.harmony.StatusBarDarkMode();
    }
  });

  // 获取用户信息
  const userInfo = Taro.getStorageSync("userInfo");
  
  // 使用 ref 来避免 useCallback 依赖项问题
  const loadingRef = useRef(false);

  // ==================== 生命周期 ====================
  useLoad(() => {
    loadFriendsList(1, true);
  });

  // ==================== 数据加载 ====================
  const loadFriendsList = useCallback(async (page: number = 1, isRefresh: boolean = false, keywordOverride?: string) => {
    // Debug模式下的模拟数据处理
    if (debugScenario === 'mock_data') {
      return loadMockData();
    }
    
    if (debugScenario === 'long_list') {
      setPageState(prev => ({
        ...prev,
        loading: true,
        error: null
      }));
      return loadLongListData(page, isRefresh);
    }
    
    if (debugScenario === 'empty_state') {
      setPageState(prev => ({
        ...prev,
        loading: false,
        data: [],
        hasMore: false,
        error: null
      }));
      return;
    }

    if (debugScenario === 'error_state') {
      setPageState(prev => ({
        ...prev,
        loading: false,
        error: '模拟网络错误'
      }));
      return;
    }

    if (debugScenario === 'loading_state') {
      // 保持loading状态用于测试
      return;
    }

    if (loadingRef.current && !isRefresh) return;

    // 获取用户ID
    const userId = userInfo?.id || userInfo?.userInfo?.id;
    
    if (!userId) {
      setPageState(prev => ({
        ...prev,
        loading: false,
        error: '用户信息获取失败，请重新登录'
      }));
      // Toast.error('用户信息获取失败，请重新登录');
      return;
    }

    loadingRef.current = true;
    setPageState(prev => ({
      ...prev,
      loading: true,
      error: null
    }));

    try {
      const requestData: any = {
        userId,
        pageNo: page,
        pageSize: pageState.pageSize
      };
      
      const currentKeyword = typeof keywordOverride === 'string' ? keywordOverride : pageState.keyword;
      requestData.followNickname = currentKeyword ? currentKeyword : '';

      const response = await getFriendsList(requestData) as FriendsListResponse;

      if (response && response.code === 0) {
        const newData = response.data.list || [];
        setPageState(prev => ({
          ...prev,
          data: isRefresh ? newData : [...prev.data, ...newData],
          hasMore: newData.length === prev.pageSize,
          page: isRefresh ? 1 : page,
          loading: false,
          error: null
        }));
      } else {
        throw new Error(response.msg || '获取好友列表失败');
      }
    } catch (error) {
      console.error('获取好友列表失败:', error);
      const errorMessage = error instanceof Error ? error.message : '网络异常，请重试';
      
      setPageState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage
      }));
      
      // Toast.error(errorMessage);
    } finally {
      loadingRef.current = false;
    }
  }, [pageState.pageSize, pageState.keyword, userInfo, debugScenario]);

  // ==================== 模拟数据 ====================
  const loadMockData = () => {
    const mockData: FriendItem[] = [
      {
        id: 1,
        userId: 300,
        followUserId: 327,
        followNickname: "王英🎈",
        followAvatar: "https://thirdwx.qlogo.cn/mmopen/vi_32/PiajxSqBRaEKibnupd52lom899mlibVYicAESECq0mITgO4bko4LicJ9pWeueZKq4mGJicjJ0a6Tse9O8LWhSqK8OD41sm3sORa88sEhflr0f3ibGJIBLBevh6PXQ/132",
        total: 100,
        newNumbers: 5,
        createTime: Date.now(),
        hasOpenOnlinePayment: 1,
        starred: 1,
        isMutual: 0,
      },
      {
        id: 2,
        userId: 300,
        followUserId: 312,
        followNickname: "小米测试机-开发",
        followAvatar: "https://thirdwx.qlogo.cn/mmopen/vi_32/ib92siaVQoGY9fOQ2t89OuiciaNLI7LjFVb85oFsdVAPW2FnicHoLDocyUqMrxIw1ZvcN7CnIZq9Ddd87icIyplD8Xhria06lF6Wl7NLXIK4vR5558/132",
        total: 200,
        newNumbers: 10,
        createTime: Date.now() - 24 * 60 * 60 * 1000,
        hasOpenOnlinePayment: 0,
        starred: 0,
        isMutual: 0,
      },
      {
        id: 3,
        userId: 300,
        followUserId: 328,
        followNickname: "张三",
        followAvatar: "https://image-resource.mastergo.com/92419737293347/152770503910353/55653774308d6602ccc86319f76f306c.webp",
        total: 150,
        newNumbers: 8,
        createTime: Date.now() - 2 * 24 * 60 * 60 * 1000,
        hasOpenOnlinePayment: 1,
        starred: 1,
        isMutual: 0,
      }
    ];

    setPageState(prev => ({
      ...prev,
      loading: false,
      data: mockData,
      hasMore: false,
      error: null
    }));

  };

  // ==================== 长列表测试数据 ====================
  const loadLongListData = (page: number = 1, isRefresh: boolean = false) => {
    const now = Date.now();
    
    // 生成大量模拟数据
    const generateMockFriend = (id: number): FriendItem => {
      const names = [
        "王英🎈", "小米测试机", "张三", "李四", "王五", "赵六", 
        "李明", "王小红", "张伟", "刘芳", "陈静", "杨帆",
        "赵丽", "钱进", "孙悦", "李娜", "周杰", "吴彦祖",
        "郑爽", "王菲", "刘德华", "张学友", "黎明", "郭富城",
        "梁朝伟", "刘嘉玲", "张曼玉", "王祖贤", "林青霞", "邱淑贞"
      ];
      
      const avatars = [
      ];

      return {
        id,
        userId: 300,
        followUserId: 327 + id,
        followNickname: names[id % names.length] + (id > names.length ? ` ${Math.floor(id / names.length)}` : ''),
        followAvatar: avatars[id % avatars.length],
        total: Math.floor(Math.random() * 1000) + 50,
        newNumbers: Math.floor(Math.random() * 20) + 1,
        createTime: now - Math.random() * 365 * 24 * 60 * 60 * 1000,
        hasOpenOnlinePayment: Math.random() > 0.7 ? 1 : 0,
        starred: Math.random() > 0.8 ? 1 : 0,
        isMutual: 0,
      };
    };

    // 模拟网络延迟
    setTimeout(() => {
      const pageSize = pageState.pageSize;
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      
      // 生成当前页数据
      const currentPageData: FriendItem[] = [];
      for (let i = startIndex; i < endIndex; i++) {
        currentPageData.push(generateMockFriend(i + 1));
      }

      // 模拟总共100条数据，分页加载
      const totalItems = 100;
      const hasMore = endIndex < totalItems;

      setPageState(prev => ({
        ...prev,
        data: isRefresh ? currentPageData : [...prev.data, ...currentPageData],
        hasMore,
        page: page,
        loading: false,
        error: null
      }));
    }, 800); // 模拟800ms网络延迟
  };

  // ==================== 事件处理 ====================
  // 搜索处理
  const handleSearch = useCallback((value: string) => {
    setPageState(prev => ({
      ...prev,
      keyword: value,
      page: 1,
      hasMore: true
    }));
    // 直接按传入关键字发起请求，避免读到旧 state
    loadFriendsList(1, true, value);
  }, [loadFriendsList]);

  const debouncedSearch = useDebounce(handleSearch, 500);

  // 搜索框onChange处理
  const handleSearchChange = (e: any) => {
    const value = e.target.value;
    setPageState(prev => ({ ...prev, keyword: value }));
    
    if (value.length > 0) {
      debouncedSearch(value);
    } else {
      // 清空搜索时，延迟执行避免重复调用
      setTimeout(() => {
        loadFriendsList(1, true);
      }, 0);
    }
  };
  
  // 下拉刷新
  const handleRefresh = useCallback(async () => {
    await loadFriendsList(1, true);
  }, [loadFriendsList]);
  const handleSearchClear = () => {
    setPageState(prev => ({ ...prev, keyword: '' }));
    // 清空时显式传空关键字，避免请求中 followNickname 残留
    loadFriendsList(1, true, '');
  };

  // 跳转用户详情
  const goToUserDetail = (userId: string) => {
    if (platform === "WX") {
      wx.miniProgram.navigateTo({ url: "/pages/web?path=" + encodeURIComponent(`/pageUserInfo/userDetail/index?userId=${userId}`) });
    } else {
      Taro.navigateTo({
        url: `/pageUserInfo/userDetail/index?userId=${userId}`,
      });
    }

  };

  // 加载更多
  const handleLoadMore = useCallback(() => {
    if (pageState.hasMore && !pageState.loading) {
      loadFriendsList(pageState.page + 1);
    }
  }, [pageState.hasMore, pageState.loading, pageState.page, loadFriendsList]);

  // Debug场景切换
  

  // ==================== 渲染函数 ====================
  // 渲染好友列表项
  const renderFriendItem = (item: FriendItem) => {
    return (
      <Cell 
        key={item.followUserId} 
        className="friend-item" 
        onClick={() => goToUserDetail(item.followUserId.toString())}
        label={
          <View className="friend-item-inner">
            <View className="friend-avatar">
              <Avatar 
                src={item.followAvatar || require('../../assets/images/common/default_head.png')} 
                size="small"
              />
            </View>
            <View className="friend-content">
              <View className="nameRow">
                <Text className="nickname">{item.followNickname}</Text>
                {!!item.hasOpenOnlinePayment && (
                  <IconPayment className="jinjianIcon" size={16} />
                )}
                {!!item.starred && (
                  <IconUserStarred className="starredIcon" size={16} />
                )}
              </View>
              <View className="countRow">
                <Text className="count">上新 {item.newNumbers}</Text>
                <Text className="count">总共 {item.total}</Text>
              </View>
            </View>
          </View>
        }
      />
    );
  };

  // ==================== 主渲染 ====================
  return (
    <View className="friendsBox">
      {platform !== "WX" && <YkNavBar switchTab title="好友" />}
      
      {!userInfo || !userInfo.id ? (
        // 未登录状态
        <View className="wait-login-container">
          <View className="wait-login-container-content">
            <View className="wait-login-container-text-area">
              <Text className="wait-login-container-text-area-title">登录可查看好友列表</Text>
              <Text className="wait-login-container-text-area-subtitle">您当前未登录账号，请登录后查看好友列表</Text>
            </View>
            <View className="wait-login-container-image-wrapper">
              <Image
                className="wait-login-container-image-wrapper-bg"
                src={require("@/assets/images/common/friend_wait_login_bg.png")}
                mode="aspectFill"
              />
            </View>
          </View>
          <View 
            className="wait-login-container-login-btn"
            onClick={() => {
              if (platformRef.current === "WX") {
                wx.miniProgram.navigateTo({ url: "/pages/index/index" });
              } else {
                Taro.navigateTo({ url: "/pages/login/index" });
              }
            }}
          >
            <Text>立即登录</Text>
          </View>
        </View>
      ) : (
        // 已登录状态
        <>
          {/* 搜索栏 */}
          <View className="search-container" 
           style={{ ...platform !== "WX" ? { top: '80px' } : { top: '0' } }}>
            <SearchBar
              shape="square"
              placeholder="请输入好友昵称"
              value={pageState.keyword}
              onChange={handleSearchChange}
              clearable
              onClear={handleSearchClear}
              onCancel={handleSearchClear} 
            />
          </View>

          {/* 粉丝入口 */}
          <View className="fansContainer">
            <View className="fansEntry" onClick={() => {
              if (platform === "WX") {
                wx.miniProgram.navigateTo({ url: "/pages/web?path=/pageUserInfo/fans/index" });
              } else {
                Taro.navigateTo({ url: '/pageUserInfo/fans/index' });
              }
            }}>
              <Avatar shape="circle" size="medium" className="fansIcon" avatarStyle={{backgroundColor: '#FF7D00'}}>
                <IconFans size={22} color="#fff" />
              </Avatar>
              <Text className="fansText">粉丝</Text>
            </View>
          </View>
          <PullRefresh onRefresh={handleRefresh}>
          <View className="listBox">
            {/* 空状态 */}
            {pageState.data.length === 0 && !pageState.loading && (
              <View className="emptyBox">
                <View className="empty-content">
                  {/* <Image src={require('../../assets/images/common/not_content.png')} className="emptyIcon" bottomOverlap={null} /> */}
                  <View className="empty-text-container">
                    <Text className="emptyText">暂无好友</Text>
                    <Text className="subText">快去扫码添加好友吧~</Text>
                  </View>
                </View>
              </View>
            )}

            {/* 好友列表 */}
            {pageState.data.length > 0 && (
              <>
                <View className="list-header">
                  <Text className="listTitle">全部</Text>
                </View>
                <Cell.Group className="list-container" bordered={false}>
                  {pageState.data.map(renderFriendItem)}
                </Cell.Group>
                {/* 加载更多 */}
                {pageState.page > 1 && (
                <LoadMore
                  style={{ paddingTop: 16, paddingBottom: 16 }}
                  status={pageState.loading ? "loading" : pageState.hasMore ? "prepare" : "nomore"}
                  threshold={0}
                  getData={handleLoadMore}
                />
                )}
              </>
            )}

          </View>
          </PullRefresh>
        </>
      )}
      
      <YkSwitchTabBar activeTab={1} />

      {/* Debug面板 */}
      {/* {process.env.NODE_ENV === 'development' && (
        <DebugPanel
          scenario={debugScenario}
          config={debugConfig}
          onLoadMockData={handleDebugScenario}
          statusInfo={{
            '数据长度': pageState.data.length,
            '加载状态': pageState.loading ? '加载中' : '已完成',
            '当前页码': pageState.page,
            '每页条数': pageState.pageSize,
            '是否还有更多': pageState.hasMore ? '是' : '否',
            '调试场景': debugScenario || '无',
            '错误信息': pageState.error || '无'
          }}
          position="bottom-right"
        />
      )} */}
    </View>
  );
} 