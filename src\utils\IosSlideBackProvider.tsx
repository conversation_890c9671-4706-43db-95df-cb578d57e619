import React, { createContext, useContext, useEffect } from 'react';
import Taro from '@tarojs/taro';
import { throttle } from './utils';

// 创建Context
const IosSlideBackContext = createContext<{
  enable: boolean;
  setEnable: (enable: boolean) => void;
}>({
  enable: true,
  setEnable: () => {},
});

// 全局变量
let startTime = 0;
let currentX = 0;
let moving = false;
let startX = 0;

// Provider组件
interface IosSlideBackProviderProps {
  children: React.ReactNode;
  defaultEnable?: boolean;
}

export const IosSlideBackProvider: React.FC<IosSlideBackProviderProps> = ({
  children,
  defaultEnable = true,
}) => {
  const [enable, setEnable] = React.useState(defaultEnable);

  useEffect(() => {
    if (!enable) return;

    // 检查是否在iOS环境下
    let uaAll = window.navigator.userAgent;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;

    if (!isIos) {
      console.log('非iOS环境，不启用左滑返回功能');
      return;
    }

    console.log('iOS环境，启用左滑返回功能');

    const touchStartHandler = (e: TouchEvent) => {
      const pages = Taro.getCurrentPages();
      const currentPage = pages[pages.length - 1];
      
      // 只有在页面栈大于1时才启用
      if (pages.length <= 1) return;

      const touchX = e.touches[0].clientX;
      const threshold = 16; //window.innerWidth * 0.1;
      
      // 只有在屏幕左侧10%区域内开始触摸才触发
      if (touchX < threshold) {
        startTime = Date.now();
        const curPage = document.getElementById(currentPage.$taroPath) as HTMLElement;
        
        if (curPage?.style) {
          curPage.style.willChange = 'transform';
          curPage.style.overflowY = 'hidden';
        }
        
        startX = touchX;
        moving = true;
        console.log('开始左滑手势');
      }
    };

    const touchMoveHandler = throttle((e: TouchEvent) => {
      if (!moving) return;

      const pages = Taro.getCurrentPages();
      const currentPage = pages[pages.length - 1];
      
      const touchX = e.touches[0].clientX;
      currentX = touchX;
      const moveDistance = touchX - startX;
      const curPage = document.getElementById(currentPage.$taroPath) as HTMLElement;
      
      if (curPage?.style) {
        curPage.style.transform = `translate3d(${moveDistance}px, 0, 0)`;
      }
    }, 100);

    const touchEndHandler = () => {
      if (!moving) return;

      const pages = Taro.getCurrentPages();
      const currentPage = pages[pages.length - 1];
      
      const endTime = Date.now();
      const elapsedTime = endTime - startTime;
      moving = false;
      
      const curPage = document.getElementById(currentPage.$taroPath) as HTMLElement;
      if (!curPage?.style) return;
      
      curPage.style.transition = 'transform 0.1s ease';
      const moveDistance = currentX - startX;
      const halfWidth = window.innerWidth * 0.5;
      const speed = moveDistance / elapsedTime;
      
      // 设置速度阈值，可根据实际情况调整
      const speedThreshold = 0.1;
      
      console.log(`左滑结束 - 距离: ${moveDistance}, 速度: ${speed}, 阈值: ${speedThreshold}`);

      console.log(`window.innerWidth: ${window.innerWidth}, startX: ${startX}, currentX: ${currentX}`);

      // 滑动距离大于屏幕一半或滑动速度大于阈值时返回上一页 moveDistance > halfWidth || speed > speedThreshold
      if (moveDistance > halfWidth || (speed > speedThreshold && moveDistance > 10)) {
        console.log('触发返回操作');
        curPage.style.transform = 'translate3d(100%,0,0)';
        Taro.navigateBack();
      } else {
        console.log('未达到返回条件，恢复原位');
        curPage.style.transform = 'translate3d(0px,0,0)';
      }
      
      // 清理样式
      setTimeout(() => {
        if (curPage?.style) {
          curPage.style.willChange = '';
          curPage.style.transition = '';
          curPage.style.transform = '';
          curPage.style.overflowY = 'auto';
        }
      }, 300);
    };

    // 添加事件监听
    document.addEventListener('touchstart', touchStartHandler);
    document.addEventListener('touchmove', touchMoveHandler);
    document.addEventListener('touchend', touchEndHandler);

    return () => {
      document.removeEventListener('touchstart', touchStartHandler);
      document.removeEventListener('touchmove', touchMoveHandler);
      document.removeEventListener('touchend', touchEndHandler);
    };
  }, [enable]);

  return (
    <IosSlideBackContext.Provider value={{ enable, setEnable }}>
      {children}
    </IosSlideBackContext.Provider>
  );
};

// 自定义hook，用于在组件中控制iOS左滑返回功能
export const useIosSlideBackControl = () => {
  const context = useContext(IosSlideBackContext);
  if (!context) {
    throw new Error('useIosSlideBackControl must be used within IosSlideBackProvider');
  }
  return context;
};
