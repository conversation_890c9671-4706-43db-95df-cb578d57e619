import { View, Text } from "@tarojs/components";
import { Popup, But<PERSON> } from "@arco-design/mobile-react";
import { AuthTypes } from "@/utils/config/authTypes";

// import * as Icons from "@arco-design/mobile-react/esm/icon";
import * as Icons from "@arco-iconbox/react-yk-arco";
import "./index.less";

const getPermissionData = (type) => {
  switch (type) {
    case AuthTypes.CAMERA:
      return {
        title: `${APP_NAME_CN}申请获取相机权限`,
        img: "IconCamera",
      };
    case AuthTypes.GALLERY_PHOTO:
    case AuthTypes.GALLERY_VIDEO:
    case AuthTypes.GALLERY_AUDIO:
    case AuthTypes.STORAGE:
      return {
        title: `${APP_NAME_CN}申请获取存储权限`,
        img: "IconStorage",
      };
    case AuthTypes.AUDIO:
      return {
        title: `${APP_NAME_CN}申请获取麦克风权限`,
        img: "IconVoice",
      };
    case AuthTypes.NOTICE:
      return {
        title: `${APP_NAME_CN}申请获取通知权限`,
        img: "IconNotification",
      };

    case AuthTypes.LOCATION:
      return {
        title: `${APP_NAME_CN}申请获取位置权限`,
        img: "IconLocation",
      };
    case AuthTypes.CONTACT:
      return {
        title: `${APP_NAME_CN}申请获取通讯录权限`,
        img: "IconMobile",
      };
    default:
      return {
        title: "",
        img: "",
      };
  }
};

const getIcon = (iconName) => {
  const IconComponent = Icons[iconName]; // 从 Icons 对象中获取对应图标组件
  return IconComponent ? <IconComponent /> : null; // 确保存在对应图标
};

const PermissionPopup = ({
  visible,
  type,
  hintText,
  hideButtons = true,
  onClose,
  onConfirm,
}) => {
  const { title: titleText, img: imgPath } = getPermissionData(type);

  return (
    <Popup className="permission-popup" visible={visible} direction="center">
      <View className="popupBox">
        <View className="popupBox-icon"> {getIcon(imgPath)}</View>
        <View className="popupBox-container">
          <Text className="popupBox-container-title">{titleText}</Text>
          <Text className="popupBox-container-hint">{hintText}</Text>
        </View>
        {!hideButtons && (
          <View className="popupBox-bottom">
            <Button className="popupBox-bottom-confirm" onClick={onConfirm}>
              手动开启
            </Button>
            <Button className="popupBox-bottom-cancel" onClick={onClose}>
              暂不开启
            </Button>
          </View>
        )}
      </View>
    </Popup>
  );
};

export default PermissionPopup;
