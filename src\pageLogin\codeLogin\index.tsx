import { View, Text } from '@tarojs/components'
import { Button, Image, Input, Checkbox } from '@arco-design/mobile-react'
import YkNavBar from '@/components/ykNavBar'
import UserAgreementPopup from '@/components/UserAgreementPopup'
import './index.less'
import Taro from '@tarojs/taro'
import { toast } from "@/utils/yk-common";
import { useState, useEffect } from 'react';

export default function CodeLogin() {
  const [input, setInput] = useState('');
  const [check, setCheck] = useState(false);
  const [visible, setVisible] = useState(false);
  const [isShowKeyBoard, setIsShowKeyBoard] = useState(false);

  useEffect(() => {
    window.webShowKeyBoard = showKeyBoard;
    window.webHideKeyBoard = hideKeyBoard;
    return () => {
      delete window.webShowKeyBoard;
      delete window.webHideKeyBoard;
    }
  }, []);

  const showKeyBoard = () => {
    setIsShowKeyBoard(true);
  }
  const hideKeyBoard = () => {
    setIsShowKeyBoard(false);
  }

  const goCode = () => {
    if (!check) {
      setVisible(true);
      return;
    }
    if (input.length !== 13) {
      toast("error", {
        content: "请输入正确的手机号",
        duration: 2000,
      });
      return;
    }
    Taro.navigateTo({url: '/pageLogin/codeLogin/code?phone=' + input.replace(/ /g, '')});
  }

  return (
    <View className='new-login'>
      <YkNavBar title=''/>
      <View className='logo-container'>
        <Image className='logo' src={require('../../assets/images/login/logo.png')} bottomOverlap={null} />
        <Text className='app-name'>验证码登录</Text>
      </View>

      <View className='input-container'>
        <Input className='input-field' placeholder='请输入手机号' type='tel' border='none'             value={input}
            maxLength={13}
            onInput={(_, value) => {
                const newValue = (value || '').replace(/ /g, '');
                setInput(`${newValue.slice(0, 3)} ${newValue.slice(3,7)} ${newValue.slice(7)}`.trim());
            }} />
      </View>

      <View className='login-buttons'>
        <Button className='code-button' onClick={goCode}>获取验证码</Button>
      </View>

{!isShowKeyBoard && (

      <View className='pwd-login' onClick={() => {Taro.navigateBack()}}>
        <Image className='pwd-logo' src={require('../../assets/images/login/pwdlogin.png')} bottomOverlap={null} />
        <Text className='pwd-text'>密码登录</Text>
      </View>
   )}

{!isShowKeyBoard && (
      <View className='agreement'>
      <Checkbox value={''} checked={check} onChange={() => { setCheck(!check) }}></Checkbox>
      <Text>已阅读并同意</Text>
        <Text className='link' onClick={() => {Taro.navigateTo({url: `/pageSetting/webView/index?url=${USER_AGREEMENT}&name=用户协议`})}}>《用户协议》</Text>
        <Text>和</Text>
        <Text className='link' onClick={() => {Taro.navigateTo({url: `/pageSetting/webView/index?url=${PRIVACY_POLICY}&name=隐私政策`})}}>《隐私政策》</Text>
      </View>
      )}


      <UserAgreementPopup
        visible={visible}
        onClose={() => setVisible(false)}
        onConfirm={() => {
          // 在 codeLogin 页面中，同意后不需要执行特殊操作
        }}
        onSetCheck={setCheck}
      />
    </View>
  )
} 