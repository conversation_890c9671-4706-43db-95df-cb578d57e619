/**
 * YkIcons 工具函数
 * 用于动态加载和管理图标组件
 */

import * as YkIcons from './index';

/**
 * 获取所有图标组件
 * @returns 图标组件数组，包含名称和组件
 */
export const getAllIcons = () => {
  return Object.entries(YkIcons)
    .filter(([name, component]) => 
      name.startsWith('Icon') && typeof component === 'function'
    )
    .map(([name, component]) => ({
      name,
      component,
      displayName: name.replace('Icon', ''),
    }));
};

/**
 * 获取图标总数
 * @returns 图标数量
 */
export const getIconCount = () => {
  return getAllIcons().length;
};

/**
 * 根据名称查找图标
 * @param iconName 图标名称
 * @returns 图标组件或undefined
 */
export const findIcon = (iconName: string) => {
  const icons = getAllIcons();
  return icons.find(icon => 
    icon.name === iconName || 
    icon.displayName.toLowerCase() === iconName.toLowerCase()
  );
};

/**
 * 检查图标是否存在
 * @param iconName 图标名称
 * @returns 是否存在
 */
export const hasIcon = (iconName: string): boolean => {
  return findIcon(iconName) !== undefined;
};

/**
 * 获取图标列表信息（用于调试）
 * @returns 图标信息数组
 */
export const getIconsInfo = () => {
  const icons = getAllIcons();
  return {
    total: icons.length,
    icons: icons.map(({ name, displayName }) => ({
      name,
      displayName,
      path: `@/components/YkIcons/${name}`
    })),
    lastUpdated: new Date().toISOString()
  };
};
