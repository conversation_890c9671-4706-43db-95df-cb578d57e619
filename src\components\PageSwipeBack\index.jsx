import { useState } from 'react';
import Taro from '@tarojs/taro';
import { View } from '@tarojs/components';
import './index.less'; // 样式文件

const PageSwipeBack = ({ leftDrag = true, rightDrag = false, children }) => {
  const [startPosX, setStartPosX] = useState(0);
  const [direction, setDirection] = useState('left');
  const windowWidth = Taro.getSystemInfoSync().windowWidth;

  const handleStart = (e) => {
    if (!e.touches || e.touches.length === 0) return;
    const startX = e.touches[0].clientX;
    setStartPosX(startX);
    setDirection(startX > windowWidth / 2 ? 'right' : 'left');
  };

  const handleEnd = (e) => {
    if (!e.changedTouches || e.changedTouches.length === 0) return;
    const endX = e.changedTouches[0].clientX;
    const offset = endX - startPosX;

    const canDrag =
      (direction === 'left' && offset >= 50) ||
      (direction === 'right' && offset <= -50);

    if (canDrag) {
      setStartPosX(0);
      Taro.navigateBack();
    }
  };

  return (
    <View>
      {/* 左侧滑动区域 */}
      {leftDrag && (
        <View
          className="left-side"
          onTouchStart={(e) => handleStart(e)}
          onTouchEnd={(e) => handleEnd(e)}
        />
      )}

      {/* 右侧滑动区域 */}
      {rightDrag && (
        <View
          className="right-side"
          onTouchStart={(e) => handleStart(e)}
          onTouchEnd={(e) => handleEnd(e)}
        />
      )}

      {/* 渲染子内容 */}
      <View>{children}</View>
    </View>
  );
};

export default PageSwipeBack;

