import { View, Text } from "@tarojs/components";
import Taro from "@tarojs/taro";
import React, { useState, useEffect } from "react";
import YkNavBar from "@/components/ykNavBar/index";
import { Checkbox, Dialog } from "@arco-design/mobile-react";
import "./setSort.less";
import  { useRef } from "react";
const SORT_OPTIONS = [
  { label: "默认排序", value: "0" },
  { label: "字母排序", value: "1" }
];

export default function SetSort() {
  const [selected, setSelected] = useState("");
  const pending = useRef("default");
  const [platform,setPlatform] = useState<string>("H5");

  const openSort = ()=>{
    Dialog.confirm({
      title: "确认修改排序方式?",
      children: " 排序方式修改后目录将按新的方式排序并显示",
      okText: "确定",
      cancelText: "取消",
      platform: 'ios',
      onOk: () => {
        handleConfirm(pending.current);
      }
    })
  }

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  // 选择排序方式时，先弹出确认框
  const handleSelect = (value) => {
    pending.current = value;
    openSort();
  };

  // 确认修改
  const handleConfirm = (value) => {
    Taro.setStorageSync('selectedTagSort', value);
    setSelected(pending.current);

    // 通知标签和目录页面刷新数据
    Taro.eventCenter.trigger('refreshTagList');
    Taro.eventCenter.trigger('refreshCatalogList');

    // 返回上一页
    Taro.navigateBack();
  };
  useEffect(() => {
    const selectedTagSort = Taro.getStorageSync('selectedTagSort');
    setSelected(selectedTagSort || "0");
  }, []);
  return (
    <View className="set-sort-page">
      {platform !== "WX" &&<YkNavBar title="排序方式" />}
      <View className="set-sort-list">
        {SORT_OPTIONS.map((item, idx) => (
          <View
            className="set-sort-item"
            key=""
          >
            <Checkbox
              checked={selected === item.value}
              onChange={() => handleSelect(item.value)}
              className="set-sort-checkbox"
              shape="circle"
              value=""
            >
                <Text className="set-sort-label">{item.label}</Text>
            </Checkbox>
            {idx !== SORT_OPTIONS.length - 1 && <View className="set-sort-divider" />}
          </View>
        ))}
      </View>
      {/* {showConfirm && (
        <View className="sort-confirm-mask">
          <View className="sort-confirm-modal">
            <View className="sort-confirm-title">确认修改排序方式?</View>
            <View className="sort-confirm-content">
              排序方式修改后目录将按新的方式<br />
              排序并显示
            </View>
            <View className="sort-confirm-actions">
              <View className="sort-confirm-cancel" onClick={() => setShowConfirm(false)}>取消</View>
              <View className="sort-confirm-ok" onClick={() => handleConfirm(pending)}>确定</View>
            </View>
          </View>
        </View>
      )} */}
    </View>
  );
}
