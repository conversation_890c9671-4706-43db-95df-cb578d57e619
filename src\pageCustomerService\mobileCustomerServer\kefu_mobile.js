// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import request from './request'

/**
 * 客服详细信息
 * @constructor
 */
export function serviceInfo() {
    return request({
        url: 'service/info',
        method: 'get',
        kefu: true
    });
}

/**
 * 用户端发送消息
 * @param data
 */
export function sendMessageMobile(data) {
    return request({
        url: 'service/send_message',
        method: 'post',
        mobile: true,
        data
    });
}

// userRecord
export function userRecord(params) {
    return request({
        url: 'user/record',
        method: 'get',
        mobile: true,
        params
    })
}

/*
  客户端 上传图片
*/
export function serviceUpload(data) {
    return request({
        url: 'service/upload',
        method: 'post',
        mobile: true,
        data
    })
}