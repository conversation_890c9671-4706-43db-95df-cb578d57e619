import { View, Text } from "@tarojs/components";
import { useEffect,useRef, useState } from "react";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import { Image } from "@arco-design/mobile-react";
import Taro from "@tarojs/taro";
import YkNavBar from "@/components/ykNavBar/index";
const userInfo = Taro.getStorageSync('userInfo') || {};

// 组件
export default function aboutUs() {
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
 

   const formatPhoneNumber = (phoneNumber) => {
      const start = phoneNumber.substring(0, 3);
      const end = phoneNumber.substring(phoneNumber.length - 4);
      return start + "****" + end;
  };


  return (
    <View className="PICCPageContent">
      {platform !== "WX" &&<YkNavBar title="" />}
      <View className="PICCPageContent-title">
        <View className="PICCPageContent-title-name">「手机号」收集情况</View>
      </View>
      <View className="PICCModule">
        <View className="PICCModule-item">
          <View className="PICCModule-item-label">内容</View>
          <View className="PICCModule-item-content">{formatPhoneNumber(userInfo.mobile)}</View>
        </View>
        <View className="PICCModule-item">
          <View className="PICCModule-item-label">目的</View>
          <View className="PICCModule-item-content">
            <Text className="PICCModule-item-content-text">
              用户自行完善设置
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
}
