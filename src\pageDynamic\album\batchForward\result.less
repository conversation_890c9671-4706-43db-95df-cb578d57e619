@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pageDynamic/album/batchForward/result"] {
  .batch-forward-result {
    min-height: 100vh;
    background-color: @card-background-color !important;
    .use-dark-mode-query({
    background-color: @dark-card-background-color !important;
  });

    // 主要内容区域
    .main-content {
      margin-top: 165px;
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 0px 30px;

      // 成功图标
      .success-icon {
        margin-bottom: 20px;
        width: 50px;
        height: 50px;
        color: var(--dark-primary-color);
      }

      // 转发成功文字
      .success-text {
        font-size: 15px;
        color: #333333;
        .use-var(color, font-color);
        .use-dark-mode-query({
        color: var(--dark-font-color) !important;
      });
        font-weight: 500;
        margin-bottom: 12px;
        text-align: center;
      }

      // 描述文字
      .success-desc {
        font-size: 14px;
        color: #666666;
        .use-dark-mode-query({
        color: var(--dark-font-color);
      });
        text-align: center;
        line-height: 1.5;

        .highlight {
          color: var(--dark-primary-color);
          font-weight: 600;
        }
      }
    }

    // 底部按钮区域
    .bottom-button {
      margin-top: 300px;
      padding: 0 30px;
      padding-bottom: constant(safe-area-inset-bottom);
      padding-bottom: env(safe-area-inset-bottom);
      display: flex;
      gap: 16px;
      justify-content: center;
      align-items: center;

      // 按钮样式
      .result-btn {
        flex: 1;
        font-size: 16px;
        font-weight: 500;
        border: none;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 44px !important;
        border-radius: 6px !important;

        &.secondary {
          background-color: transparent !important;
          color: var(--dark-primary-color) !important;
          border: 1px solid var(--dark-primary-color) !important;

          &:active {
            background-color: rgba(22, 93, 255, 0.1) !important;
          }
        }

        &.primary {
          background-color: var(--dark-primary-color) !important;
          color: #ffffff !important;
          border: none !important;

          &:active {
            background-color: var(
              --dark-primary-color-hover,
              #0e4ba8
            ) !important;
          }
        }
      }
    }
    
  }
}
