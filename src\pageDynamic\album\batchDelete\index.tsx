import { View, Text, Image, ScrollView } from "@tarojs/components";
import React, { useState, useEffect, useRef  } from "react";
import Taro, { useDidShow, useReady } from "@tarojs/taro";
import { Dialog, SearchBar, ActionSheet, Checkbox, Button } from "@arco-design/mobile-react";
import YkNavBar from "@/components/ykNavBar/index";
import { getMyAlbumList,getUserHomeTopData, deleteDynamic, updateDynamic } from "@/utils/api/common/common_user";
import "./index.less";
import { count } from "console";
import { content } from "html2canvas/dist/types/css/property-descriptors/content";
import { toast } from "@/utils/yk-common";
import { IconLoadEmpty } from "@/components/YkIcons";
import SelectQuantityManager from '@/components/SelectQuantityPopup/SelectQuantityManager';

// 定义 dynamic 类型
interface DynamicItem {
  id: string | number;
  content: string;
  price: number;
  time: string;
  pictures: string;  // API returns pictures as comma-separated string
  coverImage: string[];  // Transformed pictures into array
  skus?: string[];
  color?: string[];
  digitalWatermark?: string;
  isTop?: number;  // Add isTop property to the interface
}

interface GroupedItem {
  group: string;
  items: DynamicItem[];
}

interface DynamicData {
  items: GroupedItem[];
  totalCount?: number;
  hasMore?: boolean;
  allItems?: DynamicItem[]; // 添加 allItems 字段存储所有商品数据
}

interface UserHomeTopData {
  avatar?: string;
  nickname?: string;
  total?: number;
}

// 定义 tab 类型
type TabType = 'all' | 'new' | 'video' | 'image';

export default function BatchDeletePage() {
  const [dynamic, setDynamic] = useState<DynamicData>({ items: [], hasMore: true, allItems: [] });
  const [selectedIds, setSelectedIds] = useState<(string|number)[]>([]);
  const [isAllSelected, setIsAllSelected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // 自定义数量相关状态
  const [popupVisible, setPopupVisible] = useState(false);
  const [pageNo, setPageNo] = useState(1);
  const [pageSize] = useState(20);
  const [sortType, setSortType] = useState(1);
  const loginUserInfo = Taro.getStorageSync('userInfo')
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  const getDynamicListData = async (content=' ') => {
    if (isLoading) return; // 防止重复加载

    setIsLoading(true);
    Taro.showLoading({
      title: '加载中...',
      mask: true
    });

    const data = {
      pageNo: pageNo,
      pageSize: pageSize,
      userId: loginUserInfo.id,
      sort: sortType,
      homePageCountType:1,
    } as any;

    try {
      const res: any = await getMyAlbumList(data);
      Taro.hideLoading();
      
      if (res && res.code === 0 && res.data) {
        const itemsWithSkus = res.data.list?.map(item => {
          const pictures = item.pictures || '';
          // 分割图片字符串，并取第一张（过滤空值）
          const pictureArray = pictures.split(',').filter(Boolean);
          const coverImage = pictureArray[0] || ''; // 如果没有图片则设为空字符串;

          return {
            ...item,
            pictures,
            coverImage: coverImage,
            pictureArray: pictureArray,
          };
        }) || [];
  
        // 更新状态，累加数据
        setDynamic(prevDynamic => {
          // 合并所有商品数据
          const allItems = pageNo === 1 ? itemsWithSkus : [...(prevDynamic.items || []), ...itemsWithSkus];
          const hasMore = res.data.list?.length === pageSize;

          return {
            items: allItems,
            hasMore
          };
        });

      } else {
        toast("info", {
        content: res.msg || '网络异常，请重试',
        duration: 2000
      });
      }
    } catch (error) {
      Taro.hideLoading();
      toast("info", {
        content: "网络异常，请重试",
        duration: 2000
      });
      console.error('获取动态列表失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 获取数据逻辑同主页面
  useEffect(() => {
    getDynamicListData();
  }, []);

  // 加载更多
  const loadMore = () => {
    if (!isLoading && dynamic.hasMore) {
      setPageNo(prev => prev + 1);
    }
  };

  // 监听页码变化，加载数据
  useEffect(() => {
    if (pageNo > 1) {
      getDynamicListData();
    }
  }, [pageNo]);

  // 自定义数量选择函数
  const selectNum = (num: number) => {
    const allItems = dynamic.items || [];
    let newIds: (string | number)[] = [];

    const selectCount = Math.min(num, allItems.length);
    for (let i = 0; i < selectCount; i++) {
      newIds.push(allItems[i].id);
    }

    setSelectedIds(newIds);
    setIsAllSelected(newIds.length === allItems.length);
  };



  // 全选/反选
  const handleSelectAll = () => {
    if (isAllSelected) {
      setSelectedIds([]);
      setIsAllSelected(false);
    } else {
      // 直接使用API返回的数组结构
      const all = (dynamic.items || []).map((item: any) => item.id);
      setSelectedIds(all);
      setIsAllSelected(true);
    }
  };

  // 单选
  const handleSelect = (id: any) => {
    let newSelected;
    if (selectedIds.includes(id)) {
      newSelected = selectedIds.filter((_id: any) => _id !== id);
    } else {
      newSelected = [...selectedIds, id];
    }

    setSelectedIds(newSelected);
    // 直接使用API返回的数组结构
    setIsAllSelected(newSelected.length === (dynamic.items || []).length);
  };

  // 批量删除
  const handleBatchDelete = () => {
    if (selectedIds.length === 0) return;
    Dialog.confirm({
      platform: 'ios',
      className: 'dialog-input-demo',
      title: '温馨提示',
      contentAlign: 'left',
      children: (<>
          <div className="dialog-input-demo-hint" style={{ textAlign: 'center' }}> 删除后不可恢复<br/>确认要删除<span style={{ color: '#f50' }}>{selectedIds.length}</span>件商品吗？</div>
      </>),

      onOk: async () => {
        setIsLoading(true);
        try {
          // 使用批量删除接口，传入逗号分隔的 ids
          const idsString = selectedIds.join(',');
          const res: any = await deleteDynamic(idsString);

          if (res && res.code === 0) {
            // 删除成功后，重新获取数据来刷新当前页面列表
            setSelectedIds([]);
            setIsAllSelected(false);

            // 重置页码并重新加载数据
            setPageNo(1);
            // setDynamic({ items: [], hasMore: true });

            toast("info", {
              content: "删除成功",
              duration: 2000
            });
            getDynamicListData();

            // 触发主页面刷新
            Taro.eventCenter.trigger('refreshAlbumList');
          } else {
            toast("info", {
              content: res.message || "删除失败，请重试",
              duration: 2000
            });
          }
        } catch (error) {
          console.error('批量删除失败:', error);
          toast("info", {
            content: "删除失败，请重试",
            duration: 2000
          });
        } finally {
          setIsLoading(false);
        }
      }
    });
  };

  // 渲染单个动态项目 - 参考批量上下架样式
  const renderDynamicItem = (item: any) => {
    const images = item.pictures ? item.pictures.split(',').filter((img: string) => img.trim() !== '') : [];

    return (
      <View key={item.id} className="dline">
        <View className="dline-left">
          <Checkbox
            checked={selectedIds.includes(item.id)}
            className="dline-left-change"
            onChange={() => handleSelect(item.id)}
          />
        </View>
        <View className="dline-right">
          {item.content && (
            <View className={`dline-right-top ${!item.pictures || item.pictures === '' ? 'topBg' : ''}`}>
              <View className="dynamic-title">{item.content}</View>
            </View>
          )}
          {item.pictures && (
            <View className="dline-right-bottom">
              {images.slice(0, 5).map((imgUrl: string, imgIndex: number) => (
                <View key={imgIndex} className="imageItem">
                  <Image
                    src={imgUrl}
                    mode="aspectFill"
                    className="imageItem-image"
                  />
                  {imgIndex === 4 && images.length > 5 && (
                    <View className="imageItem-mask">
                      <Text>+{images.length - 5}</Text>
                    </View>
                  )}
                </View>
              ))}
            </View>
          )}
        </View>
      </View>
    );
  };

  // 渲染空内容
  const renderEmptyContent = () => (
    <View className="not_content">
      {/* <Image
        className="not_content-image"
        src={require("@/assets/images/common/not_content_trend.png")}
      /> */}
      <IconLoadEmpty className="not_content-image" />

      <Text>暂无动态内容</Text>
    </View>
  );

  // 列表渲染
  const renderList = () => {
    // 直接使用API返回的数据结构
    const allItems = dynamic.items || [];

    return (
      <View className="boxContent">
        {allItems.length === 0 && !isLoading? (
          renderEmptyContent()
        ) : (
          allItems.map((item: any) => renderDynamicItem(item))
        )}
      </View>
    );
  };

  return (
    <View className="batch-delete-page">
      {platform !== "WX" &&<YkNavBar title="批量删除" />}

      {/* 列表内容区域 */}
      <ScrollView
        className="content-scroll"
        scrollY
        onScrollToLower={loadMore}
        lowerThreshold={50}
      >
        {renderList()}
        {/* 加载更多提示 */}
        {isLoading && dynamic.items && dynamic.items.length > 0 && (
          <View className="loading-more">
            <Text className="loading-text">加载中...</Text>
          </View>
        )}
        {!dynamic.hasMore && dynamic.items && dynamic.items.length > 0 && (
          <View className="no-more">
            <Text className="no-more-text">没有更多了</Text>
          </View>
        )}
      </ScrollView>

      {/* 底部操作栏 - 完全照搬批量上下架样式 */}
      <View className="footerBtnBox">
        <View className="footerBtnBox-change">
          <Checkbox
            checked={isAllSelected}
            onChange={handleSelectAll}
            className="footerBtnBox-change-image"
          />
          <View className="footerBtnBox-change-c" onClick={() => setPopupVisible(true)}>
            <Text className="footerBtnBox-change-c-text">
              选中{selectedIds.length}条
            </Text>
            <Image
              src={require("@/assets/images/common/check_all_icon.png")}
              className="footerBtnBox-change-c-img"
            />
          </View>
        </View>

        <Button
          type="primary"
          className={selectedIds.length > 0 ? "footerBtnBox-btn" : "footerBtnBox-notbtn"}
          disabled={selectedIds.length === 0}
          onClick={handleBatchDelete}
          loading={isLoading}
        >
          删除
        </Button>
      </View>

      <SelectQuantityManager
        visible={popupVisible}
        onClose={() => setPopupVisible(false)}
        onSelectAll={handleSelectAll}
        onSelectCustom={selectNum}
        totalCount={dynamic.items?.length || 0}
      />
    </View>
  );
}
