import { View, Text } from '@tarojs/components'
import { Button, Image, Avatar } from '@arco-design/mobile-react'
import { IconTriDown } from '@arco-design/mobile-react/esm/icon'
import './index.less'
import Taro from '@tarojs/taro'

import { socialLogin, getUserInfo, followUser, getFollowUserInfo, getSocailAuthRedirect } from '@/utils/api/common/common_user'
import { useState, useEffect } from 'react'
import YkNavBar from '@/components/ykNavBar'
import { md5 } from 'js-md5'
import {URL_BASE} from '@/utils/api/urls';

import { toast } from "@/utils/yk-common";
export default function FollowUser() {
  const [userInfo, setUserInfo] = useState({});
  const [friendId, setFriendId] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [friendInfo, setFriendInfo] = useState({});
  const [showLandingView, setShowLandingView] = useState(true);
  useEffect(() => {
    // 从URL获取user_id参数, 获取被关注者的信息
    const params = Taro.getCurrentInstance().router.params;
    console.log("[Taro解析的params]: " + JSON.stringify(params));

    // 添加完整的URL日志，检查当前URL中是否包含code
    const currentUrl = window.location.href;
    console.log("[当前完整URL]: " + currentUrl);
     
    // 获取被关注用户信息
    if (params && params.uid) {
      setFriendId(params.uid);
      getFollowUserInfo({ userId: params.uid }).then(res => {
        if (res && res.code === 0) {
          setFriendInfo(res.data);
          setIsLoading(false);
          console.log("[friendInfo]: " + JSON.stringify(res.data));
        }
      }).catch(err => {
        console.error('获取关注用户信息失败:', err);
        setIsLoading(false);
      });
    } else {
      // 没有uid时，直接进行微信授权跳转
      console.error('请求参数中没有uid！');
    }
    
    if (params && params.redirect === 'wx') {
      // 如果redirect=wx，则直接进行微信授权跳转      
      console.log("[检测到redirect=wx参数，准备跳转微信授权]");
      redirectToWxAuth(params.uid);
      return;
    }
    
    // 🔧 新增：测试参数 redirect=no，不进行任何跳转，仅用于测试页面展示
    if (params && params.redirect === 'no') {
      console.log("[检测到redirect=no参数，测试模式，不进行跳转]");
      console.log("[测试模式] 当前页面参数:", JSON.stringify(params));
      console.log("[测试模式] 用户信息:", JSON.stringify(friendInfo));
      // 在测试模式下，仅显示Landing View，不执行任何跳转逻辑
      setShowLandingView(true);
      return;
    }
    
    // 🔧 新增：检测authorized参数，表示已通过微信授权回调
    if (params && params.uid && params.authorized !== undefined && params.code && params.state) {
      console.log("[检测到authorized参数，准备进行微信登录] code:"+params.code+" state:"+params.state);
      setShowLandingView(false); // 隐藏Landing View
      handleWechatLogin(params.code, params.state); // 执行微信登录和关注逻辑
      return;
    }
    
    // 如果只有 uid 没有其他参数，说明是首次访问，显示 Landing View
    if (params && params.uid && !params.code && params.authorized === undefined) {
      setShowLandingView(true);
    }

  }, []);


  // 重定向到微信授权页面
  const redirectToWxAuth = (userId) => {
    const strState = md5(new Date().getTime().toString());
    const baseUrl = Taro.getStorageSync('baseUrl') || URL_BASE;
    
    // 🔧 修改：在重定向URI中添加authorized参数，表示已授权
    const redirectUri = `${baseUrl}/#/pageUserInfo/followUser/index?uid=${userId}&authorized`;
    console.log("[重定向URI模板]: " + redirectUri);
    
    // 正确编码重定向URI
    const encodedRedirectUri = encodeURIComponent(redirectUri);
    
    const wxAuthUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx95923db8e0a4db59&redirect_uri=${encodedRedirectUri}&response_type=code&scope=snsapi_userinfo&state=${strState}#wechat_redirect`;
    
    console.log("[跳转到微信授权页]: " + wxAuthUrl);
    
    // 跳转到微信授权页面
    window.location.href = wxAuthUrl;
  };

  // 微信登录处理
  const handleWechatLogin = (code,state) => {
    let data = {
      type: 31,
      code: code,
      state: state
    }
    console.log("[开始处理微信登录]: " + JSON.stringify(data));

    socialLogin(data).then((res) => {
      console.log("[微信登录返回]: " + JSON.stringify(res));
      if (res && res.code === 0) {
        Taro.setStorageSync('userInfo', res.data);
        console.log("[微信登录成功，存储用户信息]");

        getUserInfo().then((res2) => {
          console.log("[获取详细用户信息]: " + JSON.stringify(res2));
          if (res2 && res2.code === 0) {
            //合并用户信息
            Taro.setStorageSync('userInfo', {
              ...res2.data,
              ...res.data
            });
            setUserInfo(res2.data);
            setIsLoading(false);
            console.log("[合并用户信息完成]");
            
            // 从URL获取user_id参数, 获取被关注者的信息
            const params = Taro.getCurrentInstance().router.params;
            console.log("[Taro解析的params]: " + JSON.stringify(params));
            console.log("[friendId :>>>>>>>>>]friendId " + friendId + "friendInfo " + JSON.stringify(friendInfo));
            // 登录成功后自动执行关注
            if (params && params.uid) {
              console.log("[有friendId，准备关注用户]: " + params.uid);
              console.log("[当前用户信息]: " + JSON.stringify(userInfo));
              let followUserData = {
                userId: res2.data.id,
                followUserId: params.uid
              }
              console.log("[followUserData]: " + JSON.stringify(followUserData))
              followUser(followUserData).then((res3) => {
                console.log("[关注接口返回]： " + JSON.stringify(res3))
                // if(res3.code === 0) {
                  console.log("[关注成功] ");
                  // 🔧 关注成功后跳转到结果页面，只传递用户ID
                  Taro.redirectTo({ 
                    url: `/pageUserInfo/followUser/result?uid=${params.uid}` 
                  });
                // } else {
                //   console.error("[关注失败]: " + res3.msg);
                //   // 关注失败时重置状态，继续显示 Landing View
                //   setShowLandingView(true);
                //   setIsLoading(false);
                // }
              }).catch(err => {
                console.error('[关注接口异常]:', err);
                // 异常时重置状态
                setShowLandingView(true);
                setIsLoading(false);
              })
            } else {
              // 如果没有friendId，返回首页
              console.log("[没有friendId，准备返回首页]");
              //Taro.reLaunch({ url: '/pages/index/index' });
            }
          }
        });
      } else {
        console.error("[微信登录失败]: " + res.msg);
        toast("info", {
        content: res.msg || '登录失败',
        duration: 2000
      });
        setIsLoading(false);
      }
    }).catch(err => {
      console.error('[社交登录异常]:', err);
      toast("info", {
        content: "登录失败，请重试",
        duration: 2000
      });
      setIsLoading(false);
    });
  };

  // 跳转到应用市场 todo
  const goToAppStore = () => {
    // 获取当前系统信息
    Taro.getSystemInfo({
      success: function(res) {
        const system = res.platform;
        
        if (system === 'android') {
          // Android平台跳转到应用宝或其他应用市场
          // 可以使用应用宝的URL Scheme或者其他应用市场的链接
          window.location.href = 'market://details?id=com.yourcompany.appname';
          
          // 如果上面的链接无法打开，可以尝试直接跳转到应用宝网页版
          setTimeout(() => {
            window.location.href = 'https://a.app.qq.com/o/simple.jsp?pkgname=com.yourcompany.appname';
          }, 500);
        } else if (system === 'ios') {
          // iOS平台跳转到App Store
          window.location.href = 'itms-apps://itunes.apple.com/app/idYOUR_APP_ID';
          
          // 如果上面的链接无法打开，可以尝试直接跳转到App Store网页版
          setTimeout(() => {
            window.location.href = 'https://apps.apple.com/app/idYOUR_APP_ID';
          }, 500);
        } else {
          // 其他平台，可以提供一个通用的下载页面
          window.location.href = 'https://www.yourwebsite.com/download';
        }
      }
    });
  };

  // 渲染气泡消息组件
  const renderBubbleMessage = () => {
    return (
      <View className='bubble-message-container' onClick={() => redirectToWxAuth(friendId)}>
        <View className='bubble-wrapper'>
          <View className='bubble-content'>
            <Text className='bubble-text'>点击"使用完整服务"</Text>
          </View>
          {/* 向下的箭头 - 位于气泡底部 */}
          <IconTriDown className="bubble-arrow down" color={'#05C160'} size={12} />
        </View>
      </View>
    );
  };

  // 渲染landing视图
  const renderLandingView = () => {
    const params = Taro.getCurrentInstance().router.params;
    const isTestMode = params && params.redirect === 'no';
    
    const formattedUserName = friendInfo.nickname ? friendInfo.nickname : `用户${friendInfo.userId}`;
    return (
      <View className='follow-user'>   
        <View className='follow-info-container'>
          <Avatar src={friendInfo.avatar || require('../../assets/images/login/logo.png')} size="medium" />
          <Text className='nickname'>{formattedUserName}</Text>
          <Text className='hint-text'>
            点击"使用完整服务"关注查看我的相册
          </Text>
        </View>
        
        {/* 右下角气泡消息 */}
        {renderBubbleMessage()}
      </View>
    );
  };

  return renderLandingView();
} 