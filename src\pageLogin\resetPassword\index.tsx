import { View, Text } from '@tarojs/components'
import { Button, Input, CountDown } from '@arco-design/mobile-react'
import './index.less'
import Taro from '@tarojs/taro'
import YkNavBar from '@/components/ykNavBar'
import React from 'react'
import { getSmsCode, resetPassword } from '@/utils/api/common/common_user'
import { toast } from '@/utils/yk-common'

export default function ResetPassword() {
  const [code, setCode] = React.useState("");
  const [phone, setPhone] = React.useState("");
  // 密码
  const [password, setPassword] = React.useState("");
  // 二次密码
  const [repeat_password, setRepeatPassword] = React.useState("");
  //验证码倒计时
  const [countDown, setCountDown] = React.useState(60);
  // 是否开启倒计时
  const [autoStart, setAutoStart] = React.useState(false);

  // 倒计时结束
  const onFinishCountDown = () => {
    setAutoStart(false);
    setCountDown(60);
  };

  // 获取验证码
  const getCode = () => {
    if (phone.length !== 11) {
      toast("info", {
        content: "请输入正确的手机号",
        duration: 2000,
      });   
      return;
    }
    setAutoStart(true);
    let data = {
      mobile: phone,
      scene: 4, 
    };
    getSmsCode(data)
      .then((res: any) => {
        console.log("getSmsCode", res);
        if (res && res.code == 0) {
          toast("success", {
            content: "验证码发送成功",
            duration: 2000,
          });
        } else {
          toast("error", {
            content: res.msg,
            duration: 2000,
          });
        }
      })
      .catch((err) => {
        toast("error", {
          content: err,
          duration: 2000,
        });
      });
  };

  // 确认绑定
  const handleConfirmBind = () => {
    if (code.length < 4) {
      toast("info", {
        content: "请输入正确的验证码",
        duration: 2000,
      });
      return;
    }
    if (password !== repeat_password) {
      toast("info", {
        content: "两次输入的密码不一致",
        duration: 2000,
      });
      return;
    }

    let data = {
      mobile: phone,
      code: code,
      password: password,
    };
    resetPassword(data)
      .then((res: any) => {
        if (res && res.code == 0) {
          toast("success", {
            content: "更改成功",
            duration: 2000,
          });
          setCode("");
          setAutoStart(false);
          setCountDown(60);
        } else {
          toast("error", {
            content: res.msg,
            duration: 2000,
          });
        }
      })
      .catch((err) => {
        toast("error", {
          content: err,
          duration: 2000,
        });
      });
  };



  return (
    <View className='reset-password'>
      <YkNavBar title=''/>
      <View className='title-container'>
        <Text className='title'>重置密码</Text>
        <Text className='subtitle'>验证手机号后即可重新设置密码</Text>
      </View>

      <View className='input-container'>
        <Input className='input-field' label='手机号' placeholder='请输入手机号' border='none' onChange={(_, value) => setPhone(value)} />
        <View className='verification-row'>
          {/* <Input className='input-field verification' placeholder='请输入验证码' border='none' />
          <Text className='get-code'>获取验证码</Text> */}

          <Input
          label="验证码"
          placeholder="请输入验证码"
          type="tel"
          validator={(val) => val.length <= 8}
          className="input-verification"
          clearable
          value={code}
          onChange={(_, value) => setCode(value)}
          onClear={() => setCode("")}
          border="none"
          suffix={
            !autoStart ? (
              <Button
                inline
                size="mini"
                type="ghost"
                onClick={() => getCode()}
              >
                获取验证码
              </Button>
            ) : (
              <CountDown
                millisecond
                format="ss"
                time={{
                  days: 0,
                  hours: 0,
                  minutes: 0,
                  seconds: countDown,
                  milliseconds: 0,
                }}
                autoStart={autoStart}
                onFinish={onFinishCountDown}
                renderChild={(timeData) => (
                  <Button
                    inline
                    size="mini"
                    type="ghost"
                    style={{
                      color: "#999999",
                      border: "none",
                      fontWeight: "bold",
                    }}
                    disabled
                  >
                    重新获取({timeData.seconds}s)
                  </Button>
                )}
              />
            )
          }
        />
        </View>
        <Input className='input-field' label='设置新密码' placeholder='请输入新密码' type='password' border='none' onChange={(_, value) => setPassword(value)} />
        <Input className='input-field' label='确认新密码' placeholder='请再次输入新密码' type='password' border='none' onChange={(_, value) => setRepeatPassword(value)} />
      </View>

      <View className='submit-button'>
        <Button className='complete-button' onClick={handleConfirmBind}>完成</Button>
      </View>
    </View>
  )
} 