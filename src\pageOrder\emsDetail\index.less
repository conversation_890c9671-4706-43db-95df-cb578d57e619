@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pageOrder/emsDetail/index"] {
  .ems-details-box {
    min-height: 100vh;
    background-color: #f8f9fa;
    .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });

    // 数据来源提示
    .data-source {
      padding: 12px 16px;
      background-color: #fff;
      .use-dark-mode-query({
      background-color: @dark-card-background-color !important;
    });

      .data-source-text {
        font-size: 12px;
        color: #999;
        .use-dark-mode-query({
        color: @dark-font-color !important;
      });

        .company-name {
          color:  var(--primary-color);
        }
      }
    }

    // 快递单号信息
    .tracking-info {
      margin-top: 8px;
      padding: 16px;
      background-color: #fff;
      .use-dark-mode-query({
      background-color: @dark-card-background-color !important;
    });

      .tracking-row {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .tracking-label {
          font-size: 14px;
          color: #333;
          .use-dark-mode-query({
          color: @dark-font-color !important;
        });
        }

        .tracking-number {
          flex: 1;
          font-size: 14px;
          color: #333;
          margin-left: 8px;
          .use-dark-mode-query({
          color: @dark-font-color !important;
        });
        }

        .copy-btn {
          font-size: 14px;
          color:  var(--primary-color);
          padding: 4px 8px;
        }
      }

      .address-row {
        display: flex;
        align-items: flex-start;

        .address-label {
          font-size: 14px;
          color: #333;
          white-space: nowrap;
          .use-dark-mode-query({
          color: @dark-font-color !important;
        });
        }

        .address-text {
          flex: 1;
          font-size: 14px;
          color: #666;
          margin-left: 8px;
          line-height: 1.4;
          .use-dark-mode-query({
          color: @dark-font-color !important;
        });
        }
      }
    }

    // 物流轨迹
    .logistics-timeline {
      margin-top: 8px;
      background-color: #fff;
      .use-dark-mode-query({
      background-color: @dark-card-background-color !important;
    });

      .timeline-item {
        display: flex;
        padding: 16px;
        // border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .timeline-dot-wrapper {
          position: relative;
          margin-right: 12px;
          display: flex;
          flex-direction: column;
          align-items: center;

          .timeline-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #d9d9d9;
            margin-top: 6px;

            &.active {
              background-color:   var(--primary-color);
            }
          }

          .timeline-line {
            width: 1px;
            flex: 1;
            background-color: #f0f0f0;
            margin-top: 4px;
          }
        }

        .timeline-content {
          flex: 1;

          .timeline-time {
            font-size: 12px;
            color: #999;
            margin-bottom: 4px;
            .use-dark-mode-query({
            color: @dark-font-color !important;
          });
          }

          .timeline-context {
            font-size: 14px;
            color: #333;
            line-height: 1.4;
            .use-dark-mode-query({
            color: @dark-font-color !important;
          });
          }
        }
      }

      .no-data {
        padding: 40px;
        text-align: center;
        color: #999;
        .use-dark-mode-query({
        color: @dark-font-color !important;
      });
      }
    }
  }
}
