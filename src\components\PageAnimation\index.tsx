import { useEffect, useState, useRef } from 'react';
import { useRouter } from '@tarojs/taro';
import './index.less';

const PageAnimation=  ({ children }) => {
  const [animationClass, setAnimationClass] = useState<string>('');
  const [virtualPageContent, setVirtualPageContent] = useState<string | null>(null);
  const router = useRouter();
  const virtualPageRef = useRef<HTMLDivElement>(null);

  const handlePageTransition = (type: string) => {
    if (type === 'navigateBack') {
      // 返回页面时模拟抽屉关闭动画
      setAnimationClass('hpa-drawer-close');
      setTimeout(() => {
        setAnimationClass('');
        setVirtualPageContent(null); // 清理虚拟页面内容
      }, 300); // 动画持续时间与样式保持一致
    } else {
      // 非返回操作时，设置虚拟页面内容
      const currentPage = document.querySelector('.taro_page');
      if (currentPage) {
        setVirtualPageContent(currentPage.innerHTML);
        setAnimationClass('hpa-drawer-open');
      }
    }
  };

  useEffect(() => {
    // 页面首次加载时显示真实内容，无动画
    if (!router.type) {
      setAnimationClass('');
    } else {
      // 模拟 Taro 路由切换动画
      handlePageTransition(router.type);
    }
  }, [router]);

  return (
    <div className="page-animation-wrapper">
      {/* 真实页面 */}
      <div className={`taro_page ${animationClass}`}>{children}</div>

      {/* 虚拟页面 */}
      {virtualPageContent && (
        <div ref={virtualPageRef} className={`virtual-page ${animationClass}`}>
          <div dangerouslySetInnerHTML={{ __html: virtualPageContent }} />
        </div>
      )}
    </div>
  );
};

export default PageAnimation;
