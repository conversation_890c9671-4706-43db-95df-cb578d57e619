import { View, Text } from "@tarojs/components";
import React, { useEffect,useRef, useState } from "react";
import "./index.less";
import { Cell } from "@arco-design/mobile-react";
import Taro from "@tarojs/taro";
// 组件
import YkNavBar from "@/components/ykNavBar/index";
export default function OrderSetting() {
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  // 前往配送方式页面
  const goToDeliveryManager = () => {
    Taro.navigateTo({
      url: "/pageOrder/orderSetting/deliveryManager/index",
    });
  };

  // 前往运费模版页面
  const goToEmsTemplate = () => {
    Taro.navigateTo({
      url: "/pageOrder/orderSetting/emsTemplate/index",
    });
  };

  // 返回上一页
  const handleBack = () => {
    console.log("OrderSetting PAGES -------: " + JSON.stringify(Taro.getCurrentPages()));
    Taro.navigateBack({
      delta: 1,
    });
  };

  return (
    <View className="orderSettingPage">
    {platform !== "WX" &&  <YkNavBar title="交易设置" onClickLeft={handleBack}/>}

      <View className="container">
        {/* <Text className="container-title">店铺</Text> */}

        <View className="container-box">
        <Cell.Group bordered={false}>
            <Cell
              label="配送方式"
              className="container-box-cell"
              showArrow
              onClick={goToDeliveryManager}
            />

            <Cell
              label="运费模版"
              className="container-box-cell"
              showArrow
              onClick={goToEmsTemplate}
            />
            </Cell.Group>
        </View>
      </View>
    </View>
  );
} 