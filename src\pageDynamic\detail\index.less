@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pageDynamic/detail/index"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
      background-color: @dark-background-color !important;
    });

  .detailBox {
    width: 100%;
    min-height: 100vh;
    // background: #fff;
    // padding-bottom: 60px;

    .swiperList {
      width: 100%;
      position: relative;

      .imgnum {
        position: absolute;
        right: 15px;
        bottom: 15px;
        background: rgba(0, 0, 0, 0.3);
        padding: 3px 12px;
        border-radius: 12px;
        color: #fff;
        font-size: 10px;
      }
    }

    .opera {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      margin: 15px 0;

      .opera-text {
        color: #576b95;
        .use-dark-mode-query({
        color: #a0b0d0;
      });
        font-size: 12px;
        margin: 0 7px;
      }
    }

    .dynamicSign {
      width: 100%;
      border-top: 8px solid #f3f4f6;
      border-bottom: 8px solid #f3f4f6;
      .use-dark-mode-query({
      border-top: 8px solid #3a3b3c;
      border-bottom: 8px solid #3a3b3c;
    });
      padding: 15px 0;

      &-price {
        margin: 0 15px 10px;
        display: flex;
        align-items: center;

        &-title {
          color: #999;
          font-size: 13px;
        }

        &-text {
          margin-left: 10px;
          color: #e35848;
          font-size: 21px;

          &:first-letter {
            font-size: 11px;
          }
        }
      }

      &-desc {
        margin: 0 15px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;

        &-text {
          flex: 1;
          color: @font-color;
          .use-dark-mode-query({
          color: @dark-font-color;
        });

          font-weight: bold;
          font-size: 15px;

          // 展开收起链接样式
          .demo-link {
            color: #355a95;
            cursor: pointer;
            text-decoration: none;

            &:hover {
              text-decoration: underline;
            }
          }

          // 支持文本选择的样式
          &.selectable-text {
            user-select: text;
            -webkit-user-select: text;
            -moz-user-select: text;
            -ms-user-select: text;
          }
        }

        &-collect {
          flex-shrink: 0;
          min-width: 30px;
          display: flex;
          flex-direction: column;
          align-items: center;

          &-img {
            width: 18px;
            height: 18px;
          }

          &-text {
            margin-top: 4px;
            color: @sub-font-color;
            font-size: 11px;
            .use-dark-mode-query({
            color: @dark-sub-font-color;
          });
          }
        }
      }
    }

    .myInfo {
      padding: 15px;
      border-bottom: 8px solid #f3f4f6;
      .use-dark-mode-query({
      border-bottom: 8px solid #3a3b3c;
    });

      &-head {
        display: flex;
        align-items: center;

        &-image {
          width: 36px;
          height: 36px;
          border-radius: 50%;
        }

        &-name {
          flex: 1;
          margin-left: 14px;
          font-size: 15px;
          color: @font-color;
          .use-dark-mode-query({
          color: @dark-font-color;
        });
          font-weight: bold;
          display: flex;
          align-items: center;

          .payment-icon {
            width: 16px;
            height: 16px;
            margin-left: 6px;
          }
        }

        &-entry {
          padding: 5px;
          border: 1px solid var(--primary-color);
          border-radius: 6px;
          color: var(--primary-color);
          font-size: 13px;
          .use-dark-mode-query({
          border: 1px solid var(--dark-primary-color);
          color: var(--dark-primary-color);
        });
        }
      }

      &-more {
        margin-top: 14px;
        padding: 16px 0;
        display: flex;
        align-items: center;

        &-item {
          flex: 1;
          text-align: center;

          &-text {
            color: @sub-font-color;
            .use-dark-mode-query({
            color: @dark-sub-font-color;
          });
            font-size: 13px;
          }

          &-num {
            margin-left: 8px;
            color: @font-color;
            .use-dark-mode-query({
            color: @dark-font-color;
          });
            font-size: 12px;
          }
        }

        &-divider {
          width: 1px;
          height: 18px;
          background: #f3f4f6;
          .use-dark-mode-query({
          background: #3a3b3c;
        });
        }
      }
    }

    .footer {
      position: fixed;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 60px;
      padding: 0 15px;
      // background: #fff;
      background-color: var(--page-primary-background-color) !important;
      .use-dark-mode-query({
        background-color: @dark-background-color !important;
      });
      display: flex;
      align-items: center;
      z-index: 99;
      box-sizing: border-box;

      &-share {
        flex: 1;
        height: 44px;

        // background: #6cbe70;
        border-radius: 7px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 14px;
      }

      &-car {
        margin-left: 15px;

        &-img {
          width: 24px;
          height: 24px;
        }
      }
    }

    .dlistImage_title {
      text-align: center;
      margin: 15px 0;
      width: 100%;
      font-size: 13px;
      color: @sub-font-color;
      .use-dark-mode-query({
      color: @dark-sub-font-color;
    });
    }

    .dlistImage_desc {
      word-break: break-all;
      margin: 15px;
      margin-bottom: 25px;
      font-size: 13px;
      color: @font-color;
      .use-dark-mode-query({
      color: @dark-font-color;
    });
    }

    .dlistImage {
      width: 100%;
      display: block;
      // background-color: #ffffff;
      margin-bottom: 8px;
    }
  }

    // 悬浮购物车样式
    .car {
      position: fixed;
      right: 15px;
      bottom: 138px;
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 99;
  
      &-img {
        width: 50px;
        height: 50px;
      }
  
      &-text {
        position: absolute;
        top: -5px;
        right: -5px;
        min-width: 18px;
        height: 18px;
        padding: 0 4px;
        background: #eb483f;
        border-radius: 9px;
        color: #fff;
        font-size: 12px;
        text-align: center;
        line-height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        z-index: 1;
      }
    }
  
}
