@import "@arco-design/mobile-react/style/mixin.less";

.msgx-container {
  position: relative;
  display: inline-block;

  // CSS 变量默认值
  --msgx-vertical-offset: 10px;
  --msgx-horizontal-offset: 10px;
  --msgx-arrow-size: 8px;
  --msgx-bg-color: #000;
}

.msgx-content {
  position: absolute;
  box-sizing: border-box;
  width: max-content;
  z-index: 1000;
  will-change: transform;
  transition: opacity 0.2s ease-in-out;
  white-space: nowrap;

  .msgx-inner {
    position: relative;
    z-index: 1;
    border-radius: inherit;
    background-color: transparent;
    color: inherit;
    padding: 0;
    box-shadow: none;
  }

  .msgx-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border: var(--msgx-arrow-size) solid transparent;
  }

  // 上方位置 - 气泡在触发元素上方
  &.msgx-topLeft,
  &.msgx-topCenter,
  &.msgx-topRight {
    bottom: calc(100% + var(--msgx-vertical-offset));

    .msgx-arrow {
      top: 100%;
      border-top-color: var(--msgx-bg-color);
      border-bottom-width: 0;
    }
  }

  &.msgx-topLeft {
    left: calc(0px - var(--msgx-horizontal-offset));

    .msgx-arrow {
      left: calc(var(--msgx-horizontal-offset) + var(--msgx-arrow-size) / 2);
    }
  }

  &.msgx-topCenter {
    left: 50%;
    transform: translateX(-50%);

    .msgx-arrow {
      left: 50%;
      transform: translateX(-50%);
    }
  }

  &.msgx-topRight {
    right: calc(0px - var(--msgx-horizontal-offset));

    .msgx-arrow {
      right: calc(var(--msgx-horizontal-offset) + var(--msgx-arrow-size) / 2);
    }
  }

  // 下方位置 - 气泡在触发元素下方
  &.msgx-bottomLeft,
  &.msgx-bottomCenter,
  &.msgx-bottomRight {
    top: calc(100% + var(--msgx-vertical-offset));

    .msgx-arrow {
      bottom: 100%;
      border-bottom-color: var(--msgx-bg-color);
      border-top-width: 0;
    }
  }

  &.msgx-bottomLeft {
    left: calc(0px - var(--msgx-horizontal-offset));

    .msgx-arrow {
      left: calc(var(--msgx-horizontal-offset) + var(--msgx-arrow-size) / 2);
    }
  }

  &.msgx-bottomCenter {
    left: 50%;
    transform: translateX(-50%);

    .msgx-arrow {
      left: 50%;
      transform: translateX(-50%);
    }
  }

  &.msgx-bottomRight {
    right: calc(0px - var(--msgx-horizontal-offset));

    .msgx-arrow {
      right: calc(var(--msgx-horizontal-offset) + var(--msgx-arrow-size) / 2);
    }
  }

  // 左侧位置 - 气泡在触发元素左侧
  &.msgx-leftTop,
  &.msgx-leftCenter,
  &.msgx-leftBottom {
    right: calc(100% + var(--msgx-horizontal-offset));

    .msgx-arrow {
      left: 100%;
      border-left-color: var(--msgx-bg-color);
      border-right-width: 0;
    }
  }

  &.msgx-leftTop {
    top: calc(0px - var(--msgx-vertical-offset));

    .msgx-arrow {
      top: calc(var(--msgx-vertical-offset) + var(--msgx-arrow-size) / 2);
    }
  }

  &.msgx-leftCenter {
    top: 50%;
    transform: translateY(-50%);

    .msgx-arrow {
      top: 50%;
      transform: translateY(-50%);
    }
  }

  &.msgx-leftBottom {
    bottom: calc(0px - var(--msgx-vertical-offset));

    .msgx-arrow {
      bottom: calc(var(--msgx-vertical-offset) + var(--msgx-arrow-size) / 2);
    }
  }

  // 右侧位置 - 气泡在触发元素右侧
  &.msgx-rightTop,
  &.msgx-rightCenter,
  &.msgx-rightBottom {
    left: calc(100% + var(--msgx-horizontal-offset));

    .msgx-arrow {
      right: 100%;
      border-right-color: var(--msgx-bg-color);
      border-left-width: 0;
    }
  }

  &.msgx-rightTop {
    top: calc(0px - var(--msgx-vertical-offset));

    .msgx-arrow {
      top: calc(var(--msgx-vertical-offset) + var(--msgx-arrow-size) / 2);
    }
  }

  &.msgx-rightCenter {
    top: 50%;
    transform: translateY(-50%);

    .msgx-arrow {
      top: 50%;
      transform: translateY(-50%);
    }
  }

  &.msgx-rightBottom {
    bottom: calc(0px - var(--msgx-vertical-offset));

    .msgx-arrow {
      bottom: calc(var(--msgx-vertical-offset) + var(--msgx-arrow-size) / 2);
    }
  }
}

// 默认主题样式（仅兜底，实际以内联样式为主）
.msgx-content {
  background-color: transparent;
  color: inherit;
  border-radius: 4px;
  padding: 0; // 由组件内联样式控制
  box-shadow: none;
  font-size: 14px;
  line-height: 1.4;
}

// 支持暗色模式
.use-dark-mode-query({
  .msgx-container {
    --msgx-bg-color: #2c2c2c;
  }

  .msgx-content {
    background-color: transparent; // 深色模式下也保持透明，由内联样式统一控制
    color: inherit;
    box-shadow: none;
  }
});

// 响应式适配
@media (max-width: 768px) {
  .msgx-container {
    --msgx-arrow-size: 6px;
  }

  .msgx-content {
    max-width: 80vw;
    font-size: 12px;
    padding: 6px 10px;
  }
}
