import { useState, useEffect,useRef } from 'react';
import { View, Text, Image, Input } from '@tarojs/components';
import Taro, { useLoad, useRouter } from '@tarojs/taro';
import {
  Checkbox,
  Textarea,
  Button,
  Dialog,
  Toast,
  Stepper,
  Loading
} from '@arco-design/mobile-react';

import YkNavBar from '@/components/ykNavBar/index';
import { refundPage, getUserOrderDetails, refundMoney } from '@/utils/api/common/common_user';
import { toast } from "@/utils/yk-common";
import './index.less';

// 商品格式接口
interface GoodsFormat {
  id: number;
  color: string;
  spec: string;
  price: number;
  number: number;
  refund_number: number;
  isSelected?: boolean;
  originalNumber?: number;
  maxRefundQuantity?: number; // 最大可退数量
}

// 商品接口
interface GoodsItem {
  id: number;
  dynamic_id: number;
  content: string;
  pictures: string;
  price: number;
  number: number;
  refund_number: number;
  format: GoodsFormat[];
  isAllCheck?: boolean;
}

// 退款信息接口
interface RefundInfo {
  money: number;
  refund_money: number;
  freight: number;
  refund_freight: number;
}

// API响应接口
interface RefundPageResponse {
  code: number;
  data: {
    send?: GoodsItem[];
    notSend?: GoodsItem[];
    money: number;
    refund_money: number;
    freight: number;
    refund_freight: number;
  };
  msg: string;
}



export default function RefundPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [refundInfo, setRefundInfo] = useState<RefundInfo>({
    money: 0,
    refund_money: 0,
    freight: 0,
    refund_freight: 0
  });
  const [sendGoods, setSendGoods] = useState<GoodsItem[]>([]);
  const [notSendGoods, setNotSendGoods] = useState<GoodsItem[]>([]);
  const [refundAmount, setRefundAmount] = useState('');
  const [refundFreight, setRefundFreight] = useState('');
  const [refundRemark, setRefundRemark] = useState('');
  const [orderId, setOrderId] = useState('');
  const [isAllSelected, setIsAllSelected] = useState(false);

  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);

  useLoad(() => {
    const { orderId: id } = router.params;
    if (id) {
      setOrderId(id);
      loadRefundData(id);
    }
  });

  // 监听商品选中状态变化，自动更新全选状态
  useEffect(() => {
    setIsAllSelected(checkAllSelected());
  }, [sendGoods, notSendGoods]);


  // 加载退款数据
  const loadRefundData = async (id: string) => {
    try {
      setLoading(true);
      const response = await getUserOrderDetails({ id });

      if (response && response.code === 0) {
        const orderData = response.data;

        // 设置退款信息
        setRefundInfo({
          money: orderData.totalPrice || 0,
          refund_money: orderData.orderRefund?.refundAmount || 0, // 已退款金额
          freight: orderData.freightAmount || 0, // 运费
          refund_freight: orderData.orderRefund?.refundPostage || 0 // 已退运费
        });

        // 处理商品数据，根据 isDelivery 字段分类
        const sendGoodsList: GoodsItem[] = [];
        const notSendGoodsList: GoodsItem[] = [];

        orderData.userOrderDynamics?.forEach((dynamic: any) => {
          const sentFormats: any[] = [];
          const notSentFormats: any[] = [];

          dynamic.orderDetails?.forEach((detail: any) => {
            const totalQuantity = detail.quantity || 0;
            const shippedQuantity = detail.quantityShipped || 0;
            const unshippedQuantity = totalQuantity - shippedQuantity;

            // 处理已发货部分
            if (shippedQuantity > 0) {
              const refundedQuantity = detail.refundQuantity || 0;
              const availableRefundQuantity = shippedQuantity - refundedQuantity;

              // 只有可退数量大于0的才显示
              if (availableRefundQuantity > 0) {
                sentFormats.push({
                  id: detail.orderDetailId,
                  color: detail.colorName || '',
                  spec: detail.specName || '',
                  price: dynamic.price || 0,
                  number: availableRefundQuantity, // 使用可退数量
                  refund_number: refundedQuantity,
                  isSelected: false,
                  originalNumber: shippedQuantity,
                  maxRefundQuantity: availableRefundQuantity
                });
              }
            }

            // 处理未发货部分
            if (unshippedQuantity > 0) {
              const refundedQuantity = detail.refundQuantity || 0;
              // 未发货部分的可退数量 = 未发货数量 - 已退款数量
              const availableUnshippedRefundQuantity = Math.max(0, unshippedQuantity - refundedQuantity);

              // 只有可退数量大于0的才显示
              if (availableUnshippedRefundQuantity > 0) {
                notSentFormats.push({
                  id: detail.orderDetailId,
                  color: detail.colorName || '',
                  spec: detail.specName || '',
                  price: dynamic.price || 0,
                  number: availableUnshippedRefundQuantity, // 使用可退数量
                  refund_number: refundedQuantity, // 已退款数量
                  isSelected: false,
                  originalNumber: unshippedQuantity,
                  maxRefundQuantity: availableUnshippedRefundQuantity
                });
              }
            }
          });

          // 添加已发货商品
          if (sentFormats.length > 0) {
            sendGoodsList.push({
              id: dynamic.id,
              dynamic_id: dynamic.dynamicsId,
              content: dynamic.content || '',
              pictures: dynamic.pictures || '',
              price: dynamic.price || 0,
              number: sentFormats.reduce((sum: number, fmt: any) => sum + fmt.number, 0),
              refund_number: sentFormats.reduce((sum: number, fmt: any) => sum + fmt.refund_number, 0),
              format: sentFormats,
              isAllCheck: false
            });
          }

          // 添加未发货商品
          if (notSentFormats.length > 0) {
            notSendGoodsList.push({
              id: dynamic.id, 
              dynamic_id: dynamic.dynamicsId,
              content: dynamic.content || '',
              pictures: dynamic.pictures || '',
              price: dynamic.price || 0,
              number: notSentFormats.reduce((sum: number, fmt: any) => sum + fmt.number, 0),
              refund_number: notSentFormats.reduce((sum: number, fmt: any) => sum + fmt.refund_number, 0),
              format: notSentFormats,
              isAllCheck: false
            });
          }
        });

        setSendGoods(sendGoodsList);
        setNotSendGoods(notSendGoodsList);
      } else {
        toast("info", {
        content: response.msg || '获取订单详情失败',
        duration: 2000
      });
      }
    } catch (error) {
      console.error('加载退款数据失败:', error);
      toast("info", {
        content: "网络异常，请重试",
        duration: 2000
      });
    } finally {
      setLoading(false);
    }
  };

  // 处理商品选择
  const handleGoodsSelect = (goodsId: number, isNotSend: boolean) => {
    const updateGoods = (goods: GoodsItem[]) => {
      return goods.map(item => {
        if (item.id === goodsId) {
          const newIsAllCheck = !item.isAllCheck;
          return {
            ...item,
            isAllCheck: newIsAllCheck,
            format: item.format.map(fmt => ({
              ...fmt,
              isSelected: newIsAllCheck
            }))
          };
        }
        return item;
      });
    };

    if (isNotSend) {
      setNotSendGoods(updateGoods);
    } else {
      setSendGoods(updateGoods);
    }
  };

  // 处理SKU选择
  const handleSkuSelect = (goodsId: number, formatIndex: number, isNotSend: boolean) => {
    const updateGoods = (goods: GoodsItem[]) => {
      return goods.map(item => {
        if (item.id === goodsId) {
          const updatedFormat = [...item.format];
          updatedFormat[formatIndex] = {
            ...updatedFormat[formatIndex],
            isSelected: !updatedFormat[formatIndex].isSelected
          };

          // 检查是否所有SKU都被选中
          const allSelected = updatedFormat.every(fmt => fmt.isSelected);

          return {
            ...item,
            format: updatedFormat,
            isAllCheck: allSelected
          };
        }
        return item;
      });
    };

    if (isNotSend) {
      setNotSendGoods(updateGoods);
    } else {
      setSendGoods(updateGoods);
    }
  };

  // 处理SKU数量变更
  const handleSkuQuantityChange = (goodsId: number, formatIndex: number, quantity: number, isNotSend: boolean) => {
    const updateGoods = (goods: GoodsItem[]) => {
      return goods.map(item => {
        if (item.id === goodsId) {
          const updatedFormat = [...item.format];
          const originalQuantity = updatedFormat[formatIndex].originalNumber || updatedFormat[formatIndex].number;
          const newQuantity = Math.max(1, Math.min(quantity, originalQuantity));

          if (quantity > originalQuantity) {
            Toast.error(`该规格最多只能选择${originalQuantity}件`);
            return item;
          }

          updatedFormat[formatIndex] = {
            ...updatedFormat[formatIndex],
            number: newQuantity
          };

          return {
            ...item,
            format: updatedFormat
          };
        }
        return item;
      });
    };

    if (isNotSend) {
      setNotSendGoods(updateGoods);
    } else {
      setSendGoods(updateGoods);
    }
  };

  // 获取选中的商品数量
  const getSelectedCount = () => {
    let count = 0;
    [...sendGoods, ...notSendGoods].forEach(item => {
      item.format.forEach(fmt => {
        if (fmt.isSelected) {
          count += fmt.number;
        }
      });
    });
    return count;
  };

  // 获取选中商品的总价格
  const getSelectedPrice = () => {
    let totalPrice = 0;
    [...sendGoods, ...notSendGoods].forEach(item => {
      item.format.forEach(fmt => {
        if (fmt.isSelected) {
          totalPrice += fmt.price * fmt.number;
        }
      });
    });
    return totalPrice.toFixed(2);
  };

  // 获取选中的商品种类数（不同规格算不同种类）
  const getSelectedItemCount = () => {
    let count = 0;
    [...sendGoods, ...notSendGoods].forEach(item => {
      item.format.forEach(fmt => {
        if (fmt.isSelected) {
          count += fmt.number; // 使用选中商品的实际数量
        }
      });
    });
    return count;
  };

  // 检查是否全选
  const checkAllSelected = () => {
    const allItems = [...sendGoods, ...notSendGoods];
    if (allItems.length === 0) return false;

    return allItems.every(item =>
      item.format.every(fmt => fmt.isSelected)
    );
  };

  // 处理全选
  const handleSelectAll = () => {
    const newIsAllSelected = !isAllSelected;
    setIsAllSelected(newIsAllSelected);

    // 更新所有商品的选中状态
    const updateAllGoods = (goods: GoodsItem[]) => {
      return goods.map(item => ({
        ...item,
        isAllCheck: newIsAllSelected,
        format: item.format.map(fmt => ({
          ...fmt,
          isSelected: newIsAllSelected
        }))
      }));
    };

    setNotSendGoods(updateAllGoods);
    setSendGoods(updateAllGoods);
  };

  // 处理退款金额输入
  const handleRefundAmountChange = (value: string) => {
    // 限制输入格式：最多6位整数，2位小数
    const sanitizedValue = value.replace(/[^\d.]/g, '').replace(/^(\d*\.\d{2}).*$/, '$1');
    const [integerPart, decimalPart] = sanitizedValue.split('.');

    let finalValue = integerPart;
    if (integerPart && integerPart.length > 6) {
      finalValue = integerPart.slice(0, 6);
    }
    if (decimalPart !== undefined) {
      const limitedDecimal = decimalPart.length > 2 ? decimalPart.slice(0, 2) : decimalPart;
      finalValue = `${finalValue}.${limitedDecimal}`;
    }

    // 验证是否超过最大可退款金额
    const maxRefundAmount =  Number((refundInfo.money - refundInfo.refund_money).toFixed(2));
    const inputValue = Number(finalValue);

    if (inputValue > maxRefundAmount) {
      toast("info", {
        content: `退款金额不能超过¥${maxRefundAmount.toFixed(2)}`,
        duration: 2000
      });
      setRefundAmount(maxRefundAmount.toString());
    } else {
      setRefundAmount(finalValue);
    }
  };

  // 处理运费输入
  const handleFreightChange = (value: string) => {
    const sanitizedValue = value.replace(/[^\d.]/g, '').replace(/^(\d*\.\d{2}).*$/, '$1');
    const [integerPart, decimalPart] = sanitizedValue.split('.');

    let finalValue = integerPart;
    if (integerPart && integerPart.length > 6) {
      finalValue = integerPart.slice(0, 6);
    }
    if (decimalPart !== undefined) {
      const limitedDecimal = decimalPart.length > 2 ? decimalPart.slice(0, 2) : decimalPart;
      finalValue = `${finalValue}.${limitedDecimal}`;
    }

    // 验证是否超过最大可退运费
    const maxRefundFreight = Number((refundInfo.freight - refundInfo.refund_freight).toFixed(2));
    const inputValue = Number(finalValue);

    if (inputValue > maxRefundFreight) {
      toast("info", {
        content: `运费退款不能超过¥${maxRefundFreight.toFixed(2)}`,
        duration: 2000
      });
      setRefundFreight(maxRefundFreight.toString());
    } else {
      setRefundFreight(finalValue);
    }
  };

  // 显示确认弹窗
  const showConfirmDialog = () => {
    const selectedItemCount = getSelectedItemCount(); // 选中的商品种类数
    const inputRefundAmount = Number(refundAmount || 0);
    const inputRefundFreight = Number(refundFreight || 0);

    // 计算总退款金额：手动输入的退款金额 + 运费
    const totalAmount = inputRefundAmount + inputRefundFreight;

    // 构建弹窗内容
    const amountText = inputRefundFreight > 0
      ? `退款总额    ¥${totalAmount.toFixed(2)}(含运费¥${inputRefundFreight.toFixed(2)})`
      : `退款总额    ¥${totalAmount.toFixed(2)}`;

    Dialog.confirm({
      title: (
        <View>
          确定退款 <Text style={{ color: '#eb483f', fontWeight: '500' }}>{selectedItemCount}</Text> 件商品?
        </View>
      ),
      children: (
        <View className='confirm-content'>
          <Text className='confirm-amount'>{amountText}</Text>
          <View className='confirm-method'>
            <View className='method-row'>
              <Text className='method-label'>退回方式</Text>
              <Text className='method-value'>原路退回</Text>
            </View>
          </View>
        </View>
      ),
      okText: '退款',
      cancelText: '取消',
      onOk: handleRefund,
      platform: 'ios'
    });
  };

  // 提交退款
  const handleRefund = async () => {
    try {

      // 验证退款金额
      const maxRefundAmount = refundInfo.money - refundInfo.refund_money;
      const maxRefundFreight = refundInfo.freight - refundInfo.refund_freight;

      if (Number(refundAmount) > maxRefundAmount) {
        Toast.error(`商品金额最多可退¥${maxRefundAmount.toFixed(2)}`);
        return;
      }

      if (Number(refundFreight) > maxRefundFreight) {
        Toast.error(`运费最多可退¥${maxRefundFreight.toFixed(2)}`);
        return;
      }

      // 构建新格式退款数据
      const userOrderRefundDetails: any[] = [];
      [...sendGoods, ...notSendGoods].forEach(item => {
        item.format.forEach(fmt => {
          if (fmt.isSelected) {
            userOrderRefundDetails.push({
              userOrderDetailId: fmt.id,
              refundQuantity: fmt.number
            });
          }
        });
      });

      const refundData = {
        userOrderId: parseInt(orderId),
        reason: refundRemark || '',
        refundAmount: Number(refundAmount || 0),
        refundPostage: Number(refundFreight || 0),
        userOrderRefundDetails: userOrderRefundDetails
      };

      console.log('退款请求数据:', refundData);

      const response = await refundMoney(refundData);

      if (response && response.code === 0) {
        Toast.success('退款申请已提交');
        Taro.eventCenter.trigger('refreshOrderList');
        setTimeout(() => {
          Taro.navigateBack();
        }, 1500);
      } else {
        Toast.error(response.msg || '退款申请失败');
      }
    } catch (error) {
      console.error('退款申请失败:', error);
      Toast.error('退款申请失败');
    }
  };

  // 渲染商品列表
  const renderGoodsList = (goods: GoodsItem[], title: string, isNotSend: boolean) => {
    if (goods.length === 0) return null;

    return (
      <View className='goods-section'>
        <View className='goods-section-header'>
          <Text className='goods-section-title'>{title}</Text>
          <View className='goods-section-line' />
        </View>

        {goods.map((item) => (
          <View key={item.id} className='refund-product-card'>
            <View className='refund-product-main'>
              <Checkbox
                checked={item.isAllCheck}
                onChange={() => handleGoodsSelect(item.id, isNotSend)}
                className='refund-checkbox'
                value=''
              />
              <View className='refund-product-img-wrapper'>
                <Image className='refund-product-img' src={item.pictures} />
              </View>
              <View className='refund-product-info'>
                <Text className='refund-product-title'>{item.content}</Text>
                <View className='refund-product-meta'>
                  <Text className='refund-product-price'>￥{item.price}</Text>
                </View>
              </View>
            </View>

            {/* SKU列表 */}
            {item.format && item.format.map((format, formatIndex) => (
              <View key={formatIndex} className='refund-product-sku'>
                <View className='refund-product-sku-main'>
                  <Checkbox
                    checked={format.isSelected}
                    onChange={() => handleSkuSelect(item.id, formatIndex, isNotSend)}
                    className='refund-checkbox'
                    value=''
                  />
                  <View className='refund-product-sku-info'>
                    <Text className='refund-product-sku-text'>
                      {format.color} {format.spec}
                    </Text>
                    <Text className='refund-product-sku-quantity'>x{format.originalNumber}</Text>
                  </View>
                  <View className='refund-product-sku-controls'>
                    <Stepper
                      defaultValue={format.number}
                      min={1}
                      max={format.maxRefundQuantity || format.number}
                      step={1}
                      onChange={(val) => handleSkuQuantityChange(item.id, formatIndex, val || format.number, isNotSend)}
                    />
                  </View>
                </View>
              </View>
            ))}
          </View>
        ))}
      </View>
    );
  };

  if (loading) {
    return (
      <View className='refund-page'>
        {platform !== "WX" &&<YkNavBar title='退款' />}
        <View className='loading-container'>
          <Loading />
        </View>
      </View>
    );
  }

  return (
    <View className='refund-page'>
      {platform !== "WX" &&<YkNavBar title='退款' />}

      {/* 未发货商品 */}
      {renderGoodsList(notSendGoods, '未发货商品', true)}

      {/* 已发货商品 */}
      {renderGoodsList(sendGoods, '已发货商品', false)}

      {/* 退款金额输入区域 */}
      <View className='refund-amount-section'>
        {/* 商品退款金额 */}
        <View className='amount-item'>
          <View className='amount-item-left'>
            {refundInfo.money > refundInfo.refund_money ? (
              <>
                <Text className='amount-item-title'>商品退款金额</Text>
                <Text className='amount-item-hint'>
                  {refundInfo.refund_money > 0
                    ? `已退¥${refundInfo.refund_money}，最多可再退¥${(refundInfo.money - refundInfo.refund_money).toFixed(2)}`
                    : `最多可退¥${refundInfo.money}`
                  }
                </Text>
              </>
            ) : (
              <Text className='amount-item-title'>商品退款金额(¥{refundInfo.money})已全退</Text>
            )}
          </View>
          {refundInfo.money > refundInfo.refund_money && (
            <View className='amount-item-right'>
              <View className='amount-input-wrapper'>
                <Input
                  className='amount-input'
                  placeholder='0.00'
                  value={refundAmount}
                  onInput={(e) => handleRefundAmountChange(e.detail.value)}
                />
                <Text className='amount-unit'>元</Text>
              </View>
            </View>
          )}
        </View>

        {/* 运费退款金额 */}
        <View className='amount-item'>
          <View className='amount-item-left'>
            {refundInfo.freight > refundInfo.refund_freight ? (
              <>
                <Text className='amount-item-title'>运费</Text>
                <Text className='amount-item-hint'>
                  {refundInfo.refund_freight > 0
                    ? `已退¥${refundInfo.refund_freight}，最多可再退¥${(refundInfo.freight - refundInfo.refund_freight).toFixed(2)}`
                    : `最多可退¥${refundInfo.freight}`
                  }
                </Text>
              </>
            ) : (
              <Text className='amount-item-title'>运费(¥{refundInfo.freight})已全退</Text>
            )}
          </View>
          {refundInfo.freight > refundInfo.refund_freight && (
            <View className='amount-item-right'>
              <View className='amount-input-wrapper'>
                <Input
                  className='amount-input'
                  placeholder='0.00'
                  value={refundFreight}
                  onInput={(e) => handleFreightChange(e.detail.value)}
                />
                <Text className='amount-unit'>元</Text>
              </View>
            </View>
          )}
        </View>

        {/* 退款备注 */}
        <View className='remark-section'>
          <Textarea
            border='none'
            className='remark-input'
            placeholder='请填写退款原因（买家可见）'
            value={refundRemark}
            onInput={(e) => setRefundRemark(e.target.value)}
            maxLength={200}
          />
        </View>
      </View>

      {/* 底部操作栏 */}
      <View className='bottom-bar'>
        <View className='bottom-bar-left'>
          <Checkbox
            checked={checkAllSelected()}
            onChange={handleSelectAll}
            className='select-all-checkbox'
            value=''
          />
          <Text className='select-all-text'>全选</Text>
        </View>
        <View className='bottom-bar-center'>
          <Text className='selected-text'>共 </Text>
          <Text className='selected-count'>{getSelectedCount()}</Text>
          <Text className='selected-text'> 件 </Text>
          {/* <Text className='selected-price'>¥{getSelectedPrice()}</Text> */}
          <Text className='selected-price'>¥{refundAmount||0}</Text>
        </View>
        <View className='bottom-bar-right'>
          <Button
            className='refund-btn'
            type='primary'
            onClick={showConfirmDialog}
            disabled={getSelectedCount() === 0 && !refundAmount && !refundFreight}
          >
            退款
          </Button>
        </View>
      </View>


    </View>
  );
}