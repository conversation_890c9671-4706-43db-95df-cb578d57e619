@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pageSetting/webView/index"] {
  .webview {
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    .use-var(background-color, background-color);
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
    .agreement-content {
      width: 100%;
      padding: 20px 16px;
      word-wrap: break-word; // 允许长单词或 URL 地址换行到下一行
      word-break: break-all; // 允许在单词内换行
      .use-var(color, font-color);
      .use-dark-mode-query({
      color: @dark-font-color;
    });
      box-sizing: border-box;
    }

    .webview-container {
      width: 100%;
      flex: 1;
      position: relative;
      top:80px;
      min-height: 0; // 允许在 flex 容器中正确收缩，避免被撑出视口
    }
  }

  .taro-webview{
    position: fixed !important;
    top: 80px !important;
  }

  .arco-theme-dark {
    .agreement-content {
      .MsoNormal span {
        color: #ffffff !important;
      }
    }
  }
}
