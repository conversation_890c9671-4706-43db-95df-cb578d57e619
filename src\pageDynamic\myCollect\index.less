@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageDynamic/myCollect/index"] {
  .my-collect {
    width: 100%;
    height: 100vh;
    background-color: #f3f4f6;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });

    &.hiddenStyle {
      opacity: 0;
    }
  }

  .searchLine-collect {
    width: 100%;
    background-color: #f3f4f6;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
    // 固定定位，避免被推出屏幕
    position: fixed;
    // top: 76px; /* 导航栏高度88px */
    left: 0;
    right: 0;
    z-index: 50; /* 确保在内容之上，但在导航栏之下 */
    // padding: 8px 0;
    box-sizing: border-box;

    .demo-search-btn {
      padding: 0 12px;
      font-size: 13px;
      color: var(--primary-color);
      background: transparent;
      border: none;
      cursor: pointer;

      &:active {
        opacity: 0.7;
      }
    }
  }

  .notmorelist {
    position: relative;
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999999;
    .use-dark-mode-query({
    color: @dark-font-color;
  });
    font-size: 12px;
    margin-top: 10px;
  }

  .not_content {
    position: relative;
    width: 100%;
    height: 50vh;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    font-size: 15px;
    font-weight: normal;
    color: #999999;
    .use-dark-mode-query({
    color: @dark-font-color;
  });

    &-image {
      width: 100px;
      height: 100px;
      display: block;
      margin-bottom: 15px;
    }
  }

  .filter {
    width: max-content;
    margin: 10px 15px;
    padding: 4px 12px;
    border: 1px solid var(--primary-color);
    border-radius: 50px;
    align-items: center;
    display: flex;

    &-text {
      display: inline-block;
      text-align: center;
      font-size: 11px;
      color: var(--primary-color);
    }

    &-img {
      display: inline-block;
      margin-left: 8px;
      width: 7px;
      height: 7px;
    }
  }

  .boxContent {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    padding-bottom: 20px; // 为底部操作栏预留更多空间
    min-height: 95vh;
  }

  .list {
    width: 100%;
    overflow: hidden;

    .p1Bg {
      margin-right: 15px;
      padding: 5px;
      background-color: #f3f4f6;
      .use-dark-mode-query({
      background-color: @dark-cell-background-color;
    });
    }

    &-item {
      display: flex;
      flex-direction: column;

      &-left {
        margin-bottom: 7.5px;
        margin-top: 15px;
        margin-left: 15px;

        &-date {
          font-size: 15px;
          color: #000000;
          .use-dark-mode-query({
          color: @dark-font-color;
        });
          display: flex;
          align-items: center;

          &-icon {
            margin-left: 3px;
            width: 10px;
            height: 10px;
            display: block;
          }
        }
      }

      &-rightC {
        display: flex;
        align-items: center;

        &-left {
          margin-left: 20px;
          display: flex;
          align-items: center;
          justify-content: center;

          &-checkbox {
            // 列表项复选框样式
            .arco-checkbox {
              --checkbox-border-color: #d9d9d9;
              --checkbox-checked-background-color: var(--primary-color);
              --checkbox-checked-border-color: var(--primary-color);
            }
          }

          &.hiddenStyle {
            display: none;
          }
        }

        &-right {
          margin-left: 15px;
          padding: 10px 0;
          display: flex;
          align-items: center;
          flex: 1;

          &-img {
            display: flex;
            justify-content: space-around;
            margin-right: 10px;
            width: 82px;
            height: 82px;

            &-img2 {
              width: 40px;
              height: 82px;
              display: block;
            }

            &-item {
              display: flex;
              flex-direction: column;
              justify-content: space-around;

              &-img4 {
                width: 40px;
                height: 40px;
                display: block;
              }
            }
          }

          &-right {
            height: 82px;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            &-p1 {
              margin-right: 10px;

              &-text {
                max-height: 30px;
                line-height: 15px;
                font-size: 13px;
                color: #333333;
                .use-dark-mode-query({
                color: @dark-font-color;
              });
                text-overflow: ellipsis;
                overflow: hidden;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
              }
            }

            &-price {
              width: 100%;
              font-size: 13px;
              color: #eb483f;

              &:first-letter {
                font-size: 11px;
              }
            }
          }

          &-right2 {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            &-p1 {
              // width: 100%;
              margin-bottom: 7px;

              &-text {
                max-height: 30px;
                line-height: 15px;
                font-size: 13px;
                color: #333333;
                .use-dark-mode-query({
                color: @dark-font-color;
              });
                text-overflow: ellipsis;
                overflow: hidden;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
              }
            }

            &-price {
              margin-bottom: 7px;
              width: 100%;
              font-size: 13px;
              color: #eb483f;

              &:first-letter {
                font-size: 11px;
              }
            }
          }
        }
      }
    }
  }

  .stickyTop {
    position: -webkit-sticky;
    position: sticky;
    top: -1px;
    background-color: #ffffff;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
  }

  .footerBtnBox-collect {
    position: fixed;
    bottom: 0;
    left: 0;
    width: calc(100% - 30px);
    height: 50px;
    padding: 0 15px;
    background-color: #ffffff;
    .use-dark-mode-query({
    background-color: @dark-cell-background-color;
  });
    z-index: 3090;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-change {
      flex: 1;
      display: flex;
      align-items: center;

      &-checkbox {
        margin-right: 10px;
        // 自定义 checkbox 样式
        .arco-checkbox {
          --checkbox-border-color: #d9d9d9;
          --checkbox-checked-background-color: var(--primary-color);
          --checkbox-checked-border-color: var(--primary-color);
        }
      }

      &-c {
        display: flex;
        align-items: center;
        justify-content: center;

        &-img {
          width: 8px;
          height: 12px;
          display: block;
          margin-left: 10px;
        }

        &-text {
          font-size: 13px;
          color: #333333;
          .use-dark-mode-query({
          color: @dark-font-color;
        });
        }
      }
    }

    &-btn {
      width: 130px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6px;
      background-color: var(--primary-color);
      color: #ffffff;
      font-size: 13px;
    }

    &-notbtn {
      width: 130px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6px;
      background-color: var(--primary-color);
      opacity: 0.3;
      color: #ffffff;
      font-size: 13px;
    }

    &-managerbtn {
      width: 345px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6px;
      background-color: var(--primary-color);
      color: #ffffff;
      font-size: 13px;
    }
  }

  .popup {
    position: relative;
    width: 100%;

    &-bottomBox {
      width: 110px;
      overflow: hidden;
      background-color: #ffffff;
      .use-dark-mode-query({
      background-color: @dark-cell-background-color;
    });
      border-radius: 6px;
      margin: 0 15px;
      margin-bottom: 57px;

      &-line {
        padding: 0 15px;
        width: calc(100% - 30px);
        height: 38px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 13px;
        color: #333333;
        .use-dark-mode-query({
        color: @dark-font-color;
      });
      }

      &-divider {
        margin: 0 15px;
        border-bottom: 1px solid var(--line-color);
        .use-dark-mode-query({
        border-bottom: 1px solid @dark-line-color;
      });
      }
    }
  }

  .totalNum {
    padding-top: 10px;
    margin: 10px 15px;
    font-size: 13px;
    color: #999999;
    .use-dark-mode-query({
    color: @dark-font-color;
  });
  }

  .footer_content_z {
    height: 60px;
  }


}
