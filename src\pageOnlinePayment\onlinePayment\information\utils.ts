import { BankCardInfo, FormErrors } from './types';

// 验证银行卡号
export const validateBankCardNumber = (number: string): boolean => {
  // 移除空格
  const cleanNumber = number.replace(/\s/g, '');
  // 银行卡号为16-19位数字
  return /^\d{16,19}$/.test(cleanNumber);
};

// 验证表单
export const validateForm = (info: BankCardInfo): FormErrors => {
  const errors: FormErrors = {};

  // 验证姓名/开户名称
  if (!info.bankCard.name) {
    errors.name = info.type === 'grzh' ? '请输入持卡人姓名' : '请输入开户名称';
  }

  // 验证银行卡号/账号
  if (!info.bankCard.number) {
    errors.number = info.type === 'grzh' ? '请输入银行卡号' : '请输入银行账号';
  } else if (info.type === 'grzh' && !validateBankCardNumber(info.bankCard.number)) {
    errors.number = '请输入正确的银行卡号';
  }

  // 验证支行名称
  if (!info.bankCard.branch) {
    errors.branch = '请输入开户支行名称';
  }

  return errors;
};

// 格式化银行卡号（每4位加空格）
export const formatBankCardNumber = (number: string): string => {
  return number.replace(/\s/g, '').replace(/(\d{4})(?=\d)/g, '$1 ');
};

// 清理银行卡号格式（移除空格）
export const cleanBankCardNumber = (number: string): string => {
  return number.replace(/\s/g, '');
};


// 获取URL参数的通用函数
const getUrlParam = (paramName: string): string | null => {
  if (typeof window === 'undefined') {
    return null;
  }

  // 在Taro H5环境中，URL参数可能在hash后面
  const searchUrl = window.location.search;
  const hashUrl = window.location.hash;

  // 尝试从search参数中获取
  let urlParams = new URLSearchParams(searchUrl);
  let param = urlParams.get(paramName);

  // 如果search中没有，尝试从hash中获取
  if (param === null && hashUrl.includes('?')) {
    const hashSearch = hashUrl.split('?')[1];
    urlParams = new URLSearchParams(hashSearch);
    param = urlParams.get(paramName);
  }

  return param;
};

// 检查URL中是否存在指定参数（无论是否有值）
const hasUrlParam = (paramName: string): boolean => {
  if (typeof window === 'undefined') {
    return false;
  }

  // 在Taro H5环境中，URL参数可能在hash后面
  const searchUrl = window.location.search;
  const hashUrl = window.location.hash;

  // 检查search参数中是否存在
  let urlParams = new URLSearchParams(searchUrl);
  if (urlParams.has(paramName)) {
    return true;
  }

  // 检查hash中是否存在
  if (hashUrl.includes('?')) {
    const hashSearch = hashUrl.split('?')[1];
    urlParams = new URLSearchParams(hashSearch);
    if (urlParams.has(paramName)) {
      return true;
    }
  }

  return false;
};