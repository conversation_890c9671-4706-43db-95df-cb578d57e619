// src/utils/navigationManager.ts
import Taro from '@tarojs/taro';

class NavigationManager {
  private isNavigating: boolean = false;
  private originalNavigateTo: typeof Taro.navigateTo;
  private originalRedirectTo: typeof Taro.redirectTo;
  private originalSwitchTab: typeof Taro.switchTab;
  
  constructor() {
    // 保存原始方法
    this.originalNavigateTo = Taro.navigateTo.bind(Taro);
    this.originalRedirectTo = Taro.redirectTo.bind(Taro);
    this.originalSwitchTab = Taro.switchTab.bind(Taro);
    
    // 重写方法
    this.overrideNavigationMethods();
    
    // 设置全局错误处理
    this.setupGlobalErrorHandler();
  }

  private overrideNavigationMethods() {
    // 重写 navigateTo
    Taro.navigateTo = (options: Taro.navigateTo.Option) => {
      return this.safeNavigateWithSilentReject(() => this.originalNavigateTo(options));
    };

    // 重写 redirectTo
    Taro.redirectTo = (options: Taro.redirectTo.Option) => {
      return this.safeNavigateWithSilentReject(() => this.originalRedirectTo(options));
    };

    // 重写 switchTab
    Taro.switchTab = (options: Taro.switchTab.Option) => {
      return this.safeNavigateWithSilentReject(() => this.originalSwitchTab(options));
    };
  }


  // 提供手动重置方法（调试用）
  public resetNavigationState() {
    this.isNavigating = false;
  }

  // 获取当前状态
  public getNavigationState() {
    return this.isNavigating;
  }

  // 设置全局错误处理
  private setupGlobalErrorHandler() {
    // 处理未捕获的 Promise 拒绝
    if (typeof window !== 'undefined') {
      window.addEventListener('unhandledrejection', (event) => {
        const error = event.reason;
        
        // 检查是否是我们的导航错误 - 支持多种检测方式
        const isNavigationError = error && (
          error.isNavigationError === true ||
          error.name === 'NavigationBlocked' ||
          error.code === 'NAVIGATION_IN_PROGRESS' ||
          (error.message && error.message.includes('Navigation in progress'))
        );
        
        if (isNavigationError) {
          // 阻止错误在控制台显示
          event.preventDefault();
          return;
        }
      });

      // 额外的错误处理 - 针对某些框架可能不触发 unhandledrejection 的情况
      const originalConsoleError = console.error;
      console.error = (...args) => {
        const errorMessage = args.join(' ');
        if (errorMessage.includes('NavigationBlocked') || errorMessage.includes('Navigation in progress')) {
          console.log('[Navigation] 🚫 已拦截控制台导航错误输出');
          return;
        }
        originalConsoleError.apply(console, args);
      };
    }
  }

  // 安全导航方法 - 永远不会抛出未捕获的错误
  private safeNavigateWithSilentReject<T>(navigationFn: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      // 防止重复跳转
      if (this.isNavigating) {
        // 返回一个永远 pending 的 Promise，这样不会触发 catch
        // 或者可以选择 resolve 一个特殊值
        return; // 直接返回，不 resolve 也不 reject
      }

      this.isNavigating = true;

      navigationFn()
        .then((result) => {
          resolve(result);
        })
        .catch((error) => {
          reject(error);
        })
        .finally(() => {
          // 延迟重置状态，防止快速连续点击
          setTimeout(() => {
            this.isNavigating = false;
          }, 500);
        });
    });
  }

  // 检查是否是导航错误
  public static isNavigationError(error: any): boolean {
    return error && (
      error.isNavigationError === true ||
      error.name === 'NavigationBlocked' ||
      error.code === 'NAVIGATION_IN_PROGRESS'
    );
  }
}

// 创建全局实例并导出
const navigationManager = new NavigationManager();

// 导出工具函数
export const safeNavigate = async (navigationFn: () => Promise<any>, options?: {
  onBlocked?: () => void;
  onError?: (error: any) => void;
  silent?: boolean;
}): Promise<boolean> => {
  try {
    await navigationFn();
    return true;
  } catch (error) {
    if (NavigationManager.isNavigationError(error)) {
      // 导航被阻止
      if (options?.onBlocked) {
        options.onBlocked();
      } else if (!options?.silent) {
      }
      return false;
    } else {
      // 其他错误
      if (options?.onError) {
        options.onError(error);
      } else {
      }
      return false;
    }
  }
};

export default navigationManager;