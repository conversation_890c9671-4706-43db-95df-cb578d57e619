import { View, Text, Image } from "@tarojs/components";
import React, { useState, useEffect, useRef } from "react";
import Taro, { useDidShow } from "@tarojs/taro";
import { Button, Input, Checkbox } from "@arco-design/mobile-react";
import YkNavBar from "@/components/ykNavBar/index";
import { toast } from "@/utils/yk-common";
import "./index.less";
import { IconAdd } from "@arco-design/mobile-react/esm/icon";
// 导入图片
import checkAllIcon from "@/assets/images/common/check_all_icon.png";
import addressClearIcon from "@/assets/images/common/address_clear_icon.png";
import jinjianCheckIcon from "@/assets/images/common/jinjian_check_shlx_icon.png";
import { IconRight } from "@arco-iconbox/react-yk-arco";
import { batchUpdateDynamic,moveHouseDynamic } from "@/utils/api/common/common_user";
import { color } from "html2canvas/dist/types/css/types/color";
export default function EditPrice() {
  const [showPage, setShowPage] = useState(false);
  const [curType, setCurType] = useState(1); // 1: 保留原价, 2: 改价, 3: 去掉原价
  const [curChangeType, setCurChangeType] = useState(1); // 1: 金额, 2: 比例, 3: 统一价
  const [curFanshiType, setCurFanshiType] = useState(1); // 1: 增加, 2: 减少
  const [price, setPrice] = useState("");
  const [percent, setPercent] = useState("");
  const [content, setContent] = useState("");
  const [tags, setTags] = useState("");
  const [specs, setSpecs] = useState("");
  // const [dynamicList, setDynamicList] = useState<any[]>([]);
  const dynamicListRef = useRef<any[]>([]);
  const originalDataRef = useRef<any[]>([]); // 保存原始数据
  const [paddingBottom, setPaddingBottom] = useState("0px");

  // 新增状态
  const [contentCurType, setContentCurType] = useState(0); //0 保留文案 1: 关键字替换, 2: 全文替换, 3: 追加文案
  const [appendType, setAppendType] = useState(1); // 1: 追加到文首, 2: 追加到文末
  const [replaceList, setReplaceList] = useState([
    { oldText: "", newText: "" },
    { oldText: "", newText: "" },
    { oldText: "", newText: "" },
  ]);
  const [tagStatus, setTagStatus] = useState(2); //1: 无, 2: 保留原有标签 3: 自定义标签,
  const [formatStatus, setFormatStatus] = useState(2); // 1: 无, 2: 保留原有规格, 3: 自定义规格
  const [checkTagList, setCheckTagList] = useState<any[]>([]); // 选中的标签列表
  const [checkFormatList, setCheckFormatList] = useState<any[]>([]); // 选中的规格列表
  const [checkColorList, setCheckColorList] = useState<any[]>([]); // 选中的颜色列表

  // 去掉原价相关状态
  const [delPriceText, setDelPriceText] = useState(1); // 0: 不去掉文案价, 1: 去掉文案价 (默认选中)
  const [delPriceInput, setDelPriceInput] = useState(1); // 0: 不去掉已填写价格, 1: 去掉已填写价格 (默认选中)
  const [delPriceNum, setDelPriceNum] = useState(0); // 0: 保留小数点, 1: 去掉小数点后两位

  // 悬浮窗状态
  const [showTagPopup, setShowTagPopup] = useState(false);
  const [showFormatPopup, setShowFormatPopup] = useState(false);
  const [showRulePopup, setShowRulePopup] = useState(false);
  const [tagPopupTop, setTagPopupTop] = useState(350);
  const [formatPopupTop, setFormatPopupTop] = useState(400);
  const [platform,setPlatform] = useState<string>("H5");
  const [type, setType] = useState("batchEdit");
  const [linkId, setLinkId] = useState("");
  useEffect(() => {
    const params = Taro.getCurrentInstance().router?.params;
    if (params?.linkId) {
      setLinkId(params?.linkId);
    }
    if (params?.type) {
      setType(params?.type);
      if(params?.type!=="batchEdit"){
        const storedMoveObject = Taro.getStorageSync("moveObject");
        if (storedMoveObject) {
          moveObject.current=(storedMoveObject);
          console.log('获取到的 moveObject:', storedMoveObject);
  
          // 同步更新页面状态
          if (storedMoveObject.curType !== undefined) setCurType(storedMoveObject.curType);
          if (storedMoveObject.curChangeType !== undefined) setCurChangeType(storedMoveObject.curChangeType);
          if (storedMoveObject.curFanshiType !== undefined) setCurFanshiType(storedMoveObject.curFanshiType);
          if (storedMoveObject.price !== undefined) setPrice(storedMoveObject.price);
          if (storedMoveObject.percent !== undefined) setPercent(storedMoveObject.percent);
          if (storedMoveObject.delPriceNum !== undefined) setDelPriceNum(storedMoveObject.delPriceNum);
          if (storedMoveObject.delPriceText !== undefined) setDelPriceText(storedMoveObject.delPriceText);
          if (storedMoveObject.delPriceInput !== undefined) setDelPriceInput(storedMoveObject.delPriceInput);
          if (storedMoveObject.tagStatus !== undefined) setTagStatus(storedMoveObject.tagStatus);
          if (storedMoveObject.formatStatus !== undefined) setFormatStatus(storedMoveObject.formatStatus);
          if (storedMoveObject.tags !== undefined) setTags(storedMoveObject.tags);
          if (storedMoveObject.specs !== undefined) setSpecs(storedMoveObject.specs);
          if (storedMoveObject.checkTagList !== undefined) setCheckTagList(storedMoveObject.checkTagList);
          if (storedMoveObject.checkFormatList !== undefined) setCheckFormatList(storedMoveObject.checkFormatList);
          if (storedMoveObject.checkColorList !== undefined) setCheckColorList(storedMoveObject.checkColorList);
          if (storedMoveObject.contentCurType !== undefined) setContentCurType(storedMoveObject.contentCurType);
          if (storedMoveObject.appendType !== undefined) setAppendType(storedMoveObject.appendType);
          if (storedMoveObject.replaceList !== undefined) setReplaceList(storedMoveObject.replaceList);
          if (storedMoveObject.content !== undefined) setContent(storedMoveObject.content);
        }
      }
    }
  }, []);

  const moveObject = useRef({
    curType: 0,
    contentCurType: 0,
    tagStatus: 2,
    formatStatus: 2,
    checkTagList: [],
    checkFormatList: [],
    checkColorList: [],
    delPriceText: 1,
    delPriceInput: 1,
    delPriceNum: 0,
    appendType: 1,
    contentType: 0,
    curChangeType: 1,
    curFanshiType: 1,
    price: '',
    percent: '',
    content: '',
    tags: '',
    specs: '',
    replaceList: [
      { oldText: '', newText: '' },
      { oldText: '', newText: '' },
      { oldText: '', newText: '' }
    ]
  });

  const showRulePop = () => {
    setShowRulePopup(true);
  };

  // 关闭规则说明弹框
  const closeRulePop = () => {
    setShowRulePopup(false);
  };

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  useDidShow(() => {
    // 检测平台类型设置底部边距
    const uaAll = navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;

    if (isIos) {
      setPaddingBottom("34px");
    } else if (isAndroid) {
      setPaddingBottom("0px");
    }

    setTimeout(() => {
      setShowPage(true);
    }, 200);

    // 获取传递的动态列表数据
    const selectedDynamics = Taro.getStorageSync("selectedDynamics");
    console.log("selectedDynamics2222", selectedDynamics);
    if (selectedDynamics) {
      // setDynamicList(selectedDynamics);
      dynamicListRef.current = selectedDynamics;
      // 保存原始数据的深拷贝
      originalDataRef.current = JSON.parse(JSON.stringify(selectedDynamics));
    }

    // 监听页面显示，检查是否有新选择的标签和规格
    const checkSelectedData = () => {
      let needUpdateDynamicList = false;
      let updatedList = [...dynamicListRef.current];

      // 检查标签选择
      const selectedArticles = Taro.getStorageSync("selectedArticles");
      console.log("selectedArticles", selectedArticles);
    //   {
    //     "id": 29,
    //     "name": "特价专区"
    // }
      if (selectedArticles && selectedArticles.length > 0) {
        setCheckTagList(selectedArticles);
        const tagNames = selectedArticles
          .map((item: any) => item.name)
          .join(",");
        const tagIds = selectedArticles.map((item: any) => item.id).join(",");
        setTags(tagNames);
        moveObject.current={
          ...moveObject.current,
          tags: tagNames,
          checkTagList: selectedArticles
        };

        // 将标签赋值给dynamicList的每一项
        updatedList = updatedList.map((item) => ({
          ...item,
          labelAndCatalogueIds: tagIds,
          labelAndCatalogueNames: tagNames,
          // 根据操作类型设置不同的数据格式
          ...(type === "move" ? {
            tags: selectedArticles.map(article => article.name)
          } : {
            labels: selectedArticles.reduce((acc, article) => {
              acc[article.id] = article.name;
              return acc;
            }, {})
          })
        }));
        needUpdateDynamicList = true;

        // 清除存储，避免重复使用
        Taro.removeStorageSync("selectedArticles");
      }

      // 检查规格选择
      const selectedFormats = Taro.getStorageSync("selectedFormats");
    //   {
    //     "id": 41,
    //     "userId": 300,
    //     "name": "44.5",
    //     "createTime": 1761291467000
    // }
      const selectedColors = Taro.getStorageSync("selectedColors");
    //   {
    //     "id": 21,
    //     "userId": 300,
    //     "name": "很粗糙",
    //     "createTime": 1761294039000
    // }
      console.log("selectedFormats", selectedFormats);
      console.log("selectedColors", selectedColors);

      if (selectedFormats || selectedColors) {
        const formats = selectedFormats || [];
        const colors = selectedColors || [];

        setCheckFormatList(formats);
        setCheckColorList(colors);

        const formatNames = formats.map((item: any) => item.name).join(",");
        const formatIds = formats.map((item: any) => item.id).join(",");
        const colorNames = colors.map((item: any) => item.name).join(",");
        const colorIds = colors.map((item: any) => item.id).join(",");
        const specsText = [formatNames, colorNames].filter(Boolean).join(" ");
        setSpecs(specsText);
        moveObject.current={
          ...moveObject.current,
          specs: specsText,
          checkFormatList: formats,
          checkColorList: colors
        };
        // 将规格赋值给dynamicList的每一项
        updatedList = updatedList.map((item) => ({
          ...item,
          // 根据操作类型设置不同的数据格式
          ...(type === "move" ? {
            skus: formats.map((format: any) => format.name),
            colors: colors.map((color: any) => color.name)
          } : {
            specifications: formats.reduce((acc: any, format: any) => {
              acc[format.id] = format.name;
              return acc;
            }, {}),
            colors: colors.reduce((acc: any, color: any) => {
              acc[color.id] = color.name;
              return acc;
            }, {})
          })
        }));

        // updatedList = updatedList.map(item => ({
        //   ...item,
        //   productSpecificationsIds: formatIds,
        //   productSpecificationsNames: formatNames
        // }));
        needUpdateDynamicList = true;

        // 清除存储，避免重复使用
        Taro.removeStorageSync("selectedFormats");
        Taro.removeStorageSync("selectedColors");
      }

      // 如果有更新，则更新dynamicList
      if (needUpdateDynamicList) {
        dynamicListRef.current = updatedList;
        // setDynamicList(updatedList);
        console.log("Updated dynamicList with tags/specs:", updatedList);
      }
    };

    // 页面显示时检查数据
    checkSelectedData();

    // 恢复编辑文案的状态
    const editState = Taro.getStorageSync("editContentState");
    if (editState) {
      setContentCurType(editState.contentCurType || 0);
      setAppendType(editState.appendType || 1);
      setReplaceList(
        editState.replaceList || [
          { oldText: "", newText: "" },
          { oldText: "", newText: "" },
          { oldText: "", newText: "" },
        ]
      );
      setContent(editState.content || "");

      // 更新 moveObject
      moveObject.current={
        ...moveObject.current,
        contentCurType: editState.contentCurType || 0,
        appendType: editState.appendType || 1,
        replaceList: editState.replaceList || [
          { oldText: "", newText: "" },
          { oldText: "", newText: "" },
          { oldText: "", newText: "" },
        ],
        content: editState.content || ""
      };

      // 清除存储，避免重复使用
      Taro.removeStorageSync("editContentState");
      console.log("恢复编辑文案状态:", editState);
    }
  });

  // 处理素材搬家的数据转换和提交
  const handleMoveHouse = async () => {
    // 先应用所有的编辑逻辑到 dynamicListRef.current
    let updatedList = [...dynamicListRef.current];

    // 1. 应用价格修改逻辑
    if (curType === 2) {
      // 改价
      if (curChangeType === 1) {
        // 金额
        if (!price) {
          toast("info", {
            content: "请输入金额",
            duration: 2000,
          });
          return;
        }
        const priceNum = parseFloat(price);
        if (priceNum <= 0) {
          toast("info", {
            content: "金额必须大于0",
            duration: 2000,
          });
          return;
        }
        if (priceNum >= 1000) {
          toast("info", {
            content: "金额必须小于1000",
            duration: 2000,
          });
          return;
        }

        // 应用金额变更
        updatedList = updatedList.map((item) => {
          const currentPrice = parseFloat(item.optimaPrice || item.price || "0");
          let newPrice;
          if (curFanshiType === 1) {
            // 增加
            newPrice = currentPrice + priceNum;
          } else {
            // 减少
            newPrice = Math.max(0, currentPrice - priceNum);
          }

          // 根据是否勾选"去掉小数点后两位"来处理价格格式
          const finalPrice =
            delPriceNum === 1
              ? Math.floor(newPrice).toString()
              : newPrice;

          return { ...item, optimaPrice: finalPrice, price: finalPrice };
        });
      } else if (curChangeType === 2) {
        // 比例
        if (!percent) {
          toast("info", {
            content: "请输入比例",
            duration: 2000,
          });
          return;
        }
        const percentNum = parseFloat(percent);
        if (percentNum < 1) {
          toast("info", {
            content: "比例必须大于等于1%",
            duration: 2000,
          });
          return;
        }
        if (percentNum > 100) {
          toast("info", {
            content: "比例必须小于等于100%",
            duration: 2000,
          });
          return;
        }

        // 应用比例变更
        updatedList = updatedList.map((item) => {
          const currentPrice = parseFloat(item.optimaPrice || item.price || "0");
          let newPrice;
          if (curFanshiType === 1) {
            // 增加
            newPrice = currentPrice * (1 + percentNum / 100);
          } else {
            // 减少
            newPrice = Math.max(0, currentPrice * (1 - percentNum / 100));
          }

          // 根据是否勾选"去掉小数点后两位"来处理价格格式
          const finalPrice =
            delPriceNum === 1
              ? Math.floor(newPrice).toString()
              : newPrice;

          return { ...item, optimaPrice: finalPrice, price: finalPrice };
        });
      } else if (curChangeType === 3) {
        // 统一价
        if (!price) {
          toast("info", {
            content: "请输入统一价格",
            duration: 2000,
          });
          return;
        }
        const priceNum = parseFloat(price);
        if (priceNum < 0) {
          toast("info", {
            content: "价格不能为负数",
            duration: 2000,
          });
          return;
        }
        if (priceNum >= 1000) {
          toast("info", {
            content: "价格必须小于1000",
            duration: 2000,
          });
          return;
        }

        // 应用统一价格
        updatedList = updatedList.map((item) => ({
          ...item,
          optimaPrice: priceNum,
          price: priceNum,
        }));
      }
    } else if (curType === 3) {
      // 去掉原价
      updatedList = updatedList.map((item) => {
        let updatedItem = { ...item };

        // 根据用户选择处理文案价
        if (delPriceText === 1) {
          // 去掉文案价：使用正则表达式去掉content中的价格信息
          const content = updatedItem.title || updatedItem.content || "";
          // 参考原有正则，匹配各种价格格式
          const priceRegex =
            /\b[￥¥]?[Pp]?[0-9]+(?:\.\d+)?(?:元|米|元钱|价格)?\b|[￥¥$]/g;
          updatedItem.title = content.replace(priceRegex, "").trim();
          updatedItem.content = content.replace(priceRegex, "").trim();
        }

        // 根据用户选择处理已填写价格
        if (delPriceInput === 1) {
          // 去掉已填写价格：清空price字段
          updatedItem.optimaPrice = "0";
          updatedItem.price = "0";
        }

        return updatedItem;
      });
    }

    // 2. 应用文案修改逻辑
    if (contentCurType === 1) {
      // 关键字替换
      updatedList = updatedList.map((item) => {
        let itemContent = item.title || item.content || "";
        replaceList.forEach((replaceItem) => {
          if (replaceItem.oldText && replaceItem.newText) {
            const regex = new RegExp(replaceItem.oldText, "g");
            itemContent = itemContent.replace(regex, replaceItem.newText);
          }
        });
        return { ...item, title: itemContent, content: itemContent };
      });
    } else if (contentCurType === 2) {
      // 全文替换
      if (content) {
        updatedList = updatedList.map((item) => ({
          ...item,
          title: content,
          content: content,
        }));
      }
    } else if (contentCurType === 3) {
      // 追加文案
      if (content) {
        updatedList = updatedList.map((item) => {
          const originalContent = item.title || item.content || "";
          const newContent =
            appendType === 1
              ? content + originalContent
              : originalContent + content;
          return { ...item, title: newContent, content: newContent };
        });
      }
    }

    // 3. 应用标签和规格的修改（如果有从搬家设置页面返回的设置）
    // 标签和规格的修改已经在 useDidShow 中通过 checkSelectedData 处理了
    // 这里只需要确保数据正确传递

    const moveData = {
      userId: Taro.getStorageSync("userInfo").id,
      materialRelocationId: linkId,
      dynamics: updatedList.map((item) => ({
        imgsSrc: item.imgsSrc || [],
        title: item.title || item.content || "",
        skus: item.skus || [],
        tags: item.tags || [],
        optimaPrice: item.optimaPrice || item.price || "0",
        digitalWatermark: item.digitalWatermark || "",
        timeStamp: item.timeStamp || "",
        time: item.time || "",
        colors: item.colors || [],
      })),
    };

    console.log("准备搬家的数据:", moveData);
    console.log("准备搬家的数据2:", updatedList);
    // 4. 调用搬家接口
    const data = {
      content: JSON.stringify(moveData),
      // content: JSON.stringify(updatedList),
    };

    try {
      Taro.showLoading({
        title: "正在搬家...",
        mask: true,
      });
      

      let res: any = await moveHouseDynamic(moveData);
      Taro.hideLoading();

      if (res && res.code == 0) {
        Taro.setStorageSync("selectedDynamics", []);
        Taro.setStorageSync("moveObject", moveObject.current);
        // 跳转搬家成功结果页，传入数量
        Taro.redirectTo({
          url: `/pageDynamic/moveMaterials/selectMaterials/result?count=${moveData.dynamics.length}`,
        });
        Taro.eventCenter.trigger("refreshAlbumList");
      } else {
        toast("error", {
          content: res.msg || "搬家失败",
          duration: 2000,
        });
      }
    } catch (error) {
      Taro.hideLoading();
      toast("error", {
        content: "搬家失败，请重试",
        duration: 2000,
      });
      console.error("搬家失败:", error);
    }
  };

  const changeType = (type: number) => {
    setCurType(type);
   // 其他情况只更新类型
   moveObject.current={
    ...moveObject.current,
    curType: type
  };
  };

  const changeChangeType = (type: number) => {
    setCurChangeType(type);
    moveObject.current={
      ...moveObject.current,
      curChangeType: type
    };
  };

  const changeFanshiType = (type: number) => {
    setCurFanshiType(type);
    moveObject.current={
      ...moveObject.current,
      curFanshiType: type
    };
  };

  const handlePriceChange = (value: string) => {
    // 限制输入格式：最多3位整数和2位小数（0-999.99）
    const regex = /^\d{0,3}(\.\d{0,2})?$/;
    if (regex.test(value) || value === "") {
      const numValue = parseFloat(value);
      // 如果有值且小于1000，则允许输入
      if (value === "" || numValue < 1000) {
        setPrice(value);
        moveObject.current={
          ...moveObject.current,
          price: value
        };
      }
    }
  };

  const handlePercentChange = (value: string) => {
    // 限制输入格式：最多3位整数，范围1-100
    const regex = /^\d{0,3}$/;
    if (regex.test(value) || value === "") {
      const numValue = parseInt(value);
      // 如果有值且在1-100范围内，则允许输入
      if (value === "" || (numValue >= 1 && numValue <= 100)) {
        setPercent(value);
        moveObject.current={
          ...moveObject.current,
          percent: value
        };
      }
    }
  };

  const next = () => {
       // 验证自定义标签和规格
       if (tagStatus === 3 && (!tags || tags.trim() === "")) {
        toast("info", {
          content: "请添加自定义标签",
          duration: 2000,
        });
        return;
      }

      if (formatStatus === 3 && (!specs || specs.trim() === "")) {
        toast("info", {
          content: "请添加自定义规格",
          duration: 2000,
        });
        return;
      }

    // 打印 moveObject 对象
    if (type==="moveSetting") {
      console.log('moveObject:', moveObject);

    // 根据选择的类型，重置其他类型的相关状态
    console.log("curType",curType);
    if (curType === 1) {
      console.log("curType1",curType);
      // 选择"保留原价"时，清空改价和去掉原价的设置
      setCurChangeType(1);
      setCurFanshiType(1);
      setPrice("");
      setPercent("");
      setDelPriceNum(0);
      setDelPriceText(1);
      setDelPriceInput(1);
      moveObject.current={
        ...moveObject.current,
        curType: curType,
        curChangeType: 1,
        curFanshiType: 1,
        price: "",
        percent: "",
        delPriceNum: 0,
        delPriceText: 1,
        delPriceInput: 1
      };
    } else if (curType === 2) {
        console.log("curType2",curType);
      // 选择"改价"时，清空去掉原价的设置
      setDelPriceText(1);
      setDelPriceInput(1);
      moveObject.current={
        ...moveObject.current,
        curType: curType,
        delPriceText: 1,
        delPriceInput: 1
      };

      if (curChangeType === 1) {
        setPercent("");
        moveObject.current={
          ...moveObject.current,
          percent: ""
        };
      }else if (curChangeType === 2) {
        setPrice("");
        moveObject.current={
          ...moveObject.current,
          price: ""
        };
      }else if (curChangeType === 3) {
        setPercent("");
        moveObject.current={
          ...moveObject.current,
          percent: ""
        };
      }
    } else if (curType === 3) {
      console.log("curType3",curType);
      // 选择"去掉原价"时，只清空改价的设置，保留去掉原价的设置
      setCurChangeType(1);
      setCurFanshiType(1);
      setPrice("");
      setPercent("");
      setDelPriceNum(0);
      moveObject.current={
        ...moveObject.current,
        curType: curType,
        curChangeType: 1,
        curFanshiType: 1,
        price: "",
        percent: "",
        delPriceNum: 0
      };
    }
    console.log('moveObject2:', moveObject.current);
      Taro.setStorageSync("moveObject", moveObject.current);
      Taro.navigateBack();
      return;
    }else if(type==="move"){
      // 素材搬家流程
      handleMoveHouse();
      return;
    }else if(type==="batchEdit"){
      if (curType === 2) {
        // 改价
        if (curChangeType === 1) {
          // 金额
          if (!price) {
            toast("info", {
              content: "请输入金额",
              duration: 2000,
            });
            return;
          }
          const priceNum = parseFloat(price);
          if (priceNum <= 0) {
            toast("info", {
              content: "金额必须大于0",
              duration: 2000,
            });
            return;
          }
          if (priceNum >= 1000) {
            toast("info", {
              content: "金额必须小于1000",
              duration: 2000,
            });
            return;
          }
  
          // 应用金额变更
          const updatedList = dynamicListRef.current.map((item) => {
            const currentPrice = parseFloat(item.price || "0");
            let newPrice;
            if (curFanshiType === 1) {
              // 增加
              newPrice = currentPrice + priceNum*100;
            } else {
              // 减少
              newPrice = Math.max(0, currentPrice - priceNum*100);
            }
  
            // 根据是否勾选"去掉小数点后两位"来处理价格格式
            const finalPrice =
              delPriceNum === 1
                ? (Math.floor(newPrice/100)*100).toString()
                : newPrice;
  
            return { ...item, price: finalPrice * 100 };
          });
          dynamicListRef.current = updatedList;
          // setDynamicList(updatedList);
          console.log("updatedList", updatedList);
        } else if (curChangeType === 2) {
          // 比例
          if (!percent) {
            toast("info", {
              content: "请输入比例",
              duration: 2000,
            });
            return;
          }
          const percentNum = parseFloat(percent);
          if (percentNum < 1) {
            toast("info", {
              content: "比例必须大于等于1%",
              duration: 2000,
            });
            return;
          }
          if (percentNum > 100) {
            toast("info", {
              content: "比例必须小于等于100%",
              duration: 2000,
            });
            return;
          }
  
          // 应用比例变更
          const updatedList = dynamicListRef.current.map((item) => {
            const currentPrice = parseFloat(item.price || "0");
            let newPrice;
            if (curFanshiType === 1) {
              // 增加
              newPrice = currentPrice * (1 + percentNum / 100);
            } else {
              // 减少
              newPrice = Math.max(0, currentPrice * (1 - percentNum / 100));
            }
  
            // 根据是否勾选"去掉小数点后两位"来处理价格格式
            const finalPrice =
              delPriceNum === 1
                ? (Math.floor(newPrice/100)*100).toString()
                : newPrice;
  
            return { ...item, price: finalPrice * 100 };
          });
          dynamicListRef.current = updatedList;
          // setDynamicList(updatedList);
          console.log("updatedList", updatedList);
        } else if (curChangeType === 3) {
          // 统一价
          if (!price) {
            toast("info", {
              content: "请输入统一价格",
              duration: 2000,
            });
            return;
          }
          const priceNum = parseFloat(price);
          if (priceNum < 0) {
            toast("info", {
              content: "价格不能为负数",
              duration: 2000,
            });
            return;
          }
          if (priceNum >= 1000) {
            toast("info", {
              content: "价格必须小于1000",
              duration: 2000,
            });
            return;
          }
  
          // 应用统一价格
          const updatedList = dynamicListRef.current.map((item) => ({
            ...item,
            price: priceNum*100,
          }));
          dynamicListRef.current = updatedList;
          // setDynamicList(updatedList);
          console.log("updatedList", updatedList);
        }
      } else if (curType === 3) {
        // 去掉原价
        const updatedList = dynamicListRef.current.map((item) => {
          let updatedItem = { ...item };
  
          // 根据用户选择处理文案价
          if (delPriceText === 1) {
            // 去掉文案价：使用正则表达式去掉content中的价格信息
            const content = updatedItem.content || "";
            // 参考原有正则，匹配各种价格格式
            const priceRegex =
              /\b[￥¥]?[Pp]?[0-9]+(?:\.\d+)?(?:元|米|元钱|价格)?\b|[￥¥$]/g;
            updatedItem.content = content.replace(priceRegex, "").trim();
          }
  
          // 根据用户选择处理已填写价格
          if (delPriceInput === 1) {
            // 去掉已填写价格：清空price字段
            updatedItem.price = "0";
          }
  
          return updatedItem;
        });
        dynamicListRef.current = updatedList;
        // setDynamicList(updatedList);
        console.log("去掉原价处理结果:", updatedList);
      }
  
      // 保存更新后的数据并返回
      Taro.setStorageSync("updatedDynamics", dynamicListRef.current);
      // Taro.eventCenter.trigger('updateDynamicPrices', dynamicListRef.current);
  
      batchUpdateDynamic(
        dynamicListRef.current,
      ).then((res) => {
        if (res && res.code === 0) {
          toast("success", {
            content: "批量编辑成功",
            duration: 2000,
          });
          Taro.eventCenter.trigger("refreshAlbumList");
        } else {
          toast("error", {
            content: res.msg,
            duration: 2000,
          });
        }
        console.log("res", res);
      });
  
      // 发送事件刷新batchEdit列表
  
      console.log("dynamicList", dynamicListRef.current);
  
      setTimeout(() => {
        Taro.navigateBack({
          delta: 1,
        });
      }, 1000);
    }else{

    }
  };


  // 修改文案
  const changeText = () => {
    console.log('contentCurType', contentCurType);
    // 将当前选中的动态数据存储到 Storage 中
    Taro.setStorageSync("selectedDynamics", dynamicListRef.current);

    // 保存当前的编辑状态，供修改文案页面使用
    const editState = {
      contentCurType,
      appendType,
      replaceList,
      content,
    };
    Taro.setStorageSync("editContentState", editState);

    Taro.navigateTo({
      url: `/pageDynamic/album/editContent/index`,
    });
  };

  // 选择标签
  const chooseTag = () => {
    // 跳转到标签选择页面
    // 保存当前选中的标签到 Storage，供选择页面回显
    if (checkTagList.length > 0) {
      Taro.setStorageSync("selectedArticles", checkTagList);
    }

    Taro.navigateTo({
      url: "/pageDynamic/releaseDynamic/selectTag/index",
    });
  };

  // 选择商品规格
  const chooseFormat = () => {
    // 跳转到商品规格选择页面
    // 保存当前选中的规格和颜色到 Storage，供选择页面回显
    if (checkFormatList.length > 0) {
      Taro.setStorageSync("selectedFormats", checkFormatList);
    }
    if (checkColorList.length > 0) {
      Taro.setStorageSync("selectedColors", checkColorList);
    }

    Taro.navigateTo({
      url: "/pageDynamic/releaseDynamic/selectProductColor/index",
    });
  };

  // 计算悬浮窗位置
  const calculatePopupPosition = () => {
    // 基础位置
    let baseTop = 200;

    // 根据当前选择的类型调整位置
    if (curType === 2) {
      // 改价模式
      baseTop += 100; // 改价选项高度

      if (curChangeType === 1 || curChangeType === 2) {
        baseTop += 80; // 改价类型高度
        baseTop += 80; // 改价方式高度
        baseTop += 40; // 去掉小数点选项高度
        baseTop += 60; // 输入框高度
      } else if (curChangeType === 3) {
        baseTop += 80; // 改价类型高度
        baseTop += 40; // 统一价提示高度
        baseTop += 60; // 输入框高度
      }
    } else if (curType === 1) {
      // 保留原价模式
      baseTop += 100; // 改价选项高度
      baseTop += 40; // 保留原价提示高度
    } else if (curType === 3) {
      // 去掉原价模式
      baseTop += 100; // 改价选项高度
      baseTop += 80; // 去掉原价选项高度
    }

    return baseTop;
  };

  // 显示标签弹框
  const showTagPop = () => {
    const top = calculatePopupPosition();
    setTagPopupTop(top);
    setShowTagPopup(true);
  };

  // 显示商品规格弹框
  const showFormatPop = () => {
    const top = calculatePopupPosition() + 50; // 商品规格比标签低50px
    setFormatPopupTop(top);
    setShowFormatPopup(true);
  };

  // 关闭标签弹框
  const closeTagPop = () => {
    setShowTagPopup(false);
  };

  // 关闭商品规格弹框
  const closeFormatPop = () => {
    setShowFormatPopup(false);
  };

  // 选择标签类型
  const checkTag = (status: number) => {
    setTagStatus(status);
      moveObject.current={
        ...moveObject.current,
        tagStatus: status
      };

    if (status === 1) {
      // 选择"无"，清空所有商品的标签信息
      const updatedList = dynamicListRef.current.map((item) => ({
        ...item,
        // labelAndCatalogueIds: "",
        // labelAndCatalogueNames: "",
        ...(type === "move" ? {
          tags: []
        } : {
          labels: {}
        })
      }));
      dynamicListRef.current = updatedList;

      // 同时清空UI状态
      setCheckTagList([]);
      setCheckTagList([]);
      setTags("");

      console.log("清空商品标签:", updatedList);
    } else if (status === 2) {
      // 选择"保留原有标签"，恢复原始数据中的标签信息
      const updatedList = dynamicListRef.current.map((item, index) => {
        const originalItem = originalDataRef.current[index];
        return {
          ...item,
          // labelAndCatalogueIds: originalItem?.labelAndCatalogueIds || "",
          // labelAndCatalogueNames: originalItem?.labelAndCatalogueNames || "",
          ...(type === "move" ? {
            tags: originalItem?.tags || []
          } : {
            labels: originalItem?.labels || {}
          })
        };
      });
      dynamicListRef.current = updatedList;

      // 恢复UI状态（如果原始数据中有标签信息）
      const hasOriginalTags = originalDataRef.current.some(
        (item) => item.labelAndCatalogueIds
      );
      if (hasOriginalTags) {
        // 暂时清空UI状态，让用户重新选择或显示原始标签
        setTags(""); // 可以根据需要显示原始标签
        setCheckTagList([]);
      }

      console.log("恢复商品标签:", updatedList);
    }
    // status === 1 (自定义标签) 不需要特殊处理，保持当前状态

    closeTagPop();
  };

  // 选择商品规格类型
  const checkFormat = (status: number) => {
    setFormatStatus(status);
    moveObject.current={
      ...moveObject.current,
      formatStatus: status
    };

    if (status === 1) {
      // 选择"无"，清空所有商品的规格信息
      const updatedList = dynamicListRef.current.map((item) => ({
        ...item,
        ...(type === "move" ? {
          skus: [],
          colors: []
        } : {
          specifications: {},
          colors: {}
        })
      }));
      dynamicListRef.current = updatedList;

      // 同时清空UI状态
      setCheckColorList([]);
      setCheckFormatList([]);
      setSpecs("");

      console.log("清空商品规格:", updatedList);
    } else if (status === 2) {
      // 选择"保留原有规格"，恢复原始数据中的规格信息
      const updatedList = dynamicListRef.current.map((item, index) => {
        const originalItem = originalDataRef.current[index];
        return {
          ...item,
          ...(type === "move" ? {
            skus: originalItem?.skus || [],
            colors: originalItem?.colors || []
          } : {
            specifications: originalItem?.specifications || {},
            colors: originalItem?.colors || {}
          })
        };
      });
      dynamicListRef.current = updatedList;

      // 恢复UI状态（如果原始数据中有规格信息）
      const hasOriginalSpecs = originalDataRef.current.some(
        (item) => item.productColorNames || item.productSpecificationsNames
      );
      if (hasOriginalSpecs) {
        // 这里可以根据需要恢复UI状态，或者保持当前状态让用户重新选择
        setSpecs(""); // 暂时清空，让用户重新选择
      }

      console.log("恢复商品规格:", updatedList);
    }
    // status === 3 (自定义规格) 不需要特殊处理，保持当前状态

    closeFormatPop();
  };

  // 清除标签
  const clearTag = () => {
    setCheckTagList([]);
    setTags("");
    moveObject.current.tags = "";
    moveObject.current.checkTagList = [];
    moveObject.current={
      ...moveObject.current,
        tags: "",
        checkTagList: []
    };
  };

  // 清除商品规格
  const clearFormat = () => {
    setCheckColorList([]);
    setCheckFormatList([]);
    setSpecs("");
    moveObject.current={
      ...moveObject.current,
      specs: "",
      checkFormatList: [],
      checkColorList: []
    };
  };

  // 切换去掉文案价
  const checkDelPriceText = () => {
    const newValue = delPriceText === 1 ? 0 : 1;
    setDelPriceText(newValue);
    moveObject.current={
      ...moveObject.current,
      delPriceText: newValue
    };
  };

  // 切换去掉已填写价格
  const checkDelPriceInput = () => {
    const newValue = delPriceInput === 1 ? 0 : 1;
    setDelPriceInput(newValue);
    moveObject.current={
      ...moveObject.current,
      delPriceInput: newValue
    };
  };

  // 切换去掉小数点
  const checkDelPriceNum = () => {
    const newValue = delPriceNum === 1 ? 0 : 1;
    setDelPriceNum(newValue);
    moveObject.current={
      ...moveObject.current,
      delPriceNum: newValue
    };
  };

  return (
    <View className={`edit-price-page ${showPage ? "" : "hidden-style"}`}>
      {platform !== "WX" &&<YkNavBar title="批量编辑" />}

      <View className="box">
        {/* 改价选项 */}
        <View className="type">
        <View
            className="type-save clickOpacity"
            onClick={() => changeType(1)}
          >
            <Text
              className={curType === 1 ? "type-save-textA" : "type-save-text"}
            >
              保留原价
            </Text>
          </View>
          <View
            className="type-change clickOpacity"
            onClick={() => changeType(2)}
          >
            <Text
              className={
                curType === 2 ? "type-change-textA" : "type-change-text"
              }
            >
              改价
            </Text>
          </View>
          <View className="type-del clickOpacity" onClick={() => changeType(3)}>
            <Text
              className={curType === 3 ? "type-del-textA" : "type-del-text"}
            >
              去掉原价
            </Text>
          </View>
        </View>

        {curType === 2 && (
          <View className="change">
            <View className="change-title">
              <Text>改价类型</Text>
            </View>

            <View className="change-type">
              <View
                className={
                  curChangeType === 1
                    ? "change-type-moneyA"
                    : "change-type-money"
                }
                onClick={() => changeChangeType(1)}
              >
                <Text>金额</Text>
              </View>
              <View
                className={
                  curChangeType === 2
                    ? "change-type-percentA"
                    : "change-type-percent"
                }
                onClick={() => changeChangeType(2)}
              >
                <Text>比例</Text>
              </View>
              <View
                className={
                  curChangeType === 3 ? "change-type-sameA" : "change-type-same"
                }
                onClick={() => changeChangeType(3)}
              >
                <Text>统一价</Text>
              </View>
            </View>
          </View>
        )}

        {curType === 2 && (curChangeType === 1 || curChangeType === 2) && (
          <View className="fanshi">
            <View className="fanshi-title">
              <Text>改价方式</Text>
            </View>

            <View className="fanshi-type">
              <View
                className={
                  curFanshiType === 1 ? "fanshi-type-addA" : "fanshi-type-add"
                }
                onClick={() => changeFanshiType(1)}
              >
                <Text>加价</Text>
              </View>
              <View
                className={
                  curFanshiType === 2 ? "fanshi-type-subA" : "fanshi-type-sub"
                }
                onClick={() => changeFanshiType(2)}
              >
                <Text>减价</Text>
              </View>
            </View>
          </View>
        )}

        {/* 统一价提示和输入框 */}
        {curType === 2 && curChangeType === 3 && (
          <View className="same">
            <View className="same-hint">
              <Text className="same-hint-text">*</Text>
              <Text className="same-hint-text2">
                编辑时，给多组商品批量设置统一价格
              </Text>
            </View>
            <View className="same-content">
              <View className="same-content-unit">
                <Text>￥</Text>
              </View>
              <Input
                border="none"
                className="same-content-input"
                placeholder="请填写统一价（选填）"
                value={price}
                onChange={(_, value) => handlePriceChange(value)}
                type="number"
              />
            </View>
          </View>
        )}

        {/* 去掉小数点选项 */}
        {curType === 2 && (curChangeType === 1 || curChangeType === 2) && (
          <View className="xiaoshu clickOpacity">
            <Checkbox
              className="xiaoshu-checkbox"
              checked={delPriceNum === 1}
              onChange={checkDelPriceNum}
            />
            <Text className="xiaoshu-text">去掉小数点后两位</Text>
          </View>
        )}

        {/* 金额输入框 */}
        {curType === 2 && curChangeType === 1 && (
          <View className="money">
            <View className="money-unit">
              <Text>￥</Text>
            </View>
            <Input
              border="none"
              className="money-input"
              placeholder="请填写金额（选填）"
              value={price}
              onChange={(_, value) => handlePriceChange(value)}
              type="number"
            />
          </View>
        )}

        {/* 比例输入框 */}
        {curType === 2 && curChangeType === 2 && (
          <View className="percent">
            <Input
              border="none"
              className="percent-input"
              placeholder="请填写比例（选填）"
              value={percent}
              onChange={(_, value) => handlePercentChange(value)}
              type="number"
            />
            <View className="percent-unit">
              <Text>%</Text>
            </View>
          </View>
        )}
      </View>

      {/* 保留原价提示 */}
      {curType === 1 && (
        <View className="save">
          <View className="save-hint">
            <Text className="save-hint-text">*</Text>
            <Text className="save-hint-text2">
              保留选中商品的价格（含文案价）
            </Text>
          </View>
        </View>
      )}

      {/* 去掉原价选项 */}
      {curType === 3 && (
        <View className="del">
          <View className="del-item1 clickOpacity">
            <Checkbox
              className="del-item1-checkbox"
              checked={delPriceText === 1}
              onChange={checkDelPriceText}
            />
            <Text className="del-item1-text">去掉文案价</Text>
          </View>

          <View className="del-item2 clickOpacity">
            <Checkbox
              className="del-item2-checkbox"
              checked={delPriceInput === 1}
              onChange={checkDelPriceInput}
            />
            <Text className="del-item2-text">去掉已填写价格</Text>
          </View>
        </View>
      )}

      {/* 第二个 box - 修改文案、标签、商品规格 */}
      <View className="box2">
        {/* 修改文案 */}
        <View className="content clickOpacity" onClick={() => changeText()}>
          <View className="content-title">
            <Text>修改文案</Text>
          </View>
          <View className="content-right">
            <Text className="content-right-text">
              {contentCurType === 0
                ? "保留文案"
                : contentCurType === 1
                ? "关键字替换"
                : contentCurType === 2
                ? "全文替换"
                : appendType === 1
                ? "追加文案(追加到文首)"
                : "追加文案(追加到文末)"}
            </Text>
            {/* <Image
              className="content-right-img"
              src={rightArrowIcon}
            /> */}
            <IconRight className="content-right-img" />
          </View>
        </View>

        <View className="line"></View>

        {/* 标签 */}
        <View className="tag">
          <View className="tag-title">
            <Text>标签</Text>
          </View>
          <View className="tag-right clickOpacity" onClick={() => showTagPop()}>
            <Text className="tag-right-text">
              {tagStatus === 1
                ? "清空标签"
                : tagStatus === 2
                ? "保留原有标签"
                : "自定义标签"}
            </Text>
            <Image className="tag-right-img" src={checkAllIcon} />
          </View>
        </View>

        {tagStatus === 3 && (
          <View className="tagC">
            <Text className="tagC-text">{tags || ""}</Text>
            {checkTagList.length > 0 && (
              <Image
                className="tagC-img clickOpacity"
                onClick={() => clearTag()}
                src={addressClearIcon}
              />
            )}
            <View className="tagC-add clickOpacity" onClick={() => chooseTag()}>
              <Text className="tagC-add-text">添加</Text>
              {/* <Image
                className="tagC-add-img"
                src={zfAddIcon}
              /> */}
              <IconAdd className="tagC-add-img" />
            </View>
          </View>
        )}

        <View className="line"></View>

        {/* 商品规格 */}
        <View className="format">
          <View className="format-title">
            <Text>商品规格</Text>
          </View>
          <View
            className="format-right clickOpacity"
            onClick={() => showFormatPop()}
          >
            <Text className="format-right-text">
              {formatStatus === 1
                ? "清空规格"
                : formatStatus === 2
                ? "保留原有规格"
                : "自定义规格"}
            </Text>
            <Image className="format-right-img" src={checkAllIcon} />
          </View>
        </View>

        <View className="rule" onClick={() => showRulePop()}>
            <Image className="rule-img" src={require('@/assets/images/common/question_circle.png')} />
            <Text className="rule-text">规则说明</Text>
        </View>

        {formatStatus === 3 && (
          <View className="formatC">
            <Text className="formatC-text">{specs || ""}</Text>
            {(checkFormatList.length > 0 || checkColorList.length > 0) && (
              <Image
                className="formatC-img clickOpacity"
                onClick={() => clearFormat()}
                src={addressClearIcon}
              />
            )}
            <View
              className="formatC-add clickOpacity"
              onClick={() => chooseFormat()}
            >
              <Text className="formatC-add-text">添加</Text>
              <IconAdd className="formatC-add-img" />
            </View>
          </View>
        )}
      </View>

      <View className="holder"></View>

      <View className="footerbtn" style={{ paddingBottom }}>
        <View className="footerbtn-btn clickBg" onClick={next}>
          <Text>确认修改</Text>
        </View>
      </View>

      {/* 标签选择悬浮窗 */}
      {showTagPopup && (
        <View className="popup-mask" onClick={closeTagPop}>
          <View
            className="tagpopup"
            style={{ top: `${tagPopupTop}px` }}
            onClick={(e) => e.stopPropagation()}
          >
            <View className="tagpopup-bottomBox">
              <View
                className="tagpopup-bottomBox-line clickOpacity"
                onClick={() => checkTag(1)}
              >
                <Text
                  className={
                    tagStatus === 1
                      ? "tagpopup-bottomBox-line-textC"
                      : "tagpopup-bottomBox-line-text"
                  }
                >
                  清空标签
                </Text>
                {tagStatus === 1 && (
                  <Image
                    className="tagpopup-bottomBox-line-img"
                    src={jinjianCheckIcon}
                  />
                )}
              </View>
              <View className="tagpopup-bottomBox-divider"></View>
              <View
                className="tagpopup-bottomBox-line clickOpacity"
                onClick={() => checkTag(2)}
              >
                <Text
                  className={
                    tagStatus === 2
                      ? "tagpopup-bottomBox-line-textC"
                      : "tagpopup-bottomBox-line-text"
                  }
                >
                  保留原有标签
                </Text>
                {tagStatus === 2 && (
                  <Image
                    className="tagpopup-bottomBox-line-img"
                    src={jinjianCheckIcon}
                  />
                )}
              </View>
              <View className="tagpopup-bottomBox-divider"></View>
              <View
                className="tagpopup-bottomBox-line clickOpacity"
                onClick={() => checkTag(3)}
              >
                <Text
                  className={
                    tagStatus === 3
                      ? "tagpopup-bottomBox-line-textC"
                      : "tagpopup-bottomBox-line-text"
                  }
                >
                  自定义标签
                </Text>
                {tagStatus === 3 && (
                  <Image
                    className="tagpopup-bottomBox-line-img"
                    src={jinjianCheckIcon}
                  />
                )}
              </View>
            </View>
          </View>
        </View>
      )}

      {/* 商品规格选择悬浮窗 */}
      {showFormatPopup && (
        <View className="popup-mask" onClick={closeFormatPop}>
          <View
            className="formatpopup"
            style={{ top: `${formatPopupTop}px` }}
            onClick={(e) => e.stopPropagation()}
          >
            <View className="formatpopup-bottomBox">
              <View
                className="formatpopup-bottomBox-line clickOpacity"
                onClick={() => checkFormat(1)}
              >
                <Text
                  className={
                    formatStatus === 1
                      ? "formatpopup-bottomBox-line-textC"
                      : "formatpopup-bottomBox-line-text"
                  }
                >
                  清空规格
                </Text>
                {formatStatus === 1 && (
                  <Image
                    className="formatpopup-bottomBox-line-img"
                    src={jinjianCheckIcon}
                  />
                )}
              </View>
              <View className="formatpopup-bottomBox-divider"></View>
              <View
                className="formatpopup-bottomBox-line clickOpacity"
                onClick={() => checkFormat(2)}
              >
                <Text
                  className={
                    formatStatus === 2
                      ? "formatpopup-bottomBox-line-textC"
                      : "formatpopup-bottomBox-line-text"
                  }
                >
                  保留原有规格
                </Text>
                {formatStatus === 2 && (
                  <Image
                    className="formatpopup-bottomBox-line-img"
                    src={jinjianCheckIcon}
                  />
                )}
              </View>
              <View className="formatpopup-bottomBox-divider"></View>
              <View
                className="formatpopup-bottomBox-line clickOpacity"
                onClick={() => checkFormat(3)}
              >
                <Text
                  className={
                    formatStatus === 3
                      ? "formatpopup-bottomBox-line-textC"
                      : "formatpopup-bottomBox-line-text"
                  }
                >
                  自定义规格
                </Text>
                {formatStatus === 3 && (
                  <Image
                    className="formatpopup-bottomBox-line-img"
                    src={jinjianCheckIcon}
                  />
                )}
              </View>
            </View>
          </View>
        </View>
      )}

      {/* 规则说明弹框 */}
      {showRulePopup && (
        <View className="popup-mask" onClick={closeRulePop}>
          <View className="rule-popup" onClick={(e) => e.stopPropagation()}>
            <View className="rule-popup-header">
              <Text className="rule-popup-title">规则说明</Text>
            </View>

            <View className="rule-popup-content">
              <View className="rule-item">
                <Text className="rule-item-title">• 保留原价</Text>
                <Text className="rule-item-desc">默认保留全部商品价格和文案价</Text>
              </View>

              <View className="rule-item">
                <Text className="rule-item-title">• 改价 - 金额/比例</Text>
                <Text className="rule-item-desc">按金额/比例对商品价格进行批量加减，可选择去掉小数点后两位</Text>
              </View>

              <View className="rule-item">
                <Text className="rule-item-title">• 改价 - 统一价</Text>
                <Text className="rule-item-desc">可批量统一设置商品价格</Text>
              </View>

              <View className="rule-item">
                <Text className="rule-item-title">• 去掉原价</Text>
                <Text className="rule-item-desc">默认去掉文案价格和文案价格</Text>
              </View>
            </View>

            <View className="rule-popup-footer">
              <View className="rule-popup-btn" onClick={closeRulePop}>
                <Text className="rule-popup-btn-text">我知道了</Text>
              </View>
            </View>
          </View>
        </View>
      )}
    </View>
  );
}
