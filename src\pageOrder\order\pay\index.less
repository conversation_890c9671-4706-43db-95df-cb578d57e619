@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pageOrder/order/pay/index"] {
  .pay-order-page {
    background: #f7f8fa;
    .use-dark-mode-query({
    background: var(--dark-background-color);    //白色字体
  });
    min-height: 100vh;
    padding-bottom: 88px; // 给底部固定栏留出空间，新的高度约80px + 8px缓冲
  }
  .pay-list-section {
    margin-bottom: 12px;
  }
  // .cell-group-body{
  //   padding-bottom: 12px;
  // }
  .pay-goods-section {
    background: #fff;
    .use-dark-mode-query({
    background: var(--dark-card-background-color);    //白色字体
  });
    border-radius: 10px;
    margin: 0 12px 12px 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
    padding: 12px;
  }
  .shop-title-row {
    display: flex;
    align-items: center;
    color: #222;
    .use-dark-mode-query({
    color: var(--dark-font-color);    //白色字体
  });
    margin-bottom: 8px;
  }
  .shop-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 8px;
  }
  .shop-title {
    font-weight: 500;
    font-size: 15px;
    margin-left: 6px;
  }
  .shop-cert {
    width: 16px;
    height: 16px;
    //background: url('cert-icon.svg') no-repeat center/contain;
  }
  .goods-row-pay {
    display: flex;
    //align-items: flex-start;
    flex-direction: column;
    margin-bottom: 10px;
  }
  .goods-img {
    width: 64px;
    height: 64px;
    border-radius: 8px;
    // background: #f5f5f5;
    margin-right: 12px;

    &.goods-img-placeholder {
      background: #f0f0f0;
      .use-dark-mode-query({
      background: #333;
    });
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
    }
  }
  .goods-info-pay {
    display: flex;
    flex: 2;
    min-width: 0;
  }
  .goods-title {
    width: 70%;
    font-size: 14px;
    color: #222;
    .use-dark-mode-query({
    color: var(--dark-font-color);    //白色字体
  });
    font-weight: 500;
    margin-bottom: 4px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    line-height: 20px;
    max-height: 40px;
  }
  .goods-meta {
    display: flex;
    align-items: center;
    margin-top: 8px;
  }
  .goods-bottom-row {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
  }
  .goods-size {
    font-weight: 500;
    font-size: 13px;
    color: #222;
    .use-dark-mode-query({
    color: var(--dark-font-color);    //白色字体
  });
  }
  .goods-count {
    font-weight: 500;
    font-size: 15px;
    color: #222;
    .use-dark-mode-query({
    color: var(--dark-font-color);    //白色字体
  });
  }
  .goods-price {
    font-weight: 500;
    font-size: 15px;
    color: #f53f3f;
  }

  .goods-stepper {
    width: 100px;
  }
  .pay-summary-list {
    margin: 12px 0 0 0;
    padding-top: 10px;
    border-top: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-top: 1px solid @dark-line-color;
  });
  }
  .pay-summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #888;
    .use-dark-mode-query({
    color: var(--dark-font-color);    //白色字体
  });
    margin-bottom: 6px;
  }
  .pay-summary-total {
    color: #222;
    font-weight: 600;
    font-size: 15px;
    .use-dark-mode-query({
    color: var(--dark-font-color);    //白色字体
  });
  }
  .pay-price {
    color: #f53f3f;
  }
  .pay-remark-row {
    margin: 12px 0 0 0;
  }
  .pay-remark-input {
    width: calc(100% - 24px);
    background: #f7f8fa;
    .use-dark-mode-query({
    background: var(--dark-background-color);    //白色字体
  });
    border-radius: 6px;
    font-size: 14px;
    padding: 8px 12px;
  }

  // 每个商品的留言输入框样式
  .item-remark-row {
    margin: 8px 0 0 0;
    padding-top: 8px;
    border-top: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-top: 1px solid @dark-line-color;
  });
  }
  .item-remark-input {
    width: calc(100% - 24px);
    background: #f7f8fa;
    border-radius: 6px;
    font-size: 13px;
    padding: 6px 10px;
    border: none;
    color: #666;
    .use-dark-mode-query({
    color: var(--dark-font-color);    //白色字体
  });
  }
  .pay-bottom-bar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    .use-dark-mode-query({
    background: var(--dark-background-color);
  });
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.03);
    display: flex;
    flex-direction: column;
    padding: 12px;
    z-index: 10;
  }

  .pay-bottom-info {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 12px;
  }

  .pay-bottom-count {
    font-size: 12px;
    color: #666;
    .use-dark-mode-query({
    color: var(--dark-font-color);
  });
  }

  .pay-bottom-price {
    font-size: 12px;
    margin-left: 5px;
    color: #f53f3f;
  }

  .pay-bottom-btn {
    width: 100%;
    height: 44px;
    font-size: 16px;
    font-weight: 600;
  }

  .demo-cell-avatar-label {
    display: flex;
    align-items: center;
    .arco-avatar {
      width: 32px;
      height: 32px;
      margin-right: 8px;
    }
  }

  .custom-sender-panel {
    background: #f7f8fa;
    .use-dark-mode-query({
    background: var(--dark-background-color);    //白色字体
  });
    border-radius: 8px;
    margin: 8px 16px 0 16px;
    padding: 0 0 0 0;
    .custom-sender-row {
      display: flex;
      align-items: center;
      height: 44px;
      border-bottom: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-bottom: 1px solid @dark-line-color;
    });
      padding: 0 12px;
      &:last-child {
        border-bottom: none;
      }
      .custom-sender-label {
        color: #222;
        .use-dark-mode-query({
        color: var(--dark-font-color);    //白色字体
      });
        font-size: 15px;
        width: 70px;
        flex-shrink: 0;
      }
      .custom-sender-input {
        flex: 1;
        background: transparent;
        border: none;
        font-size: 15px;
        color: #999;
        .use-dark-mode-query({
        color: var(--dark-font-color);    //白色字体
      });
        margin-left: 8px;
        padding: 0;
      }
    }
  }

  .order-remark-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
  }

  .order-remark-content {
    flex: 1;
    margin-right: 12px;
  }

  .order-remark-text {
    font-size: 15px;
    font-weight: 500;
    color: #222;
    line-height: 22px;
    margin-bottom: 4px;
    display: block;
    .use-dark-mode-query({
    color: var(--dark-font-color);
  });

    &:last-child {
      margin-bottom: 0;
    }
  }

  .order-remark-input {
    width: 100%;
    font-size: 14px;
    color: #222;
    .use-dark-mode-query({
    color: var(--dark-font-color);
  });
    padding: 8px 0;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    &::placeholder {
      color: #999;
      .use-dark-mode-query({
      color: #666;
    });
    }
  }

  .order-remark-camera {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    &:active {
      opacity: 0.7;
    }
  }

  .camera-icon {
    width: 20px;
    height: 20px;
  }

  // 上传图片区域
  .uploaded-images {
    display: flex;
    flex-wrap: nowrap; // 不换行
    gap: 8px;
    margin-top: 8px;
    overflow-x: auto; // 横向滚动
    overflow-y: hidden; // 禁止垂直滚动
    padding-bottom: 4px; // 给滚动条留点空间
    height: 68px; // 固定高度，与图片高度(60px) + padding(4px) + gap(4px) 一致

    // 隐藏滚动条但保持滚动功能
    &::-webkit-scrollbar {
      display: none;
    }
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .uploaded-image-item {
    position: relative;
    width: 60px;
    height: 60px;
    flex-shrink: 0; // 防止图片被压缩
  }

  .uploaded-image {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    object-fit: cover;
  }

  .delete-image-btn {
    position: absolute;
    top: 0;
    right: 0;
    width: 18px;
    height: 18px;
    background: rgba(0, 0, 0, 0.2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0 6px 0 0;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    z-index: 10;

    &:active {
      opacity: 0.8;
    }
  }

  // 下单提示样式
  .order-reminder-section {
    display: flex;
    align-items: center;
    background: #F7F8FA;
    // .use-dark-mode-query({
    //   background: rgba(255, 247, 230, 0.1);
    // });
    border-radius: 4px;
    margin: 0 16px 16px 16px;
    padding: 10px 16px;
  }

  .order-reminder-text {
    font-size: 11px;
    color: #FF7D00;
    word-break: break-all;
  }
}
