import React, { useState, useEffect, useRef } from 'react';
import { View, Text } from '@tarojs/components';
import { Button, Image, Checkbox, Stepper, SwipeAction, Dialog  } from '@arco-design/mobile-react';
import { IconDelete, IconHeart } from "@arco-design/mobile-react/esm/icon";
import {  getCartList,deleteCartDetailsBatch, updateCartDetails, favoritesDynamic,favoritesDynamicBatch, deleteFavoritesDynamic} from "@/utils/api/common/common_user";
import { updateCartCountGlobal } from "@/utils/cartUtils";
import YkNavBar from "@/components/ykNavBar/index";
import { IconPayment } from "@/components/YkIcons";
import {
  IconRight
} from "@arco-iconbox/react-yk-arco";
import Taro from "@tarojs/taro";
import { toast } from "@/utils/yk-common";
import './index.less';
import { IconLoadEmpty } from "@/components/YkIcons";
// 定义类型
interface CartDetail {
  id: number;
  userId: number;
  shoppingCartId: number;
  productColorId: number | null;
  productSpecificationsId: number | null;
  productSpecificationsName: string | null;
  productColorName: string | null;
  quantity: number;
  price: number;
  checked: boolean;
}

interface CartItem {
  id: number;
  cartItemId: number;
  shoppingCartSellerId: number;
  dynamicsId: number;
  dynamicsContent: string;
  dynamicsImage: string;
  remark: string;
  type: number;
  price: number;
  checked: boolean;
  details: CartDetail[];
  isCollect: number;
}

interface SellerCart {
  shoppingCartList: CartItem[];
  shoppingCartSellerId: number;
  shoppingCartSellerUserId: number;
  dynamicsUserName: string;
  dynamicsUserAvatar: string;
  checked: boolean;
  hasOpenOnlinePayment: number; // 是否开通在线支付：1-已开通，0-未开通
}

const Cart = () => {
  const [data, setData] = useState<SellerCart[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [totalPrice, setTotalPrice] = useState(0);
  const [isManage, setIsManage] = useState(false);
  const [selectedItems, setSelectedItems] = useState<SellerCart[]>([]);
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  // 计算总件数和总价
  const calcTotal = (cartData: SellerCart[]) => {
    let count = 0;
    let price = 0;
    cartData.forEach(seller => {
      seller.shoppingCartList?.forEach(item => {
        item.details.forEach(detail => {
          if (detail.checked) {
            count += detail.quantity;
            price += item.price * detail.quantity; // 修复：价格需要乘以数量
          }
        });
      });
    });
    setTotalCount(count);
    setTotalPrice(price);
  };

  // 更新选中项
  const updateSelectedItems = (cartData: SellerCart[]) => {
    const selected = cartData.reduce((acc, seller) => {
      const selectedCartItems = seller.shoppingCartList?.reduce((itemAcc, item) => {
        const checkedDetails = item.details.filter(detail => detail.checked);
        if (checkedDetails.length > 0) {
          itemAcc.push({
            ...item,
            details: checkedDetails
          });
        }
        return itemAcc;
      }, [] as CartItem[]);

      if (selectedCartItems?.length > 0) {
        acc.push({
          ...seller,
          shoppingCartList: selectedCartItems
        });
      }
      return acc;
    }, [] as SellerCart[]);

    setSelectedItems(selected);
    console.log(selected,'selected');
  };

  // 全选状态
  const checkedAll = data.length > 0 && data.every(seller =>
    seller.shoppingCartList?.every(item =>
      item.details.every(detail => detail.checked)
    )
  );

  // 商家选中
  const handleCheckSeller = (sellerIdx: number) => {
    setData(prev => {
      const newData = prev.map((seller, i) => {
        if (i === sellerIdx) {
          const checked = !seller.checked;
          return {
            ...seller,
            checked,
            shoppingCartList: seller.shoppingCartList?.map(item => ({
              ...item,
              checked,
              details: item.details.map(d => ({ ...d, checked }))
            }))
          };
        }
        return seller;
      });
      calcTotal(newData);
      updateSelectedItems(newData);
      return newData;
    });
  };

  // 单个商品选中
  const handleCheckItem = (sellerIdx: number, itemIdx: number) => {
    setData(prev => {
      const newData = prev.map((seller, sIdx) => {
        if (sIdx === sellerIdx) {
          const updatedItems = seller.shoppingCartList?.map((item, iIdx) => {
            if (iIdx === itemIdx) {
              const checked = !item.checked;
              return {
                ...item,
                checked,
                details: item.details.map(d => ({ ...d, checked }))
              };
            }
            return item;
          });

          // 更新商家选中状态
          const sellerChecked = updatedItems.every(item => item.checked);

          return {
            ...seller,
            checked: sellerChecked,
            shoppingCartList: updatedItems
          };
        }
        return seller;
      });
      calcTotal(newData);
      updateSelectedItems(newData);
      return newData;
    });
  };

  // 明细选中
  const handleCheckDetail = (sellerIdx: number, itemIdx: number, detailIdx: number) => {
    setData(prev => {
      const newData = prev.map((seller, sIdx) => {
        if (sIdx === sellerIdx) {
          const updatedItems = seller.shoppingCartList?.map((item, iIdx) => {
            if (iIdx === itemIdx) {
              const details = item.details.map((d, dIdx) => {
                if (dIdx === detailIdx) {
                  return { ...d, checked: !d.checked };
                }
                return d;
              });
              // item 选中=所有明细都选中
              const checked = details.every(d => d.checked);
              return { ...item, details, checked };
            }
            return item;
          });

          // 更新商家选中状态
          const sellerChecked = updatedItems.every(item => item.checked);

          return {
            ...seller,
            checked: sellerChecked,
            shoppingCartList: updatedItems
          };
        }
        return seller;
      });
      calcTotal(newData);
      updateSelectedItems(newData);
      return newData;
    });
  };

  // 全选
  const handleCheckAll = () => {
    setData(prev => {
      const checked = !checkedAll;
      const newData = prev.map(seller => ({
        ...seller,
        checked,
        shoppingCartList: seller.shoppingCartList?.map(item => ({
          ...item,
          checked,
          details: item.details.map(d => ({ ...d, checked }))
        }))
      }));
      calcTotal(newData);
      updateSelectedItems(newData);
      return newData;
    });
  };

  // 数量操作
  const handleCount = async (sellerIdx: number, itemIdx: number, detailIdx: number, newQuantity: number | string) => {
    const detail = data[sellerIdx].shoppingCartList[itemIdx].details[detailIdx];
    const item = data[sellerIdx].shoppingCartList[itemIdx];

    // 转换为数字并确保数量在1-9999范围内
    const quantity = typeof newQuantity === 'string' ? parseInt(newQuantity, 10) : newQuantity;
    const finalQuantity = Math.min(Math.max(1, Number.isNaN(quantity) ? detail.quantity : quantity), 9999);

    try {
      // 调用更新接口
      const updateData = {
        cartDetailId: detail.id,
        quantity: finalQuantity,
      };

      const res = await updateCartDetails(updateData);

      if (res && res.code === 0) {
        // 更新成功，更新本地状态
        setData(prev => {
          const newData = JSON.parse(JSON.stringify(prev));
          const targetDetail = newData[sellerIdx].shoppingCartList[itemIdx].details[detailIdx];
          targetDetail.quantity = finalQuantity;
          calcTotal(newData);
          updateSelectedItems(newData);
          return newData;
        });
      } else {
        toast("error", {
        content: res.msg || '更新失败',
        duration: 2000
      });
      }
    } catch (error) {
      console.error('更新购物车数量失败:', error);
      toast("error", {
        content: "更新失败",
        duration: 2000
      });
    }
  };

  // 删除购物车商品（SKU级别）
  const handleDeleteCartItem = async (sellerIdx: number, itemIdx: number, detailIdx: number) => {
    const detail = data[sellerIdx].shoppingCartList[itemIdx].details[detailIdx];
    try {
      const res = await deleteCartDetailsBatch({ cartDetailIds: [detail.id] });
      if (res && res.code === 0) {
        // 删除成功，重新获取购物车数据
        getCartListData();
        toast("success", {
        content: "删除成功",
        duration: 2000
      });
      } else {
        toast("error", {
        content: res.msg || '删除失败',
        duration: 2000
      });
      }
    } catch (error) {
      console.error('删除购物车商品失败:', error);
      toast("error", {
        content: "删除失败",
        duration: 2000
      });
    }
  };

  // 批量收藏
  const handleFavoritesDynamicBatch = async () => {
    console.log(selectedItems,'selectedItems');
    if(selectedItems.length === 0){
      toast("error", {
        content: "请选择要收藏的商品",
        duration: 2000
      });
      return;
    }
    //动态id拼接 转成数组
    const dynamicsIds = selectedItems.flatMap(item => item.shoppingCartList.map(itemDetail => itemDetail.dynamicsId));
    const res = await favoritesDynamicBatch({ dynamicsIds: dynamicsIds, userId: Taro.getStorageSync("userInfo").id });
    if(res && res.code === 0){
      Dialog.alert({
        title: "收藏成功",
        children: "可前往\"我的-我的收藏\"中查看和管理",
        okText: "知道了",
        platform: "ios"
      });
      getCartListData();
    }
  };

  // 批量删除
  const handleRemoveSkuBatch = async () => {
    if(selectedItems.length === 0){
      toast("error", {
        content: "请选择要移除的商品",
        duration: 2000
      });
      return;
    }
    //sku id拼接
    const detailIds = selectedItems.flatMap(item =>
      item.shoppingCartList.flatMap(itemDetail =>
        itemDetail.details.map(detail => detail.id)
      )
    );

    const res = await deleteCartDetailsBatch({ cartDetailIds: detailIds });
    if(res && res.code === 0){
      toast("success", {
        content: "移除成功",
        duration: 2000
      });
    }
    getCartListData();
  };

  // 删除整个商品（商品级别）
  const handleDeleteWholeItem = async (sellerIdx: number, itemIdx: number) => {
    const item = data[sellerIdx].shoppingCartList[itemIdx];
    try {
      const res = await deleteCartDetailsBatch({ cartItemIds: [item.id] });
      if (res && res.code === 0) {
        // 删除成功，重新获取购物车数据
        getCartListData();
        toast("success", {
        content: "删除成功",
        duration: 2000
      });
      } else {
        toast("error", {
        content: res.msg || '删除失败',
        duration: 2000
      });
      }
    } catch (error) {
      console.error('删除购物车商品失败:', error);
      toast("error", {
        content: "删除失败",
        duration: 2000
      });
    }
  };

  const addCheckedField = (list: any[]): SellerCart[] => {
    return list.map(seller => {
        return {
          shoppingCartSellerId: seller.id,
          shoppingCartSellerUserId: seller.sellerUserId,
          dynamicsUserName: seller.sellerNickname,
          dynamicsUserAvatar: seller.sellerAvatar,
          hasOpenOnlinePayment: seller.hasOpenOnlinePayment,
          checked: false,
          shoppingCartList: seller.cartItems?.map((item: any) => ({
            id: item.id,
            shoppingCartSellerId: item.cartSellerId,
            dynamicsId: item.dynamicId,
            dynamicsContent: item.content,
            dynamicsImage: item.pictures,
            remark: item.remark,
            type: item.type || 0,
            price: item.price,
            isCollect: item.isCollect,
            checked: false,
            details: item.cartDetails?.map((detail: any) => ({
              id: detail.id,
              userId: seller.userId, // 从商家信息中获取 userId
              shoppingCartId: detail.cartItemId,
              productColorId: detail.colorId || null,
              productSpecificationsId: detail.specificationId || null,
              productSpecificationsName: detail.specificationName || null,
              productColorName: detail.colorName || null,
              quantity: detail.quantity,
              price: detail.priceSnapshot,
              checked: false,
            })) || [],
          })) || [],
        };
    });
  };

  const getCartListData = async () => {
    const params = {
      pageNo: 1,
      pageSize: 10,
      userId: Taro.getStorageSync("userInfo").id,
    };
    const res = await getCartList(params);
    if(res && res.code == 0){
      const withChecked = addCheckedField(res.data.list);
      setData(withChecked);
      // 获取购物车列表后，同时更新购物车数量
      await updateCartCountGlobal();
    }
  };

  useEffect(() => {
    Taro.eventCenter.on('refreshCartList', getCartListData);
    getCartListData();
    return () => {
      Taro.eventCenter.off('refreshCartList', getCartListData);
    };
  }, []);

  useEffect(() => {
    calcTotal(data);
  }, [data]);

  return (
    <View className="cart-page">
      {/* 头部 */}
      {platform !== "WX" &&<YkNavBar  title="购物车" />}
      {/* 列表 */}
      {data.length > 0 && (
      <View className="cart-list">
        {data.map((seller, sellerIdx) => (
          <View className="cart-shop" key={seller.shoppingCartSellerId}>
            <View className="cart-shop-header">
              <Checkbox checked={seller.checked} onChange={() => handleCheckSeller(sellerIdx)} className="cart-checkbox" value="" />
              <View
                className="cart-shop-info"
                onClick={() => {
                  // Taro.navigateTo({
                  //   url: `/pages/my/index?userId=${seller.shoppingCartSellerUserId}`
                  // });
                }}
              >
                <View className="cart-shop-avatar-wrapper">
                  <Image
                    className="cart-shop-avatar"
                    src={seller.dynamicsUserAvatar}
                  />
                </View>
                <View className="cart-shop-name-group" onClick={() => {
                  Taro.navigateTo({
                    url: `/pageUserInfo/userDetail/index?userId=${seller.shoppingCartSellerUserId}`,
                  });
                }}>
                  <Text className="cart-shop-name">{seller.dynamicsUserName}</Text>
                  {seller.hasOpenOnlinePayment === 1 && (
                    // <Image className="cart-shop-wx-icon" src={require('@/assets/images/common/wx_pay.png')} />
                    <IconPayment className="cart-shop-wx-icon" />
                  )}
                  <IconRight className="cart-shop-arrow" />
                </View>
              </View>
            </View>
            <View className="cart-shop-products">
              {seller.shoppingCartList?.map((item, itemIdx) => (
                <View className="cart-product-card" key={item.id}>
                  <View >
                    <SwipeAction
                      style={{ marginRight: '-1px' }}
                      rightActions={[
                        {
                          icon: <IconHeart />,
                          text: item.isCollect ? '取消收藏' : '收藏',
                          style: {
                            backgroundColor: item.isCollect ? '#999' : '#FF9500',
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center'
                          },
                          onClick: async () => {
                            try {
                              const requestData = {
                                dynamicId: item.dynamicsId.toString(),
                                userId: Taro.getStorageSync("userInfo").id.toString()
                              };

                              let res: any;
                              if (item.isCollect) {
                                // 取消收藏
                                res = await deleteFavoritesDynamic(requestData);
                              } else {
                                // 添加收藏
                                res = await favoritesDynamic(requestData);
                              }

                              if (res && res.code === 0) {
                                // 更新本地收藏状态
                                setData(prev => {
                                  const newData = [...prev];
                                  newData[sellerIdx].shoppingCartList[itemIdx].isCollect = item.isCollect ? 0 : 1;
                                  return newData;
                                });

                                toast("success", {
        content: item.isCollect ? '收藏成功' : '取消成功',
        duration: 2000
      });

                                // 通知首页刷新列表
                                Taro.eventCenter.trigger('refreshAlbumList');
                              } else {
                                toast("error", {
        content: res.msg || (item.isCollect ? '收藏失败' : '取消收藏失败'),
        duration: 2000
      });
                              }
                            } catch (error) {
                              console.error('收藏操作失败:', error);
                              toast("error", {
        content: item.isCollect ? '收藏失败' : '取消收藏失败',
        duration: 2000
      });
                            }
                          }
                        },
                        {
                          icon: <IconDelete />,
                          text: '移除',
                          style: { backgroundColor: '#F53F3F', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' },
                          onClick: () => handleDeleteWholeItem(sellerIdx, itemIdx)
                        }
                      ]}
                    >
                      <View className="cart-product-main">
                        <Checkbox checked={item.checked} onChange={() => handleCheckItem(sellerIdx, itemIdx)} className="cart-checkbox" value="" />
                        <View className="cart-product-img-wrapper">
                          <Image className="cart-product-img" src={item.dynamicsImage?.split(',')[0]} />
                        </View>
                        <View className="cart-product-info">
                          <Text className="cart-product-title">{item.dynamicsContent}</Text>
                        </View>
                      </View>
                    </SwipeAction>

                    {item.details.map((detail, detailIdx) => (
                      <SwipeAction
                        key={detail.id}
                        style={{ marginRight: '-1px' }}
                        rightActions={[
                          {
                            icon: <IconDelete />,
                            text: '移除',
                            style: { backgroundColor: '#F53F3F', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' },
                            onClick: () => handleDeleteCartItem(sellerIdx, itemIdx, detailIdx)
                          }
                        ]}
                      >
                        <View style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <View style={{ display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }}>
                            <Checkbox checked={detail.checked} onChange={() => handleCheckDetail(sellerIdx, itemIdx, detailIdx)} className="cart-checkbox" value="" />
                            <View className="cart-product-meta">
                              <Text className="cart-product-size">
                                {detail.productColorName && detail.productSpecificationsName
                                  ? `${detail.productColorName} | ${detail.productSpecificationsName}`
                                  : detail.productColorName || detail.productSpecificationsName || '默认'}
                              </Text>
                              <Text className="cart-product-price">￥{(item.price/100 * detail.quantity).toFixed(2)}</Text>
                            </View>
                          </View>
                          <View className="cart-product-count">
                            <Stepper value={detail.quantity} defaultValue={detail.quantity} min={1} max={9999} step={1} onChange={val => handleCount(sellerIdx, itemIdx, detailIdx, val || detail.quantity)} />
                          </View>
                        </View>
                      </SwipeAction>
                    ))}

                    <View className="cart-product-remark-row">
                      <Text className="cart-product-remark">备注：{item.remark || '-'}</Text>
                    </View>

                    {/* <View className="cart-product-extra-row">
                      <View className="cart-product-send">
                        <Image
                          className="cart-product-send-icon"
                          src={require('@/assets/images/common/car_share.png')}
                        />
                        <Text className="cart-product-send-text">转发</Text>
                      </View>
                    </View> */}
                  </View>
                </View>
              ))}
            </View>
          </View>
        ))}
      </View>
      )}

      {data.length === 0 && (
        <View className="cart-empty">
          <IconLoadEmpty className="cart-empty-icon" />
          <Text className="cart-empty-text">暂无商品</Text>
        </View>
      )}

      {/* 底部栏 */}
      <View className="cart-footer">
        <View className="cart-footer-left">
          <Checkbox checked={checkedAll} onChange={handleCheckAll} className="cart-checkbox" value="" />
          <Text className="cart-footer-all" onClick={handleCheckAll}>全选</Text>
          {!isManage && (
          <Text className="cart-footer-manage" onClick={() => setIsManage(true)}>管理</Text>
          )}
        </View>


        {!isManage && (
          <>
        <View className="cart-footer-info">
          <Text className="cart-footer-total">
            共 {totalCount} 件 <Text className="cart-footer-total-price">￥{(totalPrice/100).toFixed(2)}</Text>
          </Text>
          <Text className="cart-footer-other">
            共优惠 <Text className="cart-footer-other-price">￥0</Text>
          </Text>
        </View>

        <View className="cart-footer-btn-wrap">
          <Button
            className={`cart-footer-buy ${totalCount > 0 ? 'cart-footer-buy-active' : ''}`}
            type="ghost"
            inline
            size="small"
            disabled={totalCount === 0}
            onClick={() => {
              // 检查是否为多商家
              const sellerIds = selectedItems.map(item => item.shoppingCartSellerId);
              const uniqueSellerIds = [...new Set(sellerIds)];

              if (uniqueSellerIds.length > 1) {
                toast("info", {
                  content: "不能同时购买多个商家的商品，请分别下单",
                  duration: 3000
                });
                return;
              }

              Taro.navigateTo({
                url: '/pageOrder/order/pay/index'
              });
              Taro.setStorageSync('selectedItems', selectedItems || []);
            }}
          >立即购买</Button>
        </View>
        </>
        )}

{isManage && (
  <>
  <View className="cart-footer-manage-c">
  <Button
  onClick={() => handleFavoritesDynamicBatch()}
            className={`cart-footer-manage-collect ${totalCount > 0 ? 'cart-footer-manage-collect-active' : ''}`}
            >收藏</Button>
            <Button
            onClick={() => handleRemoveSkuBatch()}
  className={`cart-footer-manage-remove ${totalCount > 0 ? 'cart-footer-manage-remove-active' : ''}`}
            >移除</Button>
  <Button
          onClick={() => setIsManage(false)}  
          >完成</Button>
  </View>
  </>
)}



      </View>
    </View>
  );
};

export default Cart;
