// 引入样式：utils/css/variable.less
@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageSetting/loginedResetPassword/index"] {
  .loginedResetPasswordPageContent {
    width: 100%;
    position: relative;

    .cell-label {
      min-width: 80px;
    }

    .arco-input-prefix {
      min-width: 104px;
      justify-content: flex-start;
    }
  }

  .pageDescribe {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 25px 30px;
    box-sizing: border-box;
    background-color: transparent;

    &-title {
      font-size: 18px;
      font-weight: bold;
      .use-var(color, font-color);
      .use-dark-mode-query({
      color: @dark-font-color;
    });
    }

    &-desc {
      font-size: 15px;
      .use-var(color, sub-font-color);
      .use-dark-mode-query({
      color: @dark-sub-font-color;
    });
    }
  }

  .inputBox {
    width: 100%;
    box-sizing: border-box;
    padding: 50px 14px;
  }

  .confirmBtnBox {
    width: 100%;
    box-sizing: border-box;
    padding: 0 30px;
  }
}
