@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pageDynamic/report/reportDetail/index"] {
  .report-detail-page {
    min-height: 100vh;
    .use-var(background-color, background-color);
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
    display: flex;
    flex-direction: column;

    .report-content {
      flex: 1;
      .rem(padding, 16);

      .report-section {
        // .rem(margin-bottom, 24);

        .section-title {
          .rem(font-size, 16);
          .rem(margin-bottom, 12);
          font-weight: 500;
          .use-var(color, font-color);
          .use-dark-mode-query({
          color: @dark-font-color;
        });
        }

        // 投诉类型特殊样式
        &:first-child .section-title {
          .rem(font-size, 14);
          // .rem(padding, 12);
          // .use-var(background-color, card-background-color);
          // .use-dark-mode-query({
          //   background-color: @dark-card-background-color;
          // });
          .rem(border-radius, 8);
          // border: 1px solid;
          // .use-var(border-color, line-color);
          // .use-dark-mode-query({
          //   border-color: @dark-line-color;
          // });
          margin-bottom: 0;
        }

        .taro-textarea {
          background-color: transparent !important;
        }

        .report-textarea {
          width: 100%;
          .rem(min-height, 120);
          .rem(margin-top, 12);
          .rem(margin-bottom, 12);
          .rem(font-size, 14);
          // border: 1px solid;
          // .use-var(border-color, line-color);
          // .use-dark-mode-query({
          //   border-color: @dark-line-color;
          // });
          .rem(border-radius, 8);
          // .use-var(background-color, card-background-color);
          // .use-dark-mode-query({
          //   background-color: @dark-card-background-color;
          // });
          .use-var(color, font-color);
          .use-dark-mode-query({
          color: @dark-font-color;
        });
          resize: none;

          &:focus {
            .use-var(border-color, primary-color);
            .use-dark-mode-query({
            border-color: @dark-primary-color;
          });
            outline: none;
          }
        }

        .image-upload-container {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          .rem(gap, 12);
          .rem(margin-top, 12);

          .image-item {
            position: relative;
            width: 100%;
            aspect-ratio: 1;

            .uploaded-image {
              width: 100%;
              height: 100%;
              .rem(border-radius, 8);
              object-fit: cover;
            }

            .remove-btn {
              position: absolute;
              .rem(top, -6);
              .rem(right, -6);
              .rem(width, 20);
              .rem(height, 20);
              background-color: #ff4d4f;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;

              .remove-icon {
                .rem(font-size, 12);
                color: white;
              }
            }
          }

          .add-image-btn {
            width: 100%;
            aspect-ratio: 1;
            // border: 2px dashed;
            // .use-var(border-color, line-color);
            // .use-dark-mode-query({
            //   border-color: @dark-line-color;
            // });
            .rem(border-radius, 8);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            .use-var(background-color, card-background-color);
            .use-dark-mode-query({
            background-color: @dark-card-background-color;
          });

            &:active {
              opacity: 0.7;
            }

            .add-image-text {
              .rem(font-size, 24);
              color: #1d2129;
              .use-dark-mode-query({
              color: @dark-font-color;
            });
            }
          }
        }
      }
    }

    .submit-container {
      .rem(padding, 16);
      .use-var(background-color, card-background-color);
      .use-dark-mode-query({
      background-color: @dark-card-background-color;
    });
      border-top: 1px solid;
      .use-var(border-top-color, line-color);
      .use-dark-mode-query({
      border-top-color: @dark-line-color;
    });

      .submit-btn {
        width: 100%;
        .rem(height, 44);
        .rem(font-size, 16);
        font-weight: 500;
      }
    }
  }
}
