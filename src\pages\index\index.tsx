import { View, Text } from "@tarojs/components";
import { useLoad, useReachBottom, usePageScroll, useDidShow } from "@tarojs/taro";
import "./index.less";
import wx from "weixin-webview-jssdk";
import {
  Toast,
  SearchBar,
  Sticky,
  LoadMore,
  Image,
  Dialog,
  PullRefresh,
  Tabs,
} from "@arco-design/mobile-react";
import { IconLoadEmpty } from "@/components/YkIcons";
import Taro from "@tarojs/taro";
import { IconCart } from "@/components/YkIcons";
import { IconTriDown } from "@arco-design/mobile-react/esm/icon";
import BottomPopup from "@/components/BottomPopup";
import { formatDate, getCSSVariableValue } from "@/utils/utils";
import {
  IconDownload,
  IconMore,
} from "@arco-iconbox/react-yk-arco";
import { toast } from "@/utils/yk-common";
import CartModal from "@/components/CartModal";

import { useSetPermission } from "@/stores/permissionStore";
import {
  AuthTypes,
  AuthStatusAndroid,
  AuthStatusIos,
} from "@/utils/config/authTypes";
import PermissionPopup from "@/components/PermissionPopup";
import { usePermission } from "@/hooks/usePermission";
import { useGlobalCallbacks } from "@/utils/globalCallbackManager";
// 组件
import YkNavBar from "@/components/ykNavBar/index";
import YkSwitchTabBar from "@/components/ykSwitchTabBar/index";
import {
  getAlbumList,
  deleteDynamic,
  dynamicRefresh,
  updateDynamic,
  favoritesDynamic,
  deleteFavoritesDynamic,
  addCart,
  unfollowUser,
  getUserInfo,
} from "@/utils/api/common/common_user";
import { fetchCartCountUtil } from "@/utils/cartUtils";
import { LoadMoreStatus } from "@arco-design/mobile-react/cjs/load-more";
import React from "react";
import { useState, useRef, useEffect, useCallback } from "react";
import { useDebounce } from "@/utils/yk-common";
import { IconPayment } from "@/components/YkIcons";
import { checkPermissionCallBack } from "@/utils/permission";

export default function Home() {
  var userInfo = Taro.getStorageSync("userInfo");
  const clickItem = useRef({});
  const operationTypeRef = useRef(""); // 操作类型标识：'download' 或 'imageSearch'
  const chooseImageRef = useRef(""); // 选择图片类型标识：'album' 或 'camera'
  const currentTypeRef = useRef(1);
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
      platformRef.current = "Android";
    } else if (isIos) {
      setPlatform("IOS");
      platformRef.current = "IOS";
    } else if (isHM) {
      setPlatform("HM");
      platformRef.current = "HM";
    } else {
      setPlatform("H5");
      platformRef.current = "H5";
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
      platformRef.current = "WX";
    }
  }, []);

  useDidShow(() => {
    if (platformRef.current == "IOS") {
      window.webkit.messageHandlers.configStatusBarStyle.postMessage("black");
    } else if (platformRef.current == "Android") {
      window.StatusBarDarkMode.StatusBarDarkMode();
    } else if (platformRef.current == "HM") {
      window.harmony.StatusBarDarkMode();
    }
  });
  
  // 处理URL参数获取用户信息
  const handleUrlParams = async () => {
    try {
      // 获取URL参数
      const params = Taro.getCurrentInstance().router?.params || {};
      console.log("params", params);
      const { userId, accessToken } = params;
      
      if (userId && accessToken) {
        console.log('检测到URL参数:', { userId, accessToken });
        
        // 创建临时用户信息对象，包含token信息
        const tempUserInfo = {
          id: userId,
          userId: userId,
          accessToken: accessToken,
          token: accessToken, // 兼容不同的token字段名
        };
        
        // 先保存临时用户信息，确保请求时有token
        Taro.setStorageSync('userInfo', tempUserInfo);
        
        // 请求完整的用户信息
        const userInfoRes = await getUserInfo();
        if (userInfoRes && userInfoRes.code === 0) {
          // 合并token信息到完整用户信息中
          const completeUserInfo = {
            ...userInfoRes.data,
            accessToken: accessToken,
            token: accessToken,
          };
          
          // 保存完整用户信息到本地
          Taro.setStorageSync('userInfo', completeUserInfo);
          console.log('用户信息获取成功:', completeUserInfo);
          userInfo = completeUserInfo
          
          // 刷新页面数据
          handleRefresh();
        } else {
          console.error('获取用户信息失败:', userInfoRes.msg);
          toast("error", {
            content: userInfoRes.msg || "获取用户信息失败",
            duration: 2000,
          });
        }
      }
    } catch (error) {
      console.error('处理URL参数失败:', error);
      toast("error", {
        content: "登录失败，请重试",
        duration: 2000,
      });
    }
  };

    // 选择图片的方法
    const chooseImage = () => {
      if (platformRef.current === "Android") {
        window.setPhotoNum?.setPhotoNum(1);
      }
      if(platformRef.current === "HM"){
        window.harmony.setPhotoNum(1);
      }
      console.log(chooseImageRef.current);
      Taro.chooseImage({
        count: 1,
        sourceType: [chooseImageRef.current], // 'album' 为从相册选择，'camera' 为拍照
        success: async (res) => {
          const imagePath = res.tempFilePaths[0];
          //转为base64
          const base64Data = await blobToBase64(imagePath);
          setSearchVal("");
          searchValRef.current = ""; // 同时清除文字搜索的ref
          searchImgRef.current = base64Data;
          albumPageRef.current = 1;
          setIsLoadEnd(false);
          getAlbumListData(); // 统一调用，内部会自动判断是否有搜索内容
        },
        fail: (err) => {
          console.error("选择图片失败:", err);
        },
      });
    };
    
  // 自定义权限同意处理，处理首页特有的逻辑
  const customWebPermissonConsent = () => {
    console.log("customWebPermissonConsent");
    console.log("operationType:", operationTypeRef.current);
    console.log("chooseImageType:", chooseImageRef.current);

    // 根据操作类型执行不同的后续处理
    if (operationTypeRef.current === "download") {
      // 下载操作
      if (platformRef.current === "Android") {
        downloadConfirm(clickItem.current);
      } else if (platformRef.current === "IOS") {
        window.webkit.messageHandlers.checkPermission.postMessage("");
      } 
    } else if (operationTypeRef.current === "imageSearch") {
      // 以图搜图操作
      console.log("执行图片选择，类型:", chooseImageRef.current);
      chooseImage();
    }

    // 清除操作类型标识
    operationTypeRef.current = "";

    return true;
  };

  // 使用全局权限管理
  const {
    initPermissions,
    hasPermission,
    requestPermission,
    webPermissonConsent,
    webPermissonDeny,
    permissionPopupProps,
    platformRef,
    authConfirmType,
  } = usePermission(customWebPermissonConsent);
  const imageRefs = useRef({});
  const [carListNum, setCarListNum] = useState(0); // 购物车数量
  const [isPopupVisible, setPopupVisible] = useState(false); // 控制选择图片方式弹窗的显示状态
  const [expandedId, setExpandedId] = useState<string | number | null>(null);
  // 简化状态管理，参考微信群代码
  const [loading, setLoading] = useState(true);
  const [canPullRefresh, setCanPullRefresh] = useState(true);
  // 防重入锁
  const isFetchingRef = useRef(false);

  // 监听页面滚动，控制下拉刷新
  usePageScroll(({ scrollTop }) => {
    // 当滚动位置为0时才允许下拉刷新
    setCanPullRefresh(scrollTop === 0);
  });

  useEffect(() => {
    // 处理URL参数
    handleUrlParams();
    
    // 初始化权限管理
    const cleanup = initPermissions();
    if(platformRef.current === "Android"){
    window.checkPermission.checkPermission();
    }
    // 使用全局回调管理器注册下载回调
    const callbackCleanup = useGlobalCallbacks('index', {
      webDownloadSuc: webDownloadSuc,
      webDownloadFail: webDownloadFail,
    });

    // 监听删除事件，刷新列表
    const handleRefreshList = async () => {
      // 重置分页状态并刷新列表
      albumPageRef.current = 1;
      setIsLoadEnd(false);
      setLoadStatus("prepare");

      // 统一调用getAlbumListData，内部会自动判断是否有搜索内容
      await getAlbumListData();
    };
    Taro.eventCenter.on("refreshAlbumList", handleRefreshList);

    return () => {
      cleanup && cleanup();
      callbackCleanup && callbackCleanup();
      // 移除事件监听器
      Taro.eventCenter.off("refreshAlbumList", handleRefreshList);
    };
  }, []);

  // 权限相关函数已移至全局权限管理，自定义逻辑在 customWebPermissonConsent 中处理

  const webDownloadSuc = () => {
    if(clickItem.current.pictures&&clickItem.current.content){
      toast("success", {
        content: "图片下载成功，动态文案已复制",
        duration: 2000,
      });
    }

    if(clickItem.current.pictures&&clickItem.current.content===""){
      toast("success", {
        content: "图片下载成功",
        duration: 2000,
      });
    }
  };

  const webDownloadFail = () => {
    toast("error", {
      content: "下载失败",
      duration: 2000,
    });
  };

  // 权限相关函数已移至全局权限管理

  const [albumList, setAlbumList] = React.useState<any>([]);
  const albumPageRef = useRef(1);
  const [albumLimit, setAlbumLimit] = React.useState(10);
  //是否已经加载完 ，且没有更多数据
  const [isLoadEnd, setIsLoadEnd] = React.useState(false);
  // 加载状态
  const [loadStatus, setLoadStatus] = React.useState<LoadMoreStatus>("prepare"); //"before-ready" | "prepare" | "loading" | "nomore" | "retry"
  // 是否显示省略号,把id添加进来
  const [ellipsis, setEllipsis] = React.useState<any>([]);
  const [searchVal, setSearchVal] = React.useState("");
  const searchValRef = useRef(""); // 实时保存搜索内容
  const searchImgRef = useRef("");
  const [activeTabIndex, setActiveTabIndex] = React.useState(0); // 当前激活的Tab索引

  // 购物车弹框相关状态
  const [actionSheetVisible, setActionSheetVisible] = useState(false);
  const [currentCartItem, setCurrentCartItem] = useState<any>(null);

  // 更多操作弹框相关状态
  const [moreActionVisible, setMoreActionVisible] = useState(false);
  const [currentMoreItem, setCurrentMoreItem] = useState<any>(null);

  // 展开or收起
  const handleEllipsis = (id: string) => {
    console.log(ellipsis, id);
    if (ellipsis.includes(id)) {
      setEllipsis(ellipsis.filter((item) => item !== id));
    } else {
      setEllipsis([...ellipsis, id]);
    }
  };

  // 处理更多操作点击
  const handleMoreAction = (item: any) => {
    setCurrentMoreItem(item);
    setMoreActionVisible(true);
  };

  // 处理更多操作弹框关闭
  const handleMoreActionClose = () => {
    setMoreActionVisible(false);
    setCurrentMoreItem(null);
  };

  // 处理更多操作确认
  const handleMoreActionConfirm = (index: number) => {
    if (!currentMoreItem) return;

    if (index === 0) {
      // 拉黑商家
      handleBlacklistUser(currentMoreItem);
    } else if (index === 1) {
      // 举报
      handleReportUser(currentMoreItem);
    }

    handleMoreActionClose();
  };

  // 拉黑商家
  const handleBlacklistUser = (item: any) => {
    console.log("handleBlacklistUser", item);
    Dialog.confirm({
      title: "拉黑商家",
      children: (
        <View>
          <Text>是否拉黑 </Text>
          <Text style={{ color: getCSSVariableValue('--primary-color') }}>{item.nickname}</Text>
        </View>
      ),
      okText: "确定",
      cancelText: "取消",
      onOk: () => {
        unfollowUser(item.userId).then((res: any) => {
          if (res && res.code == 0) {
            toast("success", {
              content: "拉黑成功",
              duration: 2000,
            });
            // 通知首页刷新数据
            // Taro.eventCenter.trigger('refreshAlbumList');
            handleRefresh();
          }
        });
      },
      platform: "ios",
    });
  };

  // 举报动态
  const handleReportUser = (item: any) => {
    console.log("handleReportUser", item);
    if (platformRef.current === "WX") {
      wx.miniProgram.navigateTo({ url: "/pages/web?path=" + encodeURIComponent(`/pageDynamic/report/reportType/index?type=dynamic&id=${item.id}`) });
    } else {
      Taro.navigateTo({
        url: `/pageDynamic/report/reportType/index?type=dynamic&id=${item.id}`,
      });
    }
    // Dialog.confirm({
    //   title: "举报",
    //   children: "确定要举报该内容吗？我们会尽快处理您的举报。",
    //   okText: "确定",
    //   cancelText: "取消",
    //   onOk: () => {
    //     // TODO: 调用举报接口
    //     toast("success", {
    //       content: "举报成功，感谢您的反馈",
    //       duration: 2000,
    //     });
    //   },
    //   platform: "ios",
    // });
  };

  // 获取相册列表 - 统一处理普通列表和搜索列表
  const getAlbumListData = async (e?: number) => {
    // 防止并发请求
    if (isFetchingRef.current) {
      return;
    }
    if (userInfo.id == undefined) {
      return;
    }

    try {
      // 首页加载显示loading，分页加载显示LoadMore的loading
      if (albumPageRef.current === 1 && !loading) {
        setLoading(true);
      } else if (albumPageRef.current > 1) {
        setLoadStatus("loading");
      }

      isFetchingRef.current = true;

      const data = {
        pageNo: albumPageRef.current,
        pageSize: albumLimit,
        userId: userInfo.id,
      } as any;

      // 判断搜索框有值就传content
      const keyword =
        searchImgRef.current?.length > 0
          ? searchImgRef.current
          : searchValRef.current;
      if (keyword && keyword.length > 0) {
        data.content = keyword;
      }

      data.homePageCountType = currentTypeRef.current;

      data.sort = currentTypeRef.current === 2 ? 1 : 2;

      // 根据当前Tab传递不同参数
      // if (activeTabIndex === 1) {
      //   data.type = 1;
      //   // 上新Tab - 您可以在这里添加上新相关的参数
      //   // 例如：data.isNew = true; 或者其他您需要的参数
      //   console.log("当前是上新Tab，可以在这里添加上新相关的参数");
      // }

      let res: any;
      res = await getAlbumList(data);

      console.log("API response:", res);

      if (res && res.code == 0) {
        const newData = res.data.list || [];
        console.log("Received data:", newData.length, "items");

        // 首页刷新（page=1）替换；分页（page>1）追加
        if (albumPageRef.current === 1) {
          setAlbumList(newData);
          console.log("Replaced list with", newData.length, "items");
        } else {
          setAlbumList((prev) => {
            const newList = [...prev, ...newData];
            console.log(
              "Appended",
              newData.length,
              "items, total:",
              newList.length
            );
            return newList;
          });
        }

        // 判断是否还有更多数据 - 完全按照微信群代码逻辑
        if (newData.length < albumLimit) {
          setIsLoadEnd(true);
          setLoadStatus("nomore");
          console.log("No more data available");
        } else {
          // 直接递增页面，和微信群代码一致
          albumPageRef.current += 1;
          setLoadStatus("prepare");
          setIsLoadEnd(false);
          console.log("More data available, next page:", albumPageRef.current);
        }
      } else {
        setLoadStatus("retry");
        console.error("API error:", res.msg);
        toast("error", {
          content: res.msg,
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("Request failed:", error);
      setLoadStatus("retry");
      toast("error", {
        content: "网络异常，请重试",
        duration: 2000,
      });
    } finally {
      setLoading(false);
      isFetchingRef.current = false;
    }
  };

  const tabData = [
    { title: "全部", type: 1, index: 0 },
    { title: "上新", type: 2, index: 1 },
  ];
  // const tabData = [{ title: "全部" }];

  const onTabsChange = (tab) => {
    console.log("Tab切换:", tab);
    // 找到对应的索引
    const tabIndex = tabData.findIndex(t => t.type === tab.type);
    setActiveTabIndex(tabIndex);
    currentTypeRef.current = tab.type;
    // 重置分页状态
    albumPageRef.current = 1;
    setIsLoadEnd(false);
    setLoadStatus("prepare");

    // 重新获取数据
    getAlbumListData(tab);
  };

  // 删除
  const handleDelete = (id: string) => {
    Dialog.confirm({
      title: "温馨提示",
      children: "删除图文后不可恢复，确定删除？",
      okText: "删除",
      cancelText: "取消",
      onOk: () => {
        delConfirm(id);
      },

      platform: "ios",
    });
  };

  const handleDown = (item: any) => {
    const isListed = item.isListed === 2; // 2表示已下架
    const actionText = isListed ? "上架" : "下架";
    const confirmText = isListed ? "确定要上架商品吗？" : "确定要下架商品吗？";

    Dialog.confirm({
      title: "温馨提示",
      children: confirmText,
      okText: "确定",
      cancelText: "取消",
      onOk: () => {
        downConfirm(item.id, isListed ? 1 : 2); // 1上架，2下架
      },

      platform: "ios",
    });
  };

  const downConfirm = (id: string, isListed: number) => {
    const item = albumList.find(item => item.id === id)
    let data = {
      ...item,
      isListed: isListed,
      id: id,
    };
    updateDynamic(data).then((res: any) => {
      if (res && res.code == 0) {
        const actionText = isListed === 1 ? "上架" : "下架";
        toast("success", {
          content: `${actionText}成功`,
          duration: 2000,
        });
        // 更新本地列表中的商品状态
        const updatedList = albumList.map((item) =>
          item.id === id ? { ...item, isListed: isListed } : item
        );
        setAlbumList(updatedList);
      } else {
        toast("error", {
          content: res.msg,
          duration: 2000,
        });
      }
    });
  };

  // 删除
  const delConfirm = (id: string) => {
    // let data = {
    //   ids: id,
    // };
    deleteDynamic(id).then((res: any) => {
      if (res && res.code == 0) {
        toast("success", {
          content: "删除成功",
          duration: 2000,
        });
        // 直接从当前列表中移除被删除的项目
        const updatedList = albumList.filter((item) => item.id !== id);
        setAlbumList(updatedList);
      } else {
        toast("error", {
          content: res.msg,
          duration: 2000,
        });
      }
    });
  };

  const handleCollect = (item: any) => {
    let data = {
      dynamicId: item.id,
      userId: userInfo.id,
    };

    if (item.isCollect == 1) {
      // 取消收藏
      deleteFavoritesDynamic(data).then((res: any) => {
        if (res && res.code == 0) {
          item.isCollect = 0;
          toast("success", {
            content: "取消成功",
            duration: 2000,
          });
          // 强制更新组件
          setAlbumList([...albumList]);
        } else {
          toast("error", {
            content: res.msg,
            duration: 2000,
          });
        }
      });
    } else {
      // 添加收藏
      favoritesDynamic(data).then((res: any) => {
        if (res && res.code == 0) {
          item.isCollect = 1;
          toast("success", {
            content: "收藏成功",
            duration: 2000,
          });
          // 强制更新组件
          setAlbumList([...albumList]);
        } else {
          toast("error", {
            content: res.msg,
            duration: 2000,
          });
        }
      });
    }
  };

  // 刷新
  const handleItemRefresh = (id: string) => {
    let data = {
      dynamicsId: id,
    };
    dynamicRefresh(data).then((res) => {
      toast("success", {
        content: "刷新成功",
        duration: 2000,
      });
    });
  };

  // 动态置顶
  const handleItemTop = (item) => {
    let data = {
      ...item,
      isTop: item.isTop == 1 ? 0 : 1,
      id: item.id,
    };
    updateDynamic(data).then((res: any) => {
      if (res && res.code == 0) {
        // 更新本地状态
        item.isTop = item.isTop == 1 ? 0 : 1;
        // 强制更新列表
        setAlbumList([...albumList]);

        toast("success", {
          content: item.isTop == 1 ? "置顶成功" : "取消置顶成功",
          duration: 2000,
        });
      } else {
        toast("error", {
          content: res.msg,
          duration: 2000,
        });
      }
    });
  };

  // 跳转到详情页
  const goToDetailPage = (item) => {
    if (platformRef.current === "WX") {
      wx.miniProgram.navigateTo({ url: "/pages/web?path=" + encodeURIComponent(`/pageDynamic/detail/index?dynamicId=${item.id}&dynamicUserId=${item.userId}`) });
    } else {
      Taro.navigateTo({
        url: `/pageDynamic/detail/index?dynamicId=${item.id}&dynamicUserId=${item.userId}`,
      });
    }
  };

  // 编辑
  const handleEdit = (item) => {
    Taro.setStorageSync("releaseDynamicList", item);
    console.log(item, "--------------");
    if (platformRef.current === "WX") {
      wx.miniProgram.navigateTo({ url: "/pages/web?path=" + encodeURIComponent(`/pageDynamic/releaseDynamic/index?type=2`) });
    } else {
      Taro.navigateTo({
        url: `/pageDynamic/releaseDynamic/index?type=2`,
      });
    }
  };

  const handleSave = (item) => {
    Taro.setStorageSync("releaseDynamicList", item);
    if (platformRef.current === "WX") {
      wx.miniProgram.navigateTo({ url: "/pages/web?path=" + encodeURIComponent(`/pageDynamic/releaseDynamic/index?type=4`) });
    } else {
      Taro.navigateTo({
        url: `/pageDynamic/releaseDynamic/index?type=4`,
      });
    }
  };

  // 打开购物车弹框
  const handleAddToCart = (item) => {
    if (!userInfo?.id) {
      Toast.error({ content: "请先登录" });
      return;
    }

    // 设置当前商品并打开弹框
    setCurrentCartItem(item);
    setActionSheetVisible(true);
  };



  const handleDownload = (item) => {
    if (item.pictures !== "") {
      if (platformRef.current === "HM" || platformRef.current === "WX") {
        downloadConfirm(item);
      }else{
      // 检查存储权限
      if (!hasPermission(AuthTypes.STORAGE)) {
        console.log("没有权限");
        // 设置操作类型为下载
        operationTypeRef.current = "download";
        // 如果没有权限，请求权限
        requestPermission(AuthTypes.STORAGE);
        return;
      }
      // 有权限则执行下载
      downloadConfirm(item);
    }
    } else {
      Taro.setClipboardData({
        data: item.content,
        success: () => {
          Taro.hideToast();
          toast("info", {
            content: "文字已复制",
            duration: 2000,
          });
        },
        fail: () => {
          Taro.hideToast();
        },
      });
    }
  };

  const downloadConfirm = (item) => {
    let imageList = item.pictures.split(",");
    if(platformRef.current === "Android"){
      window.downloadImg.downloadImg(item.pictures);
    }
    else if(platformRef.current === "HM"){
      window.harmony.downloadImg(item.pictures);
    }
    else if(platformRef.current === "WX"){
      let imgs = imageList.join("|");
      wx.miniProgram.navigateTo({ url: "/pages/downloadImgs/index?imgs=" + encodeURIComponent(imgs) });
    }
    else{
    for (let i = 0; i < imageList.length; i++) {
      // if (platformRef.current === "Android") {
      //   window.downloadImg.downloadImg(imageList[i]);
      // } else 
      if (platformRef.current === "IOS") {
        window.webkit.messageHandlers.saveImgWithUrlStr.postMessage(
          imageList[i]
        );
      } 
      // else if (platformRef.current === "HM") {
      //   window.harmony.downloadImg(imageList[i]);
      // }
    }
    }
    if (item.content !== "") {
      Taro.setClipboardData({
        data: item.content,
        success: () => {
          Taro.hideToast();
        },
        fail: () => {
          Taro.hideToast();
        },
      });
    }
  };

  // 处理图片点击预览 - 跳转到独立的图片预览页面
  const handleImagePreview = (images, index, item) => {
    const imageList = images.split(",");

    // 跳转到独立的图片预览页面
    const params = {
      images: encodeURIComponent(JSON.stringify(imageList)),
      initialIndex: index.toString(),
      dynamic: encodeURIComponent(JSON.stringify(item)),
      showCar: item.price && Number(item.price) > 0 ? "true" : "false",
      showBottom: "true",
    };
    const queryString = Object.entries(params)
      .map(([key, value]) => `${key}=${value}`)
      .join("&");
    if (platformRef.current === "WX") {
      wx.miniProgram.navigateTo({ url: "/pages/web?path=" + encodeURIComponent(`/pageDynamic/imagePreview/index?${queryString}`) });
    } else {
      Taro.navigateTo({
        url: `/pageDynamic/imagePreview/index?${queryString}`,
      });
    }
  };

  const goToUserDetailPage = (userId) => {
    if (userId == userInfo.userId) {
      console.log("goToUserDetailPage userId", userId);
      if (platformRef.current === "WX") {
        wx.miniProgram.navigateTo({ url: "/pages/web?path=/pageDynamic/album/index" });
      } else {
        Taro.navigateTo({
          url: "/pageDynamic/album/index",
        });
      }
    } else {
      console.log("goToUserDetailPage userId", userId);
      if (platformRef.current === "WX") {
        wx.miniProgram.navigateTo({ url: "/pages/web?path=" + encodeURIComponent(`/pageUserInfo/userDetail/index?userId=${userId}`) });
      } else {
        Taro.navigateTo({
        url: `/pageUserInfo/userDetail/index?userId=${userId}`,
        });
      }
    }
  };

  // 下拉刷新 - 根据搜索状态调用对应接口
  const handleRefresh = async () => {
    console.log("下拉刷新开始");

    // 重置分页和状态
    albumPageRef.current = 1;
    setIsLoadEnd(false);
    setLoadStatus("prepare");

    // 统一调用getAlbumListData，内部会自动判断是否有搜索内容
    await getAlbumListData();

    console.log("下拉刷新完成");
  };

  const handleSearch = (value: string) => {
    setSearchVal(value);
    searchValRef.current = value; // 同时更新ref
    searchImgRef.current = "";
    // setIsSearch(true);
    albumPageRef.current = 1;
    setIsLoadEnd(false);
    getAlbumListData(); // 统一调用，内部会自动判断是否有搜索内容
  };

  // 搜索框onChange处理
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchVal(value); // 立即更新输入框的值
    searchValRef.current = value; // 同时更新ref

    // 如果输入为空，立即清除搜索结果
    if (value.length === 0) {
      searchImgRef.current = "";
      getAlbumListData();
    }
    // 移除了输入时自动搜索的逻辑
  };

  // 处理键盘按键事件
  const handleKeyDown = (e) => {
    console.log("Key pressed:", e.key);
    if (e.key === "Enter") {
      console.log("Enter key pressed, searching...");
      handleSearchSubmit();
    }
  };

  // 处理键盘搜索按钮点击
  const handleSearchSubmit = (value) => {
    console.log("handleSearchSubmit called with:", value);
    const searchValue = value || searchVal;
    if (searchValue.trim().length > 0) {
      handleSearch(searchValue.trim());
    }
  };

  // 搜索框清除处理
  const handleSearchClear = () => {
    setSearchVal("");
    searchValRef.current = ""; // 同时清除ref
    searchImgRef.current = "";
    albumPageRef.current = 1;
    setIsLoadEnd(false);
    getAlbumListData(); // 统一调用，内部会自动判断
  };

  // 打开选择图片方式弹窗
  const openPopup = () => {
    if (platformRef.current === "WX") {
      chooseImageRef.current = "camera";
      chooseImage();
      return;
    }
    setPopupVisible(true);
  };

  // 关闭选择图片方式弹窗
  const handleClose = () => {
    setPopupVisible(false);
  };

  const handleConfirm = (index: number) => {
    operationTypeRef.current = "imageSearch";
    if (index === 0) {
      // 设置图片选择类型为拍照
      chooseImageRef.current = "camera";
      // 请求相机权限
      if (!hasPermission(AuthTypes.CAMERA)) {
        // 如果没有权限，请求权限
        requestPermission(AuthTypes.CAMERA);
        return;
      }
      chooseImage();
    } else if (index === 1) {
      // 设置图片选择类型为相册
      chooseImageRef.current = "album";
      // 请求相册权限
      if (!hasPermission(AuthTypes.GALLERY_PHOTO)) {
        // 如果没有权限，请求权限
        requestPermission(AuthTypes.GALLERY_PHOTO);
        return;
      }
      chooseImage();
    }
  };

  const blobToBase64 = async (blobUrl: string): Promise<string> => {
    try {
      // 首先通过fetch获取blob数据
      const response = await fetch(blobUrl);
      const blob = await response.blob();

      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          // reader.result包含base64字符串
          const base64String = reader.result as string;
          // 移除开头的 "data:image/jpeg;base64," 部分（如果后端不需要这个前缀）
          const base64 = base64String.split(",")[1];
          resolve(base64);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error("转换base64失败:", error);
      throw error;
    }
  };



  // 获取购物车数量
  const fetchCartCount = async () => {
    const count = await fetchCartCountUtil();
    setCarListNum(count);
  };

  // 监听购物车数量更新事件
  useEffect(() => {
    const handleUpdateCartCount = (count: number) => {
      setCarListNum(count);
    };

    Taro.eventCenter.on("updateCartCount", handleUpdateCartCount);
    return () => {
      Taro.eventCenter.off("updateCartCount", handleUpdateCartCount);
    };
  }, []);

  // 初始化
  useLoad(() => {
    getAlbumListData();
    fetchCartCount(); // 获取购物车数量
  });

  // 上拉触底 - 根据搜索状态调用对应接口
  useReachBottom(() => {
    // 简化检查逻辑
    if (!loading && !isLoadEnd && !isFetchingRef.current) {
      getAlbumListData();
    }
  });

  return (
    <View className="indexBox">
      {platform !== "WX" && <YkNavBar switchTab title="首页" />}
      <View className="searchLine_home"
      style={{ ...platform !== "WX" ? { top: '80px' } : { top: '0' } }}
      >
        <SearchBar
          shape="square"
          placeholder="搜索"
          clearable={true}
          value={searchVal}
          onChange={handleSearchChange}
          onClear={handleSearchClear}
          onKeyDown={handleKeyDown}
          // suffix={
          //   <Image
          //     className="search-icon"
          //     src={require("@/assets/images/common/search_img_icon.png")}
          //     mode="aspectFit"
          //     onClick={() => openPopup()}
          //   />
          // }
        />
      </View>

      <Tabs
        style={platform !== "WX" ? { top: '132px' } : { top: '50px' }}
        className="album-tabs-home"
        tabs={tabData}
        defaultActiveTab={activeTabIndex}
        tabBarHasDivider={false}
        useCaterpillar
        tabBarArrange="start"
        onAfterChange={(tab, index) => {
          console.log('[tabs]', tab, index);
          onTabsChange(tab);
        }}
      >
        {tabData.map((_, index) => (
          <View key={index}>
            {/* 空内容，实际内容在下方渲染 */}
          </View>
        ))}
      </Tabs>

      {/* 列表内容 - 只有一个PullRefresh */}
      <PullRefresh
        style={{ marginTop: '40px' }}
        className="showAlbumBox"
        disabled={loading || !canPullRefresh}
        onRefresh={handleRefresh}
        finishDelay={1000}
        loadingText={
          <View className="pull-refresh-loading">
            {/* <Loading type="dot" radius={8} /> */}
            <Text className="loading-text">正在刷新...</Text>
          </View>
        }
        finishText={<Text className="pull-refresh-success">刷新成功</Text>}
      >
          {/* 判断是否登录 */}
          {!userInfo || !userInfo.id ? (
            <View className="wait-login-container">
              <View className="wait-login-container-content">
                <View className="wait-login-container-text-area">
                  <Text className="wait-login-container-text-area-title">登录可查看关注好友的动态</Text>
                  <Text className="wait-login-container-text-area-subtitle">您当前未登录账号，请登录后查看相册动态</Text>
                </View>
                <View className="wait-login-container-image-wrapper">
                  <Image
                    className="wait-login-container-image-wrapper-bg"
                    src={require("@/assets/images/common/index_wait_login_bg.png")}
                    fit="cover"
                  />
                </View>
              </View>
              <View 
                className="wait-login-container-login-btn"
                onClick={() => {
                  if (platformRef.current === "WX") {
                    wx.miniProgram.navigateTo({ url: "/pages/index/index" });
                  } else {
                    Taro.navigateTo({ url: "/pages/login/index" });
                  }
                }}
              >
                <Text>立即登录</Text>
              </View>
            </View>
          ) : (
          <View>
            {albumList.map((item, index) => {
              return (
                <View
                  className="showAlbumBox-item"
                  onClick={(e) => {
                    e.stopPropagation();
                    goToDetailPage(item);
                  }}
                >
                  <View
                    className="showAlbumBox-item-top"
                    onClick={(e) => {
                      e.stopPropagation();
                      goToUserDetailPage(item.userId);
                    }}
                  >
                    <Image
                      fit="cover"
                      showLoading={true}
                      showError={true}
                      src={item.avatar}
                      className="showAlbumBox-item-top-image"
                      radius="50%"
                    />
                    <View className="showAlbumBox-item-top-content">
                      <View className="showAlbumBox-item-top-content-name">
                        <Text>{item.nickname}</Text>
                        {item.hasOpenOnlinePayment === 1 && (
                          // <Image
                          //   className="payment-icon"
                          //   src={require("@/assets/images/common/wx_pay.png")}
                          // />
                          <IconPayment className="payment-icon" />
                        )}
                      </View>
                      <View className="showAlbumBox-item-top-content-date">
                        {formatDate( item.createTime)}
                        {/* {formatDate(currentTypeRef.current === 2 ? item.createTime : item.updateTime)} */}
                      </View>
                    </View>

                    {item.userId !== userInfo.userId && (
                      <View
                        className="showAlbumBox-item-top-more-wrapper"
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                          handleMoreAction(item);
                        }}
                      >
                        {/* <Image
                          className="showAlbumBox-item-top-more"
                          src={require("@/assets/images/common/trend_more.png")}
                        /> */}

<IconMore className="showAlbumBox-item-top-more" />
                      </View>
                    )}
                  </View>

                  {item.content && (
                  <View className="showAlbumBox-item-title">
                    <View className="custom-ellipsis-container">
                      <Text
                        className="custom-ellipsis-text"
                        userSelect={true}
                      >
                        {ellipsis.includes(item.id)
                          ? item.content
                          : item.content && item.content.length > 100
                          ? item.content.substring(0, 100) + "..."
                          : item.content}
                      </Text>
                      {item.content && item.content.length > 100 && (
                        <Text
                          className="custom-ellipsis-toggle"
                          onClick={(e) => {
                            e.stopPropagation();
                            e.preventDefault();
                            handleEllipsis(item.id);
                          }}
                        >
                          {ellipsis.includes(item.id) ? "收起" : "展开"}
                        </Text>
                      )}
                    </View>
                  </View>
                  )}
                  
                  <View
                    className={`showAlbumBox-item-center ${
                      item.pictures
                        ? `images-${Math.min(
                            item.pictures.split(",").length,
                            9
                          )}`
                        : ""
                    }`}
                  >
                    {item.pictures &&
                      item.pictures.split(",").map((item2, index2) => {
                        console.log(item2);
                        return (
                          index2 < 9 && (
                            <View
                              key={index2}
                              className="image-wrapper"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleImagePreview(item.pictures, index2, item);
                              }}
                            >
                              <Image
                                fit="cover"
                                showLoading={true}
                                showError={true}
                                src={item2}
                                className="showAlbumBox-item-center-item"
                                ref={(ref) => {
                                  if (!imageRefs.current[item.id]) {
                                    imageRefs.current[item.id] = [];
                                  }
                                  imageRefs.current[item.id][index2] = ref;
                                }}
                              />
                            </View>
                          )
                        );
                      })}
                  </View>

                  {item.price > 0 && (
                    <View className="showAlbumBox-item-jiage">
                      <View className="showAlbumBox-item-jiage-icon">￥</View>
                      <View className="showAlbumBox-item-jiage-val">
                        {item.price / 100}
                      </View>
                    </View>
                  )}

                  <View className="move-attrs-wrap-my">
                    {(item.labelAndCatalogueIds ||
                      item.productSpecificationsNames ||
                      item.productColorNames) && (
                      <Text
                        className="move-attrs-my"
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                          setExpandedId(
                            item.id === undefined
                              ? null
                              : expandedId === item.id
                              ? null
                              : item.id
                          );
                        }}
                      >
                        商品属性
                        {expandedId === item.id ? (
                          // <Image
                          //   className="move-attrs-arrow"
                          //   src={require("@/assets/images/common/arrow_left.png")}
                          //   style={{ transform: "rotate(90deg)" }}
                          // />
                          <IconTriDown className="move-attrs-arrow" style={{ transform: "rotate(0deg)" }} />
                        ) : (
                          // <Image
                          //   className="move-attrs-arrow"
                          //   src={require("@/assets/images/common/arrow_left.png")}
                          // />
                          <IconTriDown className="move-attrs-arrow" style={{ transform: "rotate(-90deg)" }} />
                        )}
                      </Text>
                    )}

                    {expandedId === item.id && (
                      <View className="attr-content card-attr-content">
                        {item.labelAndCatalogueIds ? (
                          <View className="attr-row" key="标签">
                            <View className="attr-values">
                              {[...item.labelAndCatalogueIds.split(",")].map(
                                (val, index) => (
                                  <Text className="tag-badge" key={index}>
                                    {val}
                                  </Text>
                                )
                              )}
                            </View>
                          </View>
                        ) : (
                          <></>
                        )}
                        {item.productColorNames ? (
                          <View className="attr-row" key="颜色">
                            <View className="attr-values">
                              <Text className="attr-label">颜色：</Text>
                              {[...item.productColorNames.split(",")].map(
                                (val, index) => (
                                  <Text className="attr-value" key={index}>
                                    {val}
                                  </Text>
                                )
                              )}
                            </View>
                          </View>
                        ) : (
                          <></>
                        )}
                        {item.productSpecificationsNames ? (
                          <View className="attr-row" key="规格">
                            <View className="attr-values">
                              <Text className="attr-label">规格：</Text>
                              {[
                                ...item.productSpecificationsNames.split(","),
                              ].map((val, index) => (
                                <Text className="attr-value" key={index}>
                                  {val}
                                </Text>
                              ))}
                            </View>
                          </View>
                        ) : (
                          <></>
                        )}
                      </View>
                    )}
                  </View>

                  {item.userId == userInfo.userId && (
                    <View className="showAlbumBox-item-playMode">
                      <Text
                        className="showAlbumBox-item-playMode-link"
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                          handleDelete(item.id);
                        }}
                      >
                        删除
                      </Text>
                      <Text
                        className="showAlbumBox-item-playMode-link"
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                          handleDown(item);
                        }}
                      >
                        {item.isListed === 2 ? "上架" : "下架"}
                      </Text>
                      <Text
                        className="showAlbumBox-item-playMode-link"
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                          handleItemRefresh(item.id);
                        }}
                      >
                        刷新
                      </Text>
                      <Text
                        className="showAlbumBox-item-playMode-link"
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                          handleItemTop(item);
                        }}
                      >
                        {" "}
                        {item.isTop == 1 ? "取顶" : "置顶"}
                      </Text>
                      <Text
                        className="showAlbumBox-item-playMode-link"
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                          handleEdit(item);
                        }}
                      >
                        编辑
                      </Text>
                    </View>
                  )}

                  <View className="showAlbumBox-item-playBtn">
                    <View className="showAlbumBox-item-playBtn-left">
                      <View
                        className="showAlbumBox-item-playBtn-left-item"
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                          handleCollect(item);
                        }}
                      >
                        <Image
                          className="showAlbumBox-item-playBtn-left-item-icon"
                          bottomOverlap={null}
                          src={
                            item.isCollect === 1
                              ? require("@/assets/images/common/trend_collect_p.png")
                              : require("@/assets/images/common/trend_collect_n.png")
                          }
                        />
                        <Text className="showAlbumBox-item-playBtn-left-item-text">
                          {item.isCollect == 1 ? "已收藏" : "收藏"}
                        </Text>
                      </View>
                      {item.pictures&&(
                      <View
                        className="showAlbumBox-item-playBtn-left-item"
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                          clickItem.current = item;
                          handleDownload(item);
                        }}
                      >
                        <Image
                          className="showAlbumBox-item-playBtn-left-item-icon"
                          bottomOverlap={null}
                          src={require("@/assets/images/common/trend_download.png")}
                        />
                        {/* <IconDownload className="showAlbumBox-item-playBtn-left-item-icon" /> */}
                        <Text className="showAlbumBox-item-playBtn-left-item-text">
                          下载
                        </Text>
                      </View>
                      )}
                    </View>

                    {item.userId !== userInfo.userId && (
                      <View className="custom-save-button">
                        {/* 购物车图标 - 只有开通在线支付且有价格的商品才显示 */}
                        {item.hasOpenOnlinePayment === 1 &&
                          Number(item.price) > 0 && (
                            <>
                              <View
                                className="cart-icon-wrapper"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  e.preventDefault();
                                  handleAddToCart(item);
                                }}
                              >
                                {/* <Image
                                  className="cart-icon"
                                  src={require("@/assets/images/common/good_car_icon.png")}
                                /> */}
                                <IconCart className="cart-icon" />
                              </View>
                              <View className="divider-line"></View>
                            </>
                          )}

                        {/* 一键转存/分享按钮 */}

                        <View
                          className="save-text-wrapper"
                          onClick={(e) => {
                            e.stopPropagation();
                            e.preventDefault();
                            handleSave(item);
                          }}
                        >
                          <Text className="custom-save-button-text">
                            {item.userId == userInfo.userId
                              ? "一键分享"
                              : "一键转存"}
                          </Text>
                        </View>
                      </View>
                    )}

                    {item.userId == userInfo.userId && (
                      <View className="custom-save-button_noboder">
                        {item.hasOpenOnlinePayment === 1 &&
                          Number(item.price) > 0 && (
                            <>
                              <View
                                className="cart-icon-wrapper"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  e.preventDefault();
                                  handleAddToCart(item);
                                }}
                              >
                                {/* <Image
                                  className="cart-icon"
                                  src={require("@/assets/images/common/good_car_icon.png")}
                                /> */}
                                <IconCart className="cart-icon" />
                              </View>
                            </>
                          )}
                      </View>
                    )}
                  </View>
                </View>
              );
            })}

            {albumList.length === 0 &&!loading && (
            <View className="not_content">
              <IconLoadEmpty className="not_content-image" />
              <Text>暂无动态内容</Text>
            </View>
            )}  

{albumPageRef.current>1 && (
            <LoadMore
              style={{ paddingTop: 16, paddingBottom: 80 }}
              status={loadStatus}
            />
            )}
          </View>
          )}
      </PullRefresh>

      <YkSwitchTabBar activeTab={0} />

      <BottomPopup
        // title="请选择操作"
        options={["拍照", "从相册选择"]}
        btnCloseText="取消"
        onConfirm={handleConfirm}
        onClose={handleClose}
        visible={isPopupVisible}
      />

      <PermissionPopup {...permissionPopupProps} />

      {/* 悬浮购物车入口 */}
      <View
        className="car"
        onClick={() => {
          if (platformRef.current === "WX") {
            wx.miniProgram.navigateTo({ url: "/pages/web?path=/pageOrder/cart/index" });
          } else {
            Taro.navigateTo({ url: "/pageOrder/cart/index" });
          }
        }}
      >
        <Image
          className="car-img"
          bottomOverlap={null}
          src={require("@/assets/images/common/car_icon.png")}
        />
        {carListNum > 0 && <Text className="car-text">{carListNum}</Text>}
      </View>

      {/* 购物车弹框组件 */}
      {currentCartItem && (
        <CartModal
          visible={actionSheetVisible}
          onClose={() => setActionSheetVisible(false)}
          productData={{
            id: currentCartItem.id,
            pictures: currentCartItem.pictures,
            content: currentCartItem.content,
            dynamic_title: currentCartItem.dynamic_title,
            price: currentCartItem.price,
            colors: currentCartItem.colors,
            specifications: currentCartItem.specifications,
            userId: currentCartItem.userId, // 商家ID
          }}
          merchantInfo={{
            nickname: currentCartItem.nickname,
            avatar: currentCartItem.avatar,
          }}
          onAddCart={async (cartData) => {
            try {
              const res: any = await addCart(cartData);
              if (res && res.code === 0) {
                Toast.success({ content: '加入购物车成功' });
                // 更新购物车数量
                await fetchCartCount();
              } else {
                Toast.error({ content: res.msg || '加入购物车失败' });
              }
            } catch (error) {
              console.error('加入购物车失败:', error);
              Toast.error({ content: '加入购物车失败' });
            }
          }}
          onBuyNow={(buyNowData) => {
            // 构建选中商品数据并存储
            Taro.setStorageSync('selectedItems', buyNowData);
            // 跳转到确认订单页面
            if (platformRef.current === "WX") {
              wx.miniProgram.navigateTo({ url: "/pages/web?path=" + encodeURIComponent("/pageOrder/order/pay/index?from=detail") });
            } else {
              Taro.navigateTo({
                url: '/pageOrder/order/pay/index?from=detail'
              });
            }
          }}
        />
      )}

      {/* 更多操作弹框 */}
      <BottomPopup
        options={[
          {
            label: "拉黑商家",
            description: currentMoreItem?.nickname || ""
          },
          {
            label: "举报",
            description: "商品文案与图片不符，图文违规等"
          }
        ]}
        btnCloseText="取消"
        onConfirm={handleMoreActionConfirm}
        onClose={handleMoreActionClose}
        visible={moreActionVisible}
      />
    </View>
  );
}
