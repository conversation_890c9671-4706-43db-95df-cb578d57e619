import { IconProps } from "./types";
const IconWechat: React.FC<IconProps> = ({
  color = "#54B736",
  size = 32,
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    fill="none"
    version="1.1"
    width={size}
    height={size}
    viewBox="0 0 20 20"
    className={className}
  >
    <g>
      <g>
        <g>
          <path
            d="M18.3332861328125,12.077904453125C18.3332861328125,9.708404453125,15.972616132812501,7.778564453125,13.3228861328125,7.778564453125C10.516116132812499,7.778564453125,8.3057861328125,9.710074453125,8.3057861328125,12.077904453125C8.3057861328125,14.450764453125,10.516116132812499,16.377244453125,13.3228861328125,16.377244453125C13.9109761328125,16.377244453125,14.5040761328125,16.227894453125,15.093826132812499,16.080214453125002L16.711066132812498,16.969624453125L16.2666561328125,15.489524453125C17.451186132812502,14.596754453125001,18.3332861328125,13.413684453125,18.3332861328125,12.077904453125ZM11.7708161328125,11.420084453125C11.4015861328125,11.420084453125,11.1025361328125,11.119694453125,11.1025361328125,10.748834453125C11.1025361328125,10.377974453125,11.4015861328125,10.077584453125,11.7708161328125,10.077584453125C12.1400361328125,10.077584453125,12.4390961328125,10.377974453125,12.4390961328125,10.748834453125C12.4390961328125,11.118024453124999,12.1400361328125,11.420084453125,11.7708161328125,11.420084453125ZM15.0169761328125,11.415044453125C14.6477561328125,11.415044453125,14.3486961328125,11.114664453125,14.3486961328125,10.743804453125C14.3486961328125,10.372934453125,14.6477561328125,10.072554453125,15.0169761328125,10.072554453125C15.3862061328125,10.072554453125,15.685256132812501,10.372934453125,15.685256132812501,10.743804453125C15.685256132812501,11.114664453125,15.3862061328125,11.415044453125,15.0169761328125,11.415044453125Z"
            fill={color}
            fillOpacity="1"
          />
        </g>
        <g>
          <path
            d="M7.56591701171875,3.3333740234375C4.32309701171875,3.3333740234375,1.66668701171875,5.5535240234375,1.66668701171875,8.372764023437501C1.66668701171875,10.000534023437499,2.55048601171875,11.3363240234375,4.02738701171875,12.3733940234375L3.43762701171875,14.1555740234375L5.49926701171875,13.1168040234375C6.23771701171875,13.2628040234375,6.82914701171875,13.4138740234375,7.56591701171875,13.4138740234375C7.75136701171875,13.4138740234375,7.93514701171875,13.4054740234375,8.117257011718749,13.3903740234375C8.00197701171875,12.9943040234375,7.93514701171875,12.5798040234375,7.93514701171875,12.1485340234375C7.93514701171875,9.559194023437499,10.14881701171875,7.4581940234375,12.95058701171875,7.4581940234375C13.14268701171875,7.4581940234375,13.33148701171875,7.4716140234375,13.51858701171875,7.4934340234375C13.00738701171875,5.1071440234375,10.46625701171875,3.3333740234375,7.56591701171875,3.3333740234375ZM5.58447701171875,7.3994540234375C5.1417370117187495,7.3994540234375,4.78253701171875,7.0386640234375,4.78253701171875,6.5939540234375C4.78253701171875,6.1492540234375,5.1417370117187495,5.7884640234375,5.58447701171875,5.7884640234375C6.02720701171875,5.7884640234375,6.38640701171875,6.1492540234375,6.38640701171875,6.5939540234375C6.38640701171875,7.0386640234375,6.02720701171875,7.3994540234375,5.58447701171875,7.3994540234375ZM9.70441701171875,7.3994540234375C9.26167701171875,7.3994540234375,8.90247701171875,7.0386640234375,8.90247701171875,6.5939540234375C8.90247701171875,6.1492540234375,9.26167701171875,5.7884640234375,9.70441701171875,5.7884640234375C10.14714701171875,5.7884640234375,10.50634701171875,6.1492540234375,10.50634701171875,6.5939540234375C10.50634701171875,7.0386640234375,10.14714701171875,7.3994540234375,9.70441701171875,7.3994540234375Z"
            fill={color}
            fillOpacity="1"
          />
        </g>
      </g>
    </g>
  </svg>
);

export default IconWechat;
