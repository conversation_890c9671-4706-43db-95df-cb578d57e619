@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageOnlinePayment/onlinePayment/information/index"] {
  .merchant-info-page {
    min-height: 100vh;
    background-color: var(--card-background-color);
    .use-dark-mode-query({
    background-color: var(--dark-card-background-color);
  });
  }

  .merchant-info-content {
    padding: 0 0 16px 0;
  }

  .notice-container {
    margin: 10px 10px 0 10px;
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: var(--sub-info-font-color);
    font-size: 14px;
    .use-dark-mode-query({
    color: var(--dark-sub-info-font-color);
  });
  }

  .info-card {
    margin: 10px 10px 0 10px;
    background-color: var(--background-color);
    border-radius: 7px;
    padding: 15px;
    .use-dark-mode-query({
    background-color: var(--dark-container-background-color);
  });
  }

  .info-section {
    margin-bottom: 12px;
  }

  .info-row {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    position: relative;

    .label {
      width: 52px;
      color: var(--sub-info-font-color);
      font-size: 13px;
      line-height: 140%;
      .use-dark-mode-query({
      color: var(--dark-sub-info-font-color);
    });
    }
    .value {
      flex: 1;
      margin-left: 30px;
      color: var(--font-color);
      font-size: 13px;
      line-height: 140%;
      .use-dark-mode-query({
      color: var(--dark-font-color);
    });
    }
    .action {
      color: var(--primary-color);
      font-size: 13px;
      line-height: 140%;
      margin-left: 12px;
      cursor: pointer;
      .use-dark-mode-query({
      color: var(--dark-primary-color);
    });
      &:active {
        color: var(--dark-primary-color);
      }
      &:disabled {
        color: var(--primary-disabled-color);
        .use-dark-mode-query({
        color: var(--dark-primary-disabled-color);
      });
      }
    }
    .copy-icon {
      width: 11px;
      height: 11px;
      margin-left: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0.9;
      cursor: pointer;
      .use-dark-mode-query({
      filter: invert(0.8);
    });
    }
    .settle-desc {
      margin-left: 12px;
      color: var(--sub-info-font-color);
      font-size: 11px;
      .use-dark-mode-query({
      color: var(--dark-sub-info-font-color);
    });
    }

    // 状态样式
    .status-pending {
      color: var(--warning-color) !important;
      .use-dark-mode-query({
      color: var(--dark-warning-color) !important;
    });
    }

    .status-approved {
      color: var(--success-color) !important;
      .use-dark-mode-query({
      color: var(--dark-success-color) !important;
    });
    }

    .status-rejected {
      color: var(--danger-color) !important;
      .use-dark-mode-query({
      color: var(--dark-danger-color) !important;
    });
    }

    .reject-reason {
      color: var(--danger-color) !important;
      font-size: 12px !important;
      .use-dark-mode-query({
      color: var(--dark-danger-color) !important;
    });
    }
  }

  .divider {
    height: 0.5px;
    background-color: var(--line-color);
    margin: 15px 0;
    .use-dark-mode-query({
    background-color: var(--dark-line-color);
  });
  }

  .bank-info {
    .verify-btn {
      width: 116px;
      height: 28px;
      margin: 12px auto 0;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: var(--lighter-primary-color);
      border-radius: 7px;
      color: var(--primary-color);
      font-size: 14px;
      line-height: 140%;
      cursor: pointer;
      .use-dark-mode-query({
      background-color: var(--lighter-primary-color) !important;
      color: var(--dark-primary-color) !important;
    });
      // &:active {
      //   background-color: var(--dark-button-primary-color);
      //   color: var(--dark-font-color);
      // }
      // &:disabled {
      //   background-color: var(--disabled-color);
      //   color: var(--primary-disabled-color);
      //   .use-dark-mode-query({
      //     background-color: var(--dark-disabled-color);
      //     color: var(--dark-primary-disabled-color);
      //   });
      // }
    }
  }

  .agreement {
    display: flex;
    align-items: center;
    justify-content: space-between;
    // padding: 15px 0;
    padding: 0;
    .agreement-label {
      color: var(--sub-info-font-color);
      font-size: 13px;
      line-height: 140%;
      .use-dark-mode-query({
      color: var(--dark-sub-info-font-color);
    });
    }
    .agreement-link {
      display: flex;
      align-items: center;
      color: var(--primary-color);
      font-size: 13px;
      line-height: 140%;
      cursor: pointer;
      .use-dark-mode-query({
      color: var(--dark-primary-color);
    });
      &:active {
        color: var(--dark-primary-color);
      }
      .arrow {
        width: 11px;
        height: 11px;
        margin-left: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--sub-info-font-color);
        .use-dark-mode-query({
        color: var(--dark-sub-info-font-color);
      });
      }
    }
  }
}
