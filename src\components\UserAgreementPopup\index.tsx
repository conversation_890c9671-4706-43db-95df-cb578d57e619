import { View, Text } from '@tarojs/components'
import { Button, Popup } from '@arco-design/mobile-react'
import { IconClose } from "@arco-iconbox/react-yk-arco"
import Taro from '@tarojs/taro'
import './index.less'

interface UserAgreementPopupProps {
  /** 弹窗是否可见 */
  visible: boolean
  /** 关闭弹窗的回调 */
  onClose: () => void
  /** 同意并继续的回调 */
  onConfirm: () => void
  /** 设置同意状态的回调 */
  onSetCheck?: (checked: boolean) => void
}

/**
 * 用户协议弹窗组件
 * 用于显示用户协议和隐私政策的确认弹窗
 */
export default function UserAgreementPopup({
  visible,
  onClose,
  onConfirm,
  onSetCheck
}: UserAgreementPopupProps) {

  // 跳转到用户协议页面
  const handleUserAgreementClick = () => {
    Taro.navigateTo({ 
      url: `/pageSetting/webView/index?url=${USER_AGREEMENT}&name=用户协议` 
    })
    onClose()
  }

  // 跳转到隐私政策页面
  const handlePrivacyPolicyClick = () => {
    Taro.navigateTo({ 
      url: `/pageSetting/webView/index?url=${PRIVACY_POLICY}&name=隐私政策` 
    })
    onClose()
  }

  // 处理同意并继续
  const handleConfirm = () => {
    // 设置同意状态为true
    if (onSetCheck) {
      onSetCheck(true)
    }
    // 执行确认回调
    onConfirm()
    // 关闭弹窗
    onClose()
  }

  return (
    <Popup 
      visible={visible} 
      close={onClose} 
      contentStyle={{ borderRadius: '10px 10px 0 0' }} 
      direction={'bottom'}
    >
      <View className='user-agreement-popup-content'>
        <IconClose className='popup-close' onClick={onClose} />
        
        <View className="popup-title">用户协议及隐私保护</View>
        
        <View className="popup-desc">
          <Text>为了更好地保障您的合法权益，请您阅读并同意以下协议</Text>
          <Text className='link' onClick={handleUserAgreementClick}>
            《用户协议》
          </Text>
          <Text>和</Text>
          <Text className='link' onClick={handlePrivacyPolicyClick}>
            《隐私政策》
          </Text>
          <Text>的全部内容。</Text>
        </View>
        
        <Button className="popup-confirm" onClick={handleConfirm}>
          同意并继续
        </Button>
      </View>
    </Popup>
  )
}
