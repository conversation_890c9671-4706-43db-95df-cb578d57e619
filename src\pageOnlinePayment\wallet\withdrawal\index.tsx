import { View, Text } from "@tarojs/components";
import "./index.less";
import Taro from "@tarojs/taro";
import { useState, useEffect } from "react";
import {
  Input,
  Button,
  Toast,
  Loading,
  Keyboard,
  Dialog,
} from "@arco-design/mobile-react";
import { IconClose } from "@arco-design/mobile-react/esm/icon";

// 组件
import YkNavBar from "@/components/ykNavBar/index";
import { IconEdit } from "@arco-design/mobile-react/esm/icon";

// API
import {
  withdrawal,
  getBalance,
  getSettlementAccountModificationPage,
  getMerchantEntryApplicationPage,
} from "@/utils/api/common/common_wechat";
import { useRef } from "react";
import { createWithdrawal } from "@/utils/api/common/common_user";
// 类型定义
interface AccountInfo {
  accountName: string;
  accountNumber: string;
  bankName?: string;
}

interface BalanceInfo {
  availableAmount: number;
  sub_mchid: string;
}

interface PageState {
  loading: boolean;
  submitting: boolean;
  accountInfo: AccountInfo | null;
  balanceInfo: BalanceInfo | null;
  showConfirmDialog: boolean;
}

// 格式化银行账号（隐藏中间部分）
const formatBankAccount = (accountNumber: string): string => {
  if (!accountNumber || accountNumber.length < 8) return accountNumber;
  const start = accountNumber.substring(0, 4);
  const end = accountNumber.substring(accountNumber.length - 4);
  return `${start}****${end}`;
};

export default function Withdrawal() {
  // ==================== 状态管理 ====================
  const [amount, setAmount] = useState("");

  const [pageState, setPageState] = useState<PageState>({
    loading: true,
    submitting: false,
    accountInfo: null,
    balanceInfo: null,
    showConfirmDialog: false,
  });

  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  // ==================== 生命周期 ====================
  useEffect(() => {
    initPage();
  }, []);

  // ==================== 页面初始化 ====================
  const initPage = async () => {
    setPageState((prev) => ({ ...prev, loading: true }));
    try {
      await Promise.all([loadAccountInfo(), loadBalanceInfo()]);
    } catch (error) {
      console.error("页面初始化失败:", error);
      Toast.error("网络异常，请重试");
    } finally {
      setPageState((prev) => ({ ...prev, loading: false }));
    }
  };

  // ==================== 数据加载 ====================
  const loadAccountInfo = async () => {
    try {
      // 优先获取修改后的结算账户信息
      const modifiedResp = await getSettlementAccountModificationPage({userId: Taro.getStorageSync("userInfo")?.id, pageNo: 1, pageSize: 1 });
      const modifiedItem = modifiedResp?.data?.list?.[0];

      if (modifiedResp && modifiedResp?.code === 0 && modifiedItem) {
        setPageState((prev) => ({
          ...prev,
          accountInfo: {
            accountName: modifiedItem.accountName || "",
            accountNumber: formatBankAccount(modifiedItem.accountNumber || ""),
            bankName: modifiedItem.bankName || modifiedItem.accountBank || "",
          },
        }));
        return;
      }

      // 若未修改，回退到商户进件申请信息
      const applyResp = await getMerchantEntryApplicationPage({ userId: Taro.getStorageSync("userInfo")?.id, pageNo: 1, pageSize: 1 });
      const applyItem = applyResp?.data?.list?.[0];
      if (applyResp && applyResp?.code === 0 && applyItem) {
        setPageState((prev) => ({
          ...prev,
          accountInfo: {
            accountName: applyItem.accountName || "",
            accountNumber: formatBankAccount(applyItem.accountNumber || ""),
            bankName: applyItem.bankName || applyItem.accountBank || "",
          },
        }));
      }
    } catch (error) {
      console.error("获取账户信息失败:", error);
    }
  };

  const loadBalanceInfo = async () => {
    try {
      const res: any = await getBalance({ userId: Taro.getStorageSync("userInfo")?.id});
      console.log('res', res);
      if (res) {
        const data = (res && typeof res === 'object' && 'data' in res) ? (res as any).data : res;
        setPageState((prev) => ({
          ...prev,
          balanceInfo: {
            availableAmount: data.available_amount/100 || 0,
            sub_mchid: data.sub_mchid || ""
          },
        }));
      }
    } catch (error) {
      console.error("获取余额信息失败:", error);
    }
  };

  // ==================== 表单验证 ====================
  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!amount) {
      newErrors.amount = "请输入提现金额";
      setErrors(newErrors);
      return false;
    }

    const amountValue = parseFloat(amount);
    if (isNaN(amountValue) || amountValue <= 0) {
      newErrors.amount = "请输入有效的提现金额";
      setErrors(newErrors);
      return false;
    }

    if (amountValue < 0.1) {
      // setPageState((prev) => ({ ...prev, showMessageDialog: true }));
      // newErrors.amount = "提现金额不能少于1元";
      // setErrors(newErrors);
      Dialog.alert({
        children: `提现金额不能小于1.00`,
        platform: "ios",
        okText: "我知道了",
      })
      return false;
    }

    if (
      pageState.balanceInfo &&
      amountValue > pageState.balanceInfo.availableAmount
    ) {
      // newErrors.amount = "提现金额不能超过可提现余额";
      // setErrors(newErrors);
      Dialog.alert({
        children: `提现金额不能超过可提现余额`,
        platform: "ios",
        okText: "我知道了",
      })
      return false;
    }

    setErrors({});
    return true;
  };

  // ==================== 事件处理 ====================
    const handleInputFocus = () => {
      setKeyboardVisible(true);
    };

  const handleKeyboardInput = (char: string) => {
    if (char === "delete") {
      setAmount((prev) => prev.slice(0, -1));
    } else if (char === "close") {
      setKeyboardVisible(false);
    } else {
      const newAmount = amount + char;

      if (char === "." && amount.includes(".")) {
        return;
      }

      const parts = newAmount.split(".");
      if (parts.length > 2 || (parts[1] && parts[1].length > 2)) {
        return;
      }

      setAmount(newAmount);

      if (errors.amount) {
        setErrors((prev) => ({ ...prev, amount: "" }));
      }
    }
  };

  const handleWithdrawAll = () => {
    if (pageState.balanceInfo) {
      setAmount((pageState.balanceInfo.availableAmount).toFixed(2));
      if (errors.amount) {
        setErrors((prev) => ({ ...prev, amount: "" }));
      }
    }
  };

  const handleEditAccount = () => {
    // merchantInfo.merchantType 已经是 MerchantType 枚举值，直接使用
    Taro.navigateTo({
      url: `/pageOnlinePayment/onlinePayment/information/editSettlement?merchantType=4`
    });
  };

  const handleConfirmWithdrawal = () => {
    if (!validateForm()) return;
    setKeyboardVisible(false);
    setPageState((prev) => ({ ...prev, showConfirmDialog: true }));
  };

  const handleSubmitWithdrawal = async () => {
    setPageState((prev) => ({
      ...prev,
      submitting: true,
      showConfirmDialog: false,
    }));

    try {
      const amountValue = parseFloat(amount);
      const withdrawalData = {
        userId: Taro.getStorageSync("userInfo")?.id,
        amount: Math.round(amountValue * 100),
      };

      const response = await createWithdrawal(withdrawalData);
      if (response && response.code === 0) {
        Toast.success("提现申请已提交");
        Taro.navigateTo({
          url: `/pageOnlinePayment/wallet/withdrawal/result?status=success&amount=${amountValue}&accountName=${encodeURIComponent(pageState.accountInfo?.accountName || "")}&accountNumber=${encodeURIComponent(formatBankAccount(pageState.accountInfo?.accountNumber || ""))}`,
        });
      } else {
        Toast.error(response.msg || "提现申请失败");
      }
    } catch (error) {
      console.error("提现失败:", error);
      Toast.error("提现申请失败，请稍后重试");
    } finally {
      setPageState((prev) => ({ ...prev, submitting: false }));
    }
  };

  const handleCancelDialog = () => {
    setPageState((prev) => ({ ...prev, showConfirmDialog: false }));
  };


  // ==================== 渲染函数 ====================
  const renderAccountInfo = () => {
    if (!pageState.accountInfo) {
      return (
        <View className="account-info-loading">
          <Loading />
        </View>
      );
    }

    return (
      <View className="account-info">
        <View className="account-info-header">
          <Text className="account-info-label">收款账户</Text>
        </View>
        <View className="account-info-content">
          <Text className="account-name">
            {pageState.accountInfo.accountName}
          </Text>
          <Text className="account-number">
            {pageState.accountInfo.accountNumber}
          </Text>
        </View>
        {/* <View className="account-info-edit" onClick={handleEditAccount}> */}
          <IconEdit className="account-info-edit" onClick={handleEditAccount} />
        {/* </View> */}
      </View>
    );
  };

  const renderAmountInput = () => {
    const availableAmount = pageState.balanceInfo?.availableAmount || 0;

    return (
      <View className="amount-input-section">
        <View className="amount-input-header">
          <Text className="amount-input-label">提现金额</Text>
        </View>

        <View className="amount-input-container" onClick={handleInputFocus}>
          <View className="amount-input-wrapper">
            <Text className="currency-symbol">¥</Text>
            <Input
              className="amount-input"
              placeholder="1元起提"
              value={amount}
              // onFocus={handleInputFocus}
              readOnly
              border="none"
            />
          </View>
        </View>

        {errors.amount && (
          <Text className="error-message">{errors.amount}</Text>
        )}

        <View className="amount-info">
          <Text className="available-amount">
            可提现金额 ¥{availableAmount.toFixed(2)}
          </Text>
          <Text className="withdraw-all" onClick={handleWithdrawAll}>
            全部提现
          </Text>
        </View>
      </View>
    );
  };

  return (
    <View className="withdrawal">
      {platform !== "WX" &&<YkNavBar title="提现" />}

      {pageState.loading ? (
        <View className="loading-container">
          <Loading />
        </View>
      ) : (
        <View className="withdrawal-content">
          {renderAccountInfo()}

          <View className="withdrawal-form">
            {renderAmountInput()}

            <View className="withdrawal-submit">
              <Button
                type="primary"
                size="large"
                loading={pageState.submitting}
                disabled={pageState.submitting || !amount}
                onClick={handleConfirmWithdrawal}
                className={!amount ? "disabled-button" : ""}
              >
                确认提现
              </Button>
            </View>

            <View className="withdrawal-tips">
              <Text className="tips-text">
                建议留存部分资金，用于处理售后退款
              </Text>
            </View>
          </View>
        </View>
      )}

      {/* 确认提现弹窗 - 基于DSL设计 */}
      {pageState.showConfirmDialog && (
        <View className="withdrawal-confirm-modal">
          <View className="modal-mask" onClick={handleCancelDialog}></View>
          <View className="modal-dialog">
            {/* 弹窗标题栏 */}
            <View className="dialog-header">
              <View className="dialog-close-icon" onClick={handleCancelDialog}>
                <IconClose className="close-icon-svg" />
              </View>
              <View className="dialog-title-container">
                <Text className="dialog-title">提现金额</Text>
              </View>
            </View>

            {/* 金额显示区域 */}
            <View className="dialog-amount-container">
              <Text className="dialog-amount-text">¥{amount}</Text>
            </View>

            {/* 手续费信息区域 */}
            <View className="dialog-fee-container">
              <View className="dialog-fee-row">
                <Text className="dialog-fee-label">手续费</Text>
                <Text className="dialog-fee-value">本次提现免费</Text>
              </View>
            </View>

            {/* 确认按钮 */}
            <View className="dialog-button-container">
              <Button
                type="primary"
                size="large"
                loading={pageState.submitting}
                onClick={handleSubmitWithdrawal}
                className="dialog-confirm-button"
              >
                确认提现
              </Button>
            </View>
          </View>
        </View>
      )}

      <Keyboard
        visible={keyboardVisible}
        randomOrder={false}
        close={() => {
          setKeyboardVisible(false);        
          return {};
        }}
        onDelete={() => {
          handleKeyboardInput("delete");
          return {};
        }}
        onChange={(data) => {
          handleKeyboardInput(String(data));
          return {};
        }}
        type="number"
        title={
          <div
            style={{
              fontSize: 13,
              lineHeight: "18px",
              color: "#86909C",
              marginBottom: 8,
              textAlign: "center",
            }}
          >
            安全信息提示
          </div>
        }
      />
    </View>
  );
}
