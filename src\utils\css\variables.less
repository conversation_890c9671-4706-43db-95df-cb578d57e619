// Less 变量定义
@--success-1: #E8FFEA; // green-1 最浅的绿色背景
@--success-2: #DDEDD9; // green-2 浅绿色
@--success-3: #C4E7D0; // green-3 中浅绿色
@--success-6: #00B42A; // green-6 标准成功色

@--danger-1: #FFECE8; // red-1 最浅的红色背景
@--danger-2: #FDCDC5; // red-2 浅红色
@--danger-3: #FBACA3; // red-3 中浅红色
@--danger-6: #F53F3F; // red-6 标准危险色

@--warning-1: #FFF7E8; // orange-1 最浅的橙色背景
@--warning-2: #FDDDC3; // orange-2 浅橙色
@--warning-3: #FCC59F; // orange-3 中浅橙色
@--warning-6: #FF7D00; // orange-6 标准警告色

@--text-1: #f2f3f5; // 文本色-1
@--text-2: #c9cdd4; // 文本色-2
@--text-3: #86909c; // 文本色-3
@--text-4: #4e5969; // 文本色-4
@--text-5: #1d2129; // 文本色-5

// 对应图纸设计，自定义状态色系变量（基于arco design色彩系统）
// 全局 CSS 变量定义
:root {
  // 成功色系
  --success-1: @--success-1;
  --success-2: @--success-2;
  --success-3: @--success-3;
  --success-6: @--success-6;

  // 危险色系
  --danger-1: @--danger-1;
  --danger-2: @--danger-2;
  --danger-3: @--danger-3;
  --danger-6: @--danger-6;

  // 警告色系
  --warning-1: @--warning-1;
  --warning-2: @--warning-2;
  --warning-3: @--warning-3;
  --warning-6: @--warning-6;

  // 文本色系
  --text-1: @--text-1;
  --text-2: @--text-2;
  --text-3: @--text-3;
  --text-4: @--text-4;
  --text-5: @--text-5;
}


