import { View, Text, ScrollView } from '@tarojs/components';
import { useState, useEffect, useRef } from 'react';
import { Input, Button, Avatar, Loading } from '@arco-design/mobile-react';
import Taro from '@tarojs/taro';
import { createSSEClient } from '@/utils/api/request-sse';
import './chat.less';

// 组件
import YkNavBar from '@/components/ykNavBar';

import { IconArrowLeft, IconArrowRight, IconKefuAvatar } from '@/components/YkIcons';
// API
import {
  getAiChatConversationList,
  createAiChatConversation,
  getAiChatMessagePage
} from '@/utils/api/common/common_user';

// 类型定义
import {
  ChatPageState,
  ChatMessage,
  UserInfo,
  MessageType,
  SenderType,
  SSEMessageData
} from './types';

import { toast } from "@/utils/yk-common";

// 兼容多种存储结构的用户信息解析
const parseStoredUserInfo = (raw: any) => {
  if (!raw) return null;
  const nested = raw?.userInfo || raw?.data || {};
  const idRaw = nested.id ?? raw.id ?? nested.userId ?? raw.userId ?? nested.user_id ?? raw.user_id;
  const idNum = Number(idRaw);
  const headImg = nested.head_img || raw.head_img || nested.avatar || raw.avatar || '';
  const nick = nested.nickName || raw.nickName || nested.nickname || raw.nickname || '';
  return {
    id: Number.isFinite(idNum) ? idNum : undefined,
    user_id: Number.isFinite(idNum) ? String(idNum) : '',
    nickName: nick,
    head_img: headImg,
  } as Partial<UserInfo> & { id?: number; head_img?: string };
};

// 调试配置
const debugConfig = {
  moduleName: 'AiChat',
  scenarios: {
    [BaseDebugScenario.BASIC]: {},
    [BaseDebugScenario.API_CALLS]: {},
    [BaseDebugScenario.MOCK_DATA]: {},
    [BaseDebugScenario.LOADING_STATE]: {},
    [BaseDebugScenario.ERROR_HANDLING]: {},
    // 自定义场景
    'sse_test': { description: 'SSE流式接口测试' },
    'keyboard_test': { description: '键盘适配测试' },
    'scroll_test': { description: '滚动功能测试' }
  },
  mockDataGenerators: {
    [BaseDebugScenario.MOCK_DATA]: () => ({
      conversations: [
        { id: 'mock_conv_1', title: '模拟对话1', createTime: Date.now() },
        { id: 'mock_conv_2', title: '模拟对话2', createTime: Date.now() }
      ],
      messages: [
        {
          id: 'mock_msg_1',
          type: MessageType.TEXT,
          content: '您好！我是AI客服，有什么可以帮助您的吗？',
          sender: SenderType.AI,
          timestamp: Date.now() - 60000,
          avatar: ''
        },
        {
          id: 'mock_msg_2',
          type: MessageType.TEXT,
          content: '我想了解一下产品信息',
          sender: SenderType.USER,
          timestamp: Date.now() - 30000,
          avatar: ''
        }
      ]
    }),
    'sse_test': () => ({
      testMessages: [
        '这是第一段回复',
        '这是第二段回复',
        '这是第三段回复，内容比较长一些，用来测试流式输出的效果'
      ]
    })
  }
};


// 时间格式化函数
const formatMessageTime = (timestamp: number) => {
  const date = new Date(timestamp);
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();

  return `${month}月${day}日 ${hour}:${minute.toString().padStart(2, '0')}`;
};

// ==================== 统一系统/欢迎消息构造 ====================
const SYSTEM_MESSAGE_ID = 'system_welcome';
const AI_WELCOME_MESSAGE_ID = 'ai_welcome';

const buildSystemMessage = (): ChatMessage => ({
  id: SYSTEM_MESSAGE_ID,
  type: MessageType.TEXT,
  content: '海胆相册AI小助手 为您服务',
  sender: SenderType.SYSTEM,
  timestamp: Date.now() - 1000,
  avatar: ''
});

const buildAIWelcomeMessage = (): ChatMessage => ({
  id: AI_WELCOME_MESSAGE_ID,
  type: MessageType.TEXT,
  content: '您好，这里是智能客服小海胆，请问您有什么问题需要帮助吗？',
  sender: SenderType.AI,
  timestamp: Date.now(),
  avatar: ''
});

export default function AiChatPage() {
  const inputRef = useRef<any>(null);
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  // ==================== 状态管理 ====================
  const [pageState, setPageState] = useState<ChatPageState>({
    loading: false,
    conversationId: null,
    messages: [],
    inputText: '',
    keyboardHeight: 0,
    sending: false,
    initialized: false,
    isChatting: false
  });

  const [userInfo, setUserInfo] = useState<UserInfo>({
    user_id: '',
    nickName: ''
  });

  // ==================== Refs ====================
  const scrollViewRef = useRef<any>(null);
  const sseClientRef = useRef<any>(null);
  const finalizeTimerRef = useRef<any>(null);

  // ==================== 生命周期 ====================
  useEffect(() => {
    initPage();
    setupKeyboardListener();

    return () => {
      cleanupSSEConnection();
      cleanupKeyboardListener();
    };
  }, []);

  // ==================== 页面初始化 ====================
  const initPage = async () => {
    setPageState(prev => ({ ...prev, loading: true }));

    try {
      // 1. 获取用户信息
      const storageUserInfo = Taro.getStorageSync('userInfo');
      const parsedUser = parseStoredUserInfo(storageUserInfo);
      if (parsedUser) {
        setUserInfo(prev => ({ ...prev, ...parsedUser }));
      }

      // 2. 获取或创建对话
      await initConversation();

      setPageState(prev => ({ ...prev, initialized: true }));
    } catch (error) {
      console.error('页面初始化失败:', error);
      toast("error", {
        content: "网络异常，请重试",
        duration: 2000
      });
    } finally {
      setPageState(prev => ({ ...prev, loading: false }));
    }
  };

  // ==================== 对话初始化 ====================
  const initConversation = async () => {
    try {
      // 获取对话列表
      const conversationResponse = await getAiChatConversationList();

      if (conversationResponse && conversationResponse.code === 0 && conversationResponse.data?.length > 0) {
        // 使用第一个对话
        const conversation = conversationResponse.data[0];
        setPageState(prev => ({ ...prev, conversationId: conversation.id }));

        // 加载对话消息
        await loadMessages(conversation.id);
      } else {
        // 创建新对话
        const createResponse = await createAiChatConversation();

        if (createResponse && createResponse.code === 0) {
          const newConversationId = createResponse.data.id;
          setPageState(prev => ({ ...prev, conversationId: newConversationId }));

          // 新对话时添加系统消息和欢迎消息
          const initialMessages: ChatMessage[] = [buildSystemMessage(), buildAIWelcomeMessage()];
          setPageState(prev => ({ ...prev, messages: initialMessages }));
          setTimeout(scrollToBottom, 100);
        } else {
          throw new Error(createResponse.msg || '创建对话失败');
        }
      }
    } catch (error) {
      console.error('对话初始化失败:', error);
      throw error;
    }
  };

  // ==================== 加载消息 ====================
  const loadMessages = async (conversationId: string) => {
    try {
      const storageUserInfo = Taro.getStorageSync('userInfo');
      const parsedUser = parseStoredUserInfo(storageUserInfo);
      const userIdNum = Number(
        parsedUser?.id ?? parsedUser?.user_id ?? userInfo.user_id
      ) || 0;

      const response = await getAiChatMessagePage({
        pageNo: 1,
        pageSize: 50,
        conversationId: conversationId,
        userId: userIdNum
      });

      if (response && response.code === 0 && response.data?.list) {
        // 服务端已按新在前排序，这里直接反转为旧在前
        const messages: ChatMessage[] = response.data.list
          .slice()
          .reverse()
          .map((msg: any) => ({
            id: msg.id,
            type: MessageType.TEXT,
            content: msg.content,
            sender: msg.type === 'user' ? SenderType.USER : SenderType.AI,
            timestamp: msg.createTime,
            avatar: msg.type === 'user' ? userInfo.head_img : ''
          }));

        // 在消息列表开头插入系统消息；当历史为空时，再插入AI欢迎语
        let resultMessages: ChatMessage[] = messages;

        const hasSystemMessage = resultMessages.some(
          m => m.sender === SenderType.SYSTEM || m.id === SYSTEM_MESSAGE_ID
        );

        if (!hasSystemMessage) {
          resultMessages = [buildSystemMessage(), ...resultMessages];
        }

        if (messages.length === 0) {
          resultMessages = [buildSystemMessage(), buildAIWelcomeMessage()];
        }

        setPageState(prev => ({ ...prev, messages: resultMessages }));
        // 滚动到底部
        setTimeout(scrollToBottom, 100);
      } else {
        // 接口返回异常时，展示默认系统与欢迎消息
        const defaults = [buildSystemMessage(), buildAIWelcomeMessage()];
        setPageState(prev => ({ ...prev, messages: defaults }));
        setTimeout(scrollToBottom, 100);
      }
    } catch (error) {
      console.error('加载消息失败:', error);

      // 失败降级：展示默认系统与欢迎消息
      const defaults = [buildSystemMessage(), buildAIWelcomeMessage()];
      setPageState(prev => ({ ...prev, messages: defaults }));
      setTimeout(scrollToBottom, 100);
    }
  };

  // ==================== 发送消息 ====================
  const sendMessage = async () => {
    const message = pageState.inputText.trim();
    if (!message || pageState.sending || !pageState.conversationId) {
      return;
    }

    // 添加用户消息到界面
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: MessageType.TEXT,
      content: message,
      sender: SenderType.USER,
      timestamp: Date.now(),
      avatar: userInfo.head_img
    };

    setPageState(prev => ({
      ...prev,
      messages: [...prev.messages, userMessage],
      inputText: '',
      sending: true,
      isChatting: true // 设置为聊天状态
    }));

    // 添加AI加载消息
    const loadingMessage: ChatMessage = {
      id: (Date.now() + 1).toString(),
      type: MessageType.TEXT,
      content: '',
      sender: SenderType.AI,
      timestamp: Date.now(),
      avatar: '',
      loading: true
    };

    setPageState(prev => ({
      ...prev,
      messages: [...prev.messages, loadingMessage]
    }));

    scrollToBottom();

    try {
      // 发送流式消息
      await sendStreamMessage(message, String(loadingMessage.id));
    } catch (error) {
      console.error('发送消息失败:', error);

      // 移除加载消息，显示错误
      setPageState(prev => ({
        ...prev,
        messages: prev.messages.filter(msg => msg.id !== loadingMessage.id),
        sending: false
      }));

      toast("error", {
        content: "发送失败",
        duration: 2000
      });
    }
  };

  // ==================== 流式消息发送 ====================
  const sendStreamMessage = async (content: string, loadingMessageId: string) => {

    try {
      // 先清理旧的连接，避免并发或自动重连导致的重复请求
      if (sseClientRef.current) {
        try { sseClientRef.current.close?.(); } catch (_) {}
        sseClientRef.current = null;
      }
      if (finalizeTimerRef.current) {
        try { clearTimeout(finalizeTimerRef.current); } catch (_) {}
        finalizeTimerRef.current = null;
      }

      // 使用通用 SSE 客户端，自动拼接 URL 前缀与签名/鉴权
      const sseClient = createSSEClient('/app-api/ai/chat/message/send-stream', {
        method: 'POST',
        data: {
          conversationId: pageState.conversationId,
          content,
          useContext: true
        },
        contentType: 'json',
        // 发送消息时不需要自动重连，避免断流或读完后再次拉起
        autoReconnect: false,
      });

      sseClientRef.current = sseClient;

      let aiResponseContent = '';
      const scheduleFinalize = () => {
        try { if (finalizeTimerRef.current) clearTimeout(finalizeTimerRef.current); } catch (_) {}
        finalizeTimerRef.current = setTimeout(() => {
          setPageState(prev => ({
            ...prev,
            sending: false,
            messages: prev.messages.map(msg =>
              msg.id === loadingMessageId ? { ...msg, loading: false } : msg
            )
          }));
        }, 1200);
      };

      sseClient.addEventListener('message', (evt: any) => {
        try {
          const payload = evt?.data;
          if (!payload) return;
          if (payload === '[DONE]') {
            sseClient.close?.();
            try { if (finalizeTimerRef.current) clearTimeout(finalizeTimerRef.current); } catch (_) {}
            finalizeTimerRef.current = null;
            setPageState(prev => ({
              ...prev,
              sending: false,
              messages: prev.messages.map(msg =>
                msg.id === loadingMessageId ? { ...msg, loading: false } : msg
              )
            }));
            return;
          }
          const data: SSEMessageData = typeof payload === 'string' ? JSON.parse(payload) : payload;
          if (data && data.code === 0 && data.data?.receive?.content) {
            aiResponseContent += data.data.receive.content;
            setPageState(prev => ({
              ...prev,
              messages: prev.messages.map(msg =>
                msg.id === loadingMessageId
                  ? { ...msg, content: aiResponseContent, loading: true }
                  : msg
              )
            }));
            scrollToBottom();
            scheduleFinalize();
          }
        } catch (e) {
          console.warn('SSE message 解析失败:', e);
        }
      });

      sseClient.addEventListener('error', (_e: any) => {
        sseClient.close?.();
        try { if (finalizeTimerRef.current) clearTimeout(finalizeTimerRef.current); } catch (_) {}
        finalizeTimerRef.current = null;
        setPageState(prev => ({
          ...prev,
          sending: false,
          messages: prev.messages.map(msg =>
            msg.id === loadingMessageId ? { ...msg, loading: false } : msg
          )
        }));
      });

      sseClient.addEventListener('close', () => {
        try { if (finalizeTimerRef.current) clearTimeout(finalizeTimerRef.current); } catch (_) {}
        finalizeTimerRef.current = null;
        setPageState(prev => ({
          ...prev,
          sending: false,
          messages: prev.messages.map(msg =>
            msg.id === loadingMessageId ? { ...msg, loading: false } : msg
          )
        }));
      });

    } catch (error) {
      console.error('SSE连接失败，使用模拟回复:', error);

      // 降级到模拟回复
      setTimeout(() => {
        const aiResponse = `暂时无法回复，请稍后再试!`;

        setPageState(prev => ({
          ...prev,
          messages: prev.messages.map(msg =>
            msg.id === loadingMessageId
              ? { ...msg, content: aiResponse, loading: false }
              : msg
          ),
          sending: false
        }));

        scrollToBottom();
      }, 5000);
    }
  };

  // ==================== 滚动到底部 ====================
  const scrollToBottom = () => {
    setTimeout(() => {
      if (scrollViewRef.current) {
        // 尝试多种滚动方法
        if (scrollViewRef.current.scrollToBottom) {
          scrollViewRef.current.scrollToBottom();
        } else if (scrollViewRef.current.scrollTo) {
          scrollViewRef.current.scrollTo({ top: 999999, animated: true });
        } else {
          // 降级方案：直接操作DOM
          const scrollElement = document.querySelector('.message-list');
          if (scrollElement) {
            scrollElement.scrollTop = scrollElement.scrollHeight;
          }
        }
      }
    }, 100);
  };

  // ==================== 键盘监听 ====================
  const setupKeyboardListener = () => {

    // 参照mobileCustomerServer的键盘监听实现
    if (typeof window !== 'undefined') {
      const handleResize = () => {
        const visualViewportHeight = window.visualViewport?.height || window.innerHeight;
        const windowHeight = window.innerHeight;
        const currentKeyboardHeight = Math.max(0, windowHeight - visualViewportHeight);

        setTimeout(() => {
          setPageState(prev => ({ ...prev, keyboardHeight: currentKeyboardHeight }));

          if (currentKeyboardHeight > 0) {
            setTimeout(scrollToBottom, 200);
          }
        }, 100);
      };

      // 监听视觉视口变化
      if (window.visualViewport) {
        window.visualViewport.addEventListener('resize', handleResize);
        window.visualViewport.addEventListener('scroll', handleResize);
      }

      // 监听窗口大小变化
      window.addEventListener('resize', handleResize);

      // 存储清理函数
      (window as any).__keyboardCleanup = () => {
        if (window.visualViewport) {
          window.visualViewport.removeEventListener('resize', handleResize);
          window.visualViewport.removeEventListener('scroll', handleResize);
        }
        window.removeEventListener('resize', handleResize);
      };
    }
  };

  const cleanupKeyboardListener = () => {
    if (typeof window !== 'undefined' && (window as any).__keyboardCleanup) {
      (window as any).__keyboardCleanup();
      delete (window as any).__keyboardCleanup;
    }
  };

  // ==================== SSE连接清理 ====================
  const cleanupSSEConnection = () => {
    if (sseClientRef.current) {
      sseClientRef.current.close?.();
      sseClientRef.current = null;
    }
    if (finalizeTimerRef.current) {
      try { clearTimeout(finalizeTimerRef.current); } catch (_) {}
      finalizeTimerRef.current = null;
    }
  };

  // ==================== 事件处理 ====================
  const handleBack = () => {
    Taro.navigateBack({ delta: 1 });
  };

  const handleInputChange = (_e: any, value: string) => {
    setPageState(prev => ({ ...prev, inputText: value }));
  };

  const handleKeyPress = (e: any) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // ==================== 时间显示逻辑 ====================
  const shouldShowTime = (index: number): boolean => {
    if (index === 0) return true;
    const prev = pageState.messages[index - 1];
    const curr = pageState.messages[index];
    if (!prev || !curr) return false;
    const prevDate = new Date(prev.timestamp);
    const currDate = new Date(curr.timestamp);
    const isDifferentDay = prevDate.toDateString() !== currDate.toDateString();
    const diffMs = Math.abs(curr.timestamp - prev.timestamp);
    const moreThanTenMin = diffMs > 10 * 60 * 1000;
    return isDifferentDay || moreThanTenMin;
  };

  // ==================== 渲染消息 ====================
  const renderMessage = (message: ChatMessage, index: number) => {
    const isUser = message.sender === SenderType.USER;
    const isSystem = message.sender === SenderType.SYSTEM;
    const isAI = message.sender === SenderType.AI;

    // 时间显示：仅首条或超过阈值的分组
    const showTime = shouldShowTime(index);

    return (
      <View key={message.id} className="message-item">
        {/* 消息时间 */}
        {showTime && (
          <View className="message-time">
            {formatMessageTime(message.timestamp)}
          </View>
        )}

        {/* 系统消息 */}
        {isSystem && (
          <View className="system-message">
            <Text className="system-text">{message.content}</Text>
          </View>
        )}

        {/* 用户和AI消息 */}
        {!isSystem && (
          <View className={`message-content ${isUser ? 'user-message' : 'ai-message'}`}>
            {isAI && (
              <Avatar
                className="message-avatar"
                shape="square"
                size="small"
              >
                <IconKefuAvatar className='kefu-avatar'/>
              </Avatar>
            )}
            {isUser && (
              <Avatar
                src={message.avatar || userInfo.head_img}
                className="message-avatar"
                shape="square"
                size="small"
              />
            )}
            <View className="bubble-wrapper">
              <View className="message-bubble">
              {/* <Loading type="dot" color="#606a78" list={[0.1, 0.3, 0.5]} /> */}
                {
                  message.loading ? <Loading type="dot" color="#606a78" list={[0.1, 0.3, 0.5]} /> : <Text className="message-text">{(isAI && message.loading && !message.content) ? '  ' : message.content}</Text>
                }
                {/* <Text className="message-text">{(isAI && message.loading && !message.content) ? '  ' : message.content}</Text> */}
              </View>
              {isAI ? (
                <IconArrowLeft className="bubble-arrow left" color={'currentColor'} size={16} />
              ) : (
                <IconArrowRight className="bubble-arrow right" color={'currentColor'} size={16} />
              )}
            </View>
            <View className="avatar-placeholder" />
          </View>
        )}
      </View>
    );
  };

  // ==================== 渲染 ====================
  if (pageState.loading) {
    return (
      <View className="ai-chat-page">
        {platform !== "WX" &&<YkNavBar title="海胆专属客服" onClickLeft={handleBack} />}
        <View className="loading-container">
          <Loading />
          <Text>加载中...</Text>
        </View>
      </View>
    );
  }

  return (
    <View className="ai-chat-page">
      {platform !== "WX" &&<YkNavBar
        title={pageState.isChatting ? "正在和智能客服对话中-海胆客服" : "海胆专属客服"}
        onClickLeft={handleBack}
      />}

      {/* 消息列表 */}
      <View className="chat-container">
        <ScrollView
          ref={scrollViewRef}
          className="message-list"
          scrollY
          scrollIntoView=""
          enableBackToTop
          scrollWithAnimation
          enhanced
          showScrollbar={false}
          onScrollToLower={() => {
          }}
          onScroll={(e) => {
            // 可以在这里处理滚动事件，比如加载更多历史消息
            const { scrollTop } = e.detail;
            if (scrollTop === 0 && pageState.messages.length > 0) {
            }
          }}
        >
          {pageState.messages.length === 0 ? (
            <View className="empty-messages">
              <Text>开始与AI客服对话吧！</Text>
            </View>
          ) : (
            pageState.messages.map((message, index) => renderMessage(message, index))
          )}
          {/* 占位，避免底部输入框遮挡内容 */}
          <View className="holder" />
        </ScrollView>
      </View>

      {/* 输入框 */}
      <View
        className="input-container"
        style={{
          bottom: pageState.keyboardHeight > 0 ? `${pageState.keyboardHeight}px` : '0px'
        }}
      >
        {/* <View className="input-wrapper">
          <Input
            className="message-input"
            placeholder="请输入您的问题..."
            value={pageState.inputText}
            onChange={handleInputChange}
            onKeyUp={handleKeyPress}
            maxLength={500}
            disabled={pageState.sending}
          />
          <Button
            type="primary"
            size="small"
            className={`send-button ${pageState.inputText.trim() ? 'active' : ''}`}
            onClick={sendMessage}
            loading={pageState.sending}
            disabled={!pageState.inputText.trim() || pageState.sending}
          >
            发送
          </Button>
        </View> */}
        <Input
          placeholder="请输入内容"
          type="text"
          className="input-wrapper"
          inputClass="message-input"
          // clearable
          suffix={
              <Button
                inline
                className={`send-button ${pageState.inputText.trim() ? 'active' : ''}`}
                shape="round"
                size="medium"
                onClick={sendMessage}
                // loading={pageState.sending}
                disabled={!pageState.inputText.trim() || pageState.sending}
              >
                发送
              </Button>
          }
          border="none"
          value={pageState.inputText}
          onChange={handleInputChange}
          onKeyUp={handleKeyPress}
          ref={inputRef as any}
        />
      </View>
    </View>
  );
}