import { View, Text } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import { useRef } from "react";
import { Input, Button, CountDown, Dialog } from "@arco-design/mobile-react";
import React from "react";
import Taro from "@tarojs/taro";
import {
  bindingPhone,
  getSmsCode,
  replacePhone,
  updateUserMobile,
} from "@/utils/api/common/common_user";
import { toast } from "@/utils/yk-common";
import { useEffect, useState } from "react";
// 组件
import YkNavBar from "@/components/ykNavBar/index";

export default function bindMobilePhone() {
 
  // 用户信息
  const [userInfo, setUserInfo] = React.useState<any>(false); // 类型：any[]
  // 旧手机号
  const [oldPhone, setOldPhone] = React.useState("");
  // 手机号
  const [phone, setPhone] = React.useState("");
  // 旧验证码
  const [oldCode, setOldCode] = React.useState("");
  //验证码倒计时
  const [oldCountDown, setOldCountDown] = React.useState(60);
  // 是否开启倒计时
  const [oldAutoStart, setOldAutoStart] = React.useState(false);
  // 验证码
  const [code, setCode] = React.useState("");
  //验证码倒计时
  const [countDown, setCountDown] = React.useState(60);
  // 是否开启倒计时
  const [autoStart, setAutoStart] = React.useState(false);
  // 是否已绑定手机号
  const [isBindPhone, setIsBindPhone] = React.useState(false); // 类型：boolean[]
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []); 
  // 倒计时结束
  const onFinishCountDown = () => {
    setAutoStart(false);
    setCountDown(60);
  };
  const onFinishOldCountDown = () => {
    setOldAutoStart(false);
    setOldCountDown(60);
  };
  

  // 获取验证码
  const getCode = (phoneVal) => {
    if (phoneVal.length !== 11) {
      toast("error", {
        content: "请输入正确的手机号",
        duration: 2000,
      });
      return;
    }
    // if (
    //   userInfo.unionid == "" ||
    //   !userInfo.unionid ||
    //   userInfo.unionid == undefined
    // ) {
    //   Taro.showToast({
    //     title: "请先绑定微信号",
    //     icon: "none",
    //   });
    //   return;
    // }
    setAutoStart(true);
    let data = {
      mobile: phoneVal,
      scene: 2, // 2: 修改/绑定手机号
    };
    getSmsCode(data)
      .then((res: any) => {
        if (res && res.code == 0) {
          toast("success", {
            content: "验证码发送成功",
            duration: 2000,
          });
        } else {
          toast("error", {
            content: res.msg,
            duration: 2000,
          });
        }
      })
      .catch((err) => {
        toast("error", {
          content: err,
          duration: 2000,
        });
      });
  };

  // 初次绑定手机号
  const handleConfirmBind = () => {
    if (phone.length !== 11) {
      toast("error", {
        content: "请输入正确的手机号",
        duration: 2000,
      });
      return;
    }
    if (code.length < 4) {
      toast("error", {
        content: "请输入正确的验证码",
        duration: 2000,
      });
      return;
    }
    if (
      userInfo.unionid == "" ||
      !userInfo.unionid ||
      userInfo.unionid == undefined
    ) {
      toast("error", {
        content: "请先绑定微信号",
        duration: 2000,
      });
      return;
    }
    let data = {
      phone: phone,
      code: code,
      unionid: userInfo.unionid,
    };
    bindingPhone(data)
      .then((res: any) => {
        if (res && res.code == 0) {
          toast("success", {
            content: "绑定成功",
            duration: 2000,
          });
          setCode("");
          setPhone("");
          setAutoStart(false);
          setCountDown(60);
          let _window: any = window;
          _window.modalInstance = Dialog.alert({
            children: `已成功绑定手机号：${phone}`,
            platform: "ios",
            okText: "我知道了",
          });
        } else {
          toast("error", {
            content: res.msg,
            duration: 2000,
          });
        }
      })
      .catch((err) => {
        toast("error", {
          content: err,
          duration: 2000,
        });
      });
  };

  const handleUpdateUserMobile = () => {
    if (phone.length !== 11) {
      toast("error", {
        content: "请输入正确的手机号",
        duration: 2000,
      });
      return;
    }
    if (code.length < 4) {
      toast("error", {
        content: "请输入正确的验证码",
        duration: 2000,
      });
      return;
    }
    let data = {
      mobile: phone,
      code: code,
    };
    updateUserMobile(data).then((res: any) => {
      if (res && res.code == 0) {
        toast("success", {
          content: "更换成功",
          duration: 2000,
        });

        Taro.setStorageSync("userInfo", {
          ...userInfo,
          mobile: phone,
        });

        setCode("");
        setPhone("");
        setAutoStart(false);
        setCountDown(60);
        let _window: any = window;
        _window.modalInstance = Dialog.alert({
          children: `已成功更换绑定手机号：${phone}`,
          platform: "ios",
          okText: "我知道了",
        });
      } else {
        toast("error", {
          content: res.msg,
          duration: 2000,
        });
      }
    }).catch((err) => {
      toast("error", {
        content: err,
        duration: 2000,
      });
    });
  }

  // 更换绑定手机号
  const handleChangeBind = () => {
    if (oldPhone.length !== 11 || oldPhone != userInfo.mobile) {
      toast("error", {
        content: "请输入正确的旧手机号",
        duration: 2000,
      });
      return;
    }
    if (phone.length !== 11) {
      toast("error", {
        content: "请输入正确的手机号",
        duration: 2000,
      });
      return;
    }
    if (phone == oldPhone) {
      toast("error", {
        content: "新手机号不能与旧手机号相同",
        duration: 2000,
      });
    }
    if (oldCode.length < 4) {
      toast("error", {
        content: "请输入旧手机号的正确的验证码",
        duration: 2000,
      });
      return;
    }
    if (code.length < 4) {
      toast("error", {
        content: "请输入正确的验证码",
        duration: 2000,
      });
      return;
    }
    if (
      userInfo.unionid == "" ||
      !userInfo.unionid ||
      userInfo.unionid == undefined
    ) {
      toast("error", {
        content: "请先绑定微信号",
        duration: 2000,
      });
      return;
    }
    let data = {
      phone: oldPhone,
      code: oldCode,
      new_phone: phone,
      new_code: code,
    };
    replacePhone(data).then((res: any) => {
      if (res && res.code == 0) {
        setCode("");
        setPhone("");
        setAutoStart(false);
        setCountDown(60);
        setOldCode("");
        setOldPhone("");
        setOldAutoStart(false);
        setOldCountDown(60);
        let _window: any = window;
        _window.modalInstance = Dialog.alert({
          children: `已成功更换绑定手机号：${phone}`,
          platform: "ios",
          okText: "我知道了",
        });
      } else {
        toast("error", {
          content: res.msg,
          duration: 2000,
        });
      }
    }).catch((err) => {
      toast("error", {
        content: err,
        duration: 2000,
      });
    });
  };

  useLoad(() => {
    // 获取用户信息
    let info: any = Taro.getStorageSync("userInfo");
    console.log("info", info);
    setUserInfo(info);
    setIsBindPhone(info.mobile != "" && info.mobile != undefined);
  });

  return (
    <View className="bindMobilePageContent">
      {platform !== "WX" &&<YkNavBar title="绑定手机号" />}
      <View className="pageDescribe">
        <Text className="pageDescribe-title">
          {isBindPhone ? "手机号换绑" : "绑定手机号"}
        </Text>
        <Text className="pageDescribe-desc">
          绑定手机号后即可使用手机号登录
        </Text>
      </View>
      <View className="inputBox">
        {isBindPhone &&
          false && ( // 暂时隐藏旧手机号校验
            <>
              <Input
                label="旧手机号"
                placeholder="请输入旧手机号"
                clearable
                border="none"
                onChange={(_, value) => setOldPhone(value)}
                onClear={() => setOldPhone("")}
                value={oldPhone}
              />
              <Input
                label="验证码"
                placeholder="请输入验证码"
                validator={(val) => val.length <= 8}
                className="demo-input-btn-input"
                clearable
                value={oldCode}
                onChange={(_, value) => setOldCode(value)}
                onClear={() => setOldCode("")}
                border="none"
                suffix={
                  !autoStart ? (
                    <Button
                      inline
                      size="mini"
                      type="ghost"
                      onClick={() => getCode(oldPhone)}
                    >
                      获取验证码
                    </Button>
                  ) : (
                    <CountDown
                      millisecond
                      format="ss"
                      time={{
                        days: 0,
                        hours: 0,
                        minutes: 0,
                        seconds: oldCountDown,
                        milliseconds: 0,
                      }}
                      autoStart={oldAutoStart}
                      onFinish={onFinishOldCountDown}
                      renderChild={(timeData) => (
                        <Button inline size="mini" type="ghost" disabled>
                          重新获取({timeData.seconds}s)
                        </Button>
                      )}
                    />
                  )
                }
              />
            </>
          )}
        <Input
          label="手机号"
          placeholder="请输入手机号"
          clearable
          border="none"
          onChange={(_, value) => setPhone(value)}
          onClear={() => setPhone("")}
          value={phone}
        />
        <Input
          label="验证码"
          placeholder="请输入验证码"
          validator={(val) => val.length <= 8}
          className="demo-input-btn-input"
          clearable
          value={code}
          onChange={(_, value) => setCode(value)}
          onClear={() => setCode("")}
          border="none"
          suffix={
            !autoStart ? (
              <Button
                inline
                size="mini"
                type="ghost"
                onClick={() => getCode(phone)}
              >
                获取验证码
              </Button>
            ) : (
              <CountDown
                millisecond
                format="ss"
                time={{
                  days: 0,
                  hours: 0,
                  minutes: 0,
                  seconds: countDown,
                  milliseconds: 0,
                }}
                autoStart={autoStart}
                onFinish={onFinishCountDown}
                renderChild={(timeData) => (
                  <Button inline size="mini" type="ghost" disabled>
                    重新获取({timeData.seconds}s)
                  </Button>
                )}
              />
            )
          }
        />
      </View>
      <View className="confirmBtnBox">
        {/* {isBindPhone ? (
          <Button
            needActive
            onClick={() => handleChangeBind()}
            disabled={phone.length < 11 || code.length < 4}
          >
            更换绑定
          </Button>
        ) : (
          <Button
            needActive
            onClick={() => handleConfirmBind()}
            disabled={phone.length < 11 || code.length < 4}
          >
            绑定
          </Button>
        )} */}
        <Button
          needActive
          onClick={() => handleUpdateUserMobile()}
          disabled={phone.length < 11 || code.length < 4}
        >
          绑定
        </Button>
      </View>
    </View>
  );
}
