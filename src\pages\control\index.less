@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pages/control/index"] {
  /* 在这里设置样式 */
  background-color: #f8f9fa !important;

  .setPageContent-navbar {
    width: 100%;
    height: 44px;
    background-color: #1b1d2e;
    .arco-nav-bar-title-text {
      font-size: 17px !important;
      color: #f9dec6;
    }
  }

  .viewAll-container {
    display: flex;
    align-items: center;
    justify-content: center;
    
    .viewAll-icon {
      margin-left: 4px;
      display: flex;
      align-items: center;
    }
  }

  .orderStatsBox {
    background: var(--container-background-color);
    .use-dark-mode-query({
    background: var(--dark-container-background-color);
  });
    margin: 16px;
    border-radius: 8px;
    padding: 16px;
    top: -50px;
    position: relative;

    .orderStatsTitle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      color: var(--font-color);
      font-size: 16px;
      font-weight: bold;
      .use-dark-mode-query({
      color: var(--dark-font-color);
    });

      .orderStatsTitleItem {
        display: flex;
        align-items: center;
        justify-content: center;
        .orderStatsTitleItemRight {
          display: flex;
          align-items: center;
          margin-left: 10px;
          font-size: 11px;
          font-weight: normal;
          color: var(--sub-info-font-color);
          .use-dark-mode-query({
          color: var(--dark-sub-info-font-color);
        });

        
          .viewAll {
            color: var(--sub-info-font-color);
          }
        }
      }

      .viewAll {
        color: var(--sub-info-font-color);
        .use-dark-mode-query({
        color: var(--dark-sub-info-font-color);
      });
        font-size: 14px;
      }
    }

    .orderStatsContent {
      display: flex;
      justify-content: space-between;

      .statsItem {
        display: flex;
        flex-direction: column;
        align-items: center;

        .number {
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 4px;
          color: var(--font-color);
          .use-dark-mode-query({
          color: var(--dark-font-color);
        });
        }

        .label {
          font-size: 12px;
          color: var(--sub-info-font-color);
          .use-dark-mode-query({
          color: var(--dark-sub-info-font-color);
        });
        }
      }
    }
  }

  .albumBox {
    background: var(--container-background-color);
    .use-dark-mode-query({
    background: var(--dark-container-background-color);
  });
    margin: 16px;
    border-radius: 8px;
    padding: 16px;
    top: -50px;
    position: relative;

    .albumBoxTitle {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 16px;
      color: var(--font-color);
      .use-dark-mode-query({
      color: var(--dark-font-color);
    });
    }

    &-grid {
      &-icon {
        font-size: 36px;
        width: 36px;
        height: 36px;

        // color: #f9dec6;
        // .use-dark-mode-query({
        //   color: var(--dark-font-color);
        // });
      }

      &-text {
        font-size: 12px;
        // color: #f9dec6;
        color: var(--sub-info-font-color);
        .use-dark-mode-query({
        color: var(--dark-sub-info-font-color);
      });
        margin-top: 8px;
      }
    }
  }

  .indexBox {
    // background-color: var(--container-background-color);
    min-height: 100vh;

    // .use-dark-mode-query({
    //   background-color: var(--dark-container-background-color);
    // });
  }

  // .setPageContent-navbar {
  //   width: 100%;
  //   height: 44px;
  //   background-color: #1b1d2e;
  //   .arco-nav-bar-title-text {
  //     font-size: 17px !important;
  //     color: #f9dec6;
  //   }
  // }

  .gridModeBoxContent {
    background-color: #1b1d2e;
    height: 174px;
    padding: 15px 0;
    color: #f9dec6;
    align-items: center;
    display: flex;

    &-grid {
      background-color: #1b1d2e;

      &-icon {
        // font-size: 24px;
        width: 30px;
        height: 30px;
        color: #f9dec6;
        // .use-dark-mode-query({
        //   color: var(--dark-font-color);
        // });
      }

      &-text {
        font-size: 12px;
        color: #f9dec6;
        // color: var(--sub-info-font-color);
        // .use-dark-mode-query({
        //   color: var(--dark-sub-info-font-color);
        // });
        margin-top: 8px;
      }
    }
  }
}
