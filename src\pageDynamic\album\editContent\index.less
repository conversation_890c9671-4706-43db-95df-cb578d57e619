  @import "@arco-design/mobile-react/style/mixin.less";
.edit-content-page {
  overflow-y: auto;
  height: 100vh;
  background-color: #ffffff;
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });
}

.hidden-style {
  opacity: 0;
}

.box-content {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 7px;
  margin: 10px;
  padding: 15px;
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });
}

.type {
  display: flex;
  border-radius: 5px;
  background-color: #f7f5f5;
  padding: 5px;
  .use-dark-mode-query({
    background-color: #2a2a2a;
  });
  
  &-item0 {
      display: flex;
      padding: 5px 0;
      text-align: center;
      flex: 1;
  
      &-text {
        padding: 5px 0;
        flex: 1;
        font-size: 13px;
        color: #333333;
        .use-dark-mode-query({
          color: var(--dark-font-color);
        });
      }
  
      &-textA {
        padding: 5px 0;
        flex: 1;
        font-size: 13px;
        color: #165DFF;
        background-color: #ffffff;
        border-radius: 5px;
        .use-dark-mode-query({
          background-color: #1a1a1a;
        });
      }
    }

  &-item1 {
    display: flex;
    padding: 5px 0;
    text-align: center;
    flex: 1;

    &-text {
      padding: 5px 0;
      flex: 1;
      font-size: 13px;
      color: #333333;
      .use-dark-mode-query({
        color: var(--dark-font-color);
      });
    }

    &-textA {
      padding: 5px 0;
      flex: 1;
      font-size: 13px;
      color: #165DFF;
      background-color: #ffffff;
      border-radius: 5px;
      .use-dark-mode-query({
        background-color: #1a1a1a;
      });
    }
  }

  &-item2 {
    display: flex;
    padding: 5px 0;
    text-align: center;
    flex: 1;

    &-text {
      padding: 5px 0;
      flex: 1;
      font-size: 13px;
      color: #333333;
      .use-dark-mode-query({
        color: var(--dark-font-color);
      });
    }

    &-textA {
      padding: 5px 0;
      flex: 1;
      font-size: 13px;
      color: #165DFF;
      background-color: #ffffff;
      border-radius: 5px;
      .use-dark-mode-query({
        background-color: #1a1a1a;
      });
    }
  }

  &-item3 {
    display: flex;
    padding: 5px 0;
    text-align: center;
    flex: 1;

    &-text {
      padding: 5px 0;
      flex: 1;
      font-size: 13px;
      color: #333333;
      .use-dark-mode-query({
        color: var(--dark-font-color);
      });
    }

    &-textA {
      padding: 5px 0;
      flex: 1;
      font-size: 13px;
      color: #165DFF;
      background-color: #ffffff;
      border-radius: 5px;
      .use-dark-mode-query({
        background-color: #1a1a1a;
      });
    }
  }
}

.item1 {
  display: flex;
  flex-direction: column;
  &-item {
    border-bottom: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-bottom: 1px solid @dark-line-color;
  });
    display: flex;
    align-items: center;

    &-oldInput {
      flex: 1;
      font-size: 15px;
      color: #333333;
      .use-dark-mode-query({
        color: var(--dark-font-color);
        background-color: @dark-background-color;
      });
    }

    &-text {
      margin: 0 15px;
      font-size: 15px;
      color: #333333;
      .use-dark-mode-query({
        color: var(--dark-font-color);
      });
    }

    &-newInput {
      text-align: right;
      flex: 1;
      font-size: 15px;
      color: #333333;
      .use-dark-mode-query({
        color: var(--dark-font-color);
        background-color: @dark-background-color;
      });
    }
  }
}

.more {
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  &-text {
    font-size: 14px;
    color: #666666;
    margin-right: 5px;
    .use-dark-mode-query({
      color: var(--dark-font-color);
    });
  }

  &-img {
    width: 12px;
    height: 12px;
  }
}

.item2 {
  margin-top: 15px;
  display: flex;
  flex-direction: column;

  &-title {
    font-weight: bold;
    font-size: 15px;
    color: #000000;
    margin-bottom: 10px;
    .use-dark-mode-query({
      color: var(--dark-font-color);
    });
  }

  &-textarea {
    border-radius: 7px;
    background-color: #f7f5f5;
    .use-dark-mode-query({
      background-color: #2a2a2a;
    });

    &-text {
      width: 100%;
      padding: 10px;
      box-sizing: border-box;
      color: #333333;
      font-size: 14px;
      border: none;
      background: transparent;
      resize: none;
      .use-dark-mode-query({
        color: var(--dark-font-color);
      });
    }
  }
}

.item3 {
  margin-top: 15px;
  display: flex;
  flex-direction: column;

  &-title {
    font-weight: bold;
    font-size: 15px;
    color: #000000;
    margin-bottom: 10px;
    .use-dark-mode-query({
      color: var(--dark-font-color);
    });
  }

  &-content {
    display: flex;

    &-item1 {
      display: flex;
      align-items: center;

      &-text {
        margin-left: 5px;
        font-size: 13px;
        color: #999999;
        .use-dark-mode-query({
          color: @dark-font-color;
        });
      }
    }

    &-item2 {
      margin-left: 20px;
      display: flex;
      align-items: center;

      &-text {
        margin-left: 5px;
        font-size: 13px;
        color: #999999;
        .use-dark-mode-query({
          color: @dark-font-color;
        });
      }
    }
  }

  &-textarea {
    margin: 10px 0;
    border-radius: 7px;
    background-color: #f7f5f5;
    .use-dark-mode-query({
      background-color: #2a2a2a;
    });

    &-text {
      width: 100%;
      padding: 10px;
      box-sizing: border-box;
      color: #333333;
      font-size: 14px;
      border: none;
      background: transparent;
      resize: none;
      .use-dark-mode-query({
        color: var(--dark-font-color);
      });
    }
  }
}

.holder {
  height: 60px;
}

.footerbtn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 10px 15px;
  border-top: 1px solid var(--line-color);
  .use-dark-mode-query({
    background-color: @dark-background-color;
    border-top: 1px solid @dark-line-color;
  });

  &-btn {
    width: 100%;
    height: 40px;
    background-color: var(--primary-color);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    text {
      color: #ffffff;
      font-size: 16px;
      font-weight: bold;
    }

    &:active {
      background-color: var(--primary-color);
      opacity: 0.8;
    }
  }
}

.clickOpacity {
  cursor: pointer;
  transition: opacity 0.2s;

  &:active {
    opacity: 0.7;
  }
}

.clickBg {
  cursor: pointer;
  transition: background-color 0.2s;
}
