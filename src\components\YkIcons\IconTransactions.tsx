import { IconProps } from "./types";
const IconTransactions: React.FC<IconProps> = ({
  color = 'var(--primary-color)',
  size = 20,
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    fill="none"
    version="1.1"
    width={size}
    height={size}
    viewBox="0 0 20 20"
    className={className}
  >
    <defs>
      <clipPath id="master_svg0_450_83604/3_69164/3_38199/3_36131">
        <rect x="0" y="0" width="20" height="20" rx="0" />
      </clipPath>
    </defs>
    <g clipPath="url(#master_svg0_450_83604/3_69164/3_38199/3_36131)">
      <g>
        <g>
          <path
            d="M3.3234132538604735,14.611866507720947L2.8873492538604735,14.611866507720947C2.5692832538604735,14.620666507720948,2.272226253860474,14.457066507720947,2.1148770538604738,14.186566507720947C2.0558853538604738,14.089566507720948,2.0893671438604735,13.909066507720947,2.1360024538604736,13.786566507720947C2.215721253860474,13.572166507720947,2.4074432538604738,13.438866507720947,2.6123202538604735,13.337566507720947C2.671868253860474,13.312666507720948,2.7361052538604738,13.300266507720947,2.8008542538604737,13.301266507720948C2.971850253860474,13.296566507720947,3.1424432538604736,13.301266507720948,3.3190232538604736,13.301266507720948L3.3190232538604736,10.799276507720947C3.0866432538604736,10.799276507720947,2.8694122538604736,10.796156507720948,2.6525782538604736,10.799276507720947C2.4182052538604735,10.803566507720948,2.2790972538604737,10.682326507720948,2.1969872538604736,10.482336507720948C2.1539389538604734,10.377856507720947,2.0913600338604734,10.270656507720947,2.0865769038604736,10.162666507720948C2.075416303860474,9.910056507720947,2.4074432538604738,9.595066507720947,2.672109253860474,9.586876507720948C2.8446992538604734,9.581416507720947,3.0184852538604736,9.591556507720949,3.190283253860474,9.575956507720948C3.2409032538604734,9.571676507720948,3.326993253860474,9.505006507720946,3.327393253860474,9.466416507720947C3.3353632538604736,8.681666507720948,3.333373253860474,7.896926507720948,3.333373253860474,7.088396507720947C3.1862932538604736,7.088396507720947,3.054359253860474,7.076316507720947,2.9252152538604737,7.090736507720948C2.6737032538604737,7.118416507720947,2.4752042538604737,6.993276507720947,2.2822862538604736,6.875936507720947C2.0606685538604737,6.741836507720947,2.0363544538604734,6.447116507720947,2.160316553860474,6.231146507720947C2.359612253860474,5.883796507720947,2.6493892538604737,5.7407265077209475,3.0459882538604734,5.774636507720947C3.1304932538604735,5.781656507720947,3.216583253860474,5.774636507720947,3.3250032538604737,5.774636507720947C3.3289932538604736,5.687316507720947,3.3353632538604736,5.6124665077209475,3.3353632538604736,5.537616507720948C3.3353632538604736,4.745076507720947,3.3194232538604735,3.9521465077209474,3.3445332538604737,3.1595965077209476C3.356973253860474,2.8549065077209472,3.420813253860474,2.5543215077209473,3.5334632538604733,2.2699875077209475C3.595643253860474,2.1109335077209472,3.7905632538604737,1.9998295077209471,3.9320632538604734,1.8723515077209472C4.191143253860474,1.6364982077209473,4.516393253860473,1.6680751577209472,4.832483253860474,1.6680751577209472Q10.661093253860473,1.6680751577209472,16.490133253860474,1.6711938677209472C16.715733253860474,1.6711938677209472,16.958033253860474,1.6809398077209472,17.16373325386047,1.7589076077209473C17.442733253860474,1.8653335077209472,17.649233253860473,2.067660507720947,17.795133253860474,2.3545815077209475C17.951333253860476,2.6609955077209473,17.905933253860475,2.9650665077209473,17.906333253860474,3.270706507720947C17.911533253860476,7.838056507720947,17.906333253860474,12.405066507720948,17.916633253860475,16.97236650772095C17.916633253860475,17.362266507720946,17.818633253860476,17.701366507720948,17.557933253860476,17.97546650772095C17.356233253860474,18.188666507720946,17.101533253860474,18.332966507720947,16.769533253860473,18.331366507720947C14.996133253860474,18.321966507720948,13.222033253860474,18.327066507720946,11.449113253860475,18.327066507720946C9.163983253860474,18.327066507720946,6.878853253860473,18.319666507720946,4.593323253860474,18.333266507720946C4.1572632538604735,18.336066507720947,3.8878132538604735,18.130566507720946,3.6239432538604737,17.83666650772095C3.4820432538604735,17.67836650772095,3.4645132538604737,17.509566507720947,3.4246532538604737,17.324766507720945C3.2393032538604736,16.484266507720946,3.3808032538604733,15.635266507720948,3.3349632538604737,14.790866507720947C3.330983253860474,14.741766507720948,3.3277932538604738,14.690666507720946,3.3234132538604735,14.611866507720947ZM4.669453253860474,10.011796507720947L4.669453253860474,16.600066507720946C4.669453253860474,16.702566507720945,4.6582932538604735,16.815666507720948,4.697353253860474,16.90536650772095C4.779463253860474,17.09436650772095,4.9612232538604735,17.110366507720947,5.148963253860474,17.109966507720948Q10.637973253860473,17.10766650772095,16.127433253860474,17.109966507720948C16.516833253860476,17.109966507720948,16.655533253860476,16.977466507720948,16.655533253860476,16.59736650772095L16.655533253860476,3.424296507720947C16.655533253860476,3.112426507720947,16.434333253860473,2.8945065077209473,16.115033253860474,2.8945065077209473L5.176463253860474,2.8945065077209473C4.8145432538604735,2.8945065077209473,4.6622832538604735,3.0274465077209474,4.663873253860474,3.3903865077209474C4.674233253860473,5.594536507720948,4.669453253860474,7.802976507720947,4.669453253860474,10.011406507720947L4.669453253860474,10.011796507720947Z"
            fill={color}
            fillOpacity="1"
          />
        </g>
        <g>
          <path
            d="M11.254966574172974,12.128619553527832L11.254966574172974,13.43847955352783C11.254966574172974,13.737489553527832,11.231056574172975,14.038439553527832,11.259746574172974,14.335109553527833C11.314756574172973,14.906619553527833,10.658276574172973,15.149879553527832,10.276426574172973,14.924549553527832C10.108106574172973,14.830689553527833,10.005666574172974,14.654389553527832,10.009366574172974,14.464929553527831Q10.009366574172974,13.412359553527832,10.005776574172973,12.359799553527832L10.005776574172973,12.131739553527833C9.907326574172973,12.126279553527832,9.823226574172974,12.118099553527832,9.739516574172974,12.117709553527831C9.201416574172974,12.117709553527831,8.663316574172974,12.102509553527831,8.125619574172974,12.123169553527832C7.841822574172974,12.134079553527833,7.619806574172974,12.028049553527833,7.408154574172974,11.876399553527833C7.196502074172973,11.724749553527833,7.180956874172973,11.393389553527832,7.311295974172974,11.174689553527832C7.489865574172974,10.877239553527833,7.765292574172974,10.810189553527831,8.073005574172974,10.805509553527832C8.624256574172973,10.796929553527832,9.175516574172974,10.805509553527832,9.726766574172974,10.802389553527831C9.811666574172975,10.802389553527831,9.896966574172973,10.795379553527832,9.993426574172974,10.791089553527833L9.993426574172974,9.596619553527832C9.913706574172974,9.591939553527832,9.830396574172973,9.583749553527833,9.747096574172973,9.583369553527831C9.182686574172974,9.583369553527831,8.618276574172974,9.579859553527832,8.053873574172973,9.583369553527831C7.829066574172973,9.583369553527831,7.627778574172973,9.544379553527833,7.442831574172974,9.405989553527832C7.213242274172973,9.235239553527832,7.1773688741729735,8.929999553527832,7.310099774172974,8.678159553527832C7.469536574172974,8.376419553527832,7.722642574172974,8.312879553527832,8.021985574172973,8.309369553527832C8.360786574172973,8.305079553527833,8.699596574172974,8.309369553527832,9.038396574172975,8.307029553527832C9.118586574172973,8.302279553527832,9.198436574172973,8.293039553527832,9.277546574172973,8.279349553527831C9.027636574172973,8.018159553527832,8.799236574172973,7.769049553527832,8.560086574172974,7.531249553527832C8.360786574172973,7.336329553527833,8.137577574172974,7.165969553527832,7.949840574172974,6.962869553527832C7.824284574172974,6.826809553527832,7.699125574172974,6.661911553527832,7.657672574172974,6.490771553527832C7.628973574172973,6.373820553527832,7.716265574172974,6.201511553527832,7.8023615741729735,6.092356553527832C7.895233574172973,5.975404553527832,8.044705574172973,5.897436553527832,8.174646574172973,5.810502753527832C8.281066574172973,5.738772253527832,8.525006574172973,5.805045153527832,8.652956574172974,5.921216553527832C8.855446574172973,6.106390553527832,9.069886574172974,6.279089553527832,9.266786574172974,6.469330553527832C9.463696574172975,6.659572553527832,9.651426574172973,6.878659553527832,9.849926574172974,7.077479553527832Q10.200296574172974,7.428339553527832,10.567396574172975,7.764769553527832C10.599686574172974,7.793999553527832,10.701716574172973,7.803749553527831,10.726836574172975,7.777629553527833C10.918956574172974,7.590509553527832,11.093136574172973,7.385839553527832,11.286856574172973,7.200279553527832C11.459046574172973,7.034989553527832,11.651966574172974,6.888409553527832,11.830136574172974,6.732473553527832C11.969336574172974,6.616146553527832,12.099256574172973,6.489600553527832,12.218766574172975,6.353939553527832C12.415666574172974,6.115747553527832,12.666386574172973,5.948506553527832,12.926266574172974,5.790621753527832C12.956956574172974,5.777409235614832,12.991126574172974,5.773998983527832,13.023916574172974,5.780875763527832C13.446026574172974,5.841690653527832,13.743776574172973,6.300921553527832,13.590316574172974,6.701675553527832C13.534916574172975,6.846309553527832,13.391026574172972,6.962869553527832,13.271446574172973,7.078649553527832C13.117586574172973,7.228349553527832,12.943006574172973,7.356999553527832,12.789946574172973,7.507469553527832C12.540426574172972,7.752679553527832,12.300876574172975,8.007639553527833,12.026246574172973,8.290269553527832C12.124696574172972,8.298459553527831,12.197236574172972,8.308979553527832,12.269786574172974,8.309369553527832C12.608586574172975,8.311319553527833,12.947386574172974,8.315609553527832,13.285796574172974,8.309369553527832C13.509486574172975,8.298819553527832,13.729136574172973,8.370419553527832,13.901616574172973,8.510139553527832C13.950246574172974,8.549119553527833,13.974956574172975,8.622019553527831,13.999676574172973,8.683619553527832C14.033946574172973,8.769379553527832,14.058266574172974,8.858659553527833,14.088556574172973,8.951049553527831C14.063046574172974,9.025509553527833,14.036346574172974,9.113609553527832,14.002466574172974,9.199379553527832C13.909986574172974,9.431329553527831,13.725836574172973,9.574009553527832,13.376276574172973,9.581029553527832C12.758856574172974,9.593499553527831,12.140636574172973,9.584539553527833,11.523616574172973,9.584929553527832L11.274096574172972,9.584929553527832L11.274096574172972,10.787969553527832C11.347036574172973,10.793819553527833,11.423566574172973,10.805119553527831,11.500096574172973,10.805119553527831C12.117916574172973,10.805119553527831,12.735736574172973,10.795379553527832,13.353156574172974,10.811359553527833C13.599886574172974,10.817989553527832,13.838246574172974,10.910769553527832,13.978946574172973,11.127129553527832C14.040326574172973,11.221469553527832,14.043116574172974,11.352069553527832,14.078996574172972,11.490069553527832C14.067436574172973,11.513069553527831,14.020396574172974,11.568039553527832,14.016016574172973,11.624169553527832C13.998476574172972,11.847939553527832,13.833066574172975,11.920059553527832,13.663656574172974,12.023759553527832C13.475526574172974,12.138759553527832,13.287786574172973,12.115759553527832,13.095666574172974,12.117319553527832C12.564736574172974,12.121609553527833,12.033416574172975,12.117319553527832,11.501296574172974,12.117319553527832C11.425566574172974,12.117319553527832,11.353816574172974,12.123949553527833,11.254966574172974,12.128619553527832Z"
            fill={color}
            fillOpacity="1"
          />
        </g>
      </g>
    </g>
  </svg>
);

export default IconTransactions;
