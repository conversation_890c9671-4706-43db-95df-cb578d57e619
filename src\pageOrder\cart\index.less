@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageOrder/cart/index"] {
  .cart-page {
    background: #f7f7f7;
    .use-dark-mode-query({
    background: var(--dark-background-color);    //白色字体
  });
    min-height: 100vh;
    font-family: "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
  }

  .cart-header {
    display: flex;
    align-items: center;
    height: 48px;
    background: #fff;
    .use-dark-mode-query({
    background: var(--dark-background-color);    //白色字体
  });
    font-size: 18px;
    font-weight: 500;
    border-bottom: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-bottom: 1px solid @dark-line-color;
  });
    padding: 0 16px;
    position: sticky;
    top: 0;
    z-index: 10;
  }
  .cart-back-icon {
    font-size: 22px;
    margin-right: 16px;
    color: #333;
  }
  .cart-title {
    flex: 1;
    text-align: center;
    color: #222;
    .use-dark-mode-query({
    color: var(--dark-font-color);    //白色字体
  });
  }

  .cart-list {
    padding: 0 16px;
    padding-bottom: 80px;
  }

  .cart-shop {
    background: #fff;
    .use-dark-mode-query({
    background: var(--dark-card-background-color);    //白色字体
  });
    margin-top: 12px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    padding-bottom: 8px;
  }
  .cart-shop-header {
    display: flex;
    align-items: center;
    padding: 12px 12px 0 12px;
    min-height: 36px;
  }
  .cart-checkbox {
    width: 20px !important;
    height: 20px !important;
    margin-right: 8px;
    vertical-align: middle;
  }
  .cart-shop-avatar-wrapper {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 8px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
  }

  .cart-shop-avatar-wrapper .cart-shop-avatar {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    display: block !important;
  }
  .cart-shop-info {
    display: flex;
    align-items: center;
    flex: 1;
    cursor: pointer;
    padding: 4px 0;
    border-radius: 4px;
    transition: background-color 0.2s ease;

    &:active {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }
  .cart-shop-name-group {
    display: flex;
    align-items: center;
    flex: 1;
  }
  .cart-shop-name {
    font-size: 14px;
    color: #222;
    .use-dark-mode-query({
    color: var(--dark-font-color);    //白色字体
  });
    font-weight: 500;
    line-height: 24px;
    margin-right: 6px;
  }
  .cart-shop-wx-icon {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }
  .cart-shop-arrow {
    width: 12px;
    height: 12px;
    opacity: 0.6;
  }

  .cart-shop-products {
    padding: 0 12px;
  }
  .cart-product-card {
    background: transparent;
    border-radius: 10px;
    margin-top: 12px;
    padding: 8px;
  }
  .cart-product-main {
    display: flex;
    align-items: center;
    // padding: 12px 12px 0 12px;
  }
  .cart-product-img-wrapper {
    width: 64px;
    height: 64px;
    border-radius: 8px;
    margin-right: 12px;
    overflow: hidden;
    display: block;
    background: #f5f5f5;

    .cart-product-img {
      width: 100%;
      height: 100%;
      display: block;
      border-radius: 8px;
    }
  }
  .cart-product-info {
    flex: 1;
    min-width: 0;
  }
  .cart-product-title {
    font-size: 14px;
    color: #222;
    .use-dark-mode-query({
    color: var(--dark-font-color);    //白色字体
  });
    font-weight: 500;
    margin-bottom: 4px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    line-height: 20px;
    max-height: 40px;
  }
  .cart-product-row {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 2px;
  }
  .cart-product-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 2px;
    margin-top: 4px;
  }
  .cart-product-size {
    font-size: 14px;
    color: #222;
    .use-dark-mode-query({
    color: var(--dark-font-color);    //白色字体
  });
    font-weight: 500;
    line-height: 22px;
  }
  .cart-product-price {
    color: #ff3b30;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
  }
  .cart-product-count {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
  }
  .count-btn {
    width: 24px !important;
    height: 24px !important;
    line-height: 24px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #333;
    font-size: 18px;
    background: #fafafa;
    cursor: pointer;
    user-select: none;
    min-width: 24px;
    padding: 0;
  }
  .count-num {
    width: 32px;
    text-align: center;
    font-size: 15px;
    color: #222;
    margin: 0 6px;
  }
  .cart-product-remark-row {
    padding: 6px 12px 0 0;
  }
  .cart-product-remark {
    font-size: 12px;
    color: #aaa;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    line-height: 18px;
    max-height: 18px;
  }
  .cart-product-extra-row {
    padding: 12px 12px 0 12px;
    display: flex;
    justify-content: center;
  }
  .cart-product-send {
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    background: transparent;
    border: none;
    padding: 0;
  }
  .cart-product-send-icon {
    width: 16px;
    height: 16px;
  }
  .cart-product-send-text {
    font-size: 14px;
    color: #1677ff;
    font-weight: 500;
  }

  .cart-footer {
    position: fixed;
    left: 0;
    bottom: 0;
    width: calc(100vw - 24px);
    background: #fff;
    .use-dark-mode-query({
    background: var(--dark-background-color);    //白色字体
  });
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.04);
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    height: 72px;
    padding: 0 12px;
    z-index: 10;
  }
  .cart-footer-left {
    display: flex;
    align-items: center;
    flex: none;
    margin-top: 16px;
  }
  .cart-footer-all {
    margin-left: 8px;
    font-size: 15px;
    color: #222;
    .use-dark-mode-query({
    color: var(--dark-font-color);    //白色字体
  });
  }
  .cart-footer-manage {
    margin-left: 10px;
    font-size: 15px;
    color: var(--primary-color);
    cursor: pointer;
    font-weight: 500;
  }
  .cart-footer-info {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    // margin-left: 30px;
    margin-top: 10px;
    flex: 1;
    min-width: 0; // 允许内容收缩
    max-width: 200px; // 限制最大宽度，防止挤压按钮
    overflow: hidden; // 隐藏溢出内容
  }
  .cart-footer-total {
    font-size: 15px;
    color: #222;
    .use-dark-mode-query({
    color: var(--dark-font-color);    //白色字体
  });
    margin-right: 10px;
    margin-left: 10px;
    font-weight: 500;
    text-align: right;
    white-space: nowrap; // 防止换行
    overflow: hidden; // 隐藏溢出
    text-overflow: ellipsis; // 显示省略号
  }
  .cart-footer-total-price {
    color: #ff3b30;
    font-size: 15px;
    font-weight: 600;
    margin-left: 2px;
  }
  .cart-footer-other {
    font-size: 13px;
    color: #888;
    line-height: 20px;
    text-align: right;
  }
  .cart-footer-other-price {
    color: #ff3b30;
    font-size: 13px;
    margin-left: 2px;
    margin-right: 10px;
  }
  .cart-footer-buy {
    background: var(--primary-color) !important;
    opacity: 0.3;
    color: #fff !important;
    border: none !important;
    // border-radius: 8px !important;
    padding: 0 16px !important;
    height: 40px !important;
    font-size: 15px !important;
    font-weight: 600;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(22, 119, 255, 0.08);
    margin-top: 8px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .cart-footer-buy-active {
    background: var(--primary-color) !important;
    opacity: 1;
  }

  .cart-footer-manage-collect {
    opacity: 0.3;
  }
  .cart-footer-manage-collect-active {
    opacity: 1;
  }
  .cart-footer-manage-remove {
    opacity: 0.3;
  }
  .cart-footer-manage-remove-active {
    opacity: 1;
  }
  .cart-footer-btn-wrap {
    display: flex;
    align-items: center;
    // padding-right: 20px;
    // margin-left: 10px;
    flex-shrink: 0; // 防止按钮被压缩
    min-width: 80px; // 确保按钮有最小宽度
  }

  .cart-footer-manage-c {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 8px;
  }

  .arco-swipe-action-menu-right.arco-swipe-action-menu.action-close {
    position: relative;
    //right: 110px;
    //margin-left: 25px;
  }
  .arco-swipe-action-menu-right.arco-swipe-action-menu.action-open {
  }

  .arco-swipe-action-menu-action-info-container::after {
    height: 0;
  }

  .arco-swipe-action-menu {
    margin-left: 20px;
  }

  .cart-empty {
    display: flex;
    height: 50vh;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  
  .cart-empty-icon {
    width: 100px;
    height: 100px;
    margin-bottom: 16px;
  }
  
  .cart-empty-text {
    font-size: 14px;
    color: #999999;
  }
  
  
}
