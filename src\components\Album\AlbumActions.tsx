import { View, Text } from "@tarojs/components";
import React from "react";
import Taro from "@tarojs/taro";
import { DynamicItem } from "./AlbumCore";
import IconCart from "../YkIcons/IconCart";

export interface AlbumActionsProps {
  item: DynamicItem;
  isOwnAlbum?: boolean;
  hasOpenOnlinePayment?: number;
  onDelete?: (id: string | number) => void;
  onEdit?: (item: DynamicItem) => void;
  onDownload?: (item: DynamicItem) => void;
  onRefresh?: (id: string | number) => void;
  onToggleTop?: (id: string | number, isTop: number) => void;
  onShare?: (item: DynamicItem) => void;
  onDetail?: (item: DynamicItem) => void;
  onAddCart?: (item: DynamicItem) => void;
}

// 自己的相册操作按钮组
const OwnAlbumActions: React.FC<AlbumActionsProps> = ({
  item,
  hasOpenOnlinePayment,
  onDelete,
  onEdit,
  onDownload,
  onRefresh,
  onToggleTop,
  onShare,
  onAddCart
}) => {
  const handleDelete = (e: any) => {
    e.stopPropagation();
    onDelete?.(item.id);
  };

  const handleDownload = (e: any) => {
    e.stopPropagation();
    onDownload?.(item);
  };

  const handleRefresh = (e: any) => {
    e.stopPropagation();
    onRefresh?.(item.id);
  };

  const handleEdit = (e: any) => {
    e.stopPropagation();
    onEdit?.(item);
  };

  const handleToggleTop = (e: any) => {
    e.stopPropagation();
    onToggleTop?.(item.id, item.isTop === 1 ? 0 : 1);
  };

  const handleShare = (e: any) => {
    e.stopPropagation();
    onShare?.(item);
  };

  return (
    <View className='move-actions'>
      <View className='move-action-wrap'>
        <Text className='move-action-btn' onClick={handleDelete}>
          删除
        </Text>
        {item.pictures!==''&&(
        <Text className='move-action-btn' onClick={handleDownload}>
          下载
        </Text>
        )}
        <Text className='move-action-btn' onClick={handleRefresh}>
          刷新
        </Text>
        <Text className='move-action-btn' onClick={handleEdit}>
          编辑
        </Text>
        <Text 
          className='move-action-btn' 
          onClick={handleToggleTop}
        >
          {item.isTop === 1 ? '取顶' : '置顶'}
        </Text>
      </View>

      <View className='move-action-wrap'>
        {/* {hasOpenOnlinePayment === 1 && typeof (item as any).price !== 'undefined' && (item as any).price !== null && (item as any).price >= 0.01 ? ( */}
          <View className='move-action-add-cart' onClick={(e) => { e.stopPropagation(); onAddCart?.(item);}}>
          <IconCart  />
          </View>
         {/* ) : null} */}
      </View>
    </View>
  );
};

// 他人的相册操作按钮组
const OtherAlbumActions: React.FC<AlbumActionsProps> = ({
  item,
  hasOpenOnlinePayment,
  onDownload,
  onShare,
  onDetail,
  onAddCart,
}) => {
  const handleForwardShare = () => {
    try {
      // 将当前项作为“首图分享”数据写入缓存
      Taro.setStorageSync('releaseDynamicList', item);
      // 跳转到发布页进行转发
      Taro.navigateTo({ url: '/pageDynamic/releaseDynamic/index?type=4' });
    } catch (e) {
      console.error('[AlbumActions] 转发失败:', e);
    }
  };

  return (
    <View className='move-actions'>
      <View className='move-action-wrap'>
        {item.pictures!==''&&(
        <Text className='move-action-btn' onClick={(e) => { e.stopPropagation(); onDownload?.(item); }}>
          下载
        </Text>
        )}
        {/* next-todo: 编辑按钮暂时隐藏 */}
        { false && <Text className='move-action-btn' onClick={(e) => { e.stopPropagation(); onDetail?.(item); }}>
          编辑
        </Text>
        }
      </View>
      <View className='move-action-wrap'>
        {hasOpenOnlinePayment === 1 && typeof (item as any).price !== 'undefined' && (item as any).price !== null && (item as any).price >= 0.01 ? (
          <View className='move-action-add-cart' onClick={(e) => { e.stopPropagation(); onAddCart?.(item);}}>
          <IconCart  />
          </View>
        ) : null}
        <Text className='move-action-share' onClick={(e) => { e.stopPropagation(); onShare ? onShare(item) : handleForwardShare(); }}>
          转发
        </Text>
      </View>
    </View>
  );
};

// 主操作组件
const AlbumActions: React.FC<AlbumActionsProps> = (props) => {
  const { isOwnAlbum = false } = props;
  
  return isOwnAlbum ? (
    <OwnAlbumActions {...props} />
  ) : (
    <OtherAlbumActions {...props} />
  );
};

export default AlbumActions;
