/**
 * @description VIP会员支付服务模块，用于处理混合应用中的原生支付流程
 * 支持微信支付调起和支付结果回调处理
 */
import wx from 'weixin-webview-jssdk'
// 支付方式常量
export const PAYMENT_PROVIDERS = {
  ALIPAY: 'alipay_app',
  WECHAT: 'wx_app',
  WECHAT_LITE: 'wx_lite',
  APPLE_IAP: 'apple_iap', // 苹果内购
};

// 支付状态常量
export const PAYMENT_STATUS = {
  SUCCESS: 1,    // 支付成功
  CANCEL: 2,     // 用户取消
  FAILURE: 3,    // 支付失败
  PROCESSING: 4, // 处理中
};


/**
 * @private
 * 检测运行环境
 * @returns {{isIos: boolean, isAndroid: boolean, isApp: boolean, isH5: boolean}}
 */
function getEnvironment() {
  // 检测是否在App环境中
  const ua = window.navigator.userAgent;
  const isAndroid = ua.includes('android') && (window.wxPay || window.aliPay);
  const isIos = ua.includes('ios') && window.webkit?.messageHandlers;
  const isHM = ua.includes('hm');
  const isWX = window.__wxjs_environment === "miniprogram";
  return { isIos, isAndroid, isHM, isWX};
}


/**
 * 解析微信支付参数
 * @param {string} displayContent - 从后端返回的支付参数JSON字符串
 * @returns {object} 解析后的微信支付参数
 */
export function parseWechatPayParams(displayContent) {
  try {
    const payParams = JSON.parse(displayContent);
    return {
      appid: payParams.appid,
      partnerId: payParams.partnerId,
      prepayId: payParams.prepayId,
      packageValue: payParams.packageValue,
      noncestr: payParams.noncestr,
      timestamp: payParams.timestamp,
      sign: payParams.sign,
    };
  } catch (error) {
    console.error('解析微信支付参数失败:', error);
    throw new Error('支付参数格式错误');
  }
}

/**
 * 调起微信支付
 * @param {object} payParams - 微信支付参数
 * @param {string} payParams.appid - 微信应用ID
 * @param {string} payParams.partnerId - 商户ID
 * @param {string} payParams.prepayId - 预支付ID
 * @param {string} payParams.packageValue - 包值
 * @param {string} payParams.noncestr - 随机字符串
 * @param {string} payParams.timestamp - 时间戳
 * @param {string} payParams.sign - 签名
 * @returns {Promise<void>}
 */
export function invokeWechatPay(payParams) {
  return new Promise((resolve, reject) => {
    const { isIos, isAndroid,isHM,isWX} = getEnvironment();
    // 构造支付参数字符串
    const payParamsStr = JSON.stringify(payParams);

    console.log('[微信支付] 调起支付参数:', payParams);

    try {
      if (isAndroid && window.wxPay?.wxPay) {
        // Android 微信支付调起
        console.log('🔍 [FORCE] 调起Android微信支付:', payParamsStr);
        window.wxPay.wxPay(payParamsStr);
        console.log('🔍 [FORCE] Android微信支付调起完成');
        resolve();
      } else if (isIos && window.webkit?.messageHandlers?.wxPayWithStr) {
        // iOS 微信支付调起
        console.log('🔍 [FORCE] 调起iOS微信支付:', payParamsStr);
        window.webkit.messageHandlers.wxPayWithStr.postMessage(payParamsStr);
        console.log('🔍 [FORCE] iOS微信支付调起完成');
        resolve();
      }else if (isHM) {
        // iOS 微信支付调起
        window.harmony.wxPay(payParamsStr);
        resolve();
      } else if (isWX) {

        wx.miniProgram.navigateTo({
          url: '/pages/wxpay/index?isBuyVip=true&payParams=' + encodeURIComponent(payParamsStr)
        });
        resolve();
      } else {
        const error = new Error('微信支付功能不可用，请检查App版本');
        console.error('🔍 [FORCE] 微信支付功能检测失败:', {
          isAndroid,
          isIos,
          isHM,
          hasWxPay: !!window.wxPay,
          hasWxPayMethod: !!(window.wxPay && window.wxPay.wxPay),
          hasWebkit: !!window.webkit,
          hasMessageHandlers: !!(window.webkit && window.webkit.messageHandlers),
          hasWxPayHandler: !!(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.wxPayWithStr)
        });
        reject(error);
      }
    } catch (error) {
      console.error('🔍 [FORCE] 微信支付调起异常:', error);
      reject(new Error('支付调起失败'));
    }
  });
}

/**
 * 调起支付宝支付
 * @param {string} payInfo - 支付宝支付信息字符串
 * @returns {Promise<void>}
 */
export function invokeAlipay(payInfo) {
  return new Promise((resolve, reject) => {
    const { isIos, isAndroid,isHM} = getEnvironment();

    // 强制输出支付调起信息（用于生产环境调试）
    console.log('🔍 [FORCE] 支付宝支付调起开始:', {
      isIos,
      isAndroid,
      hasAliPay: !!window.aliPay,
      hasAliPayMethod: !!(window.aliPay && window.aliPay.aliPay),
      aliPayType: typeof window.aliPay,
      aliPayMethodType: typeof (window.aliPay && window.aliPay.aliPay),
      payInfo
    });
    console.log('[支付宝支付] 调起支付参数:', payInfo);

    try {
      if (isAndroid && window.aliPay?.aliPay) {
        // Android 支付宝支付调起
        console.log('🔍 [FORCE] 调起Android支付宝支付:', payInfo);
        window.aliPay.aliPay(payInfo);
        console.log('🔍 [FORCE] Android支付宝支付调起完成');
        resolve();
      }else if (isHM) {
        // Android 支付宝支付调起
        window.harmony.aliPay(payInfo);
        resolve();
      } else if (isIos && window.webkit?.messageHandlers?.aliPayWithStr) {
        // iOS 支付宝支付调起
        console.log('🔍 [FORCE] 调起iOS支付宝支付:', payInfo);
        window.webkit.messageHandlers.aliPayWithStr.postMessage(payInfo);
        console.log('🔍 [FORCE] iOS支付宝支付调起完成');
        resolve();
      } else {
        const error = new Error('支付宝支付功能不可用，请检查App版本');
        console.error('🔍 [FORCE] 支付宝支付功能检测失败:', {
          isAndroid,
          isIos,
          hasAliPay: !!window.aliPay,
          hasAliPayMethod: !!(window.aliPay && window.aliPay.aliPay),
          hasWebkit: !!window.webkit,
          hasMessageHandlers: !!(window.webkit && window.webkit.messageHandlers),
          hasAliPayHandler: !!(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.aliPayWithStr)
        });
        reject(error);
      }
    } catch (error) {
      console.error('🔍 [FORCE] 支付宝支付调起异常:', error);
      reject(new Error('支付调起失败'));
    }
  });
}

/**
 * 调起苹果内购支付
 * @param {string} productId - 苹果内购产品ID
 * @returns {Promise<void>}
 */
export function invokeAppleIAP(productId) {
  return new Promise((resolve, reject) => {
    const { isIos, isApp } = getEnvironment();

    // 强制输出苹果内购调起信息（用于生产环境调试）
    console.log('🔍 [FORCE] 苹果内购调起开始:', {
      isIos,
      isApp,
      hasWebkit: !!window.webkit,
      hasMessageHandlers: !!(window.webkit && window.webkit.messageHandlers),
      hasPurchaseHandler: !!(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.purchaseProduct),
      productId
    });

    if (!isIos || !isApp) {
      const error = new Error('苹果内购仅支持iOS App环境');
      console.error('🔍 [FORCE] 苹果内购环境检测失败:', error.message);
      reject(error);
      return;
    }

    if (!productId || typeof productId !== 'string') {
      const error = new Error('产品ID不能为空');
      console.error('🔍 [FORCE] 苹果内购产品ID检测失败:', error.message);
      reject(error);
      return;
    }

    console.log('[苹果内购] 调起内购参数:', { productId });

    try {
      if (window.webkit?.messageHandlers?.purchaseProduct) {
        // iOS 苹果内购调起
        console.log('🔍 [FORCE] 调起iOS苹果内购:', productId);
        window.webkit.messageHandlers.purchaseProduct.postMessage(productId);
        console.log('🔍 [FORCE] iOS苹果内购调起完成');
        resolve();
      } else {
        const error = new Error('苹果内购功能不可用，请检查App版本');
        console.error('🔍 [FORCE] 苹果内购功能检测失败:', {
          hasWebkit: !!window.webkit,
          hasMessageHandlers: !!(window.webkit && window.webkit.messageHandlers),
          hasPurchaseHandler: !!(window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.purchaseProduct)
        });
        reject(error);
      }
    } catch (error) {
      console.error('🔍 [FORCE] 苹果内购调起异常:', error);
      reject(new Error('苹果内购调起失败'));
    }
  });
}

/**
 * 处理支付结果数据
 * @param {object} payResult - submitPayOrder 返回的支付结果
 * @param {string} channelCode - 支付渠道编码
 * @returns {Promise<any>} 支付调起结果
 */
export function handlePaymentResult(payResult, channelCode) {
  return new Promise((resolve, reject) => {
    const { status, displayContent, totalAmount } = payResult;

    console.log('[支付处理] 支付结果:', payResult);
    console.log('[支付处理] 支付渠道:', channelCode);

    // 检查支付状态
    if (status !== 0) {
      reject(new Error(`支付订单状态异常: ${status}`));
      return;
    }

    // 根据显示模式处理
        // App调起支付 - 根据渠道类型选择支付方式
        try {
          if (channelCode === PAYMENT_PROVIDERS.APPLE_IAP) {
            // 苹果内购支付
            console.log('[支付处理] 调起苹果内购支付');
            // 对于苹果内购，displayContent应该是产品ID
            invokeAppleIAP(displayContent)
              .then(() => resolve({ productId: displayContent }))
              .catch(reject);
          } else if (channelCode && channelCode.startsWith('alipay')) {
            // 支付宝支付
            console.log('[支付处理] 调起支付宝支付');
            invokeAlipay(displayContent)
              .then(() => resolve({ alipayInfo: displayContent }))
              .catch(reject);
          } else if (channelCode === PAYMENT_PROVIDERS.WECHAT_LITE) {
            // 微信小程序支付
            console.log('[支付处理] 调起微信小程序支付');
            var payParams = JSON.parse(displayContent);
            payParams.totalAmount = totalAmount;
            console.log('[支付处理] 调起微信小程序支付参数:', payParams);
            invokeWechatPay(payParams)
              .then(() => resolve(payParams))
              .catch(reject);
          } else {
            // 微信支付（默认）
            console.log('[支付处理] 调起微信支付');
            const payParams = parseWechatPayParams(displayContent);
            invokeWechatPay(payParams)
              .then(() => resolve(payParams))
              .catch(reject);
          }
        } catch (error) {
          reject(error);
        }
  });
}

/**
 * 注册支付回调函数到全局回调管理器
 * @param {{onSuccess: Function, onFailure: Function, onCancel: Function}} callbacks
 * @returns {Function} 清理函数
 */
export function registerPaymentCallbacks(callbacks) {
  const { isIos} = getEnvironment();

  // 导入全局回调管理器
  import('./globalCallbackManager.js').then(({ useGlobalCallbacks }) => {
    const handlePaymentResult = (status, message = '') => {
      console.log('[支付回调] 收到支付结果:', { status, message });

      // 支付状态处理
      // 1: 成功, 2: 取消, 3: 失败, 4: 处理中
      switch (String(status)) {
        case '1':
          console.log('[支付回调] 支付成功');
          callbacks.onSuccess?.();
          break;
        case '2':
          console.log('[支付回调] 用户取消支付');
          callbacks.onCancel?.();
          break;
        case '3':
          console.log('[支付回调] 支付失败:', message);
          callbacks.onFailure?.(message || '支付失败');
          break;
        case '4':
          console.log('[支付回调] 支付处理中');
          // 处理中状态，可以显示loading或等待
          break;
        default:
          console.log('[支付回调] 未知支付状态:', status);
          callbacks.onFailure?.(`未知支付状态: ${status}`);
          break;
      }
    };

    // 使用全局回调管理器注册支付回调
    const callbackCleanup = useGlobalCallbacks('paymentService', {
      webPaySuc: handlePaymentResult,
      wxPayWithStr: handlePaymentResult,
      aliPayWithStr: handlePaymentResult,
    });

    // 注册其他平台特定的回调函数
    if (isIos) {
      // iOS 回调函数
      window.wxPayCallback = handlePaymentResult;
      window.aliPayCallback = handlePaymentResult;
      // 苹果内购回调函数
      window.purchaseProduct = (jws) => {
        console.log('[苹果内购] 收到购买成功回调:', jws);
        // JWS (JSON Web Signature) 是苹果返回的购买凭证
        callbacks.onSuccess?.(jws);
      };
      window.purchaseProductError = (errorMessage) => {
        console.log('[苹果内购] 收到购买失败回调:', errorMessage);
        callbacks.onFailure?.(errorMessage || '苹果内购失败');
      };
    }

    // 返回清理函数
    return () => {
      if (isIos) {
        delete window.wxPayCallback;
        delete window.aliPayCallback;
        delete window.purchaseProduct;
        delete window.purchaseProductError;
      }
      callbackCleanup && callbackCleanup();
      console.log('[支付回调] 已清理支付回调函数');
    };
  });

  // 临时返回一个空的清理函数，直到动态导入完成
  return () => {
    console.log('[支付回调] 临时清理函数');
  };
}