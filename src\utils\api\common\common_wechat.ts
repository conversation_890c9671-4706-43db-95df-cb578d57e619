import request from '@/utils/api/request';
import { requestUploadFile } from '@/utils/api/requestUploadFile';


/** 
 * ********************************** 平台侧 **************************************
*/
/**
 * @module createOnlinePaymentInformation 创建在线收款资料 /app-api/album/online-payment-information/create POST
 * @param userId	是	number	用户id
 */
interface createOnlinePaymentInformationData {
	userId?: number;
	[key: string]: any;
}
export const createOnlinePaymentInformation = (data: createOnlinePaymentInformationData) => {
	return request("/app-api/album/online-payment-information/create", "POST", data)
}

/**
 * @module getOnlinePaymentInformation 获取在线收款资料 /app-api/album/online-payment-information/get GET
 * @param userId	是	number	用户id
 */
interface getOnlinePaymentInformationData {
	userId?: number;
}
export const getOnlinePaymentInformation = (data: getOnlinePaymentInformationData) => {
	return request("/app-api/album/online-payment-information/get", "GET", data)
}

/**
 * @module updateOnlinePaymentInformation 更新在线收款资料 /app-api/album/online-payment-information/update PUT
 * @param id 在线收款资料id
 */
interface updateOnlinePaymentInformationData {
	id?: number;
	[key: string]: any;
}
export const updateOnlinePaymentInformation = (data: updateOnlinePaymentInformationData) => {
	return request("/app-api/album/online-payment-information/update", "PUT", data)
}

/**
 * @module deleteOnlinePaymentInformation 删除在线收款资料 /app-api/album/online-payment-information/delete DELETE
 * @param id 在线收款资料id
 */
interface deleteOnlinePaymentInformationData {
	id?: number;
}
export const deleteOnlinePaymentInformation = (data: deleteOnlinePaymentInformationData) => {
	return request("/app-api/album/online-payment-information/delete", "DELETE", data)
}

/**
 * @module getOnlinePaymentInformationList 获取在线支付信息列表 /app-api/album/online-payment-information/page GET
 * @param pageNo	是	number	页码
 * @param pageSize	是	number	每页数量
 * @param userId	是	number	用户id
 */
interface getOnlinePaymentInformationListData {
	pageNo?: number,
	pageSize?: number,
	userId?: number
}
export const getOnlinePaymentInformationList = (data: getOnlinePaymentInformationListData) => {
	return request("/app-api/album/online-payment-information/page", "GET", data)
}

/**
 * @module createMerchantEntryApplication 创建商户入驻申请 /app-api/album/merchant-entry-application/create POST
 * @param userId	是	number	用户id
 */
interface createMerchantEntryApplicationData {
	/** 用户编号 */
	userId?: number;
	/** 其他字段 */
	[key: string]: any;
}
export const createMerchantEntryApplication = (data: createMerchantEntryApplicationData) => {
	return request("/app-api/album/merchant-entry-application/create", "POST", data)
}

/**
 * @module updateMerchantEntryApplication 更新商户进件申请 /app-api/album/merchant-entry-application/update PUT
 * @param userId	是	number	用户id
 */
interface updateMerchantEntryApplicationData {
	id?: number;
	userId?: number;
	[key: string]: any;
}
export const updateMerchantEntryApplication = (data: updateMerchantEntryApplicationData) => {
	return request("/app-api/album/merchant-entry-application/update", "PUT", data)
}

/**
 * @module deleteMerchantEntryApplication 删除商户进件申请 /app-api/album/merchant-entry-application/delete DELETE
 * @param id 商户进件申请id
 */
interface deleteMerchantEntryApplicationData {
	id?: number;
}
export const deleteMerchantEntryApplication = (data: deleteMerchantEntryApplicationData) => {
	return request("/app-api/album/merchant-entry-application/delete", "DELETE", data)
}

/**
 * @module getMerchantEntryApplicationPage 获得商户进件申请分页
 * @param pageNo 页码
 * @param pageSize 每页数量
 * @param userId 用户id
 */
interface getMerchantEntryApplicationPageData {
	pageNo?: number,
	pageSize?: number,
	userId?: number
}
export const getMerchantEntryApplicationPage = (data: getMerchantEntryApplicationPageData) => {
	return request("/app-api/merchant/entry-apply/page", "GET", data)
}

/**
 * @module getMerchantEntryApplication 获得商户进件申请详情
 * @param id 商户进件申请id
 */
interface getMerchantEntryApplicationData {
	id: number;
}
export const getMerchantEntryApplication = (data: getMerchantEntryApplicationData) => {
	return request(`/app-api/album/merchant-entry-application/get?id=${data.id}`, "GET")
}

/** 
 * ********************************** 微信侧 **************************************
*/

/**
 * @module withdrawal 二级商户预约提现
 */
interface withdrawalData {
	/** 用户id */
	userId?: number;
	/** 二级商户号 */
	sub_mchid?: string;
	/** 商户预约提现单号 */
	out_request_no?: string;
	/** 提现金额 */
	amount?: number;
	/** 提现备注 */
	remark?: string;
	/** 银行附言 */
	bank_memo?: string;
	/** 出款账户类型 */
	account_type?: string;
	/** 提现结果通知地址 */
	notify_url?: string;
}
export const withdrawal = (data: withdrawalData) => {
	return request("/app-api/album/wechat/file/withdrawal", "POST", data)
}

/**
 * @module updateAccount 修改结算账号
 */
interface updateAccountData {
	/** 账户类型 */
	account_type?: string;
	/** 开户银行 */
	account_bank?: string;
	/** 开户银行全称（含支行） */
	bank_name?: string;
	/** 开户银行联行号 */
	bank_branch_id?: string;
	/** 银行账号 */
	account_number?: string;
	/** 开户名称 */
	account_name?: string;
}
export const updateAccount = (data: updateAccountData) => {
	return request("/app-api/album/wechat/file/updateAccount", "POST", data)
}

/**
 * @module createAndSubmitAccount 保存和提交修改结算账号
 */
interface createAndSubmitAccountData {
	/** 编号 */
	id?: number;
	/** 用户编号 */
	userId?: number;
	/** 账户类型 */
	accountType?: string;
	/** 开户银行 */
	accountBank?: string;
	/** 开户银行全称（含支行） */
	bankName?: string;
	/** 开户银行联行号 */
	bankBranchId?: string;
	/** 银行账号 */
	accountNumber?: string;
	/** 开户名称 */
	accountName?: string;
}

export const createAndSubmitAccount = (data: createAndSubmitAccountData) => {
	return request("/app-api/album/wechat/file/createAndSubmitAccount", "POST", data)
}

/**
 * @module submit 提交申请单
 */
interface BusinessLicenseInfo {
	/** 证书类型 */
	cert_type?: string;
	/** 营业执照扫描件 */
	business_license_copy?: string;
	/** 营业执照注册号 */
	business_license_number?: string;
	/** 商户名称 */
	merchant_name?: string;
	/** 经营者/法定代表人姓名 */
	legal_person?: string;
	/** 注册地址 */
	company_address?: string;
	/** 营业期限 */
	business_time?: string;
}

interface FinanceInstitutionInfo {
	/** 金融机构类型 */
	finance_type?: string;
	/** 金融机构许可证图片 */
	finance_license_pics?: string[];
}

interface IdCardInfo {
	/** 身份证人像面照片 */
	id_card_copy?: string;
	/** 身份证国徽面照片 */
	id_card_national?: string;
	/** 身份证姓名 */
	id_card_name?: string;
	/** 身份证号码 */
	id_card_number?: string;
	/** 身份证开始时间 */
	id_card_valid_time_begin?: string;
	/** 身份证结束时间 */
	id_card_valid_time?: string;
}

interface IdDocInfo {
	/** 证件姓名 */
	id_doc_name?: string;
	/** 证件号码 */
	id_doc_number?: string;
	/** 证件正面照片 */
	id_doc_copy?: string;
	/** 证件反面照片 */
	id_doc_copy_back?: string;
	/** 证件开始日期 */
	doc_period_begin?: string;
	/** 证件结束日期 */
	doc_period_end?: string;
}

interface UboInfo {
	/** 证件类型 */
	ubo_id_doc_type?: string;
	/** 证件正面照片 */
	ubo_id_doc_copy?: string;
	/** 证件反面照片 */
	ubo_id_doc_copy_back?: string;
	/** 证件姓名 */
	ubo_id_doc_name?: string;
	/** 证件号码 */
	ubo_id_doc_number?: string;
	/** 证件居住地址 */
	ubo_id_doc_address?: string;
	/** 证件有效期开始时间 */
	ubo_id_doc_period_begin?: string;
	/** 证件有效期结束时间 */
	ubo_id_doc_period_end?: string;
}

interface AccountInfo {
	/** 账户类型 */
	bank_account_type?: string;
	/** 开户银行 */
	account_bank?: string;
	/** 开户名称 */
	account_name?: string;
	/** 开户银行省市编码 */
	bank_address_code?: string;
	/** 开户银行联行号 */
	bank_branch_id?: string;
	/** 开户银行全称（含支行） */
	bank_name?: string;
	/** 银行账号 */
	account_number?: string;
}

interface ContactInfo {
	/** 超级管理员类型 */
	contact_type?: string;
	/** 超级管理员姓名 */
	contact_name?: string;
	/** 超级管理员证件类型 */
	contact_id_doc_type?: string;
	/** 超级管理员证件号码 */
	contact_id_card_number?: string;
	/** 超级管理员证件正面照片 */
	contact_id_doc_copy?: string;
	/** 超级管理员证件反面照片 */
	contact_id_doc_copy_back?: string;
	/** 超级管理员证件有效期开始时间 */
	contact_id_doc_period_begin?: string;
	/** 超级管理员证件有效期结束时间 */
	contact_id_doc_period_end?: string;
	/** 业务办理授权函 */
	business_authorization_letter?: string;
	/** 超级管理员手机 */
	mobile_phone?: string;
}

interface SalesSceneInfo {
	/** 店铺名称 */
	store_name?: string;
	/** 店铺链接 */
	store_url?: string;
	/** 店铺二维码 */
	store_qr_code?: string;
	/** 商家小程序APPID */
	mini_program_sub_appid?: string;
}

interface SettlementInfo {
	/** 结算规则ID */
	settlement_id?: number;
	/** 商户简称 */
	qualification_type?: string;
}

interface submitApplicationData {
	/** 业务申请编号 */
	out_request_no?: string;
	/** 主体类型 */
	organization_type?: string;
	/** 是否金融机构 */
	finance_institution?: boolean;
	/** 营业执照信息 */
	business_license_info?: BusinessLicenseInfo;
	/** 金融机构许可证信息 */
	finance_institution_info?: FinanceInstitutionInfo;
	/** 证件持有人类型 */
	id_holder_type?: string;
	/** 经营者/法人证件类型 */
	id_doc_type?: string;
	/** 法定代表人说明函 */
	authorize_letter_copy?: string;
	/** 经营者/法人身份证信息 */
	id_card_info?: IdCardInfo;
	/** 经营者/法人其他类型证件信息 */
	id_doc_info?: IdDocInfo;
	/** 经营者/法人是否为受益人 */
	owner?: boolean;
	/** 最终受益人信息列表 */
	ubo_info_list?: UboInfo[];
	/** 结算账户信息 */
	account_info?: AccountInfo;
	/** 超级管理员信息 */
	contact_info?: ContactInfo;
	/** 经营场景信息 */
	sales_scene_info?: SalesSceneInfo;
	/** 结算规则 */
	settlement_info?: SettlementInfo;
	/** 商户简称 */
	merchant_shortname?: string;
	/** 特殊资质 */
	qualifications?: string;
	/** 补充材料 */
	business_addition_pics?: string;
	/** 补充说明 */
	business_addition_desc?: string;
}

export const submitApplication = (data: submitApplicationData) => {
	return request("/app-api/album/wechat/file/submit", "POST", data)
}

/**
 * @module profitSharing 请求分账
 */
interface Receiver {
	/** 分账接收方类型 */
	type?: string;
	/** 分账接收方帐号 */
	receiver_account?: string;
	/** 分账金额分 */
	amount?: number;
	/** 分账描述 */
	description?: string;
	/** 分账个人接收方姓名 */
	receiver_name?: string;
	/** 是否分账完成 */
	finish?: boolean;
}

interface profitSharingData {
	/** 微信分配的公众账号ID */
	appid?: string;
	/** 电商平台二级商户号 */
	sub_mchid?: string;
	/** 微信订单号 */
	transaction_id?: string;
	/** 商户分账单号 */
	out_order_no?: string;
	/** 分账方列表 */
	receivers?: Receiver[];
}
export const profitSharing = (data: profitSharingData) => {
	return request("/app-api/album/wechat/file/profit-sharing", "POST", data)
}

// /**
//  * @module uploadImageWechat 图片上传接口
//  */
// interface uploadImageWechatData {
// 	/** 要上传的文件 */
// 	file: File;
// }
export const uploadImageWechat = (file) => {
	return requestUploadFile("/app-api/merchant/wechat-general/file-upload", file);
}

/**
 * @module uploadImageWechatCancel 注销申请图片上传接口
 */
export const uploadImageWechatCancel = (file) => {
	return requestUploadFile("/app-api/album/wechat/file/file-upload-cancel", file);
}

/**
 * @module createAndSubmitMerchantEntryApplication 保存和提交申请单接口
 */
interface createAndSubmitMerchantEntryApplicationData {
	userId?: number;
	[key: string]: any;
}
export const createAndSubmitMerchantEntryApplication = (data: createAndSubmitMerchantEntryApplicationData) => {
	//return request("/app-api/album/wechat/file/createAndSubmit", "POST", data)
	return request("/app-api/merchant/wechat-entry/apply", "POST", data)
}

/**
 * @module submitMerchantEntryApplication 保存和提交修改结算账号
 */
interface submitMerchantEntryApplicationData {
	[key: string]: any;	
}
export const submitMerchantEntryApplication = (data: submitMerchantEntryApplicationData) => {
	return request("/app-api/album/wechat/file/submitApplication", "POST", data)
}

/**
 * @module combinePay APP支付合单下单
 */
export const combinePay = (data: any) => {
	return request("/app-api/album/wechat/file/combinePay", "POST", data)
}

/**
 * @module appPay APP调起支付
 */
interface appPayData {
	/** 预支付ID */
	prepayId: string;
}
export const appPay = (data: appPayData) => {
	return request("/app-api/album/wechat/file/appPay", "GET", data)
}

/**
 * @module getWithdrawalStatus 二级商户查询预约提现状态（根据商户预约提现单号查询）
 */
interface getWithdrawalStatusData {
	/** 商户预约提现单号 */
	'out-request-no': string;
}
export const getWithdrawalStatus = (data: getWithdrawalStatusData) => {
	return request("/app-api/album/wechat/file/withdrawal-status", "GET", data)
}

/**
 * @module getWithdrawalStatusWechat 二级商户查询预约提现状态（根据微信支付预约提现单号查询）
 */
interface getWithdrawalStatusWechatData {
	/** 微信支付预约提现单号 */
	withdraw_id: string;
}
export const getWithdrawalStatusWechat = (data: getWithdrawalStatusWechatData) => {
	return request("/app-api/album/wechat/file/withdrawal-status-wechat", "GET", data)
}

/**
 * @module getOutRequestNo 通过业务申请编号查询申请状态
 */
interface getOutRequestNoData {
	/** 业务申请编号 */
	'out-request-no': string;
}
export const getOutRequestNo = (data: getOutRequestNoData) => {
	return request("/app-api/album/wechat/file/outRequestNo", "GET", data)
}

/**
 * @module getAccount 查询结算账号
 */
export const getAccount = () => {
	return request("/app-api/album/wechat/file/getAccount", "GET")
}

/**
 * @module getAccountApplyments 查询结算账户修改申请状态
 */
export const getAccountApplyments = () => {
	return request("/app-api/album/wechat/file/getAccountApplyments", "GET")
}


interface getBalanceData {
	userId?: number;
}
/**
 * @module getBalance 查询二级商户账户实时余额
 */
export const getBalance = (data: getBalanceData) => {
	return request("/app-api/album/wechat/file/get-balance", "GET", data)

}

/**
 * @module getApplyments 通过申请单ID查询申请状态
 */
interface getApplymentsData {
	/** 申请单ID */
	applyment_id: number;
}
export const getApplyments = (data: getApplymentsData) => {
	return request("/app-api/album/wechat/file/applyments", "GET", data)
}

/**
 * @module cancelApplications 商户注销申请
 */
interface CancelApplicationsInfoList {
	/** 注销申请材料类型 */
	application_type?: string;
	/** 注销申请材料照片ID */
	application_media_id?: string;
}

interface cancelApplicationsData {
	/** 申请注销的二级商户号 */
	sub_mchid?: string;
	/** 商户注销申请单号 */
	out_apply_no?: string;
	/** 注销申请材料 */
	application_info?: CancelApplicationsInfoList[];
}

export const cancelApplications = (data: cancelApplicationsData) => {
	return request("/app-api/album/wechat/file/cancel-applications", "POST", data)
}

/**
 * @module getCancelApplicationsStatus 查询商户注销单状态
 */
interface getCancelApplicationsStatusData {
	/** 商户注销申请单号 */
	out_apply_no: string;
}

export const getCancelApplicationsStatus = (data: getCancelApplicationsStatusData) => {
	return request("/app-api/album/wechat/file/cancel-applications-status", "GET", data)
}

/** 
 * 
 * 更新商户进件申请表状态  album/entry-form-status
 * 
 * 
 * */
interface getMerchantEntryApplyStatusPageData {
	pageNo?: number,
	pageSize?: number,
	userId?: number
}
//通过通过申请单ID或业务申请编号查询
//export const getEntryFormStatusByApplyIdOrBusinessApplyNo = () => {
export const getMerchantEntryApplyStatusPage = (data: getMerchantEntryApplyStatusPageData) => {
	return request("/app-api/merchant/entry-apply-status/page","GET",data);
}


interface getMerchantEntryByOutRequestNoData {
	/** 业务申请编号 */
	outRequestNo: string;
}
export const getMerchantEntryByOutRequestNo = (data: getMerchantEntryByOutRequestNoData) => {
	//return request("/app-api/merchant/wechat-entry/outRequestNo","GET",data);
	return request(	{
		url: "/app-api/merchant/wechat-entry/outRequestNo",
		method: 'GET',
		data: { 'out-request-no': data.outRequestNo },
		contentType: 'form'
	  })
}



// 进件照片


/**
 * @module createEntryPhotoAddress 创建进件照片地址
 */
interface createEntryPhotoAddressData {
  /** 主键ID */
  id?: number;
  /** 用户ID */
  userId?: number;
  /** 身份证正面照片 */
  idCardFront?: string;
  /** 身份证反面照片 */
  idCardBack?: string;
  /** 银行卡照片 */
  bankCard?: string;
  /** 营业执照照片 */
  businessLicense?: string;
  /** 开户许可证照片 */
  accountOpeningLicense?: string;
}

export const createEntryPhotoAddress = (data: createEntryPhotoAddressData) => {
  return request("/app-api/merchant/entry-photo-address/create", "POST", data)
}

/**
 * @module updateEntryPhotoAddress 更新进件照片地址
 */
interface updateEntryPhotoAddressData {
  /** 主键ID */
  id: number;
  /** 用户ID */
  userId?: number;
  /** 身份证正面照片 */
  idCardFront?: string;
  /** 身份证反面照片 */
  idCardBack?: string;
  /** 银行卡照片 */
  bankCard?: string;
  /** 营业执照照片 */
  businessLicense?: string;
  /** 开户许可证照片 */
  accountOpeningLicense?: string;
}

export const updateEntryPhotoAddress = (data: updateEntryPhotoAddressData) => {
  return request("/app-api/merchant/entry-photo-address/update", "PUT", data)
}

/**
 * @module getEntryPhotoAddressPage 获取进件照片地址分页
 */
interface getEntryPhotoAddressPageData {
  /** 用户ID */
  userId?: number;
  /** 页码 */
  pageNo?: number;
  /** 每页数量 */
  pageSize?: number;
}

export const getEntryPhotoAddressPage = (data: getEntryPhotoAddressPageData) => {
  return request("/app-api/merchant/entry-photo-address/page", "GET", data)
}

/**
 * @module deleteEntryPhotoAddress 删除进件照片地址
 */
interface deleteEntryPhotoAddressData {
  /** 主键ID */
  id: number;
}

export const deleteEntryPhotoAddress = (data: deleteEntryPhotoAddressData) => {
  return request("/app-api/merchant/entry-photo-address/delete", "DELETE", data)
}




/**
 * OCR专用请求函数 - 使用查询参数方式
 * 因为后端Controller参数没有@RequestParam注解，Spring会优先从查询参数获取
 */
const ocrRequest = (url: string, data: imageOcrData) => {
  // 将图片URL作为查询参数拼接
  const urlWithParams = url + `?url=${encodeURIComponent(data.url)}`;

  return request({
    url: urlWithParams,
    method: 'POST',
    contentType: 'json'
  });
};

/**
 * OCR备用请求函数 - 使用form-urlencoded格式
 * 如果查询参数方式不工作，可以尝试这种方式
 */
const ocrRequestForm = (url: string, data: imageOcrData) => {
  return request({
    url: url,
    method: 'POST',
    data: { url: data.url },
    contentType: 'form'
  });
};


/**
 * 百度OCR识别
 */
interface imageOcrData {
  /** 图片url */
  url: string;
}

/**
 * @module baiduIdCardOcr 百度身份证OCR识别
 */
export const baiduIdCardOcr = (data: imageOcrData) => {
  return ocrRequestForm("/app-api/merchant/baidu-recognize/idCard", data)
}

/**
 * @module baiduBusinessLicenseOcr 百度营业执照OCR识别
 */
export const baiduBusinessLicenseOcr = (data: imageOcrData) => {
  //return ocrRequestForm("/app-api/album/baidu/businessLicense", data)
  //return ocrRequestForm("/app-api/merchant/baidu-recognize/businessLicense", data)
  return ocrRequestForm("/app-api/merchant/baidu-recognize/businessLicense", data)
}

/**
 * @module baiduBankCardOcr 百度银行卡OCR识别
 */
export const baiduBankCardOcr = (data: imageOcrData) => {
  return ocrRequestForm("/app-api/merchant/baidu-recognize/bankCard", data)
}

/**
 * @module baiduBankAccountLicenseOcr 百度开户许可证OCR识别
 */
export const baiduBankAccountLicenseOcr = (data: imageOcrData) => {
  return ocrRequestForm("/app-api/merchant/baidu-recognize/bankAccountLicense", data)
}


/**
 * @module getSettlementAccountPage 获取结算信息分页
 */
interface getSettlementAccountPageData {
  /** 用户ID */
  userId?: number;
  /** 页码 */
  pageNo?: number;
  /** 每页数量 */
  pageSize?: number;
}

export const getSettlementAccountPage = (data: getSettlementAccountPageData) => {
  return request("/app-api/album/settlement-account/page", "GET", data)
}



/**
 * @module createSettlementAccountModification 创建结算账号修改
 */
interface createSettlementAccountModificationData {
  id?: number;
  userId?: number;
  accountType?: number;
  accountBank?: string;
  bankName?: string;
  bankBranchId?: number;
  accountNumber?: string;
  accountName?: string;
}

export const createSettlementAccountModification = (data: createSettlementAccountModificationData) => {
  return request("/app-api/album/settlement-account-modification/create", "POST", data)
}

/**
 * @module updateSettlementAccountModification 更新结算账号修改
 */
interface updateSettlementAccountModificationData {
  id: number;
  userId?: number;
  accountType?: number;
  accountBank?: string;
  bankName?: string;
  bankBranchId?: number;
  accountNumber?: string;
  accountName?: string;
}

export const updateSettlementAccountModification = (data: updateSettlementAccountModificationData) => {
  return request("/app-api/album/settlement-account-modification/update", "PUT", data)
}

/**
 * @module getSettlementAccountModificationPage 获得结算账号修改分页
 */
interface getSettlementAccountModificationPageData {
  pageNo?: number;
  pageSize?: number;
  userId?: number;
}

export const getSettlementAccountModificationPage = (data: getSettlementAccountModificationPageData) => {
  return request("/app-api/album/settlement-account-modification/page", "GET", data)
}

/**
 * @module deleteSettlementAccountModification 删除结算账号修改
 */
interface deleteSettlementAccountModificationData {
  id: number;
}

export const deleteSettlementAccountModification = (data: deleteSettlementAccountModificationData) => {
  return request("/app-api/album/settlement-account-modification/delete", "DELETE", data)
}


/**
 * @module getBankListCompany 查询支持对公业务的银行列表
 */
interface getBankListCompanyData {
  /** 偏移量 */
  offset?: number;
  /** 每页数量，最大值200 */
  limit?: number;
}

export const getBankListCompany = (data: getBankListCompanyData) => {
  return request("/app-api/album/wechat/file/bank-list-company", "GET", data)
}

/**
 * @module getBankList 查询支持个人业务的银行列表
 */
interface getBankListData {
  /** 偏移量 */
  offset?: number;
  /** 每页数量，最大值200 */
  limit?: number;
}

export const getBankList = (data: getBankListData) => {
  return request("/app-api/album/wechat/file/bank-list", "GET", data)
}



