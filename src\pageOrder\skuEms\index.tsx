import { View, Text } from "@tarojs/components";
import { useLoad} from "@tarojs/taro";
import "./index.less";
import {
  Image,
  Loading,
} from "@arco-design/mobile-react";
import React, { useState, useEffect, useRef } from "react";
import Taro from "@tarojs/taro";
import { IconRight, IconCopy } from "@arco-iconbox/react-yk-arco";
import {
  getDeliveryBySkuNumber,
} from "@/utils/api/common/common_user";
import YkNavBar from "@/components/ykNavBar/index";
import { toast } from "@/utils/yk-common";
export default function OrderDetails() {
  const [loading, setLoading] = useState(true);
  const [logisticsData, setLogisticsData] = useState<any[]>([]);
  const [goodsInfo, setGoodsInfo] = useState<any>(null);
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);

  // 获取路由参数
  const getRouterParams = () => {
    const router = Taro.getCurrentInstance().router;
    console.log("router",router);
    return {
      orderId: router?.params?.orderId,
      skuId: router?.params?.id,
      goodsInfo: router?.params?.goodsInfo ? JSON.parse(decodeURIComponent(router?.params?.goodsInfo)) : null
    };
  };

  // 获取物流详情
  const fetchOrderDetails = async () => {
    try {
      setLoading(true);
      const params = getRouterParams();

      // 设置商品信息
      if (params.goodsInfo) {
        setGoodsInfo(params.goodsInfo);
      }

      if (!params.orderId) {
        toast("info", {
        content: "订单ID不存在",
        duration: 2000
      });
        return;
      }

      const response = await getDeliveryBySkuNumber({
        userOrderId: params.orderId,
        userOrderDetailId: params.skuId
      });

      if (response && response.code === 0 && response.data && response.data.list) {
        setLogisticsData(response.data.list);
      } else {
        toast("info", {
          content: response.msg || "获取物流详情失败",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("获取物流详情失败:", error);
      toast("info", {
        content: "获取物流详情失败",
        duration: 2000
      });
    } finally {
      setLoading(false);
    }
  };

  useLoad(() => {
    fetchOrderDetails();
  });

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <View className="order-details-box">
       {platform!== "WX" && <YkNavBar title="规格物流详情" />}
        <View style={{ padding: "50px", textAlign: "center" }}>
          <Loading />
          <Text style={{ marginTop: "10px", display: "block" }}>加载中...</Text>
        </View>
      </View>
    );
  }

  // 如果没有物流数据，显示错误状态
  if (!loading && (!logisticsData || logisticsData.length === 0)) {
    return (
      <View className="order-details-box">
        {platform !== "WX" &&<YkNavBar title="规格物流详情" />}
        <View style={{ padding: "50px", textAlign: "center" }}>
          <Text>暂无物流信息</Text>
        </View>
      </View>
    );
  }
  // 格式化时间
  const formatTime = (timestamp: number) => {
    if (!timestamp) return "--";
    const date = new Date(timestamp);
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // 获取订单状态文本
  const getOrderStatusText = (orderType: number) => {
    const statusMap = {
      1: "待付款",
      2: "待发货",
      3: "已发货",
      4: "已完成",
      5: "退款中",
      6: "已退款",
      7: "已取消",
    };
    return statusMap[orderType] || "未知状态";
  };

  
  

  // 复制快递单号
  const handleCopyTrackingNumber = (orderNo: string) => {
    Taro.setClipboardData({
      data: orderNo,
      success: () => {
        Taro.hideToast();
        toast("success", {
          content: "快递单号已复制",
          duration: 2000,
        });
      },
      fail: () => {
        Taro.hideToast();
        toast("error", {
          content: "复制失败",
          duration: 2000,
        });
      },
    });
  };


    // 获取物流数据（使用真实接口数据）
    const getLogisticsData = () => {
      if (!logisticsData || logisticsData.length === 0) {
        return [];
      }

      return logisticsData.map((logistics: any) => {
        // 获取物流状态
        const status = logistics.expressTrackResult
          ? logistics.expressTrackResult.context
          : "查无物流信息";

        // 获取更新时间
        const updateTime = logistics.expressTrackResult
          ? logistics.expressTrackResult.ftime
          : new Date(logistics.createTime).toLocaleString();

        // 使用传递过来的商品信息
        const goods = goodsInfo ? [{
          image: goodsInfo.image || "https://via.placeholder.com/60x60",
          title: goodsInfo.title || "商品名称",
          price: goodsInfo.price || "0",
          spec: goodsInfo.spec || "",
          quantity: logistics.quantityShipped || 0,
        }] : [];

        return {
          id: logistics.trackingNumber, // 使用快递单号作为ID
          status: status,
          updateTime: updateTime,
          trackingNumber: logistics.trackingNumber,
          deliveryCompany: logistics.expressCompany,
          goods: goods,
          totalQuantity: logistics.quantityShipped || 0,
        };
      });
    };

  return (
    <View className="order-details-box">
      {platform !== "WX" &&<YkNavBar title="规格物流详情" />}

      {/* 商品信息 */}
      {goodsInfo && (
        <View className="order-goods-card">
          <View className="goods-list-detail">
            <View className="goods-item">
              <Image src={goodsInfo.image || "https://via.placeholder.com/60x60"} className="goods-img" />
              <View className="goods-info-detail">
                <View className="goods-title-row">
                  <Text className="goods-title">
                    {goodsInfo.title || "商品名称"}
                  </Text>
                  <View className="goods-price-section">
                    <Text className="goods-price">
                      ￥{goodsInfo.price || 0}
                    </Text>
                  </View>
                </View>
                {/* 显示SKU信息 */}
                <View className="goods-sku-ems">
                  <Text className="sku-text">
                    {goodsInfo.spec || ""}
                  </Text>
                  <Text className="sku-quantity">
                    x{goodsInfo.quantity || 0} {goodsInfo.unshippedQuantity > 0 && `(未发货${goodsInfo.unshippedQuantity})`}
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </View>
      )}

            {/* 物流模块 */}
            {getLogisticsData().map((logistics: any, index: number) => (
        <View key={index} className="logistics-card">
           <View className="logistics-card-content">
            <Text className="logistics-card-content-time">{logistics.updateTime}</Text>
            <Text className="logistics-card-content-status">已发货</Text>
          </View>
          {/* 物流信息头部 */}
          <View className="logistics-header">
            <View className="logistics-header-content" onClick={() => {
              const receivingAddress = goodsInfo?.receivingAddress || '';
              Taro.navigateTo({
                url: `/pageOrder/emsDetail/index?trackingNumber=${encodeURIComponent(logistics.trackingNumber)}&expressCompany=${encodeURIComponent(logistics.deliveryCompany)}&receivingAddress=${encodeURIComponent(receivingAddress)}`,
              });
            }}>
              <View className="logistics-status-row">
                <Text className="logistics-status">{logistics.status}</Text>
                <Text className="logistics-time">{logistics.updateTime}</Text>
              </View>
              {/* <Image
                className="arrow-right"
                src={require("@/assets/images/common/arrow_right.png")}
              /> */}
              <IconRight className="arrow-right" />

            </View>

            {/* 快递单号 */}
            <View className="logistics-tracking-row">
              <Text className="logistics-label">{logistics.deliveryCompany}单号</Text>
              <View className="logistics-tracking-info">
                <Text className="logistics-tracking-number">
                  {logistics.trackingNumber}
                </Text>
                {/* <Image
                  className="copy-icon"
                  src={require("@/assets/images/common/copy_icon.png")}
                  onClick={() =>
                    handleCopyTrackingNumber(logistics.trackingNumber)
                  }
                /> */}
                <IconCopy className="copy-icon" onClick={() =>
                  handleCopyTrackingNumber(logistics.trackingNumber)
                } />

              </View>
            </View>
          </View>

          {/* 物流商品列表 */}
          <View className="logistics-goods-list">
            {logistics.goods.map((item: any, goodsIndex: number) => (
              <View key={goodsIndex} className="goods-item">
                <Image src={item.image} className="goods-img" />
                <View className="goods-info">
                  <View className="goods-title-row">
                    <Text className="goods-title">{item.title}</Text>
                    <View className="goods-price-section">
                      <Text className="goods-price">¥{item.price}</Text>
                      <Text className="goods-quantity">x{item.quantity}</Text>
                    </View>
                  </View>
                  <View className="goods-sku">
                    <Text className="sku-text">{item.spec}</Text>
                    <Text className="sku-count">x{item.quantity}</Text>
                  </View>
                </View>
              </View>
            ))}
          </View>

          {/* 已发数量 */}
          <View className="logistics-summary">
            <Text className="logistics-summary-text">已发数量</Text>
            <Text className="logistics-summary-count">
              {logistics.totalQuantity}
            </Text>
          </View>
        </View>
      ))}
    </View>
  );
}
