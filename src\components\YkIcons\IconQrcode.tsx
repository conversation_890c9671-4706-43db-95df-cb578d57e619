import { IconProps } from './types';

const IconQrcode: React.FC<IconProps> = ({
  color = '#4E5969',
  size = 20,
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    version="1.1"
    width={size}
    height={size}
    viewBox="0 0 20 20"
    className={className}
  >
    <g>
      <g>
        <path
          d="M2.0833332538604736,2.0833332538604736C2.0833332538604736,2.0833332538604736,10.833333253860474,2.0833332538604736,10.833333253860474,2.0833332538604736C10.833333253860474,2.0833332538604736,10.833333253860474,10.833333253860474,10.833333253860474,10.833333253860474C10.833333253860474,10.833333253860474,2.0833332538604736,10.833333253860474,2.0833332538604736,10.833333253860474C2.0833332538604736,10.833333253860474,2.0833332538604736,2.0833332538604736,2.0833332538604736,2.0833332538604736C2.0833332538604736,2.0833332538604736,2.0833332538604736,2.0833332538604736,2.0833332538604736,2.0833332538604736ZM3.7499998538604737,3.7499998538604737C3.7499998538604737,3.7499998538604737,3.7499998538604737,9.166666253860473,3.7499998538604737,9.166666253860473C3.7499998538604737,9.166666253860473,9.166666253860473,9.166666253860473,9.166666253860473,9.166666253860473C9.166666253860473,9.166666253860473,9.166666253860473,3.7499998538604737,9.166666253860473,3.7499998538604737C9.166666253860473,3.7499998538604737,3.7499998538604737,3.7499998538604737,3.7499998538604737,3.7499998538604737C3.7499998538604737,3.7499998538604737,3.7499998538604737,3.7499998538604737,3.7499998538604737,3.7499998538604737ZM12.499999253860473,2.0833332538604736C12.499999253860473,2.0833332538604736,17.916666253860473,2.0833332538604736,17.916666253860473,2.0833332538604736C17.916666253860473,2.0833332538604736,17.916666253860473,7.4999997538604735,17.916666253860473,7.4999997538604735C17.916666253860473,7.4999997538604735,12.499999253860473,7.4999997538604735,12.499999253860473,7.4999997538604735C12.499999253860473,7.4999997538604735,12.499999253860473,2.0833332538604736,12.499999253860473,2.0833332538604736C12.499999253860473,2.0833332538604736,12.499999253860473,2.0833332538604736,12.499999253860473,2.0833332538604736ZM14.166666253860473,3.7499998538604737C14.166666253860473,3.7499998538604737,14.166666253860473,5.833333253860474,14.166666253860473,5.833333253860474C14.166666253860473,5.833333253860474,16.249999253860473,5.833333253860474,16.249999253860473,5.833333253860474C16.249999253860473,5.833333253860474,16.249999253860473,3.7499998538604737,16.249999253860473,3.7499998538604737C16.249999253860473,3.7499998538604737,14.166666253860473,3.7499998538604737,14.166666253860473,3.7499998538604737C14.166666253860473,3.7499998538604737,14.166666253860473,3.7499998538604737,14.166666253860473,3.7499998538604737ZM4.9999997538604735,4.9999997538604735C4.9999997538604735,4.9999997538604735,7.916666253860473,4.9999997538604735,7.916666253860473,4.9999997538604735C7.916666253860473,4.9999997538604735,7.916666253860473,7.916666253860473,7.916666253860473,7.916666253860473C7.916666253860473,7.916666253860473,4.9999997538604735,7.916666253860473,4.9999997538604735,7.916666253860473C4.9999997538604735,7.916666253860473,4.9999997538604735,4.9999997538604735,4.9999997538604735,4.9999997538604735C4.9999997538604735,4.9999997538604735,4.9999997538604735,4.9999997538604735,4.9999997538604735,4.9999997538604735ZM15.416666253860473,9.166666253860473C15.416666253860473,9.166666253860473,17.916666253860473,9.166666253860473,17.916666253860473,9.166666253860473C17.916666253860473,9.166666253860473,17.916666253860473,10.833333253860474,17.916666253860473,10.833333253860474C17.916666253860473,10.833333253860474,15.416666253860473,10.833333253860474,15.416666253860473,10.833333253860474C15.416666253860473,10.833333253860474,15.416666253860473,9.166666253860473,15.416666253860473,9.166666253860473C15.416666253860473,9.166666253860473,15.416666253860473,9.166666253860473,15.416666253860473,9.166666253860473ZM2.0833332538604736,12.499999253860473C2.0833332538604736,12.499999253860473,7.4999997538604735,12.499999253860473,7.4999997538604735,12.499999253860473C7.4999997538604735,12.499999253860473,7.4999997538604735,17.916666253860473,7.4999997538604735,17.916666253860473C7.4999997538604735,17.916666253860473,2.0833332538604736,17.916666253860473,2.0833332538604736,17.916666253860473C2.0833332538604736,17.916666253860473,2.0833332538604736,12.499999253860473,2.0833332538604736,12.499999253860473C2.0833332538604736,12.499999253860473,2.0833332538604736,12.499999253860473,2.0833332538604736,12.499999253860473ZM12.499999253860473,9.166666253860473C12.499999253860473,9.166666253860473,14.166666253860473,9.166666253860473,14.166666253860473,9.166666253860473C14.166666253860473,9.166666253860473,14.166666253860473,10.833333253860474,14.166666253860473,10.833333253860474C14.166666253860473,10.833333253860474,12.499999253860473,10.833333253860474,12.499999253860473,10.833333253860474C12.499999253860473,10.833333253860474,12.499999253860473,9.166666253860473,12.499999253860473,9.166666253860473C12.499999253860473,9.166666253860473,12.499999253860473,9.166666253860473,12.499999253860473,9.166666253860473ZM3.7499998538604737,14.166666253860473C3.7499998538604737,14.166666253860473,3.7499998538604737,16.249999253860473,3.7499998538604737,16.249999253860473C3.7499998538604737,16.249999253860473,5.833333253860474,16.249999253860473,5.833333253860474,16.249999253860473C5.833333253860474,16.249999253860473,5.833333253860474,14.166666253860473,5.833333253860474,14.166666253860473C5.833333253860474,14.166666253860473,3.7499998538604737,14.166666253860473,3.7499998538604737,14.166666253860473C3.7499998538604737,14.166666253860473,3.7499998538604737,14.166666253860473,3.7499998538604737,14.166666253860473ZM12.499999253860473,12.499999253860473C12.499999253860473,12.499999253860473,17.916666253860473,12.499999253860473,17.916666253860473,12.499999253860473C17.916666253860473,12.499999253860473,17.916666253860473,17.916666253860473,17.916666253860473,17.916666253860473C17.916666253860473,17.916666253860473,12.499999253860473,17.916666253860473,12.499999253860473,17.916666253860473C12.499999253860473,17.916666253860473,12.499999253860473,12.499999253860473,12.499999253860473,12.499999253860473C12.499999253860473,12.499999253860473,12.499999253860473,12.499999253860473,12.499999253860473,12.499999253860473ZM14.166666253860473,14.166666253860473C14.166666253860473,14.166666253860473,14.166666253860473,16.249999253860473,14.166666253860473,16.249999253860473C14.166666253860473,16.249999253860473,16.249999253860473,16.249999253860473,16.249999253860473,16.249999253860473C16.249999253860473,16.249999253860473,16.249999253860473,14.166666253860473,16.249999253860473,14.166666253860473C16.249999253860473,14.166666253860473,14.166666253860473,14.166666253860473,14.166666253860473,14.166666253860473C14.166666253860473,14.166666253860473,14.166666253860473,14.166666253860473,14.166666253860473,14.166666253860473ZM9.166666253860473,14.166666253860473C9.166666253860473,14.166666253860473,9.166666253860473,12.499999253860473,9.166666253860473,12.499999253860473C9.166666253860473,12.499999253860473,10.833333253860474,12.499999253860473,10.833333253860474,12.499999253860473C10.833333253860474,12.499999253860473,10.833333253860474,14.166666253860473,10.833333253860474,14.166666253860473C10.833333253860474,14.166666253860473,9.166666253860473,14.166666253860473,9.166666253860473,14.166666253860473C9.166666253860473,14.166666253860473,9.166666253860473,14.166666253860473,9.166666253860473,14.166666253860473ZM9.166666253860473,17.916666253860473C9.166666253860473,17.916666253860473,9.166666253860473,15.416666253860473,9.166666253860473,15.416666253860473C9.166666253860473,15.416666253860473,10.833333253860474,15.416666253860473,10.833333253860474,15.416666253860473C10.833333253860474,15.416666253860473,10.833333253860474,17.916666253860473,10.833333253860474,17.916666253860473C10.833333253860474,17.916666253860473,9.166666253860473,17.916666253860473,9.166666253860473,17.916666253860473C9.166666253860473,17.916666253860473,9.166666253860473,17.916666253860473,9.166666253860473,17.916666253860473Z"
          fillRule="evenodd"
          fill={color}
          fillOpacity="1"
        />
      </g>
    </g>
  </svg>
);

export default IconQrcode;
