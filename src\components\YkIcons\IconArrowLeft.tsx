import { IconProps } from './types';

const IconArrowLeft: React.FC<IconProps> = ({
  color = 'var(--primary-color)',
  size = 24,
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    version="1.1"
    width={size}
    height={size}
    viewBox="0 0 15 24"
    className={className}
  >
    <g transform="matrix(-1,0,0,1,30,0)">
      <path
        d="M15,4.16125L15,19.8387C15,21.5158,16.93986,22.4481,18.24939,21.4005L28.047800000000002,13.5617C29.0486,12.7611,29.0486,11.2389,28.047800000000002,10.4383L18.24939,2.59951C16.93986,1.55189,15,2.48424,15,4.16125Z"
        fill={color}
        fillOpacity="1"
      />
    </g>
  </svg>
);

export default IconArrowLeft;
