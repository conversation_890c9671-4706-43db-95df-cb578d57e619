import { View, Text, Input, Button } from "@tarojs/components";
import React, { useState, useEffect,useRef } from "react";
import { Checkbox, SearchBar } from "@arco-design/mobile-react";
import Taro,{ useLoad } from "@tarojs/taro";
import { getTagList} from "@/utils/api/common/common_user";
import YkNavBar from "@/components/ykNavBar/index";
import { toast } from "@/utils/yk-common";
import "./addTag.less";
const mockTags = [
  { id: 1, name: "01-鞋靴", count: 3 },
  { id: 2, name: "02-服饰", count: 3 },
  { id: 3, name: "03-配饰", count: 1 }
];

interface Tag {
    id: number;
    name: string;
    count: number;
    pinned?: boolean;
  }

export default function AddTag() {
  const userInfo = Taro.getStorageSync("userInfo");
  const [selected, setSelected] = useState<number[]>([]);
  const [search, setSearch] = useState("");
  const [allIds, setAllIds] = useState<number[]>([]);
  const allSelected = selected.length === allIds.length;
  const [originTags, setOriginTags] = useState<Tag[]>([]);
  const [tagLimit, setTagLimit] = React.useState(20);
  const [tags, setTags] = useState<Tag[]>([]);
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  const filteredTags = tags.filter(tag =>
    tag.name.includes(search)
  );


  const getTagListData = async () => {
    let data = {
        userId: userInfo.id,
        type: 1,
        // sortType: Taro.getStorageSync('selectedTagSort') || 0,
    };

    let res: any;

    res = await getTagList(data);

    if (res && res.code == 0) {
        const tagsList = res.data.map((item: any) => {
            // 统计 dynamicsId 的数量（逗号分隔的ID个数）
            const dynamicsCount = item.dynamicsId 
                ? item.dynamicsId.split(',').filter((id: string) => id.trim() !== '').length 
                : 0;
            return {
                id: item.id,
                name: item.name,
                count: dynamicsCount, // 使用实际的动态ID数量
                isTop: item.isTop
            };
            });
        setAllIds(res.data.map(i => i.id));
        setOriginTags(tagsList);
        setTags(tagsList);
        // 判断返回数据的总条数
        if (res.data.length < tagLimit) {    //最后一页
            // setIsLoadEnd(true);
            // setLoadStatus("nomore");
        } else {
            // setLoadStatus("prepare");
            // setIsLoadEnd(false);
        // albumPageRef.current = albumPageRef.current + 1;
        }
    } else {
        //setLoadStatus("retry");
        toast("info", {
        content: "网络异常，请重试",
        duration: 2000
      });
    }
  };

  useLoad(() => {
    //const albumPageRef = Taro.getCurrentPages()[1];
    //albumPageRef.current = 1;
    //setIsLoadEnd(false);
    getTagListData();

    // 初始化已选择的标签
    const selectedTags = Taro.getStorageSync('selectedTag') || [];
    if (selectedTags.length > 0) {
      const selectedIds = selectedTags.map((tag: any) => tag.id);
      setSelected(selectedIds);
    }
  });

  useEffect(() => {
    console.log(tags, "albumList");

  }, [tags]);


  return (
    <View className="add-tag-page">
      <View className="add-tag-navbar">
      {platform !== "WX" &&<YkNavBar title="添加标签" />}
      </View>
            
      <SearchBar 
        actionButton={<span className="demo-search-btn"></span>} 
        placeholder="搜索"
        onChange={(e) => setSearch(e.target.value)}
        className="demo-input-btn-input"
        clearable
        onClear={() => {
          setSearch('');              
        }}
      />  
  
      <View className="add-tag-list">
        <Checkbox.Group
          value={selected}
          onChange={vals => setSelected(vals as number[])}
        >
          {filteredTags.map(tag => (
            <View className="add-tag-item" key={tag.id}>
              <Checkbox value={tag.id} className="add-tag-checkbox" shape="circle">
                <Text className="add-tag-label">
                  {tag.name}（{tag.count}）
                </Text>
              </Checkbox>
            </View>
          ))}
        </Checkbox.Group>
      </View>
      <Button
        className="add-tag-btn"
        onClick={() => {
          // 根据当前选中的ID获取对应的标签对象
          const selectedItems = tags.filter(item => selected.includes(item.id));

          // 直接用当前选中的标签替换缓存中的标签
          Taro.setStorageSync('selectedTag', selectedItems);
          Taro.navigateBack();
          // 这里可以回传选中的标签
        }}
      >
        确定({selected.length})
      </Button>
    </View>
  );
}