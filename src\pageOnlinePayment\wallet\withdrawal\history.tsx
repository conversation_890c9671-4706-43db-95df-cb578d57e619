import { View, Text } from "@tarojs/components";
import "./history.less";
import Taro from "@tarojs/taro";
import { useState, useEffect } from "react";
import { Toast, LoadMore, Divider } from "@arco-design/mobile-react";
import React, { useRef } from "react";
// 组件
import YkNavBar from "@/components/ykNavBar/index";
// API
import { getWithdrawalList } from "@/utils/api/common/common_user";

// 类型定义
interface WithdrawalRecord {
  id: number;
  userId: number;
  outRequestNo: string;
  withdrawId: number;
  amount: number;
  remark: string;
  bankMemo: string;
  accountType: number;
  type: number;
  createTime: string;
  status?: string; // 提现状态
  accountName?: string; // 收款账户名称
  accountNumber?: string; // 收款账号
  arrivalTime?: string; // 到账时间
  serialNumber?: string; // 流水单号
  failureReason?: string; // 失败原因
}

interface PageState {
  loading: boolean;
  refreshing: boolean;
  data: WithdrawalRecord[];
  hasMore: boolean;
  page: number;
  pageSize: number;
}

// 提现状态映射
const getWithdrawalStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    CREATE_SUCCESS: "提现中",
    INIT: "提现中",
    SUCCESS: "提现成功",
    FAIL: "提现失败",
    REFUND: "提现失败",
    CLOSE: "提现失败",
  };
  return statusMap[status] || "提现中";
};

export default function WithdrawalHistory() {
  // ==================== 状态管理 ====================
  const [pageState, setPageState] = useState<PageState>({
    loading: false,
    refreshing: false,
    data: [],
    hasMore: true,
    page: 1,
    pageSize: 20,
  });
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  // ==================== 生命周期 ====================
  useEffect(() => {
    initPage();
  }, []);

  // ==================== 页面初始化 ====================
  const initPage = async () => {
    setPageState((prev) => ({ ...prev, loading: true }));
    try {
      await loadData(1, true);
    } catch (error) {
      console.error("页面初始化失败:", error);
      Toast.error("网络异常，请重试");
    } finally {
      setPageState((prev) => ({ ...prev, loading: false }));
    }
  };

  // ==================== 数据加载 ====================
  const loadData = async (page: number = 1, isRefresh: boolean = false, debugScenario?: string) => {
    if (pageState.loading && !isRefresh) return;

    setPageState((prev) => ({
      ...prev,
      loading: true,
      refreshing: isRefresh,
    }));

    try {
      const userInfo = Taro.getStorageSync("userInfo");
      if (!userInfo?.id) {
        console.error("[提现记录] 获取用户信息失败");
        return;
      }

      const response = await getWithdrawalList({
        pageNo: page,
        pageSize: pageState.pageSize,
        userId: userInfo.id,
      });

      if (response && response.code === 0) {
        const newData = response.data.list || [];
        setPageState((prev) => ({
          ...prev,
          data: isRefresh ? newData : [...prev.data, ...newData],
          hasMore: newData.length === prev.pageSize,
          page: page,
        }));
      } else {
        Toast.error(response.msg || "网络异常，请重试");
      }
    } catch (error) {
      console.error("加载数据失败:", error);
      Toast.error("网络错误，请重试");
    } finally {
      setPageState((prev) => ({
        ...prev,
        loading: false,
        refreshing: false,
      }));
    }
  };

  // ==================== 事件处理 ====================
  const handleBack = () => {
    Taro.navigateBack({ delta: 1 });
  };


  // 处理提现记录点击
  const handleRecordClick = (record: WithdrawalRecord) => {
    // 跳转到提现详情页
    const params = {
      id: record.id,
      withdrawId: record.withdrawId,
      amount: record.amount,
      createTime: record.createTime,
      status: getWithdrawalStatus(record),
    };

    const url = `/pages/wallet/withdrawal/detail?${Object.entries(params)
      .map(([k, v]) => `${k}=${encodeURIComponent(v || "")}`)
      .join("&")}`;

    Taro.navigateTo({ url });
  };

  // ==================== 工具函数 ====================
  const formatAmount = (amount: number): string => {
    return amount.toFixed(2);
  };

  const formatDateTime = (dateTimeStr: string): string => {
    if (!dateTimeStr) return "";
    const date = new Date(dateTimeStr);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
      2,
      "0"
    )}-${String(date.getDate()).padStart(2, "0")} ${String(
      date.getHours()
    ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}:${String(
      date.getSeconds()
    ).padStart(2, "0")}`;
  };

  const getWithdrawalStatus = (record: WithdrawalRecord): string => {
    // 根据API返回的状态字段映射到显示状态
    // 这里需要根据实际API返回的状态字段进行调整
    return record.status ? getWithdrawalStatusText(record.status) : "提现中";
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case "提现成功":
        return "success";
      case "提现失败":
        return "error";
      case "提现中":
      default:
        return "processing";
    }
  };

  // ==================== 渲染 ====================
  return (
    <View className="withdrawalHistoryPage">
      {platform !== "WX" &&<YkNavBar title="提现记录" onClickLeft={handleBack} />}

      <View className="content">
        {pageState.data.length > 0 ? (
          <View className="withdrawal-list">
            {pageState.data.map((record) => (
              <View
                key={record.id}
                className="withdrawal-item"
                onClick={() => handleRecordClick(record)}
              >
                <View className="withdrawal-header">
                  <View className="status-amount">
                    <Text
                      className={`status-text ${getStatusColor(
                        getWithdrawalStatus(record)
                      )}`}
                    >
                      {getWithdrawalStatus(record)}
                    </Text>
                    <Text className="amount-text">
                      ￥{formatAmount(record.amount)}
                    </Text>
                  </View>
                </View>
                <View className="withdrawal-notice">
                  {getWithdrawalStatus(record) === "提现失败" &&
                    record.failureReason && (
                      <View className="failure-reason">
                        <Text className="failure-label">失败原因</Text>
                        <Text className="failure-text">
                          {record.failureReason}
                        </Text>
                      </View>
                    )}
                </View>
                <View className="withdrawal-details">
                  <View className="detail-row">
                    <Text className="detail-label">收款账户</Text>
                    <View className="account-info">
                      <Text className="account-name">
                        {record.accountName || "福建朱雀科技有限公司"}
                      </Text>
                      <Text className="account-number">
                        {record.accountNumber || "6391****3333"}
                      </Text>
                    </View>
                  </View>

                  <View className="detail-row">
                    <Text className="detail-label">提现时间</Text>
                    <Text className="detail-value">
                      {formatDateTime(record.createTime)}
                    </Text>
                  </View>

                  {getWithdrawalStatus(record) === "提现成功" &&
                    record.arrivalTime && (
                      <View className="detail-row">
                        <Text className="detail-label">到账时间</Text>
                        <Text className="detail-value">
                          {formatDateTime(record.arrivalTime)}
                        </Text>
                      </View>
                    )}

                  <View className="detail-row">
                    <Text className="detail-label">提现单号</Text>
                    <Text className="detail-value">
                      {record.outRequestNo || record.withdrawId}
                    </Text>
                  </View>

                  {getWithdrawalStatus(record) === "提现成功" &&
                    record.serialNumber && (
                      <View className="detail-row">
                        <Text className="detail-label">流水单号</Text>
                        <Text className="detail-value">
                          {record.serialNumber}
                        </Text>
                      </View>
                    )}
                </View>
              </View>
            ))}
            <LoadMore
              status={
                pageState.loading
                  ? "loading"
                  : pageState.hasMore
                  ? "prepare"
                  : "nomore"
              }
              getData={async (callback) => {
                if (pageState.hasMore && !pageState.loading) {
                  try {
                    await loadData(pageState.page + 1);
                    callback(pageState.hasMore ? "prepare" : "nomore");
                  } catch (error) {
                    callback("retry");
                  }
                } else {
                  callback(pageState.hasMore ? "prepare" : "nomore");
                }
              }}
              noMoreArea={<Divider width={36}>没有更多</Divider>}
            />
          </View>
        ) : !pageState.loading ? (
          <View className="empty-state">
            <Text>暂无提现记录</Text>
          </View>
        ) : null}
      </View>
    </View>
  );
}
