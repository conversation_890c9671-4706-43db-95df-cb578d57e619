@import '@arco-design/mobile-react/style/mixin.less';
@import '@/utils/css/variables.less';

.yk-imagepicker {
  margin: 0 !important;
  padding: 0 !important;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;

  .yk-imagepicker-container {
    width: 100%;
    height: 90px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    position: relative;
    overflow: hidden;
    .use-var(background-color, background-color);
    .use-dark-mode-query({
      background-color: @dark-background-color;
      border-color: #434343;
    });
  }

  .yk-imagepicker-image-container {
    width: 100%;
    height: 100%;
    position: relative;

    .yk-imagepicker-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }

    .yk-imagepicker-delete {
      position: absolute;
      top: 4px;
      right: 4px;
      width: 20px;
      height: 20px;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      .yk-imagepicker-delete-icon {
        font-size: 16px;
        color: #fff;
        font-weight: bold;
        line-height: 1;
      }
    }
  }

  .yk-imagepicker-add {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      border-color: #1890ff;
      .use-dark-mode-query({
        border-color: #177ddc;
      });
    }

    &.disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }

    .yk-imagepicker-add-icon {
      font-size: 24px;
      color: #d9d9d9;
      margin-bottom: 4px;
      line-height: 1;
      font-weight: normal;
      .use-dark-mode-query({
        color: #434343;
      });
    }

    .yk-imagepicker-add-text {
      font-size: 12px;
      color: #999;
      .use-dark-mode-query({
        color: #666;
      });
    }
  }


  .yk-imagepicker-label {
    width: 100%;
    margin-top: 5px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 5px;
    padding: 0 10px;
    &-text {
      font-size: 11px;
      color: #86909C;
    }
  }

  .yk-imagepicker-ocr-status {
    width: 100%;
    margin-top: 8px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px 10px;
    background-color: #F2F3F5;
    border-radius: 4px;
  }

  .yk-imagepicker-ocr-text {
    font-size: 12px;
    color: #4E5969;
  }

  .yk-imagepicker-error-status {
    width: 100%;
    margin-top: 8px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding: 8px 10px;
    background-color: @--danger-1;
    border-radius: 4px;
  }

  .yk-imagepicker-error-text {
    font-size: 12px;
    color: @--danger-6;
  }
}