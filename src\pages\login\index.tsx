import { View, Text } from "@tarojs/components";
import { Button, Checkbox, Image } from "@arco-design/mobile-react";
import { IconWechat } from "@/components/YkIcons";
import "./index.less";
import Taro, { useDidShow } from "@tarojs/taro";
import {
  socialLogin,
  getUserInfo,
  appleLogin,
  hmLogin,
} from "@/utils/api/common/common_user";
import { getSystem } from "@arco-design/mobile-utils";
import { useState, useRef } from "react";
import YkNavBar from "@/components/ykNavBar";
import { Style } from "@tarojs/runtime";
import { useReady } from "@tarojs/taro";
import ProtocolMask from "@/components/ProtocolMask";
import UserAgreementPopup from "@/components/UserAgreementPopup";
import { useEffect } from "react";
import { md5 } from "js-md5";
import { toast } from "@/utils/yk-common";
import { getVersion } from "@/utils/api/common/common_user";

// 声明 window 对象的扩展类型
declare global {
  interface Window {
    acceptPrivateAgreement?: {
      acceptPrivateAgreement: () => void;
    };
    wxLogin?: {
      wxLogin: () => void;
    };
    wxLoginCode?: (data: any) => void;
    iswxinstall?: (result: string) => void;
    webkit?: {
      messageHandlers?: {
        wxLogin?: {
          postMessage: (message: string) => void;
        };
        iswxinstall?: {
          postMessage: (message: string) => void;
        };
      };
    };
  }
}

export default function Login() {
  // const [wxLoginInfoState, setWxLoginInfoState] = useState({});
  const [check, setCheck] = useState(false);
  const [visible, setVisible] = useState(false);
  const [protocolVisible, setProtocolVisible] = useState(false);
  const [platform, setPlatform] = useState("H5");
  const platformRef = useRef("H5");
  const loginType = useRef("wx");
  const [version, setVersion] = useState({});
  const [isWechatInstalled, setIsWechatInstalled] = useState(true); // 默认为true，避免闪烁

  useEffect(() => {
     // const loginInfo = window.wxLoginInfo;
    // window.wxLoginInfo = wxLoginAndroid;
    let uaAll = window.navigator.userAgent;

    // 判断是否是 Android 或 iOS
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
      platformRef.current = "Android";
    } else if (isIos) {
      setPlatform("IOS");
      platformRef.current = "IOS";
      window.appleLogin = getIosUser;
      // ios获取版本号
      window.webkit.messageHandlers.showVersion.postMessage("");
      window.showVersion = getIosVersion;
      // iOS检查微信是否安装
      window.iswxinstall = checkWechatInstall;
      if (window.webkit?.messageHandlers?.iswxinstall?.postMessage) {
        window.webkit.messageHandlers.iswxinstall.postMessage("");
      }
    } else if (isHM) {
      setPlatform("HM");
      platformRef.current = "HM";
      window.hmLoginInfo = hmLoginInfo;
    }

    window.wxLoginCode = wxLoginCode;

    getVersion(PACKAGE_NAME).then((res) => {
      console.log(res);
      if (res && res.code == 0) {
        setVersion(res.data);
        Taro.setStorageSync("versionConfig", res.data);
      }
    });

    return () => {
      delete window.wxLoginCode;
      delete window.hmLoginInfo;
      delete window.iswxinstall;
    };
  }, []);

  useDidShow(() => {
    let startPrivacy = Taro.getStorageSync("startPrivacy");
    if (!startPrivacy) {
      setProtocolVisible(true);
    } else {
      // 添加安全检查
      if (platformRef.current  === "Android") {
        window.acceptPrivateAgreement.acceptPrivateAgreement();
      } else if (platformRef.current === "IOS") {
      } else if (platformRef.current === "HM") {
        window.harmony.checkUpdate();
      }
    }
    let userInfo = Taro.getStorageSync("userInfo");
    // console.log('userInfo', userInfo);
    // 检查用户是否已登录
    if (userInfo) {
      // 检查当前页面栈，确保不会重复跳转
      const pages = Taro.getCurrentPages();
      const currentPage = pages[pages.length - 1];

      // 只有当前页面是登录页面时才跳转
      console.log("currentPage", currentPage);
      console.log("检测到用户已登录，准备跳转到首页");
      // 使用 reLaunch 而不是 navigateTo，确保清空页面栈
      Taro.reLaunch({ url: "/pages/index/index" });
    } else {
      console.log("用户未登录");
    }

   
  });

  const handleProtocolConfirm = () => {
    Taro.setStorageSync("startPrivacy", true);
    setProtocolVisible(false);
    // 添加安全检查
    if (platformRef.current === "Android") {
      window.acceptPrivateAgreement.acceptPrivateAgreement();
    }
  };

  const handleAutoLogin = () => {
    if (loginType.current === "wx") {
      handleWechatLogin();
    } else if (loginType.current === "ios") {
      handleIosLogin();
    } else if (loginType.current === "hm") {
      handleHarmonyLogin();
    }
  };

  const handleHarmonyLoginCheck = () => {
    loginType.current = "hm";
    if (!check) {
      setVisible(true);
      return;
    }
    window.harmony.hmLogin();
  };

  const handleWechatLoginCheck = () => {
    loginType.current = "wx";
    if (!check) {
      setVisible(true);
      return;
    }
    handleWechatLogin();
  };

  const isShowIosLogin = () => {
    if (
      platform == "IOS" &&
      window.localStorage.getItem(`${APP_NAME}_version`) &&
      window.localStorage
        .getItem(`${APP_NAME}_version`)
        .includes(version.iosReviewVersion)
    ) {
      return true;
    }
    return false;
  };

  const isShowHarmonyLogin = () => {
    if (
      platform == "HM" &&
      window.localStorage.getItem(`${APP_NAME}_version`) &&
      window.localStorage
        .getItem(`${APP_NAME}_version`)
        .includes(version.harmonyosReviewVersion)
    ) {
      return true;
    }
    return false;
  };

  const handleIosLoginCheck = () => {
    loginType.current = "ios";
    if (!check) {
      setVisible(true);
      return;
    }
    handleIosLogin();
  };

  const handleHarmonyLogin = () => {
    window.harmony.hmLogin();
  };

  const handleIosLogin = () => {
    window.webkit.messageHandlers.appleLogin.postMessage("");
  };

  const handleWechatLogin = () => {
    if (platform == "Android") {
      // 添加安全检查
      if (window.wxLogin?.wxLogin) {
        window.wxLogin.wxLogin();
      } else {
        toast("error", {
          content: "微信登录功能初始化失败",
          duration: 2000,
        });
      }
      // wechatLoginApi()
    } else if (platform == "IOS") {
      // 添加更完整的安全检查
      if (window.webkit?.messageHandlers?.wxLogin?.postMessage) {
        window.webkit.messageHandlers.wxLogin.postMessage("");
      } else {
        toast("error", {
          content: "微信登录功能初始化失败",
          duration: 2000,
        });
      }
    } else if (platform == "HM") {
      // 添加更完整的安全检查
      window.harmony.wxLogin();
    } else {
      toast("info", {
        content: "当前平台不支持微信登录",
        duration: 2000,
      });
    }
  };

  const wechatLoginApi = (code: string) => {
    // 修复数据类型定义
    const data: { type: number; code: string; state: string } = {
      type: 32,
      code: code,
      state: md5(new Date().getTime().toString()), // 修复 md5 调用方式
    };

    console.log(data);
    socialLogin(data).then((res) => {
      console.log(res);
      if (res && res.code == 0) {
        Taro.setStorageSync("userInfo", res.data);

        getUserInfo().then((res2) => {
          if (res2 && res2.code == 0) {
            //合并用户信息
            toast("success", {
              content: "登录成功",
              duration: 2000,
            });
            Taro.setStorageSync("userInfo", {
              ...res2.data,
              ...res.data,
            });

            Taro.reLaunch({ url: "/pages/index/index" });
          }
        });
      } else {
        toast("error", {
          content: res.msg,
          duration: 2000,
        });
      }
    });
  };

  const appleLoginApi = (sub: string) => {
    const data: { sub: string } = {
      sub: sub,
    };
    appleLogin(data).then((res) => {
      console.log(res);
      if (res && res.code == 0) {
        Taro.setStorageSync("userInfo", res.data);

        getUserInfo().then((res2) => {
          if (res2 && res2.code == 0) {
            toast("success", {
              content: "登录成功",
              duration: 2000,
            });
            Taro.setStorageSync("userInfo", {
              ...res2.data,
              ...res.data,
            });
            Taro.reLaunch({ url: "/pages/index/index" });
          }
        });
      }
    });
  };

  const hmLoginApi = (unionid: string, nickname: string, head_img: string) => {
    const data= {
      unionId: unionid,
      nickname: nickname,
      avatar: head_img,
    };
    hmLogin(data).then((res) => {
      console.log(res);
      if (res && res.code == 0) {
        Taro.setStorageSync("userInfo", res.data);

        getUserInfo().then((res2) => {
          if (res2 && res2.code == 0) {
            toast("success", {
              content: "登录成功",
              duration: 2000,
            });
            Taro.setStorageSync("userInfo", {
              ...res2.data,
              ...res.data,
            });
            Taro.reLaunch({ url: "/pages/index/index" });
          }
        });
      }
    });
  };

  const getIosVersion = (data: any) => {
      window.localStorage.setItem(`${APP_NAME}_version`, data);
  }

  const getIosUser = (data: any) => {
    console.log(data);
    if (data) {
      console.log("getIosUser: " + JSON.stringify(data));
      appleLoginApi(data);
    } else {
      toast("info", {
        content: "获取苹果信息失败",
        duration: 2000,
      });
    }
  };

  const hmLoginInfo = (data: any) => {
    console.log(data);
    if (data) {
      console.log("hmLoginInfo: " + JSON.stringify(data));
      hmLoginApi(data.unionid, data.nickname, data.head_img);
    } else {
      toast("info", {
        content: "获取华为信息失败",
        duration: 2000,
      });
    }
  };

  const wxLoginCode = (data: any) => {
    if (data) {
      console.log("wxLoginCode: " + JSON.stringify(data));
      wechatLoginApi(data);
    } else {
      toast("info", {
        content: "获取微信信息失败",
        duration: 2000,
      });
    }
  };

  const checkWechatInstall = (result: string) => {
    console.log("checkWechatInstall result: " + result);
    // iOS端返回 '1' 表示已安装微信，'0' 表示未安装
    setIsWechatInstalled(result === '1');
  };

  return (
    <View className="login">
      <YkNavBar switchTab title="" />
      <View className="logo-container">
        <Image
          className="logo"
          src={require("../../assets/images/login/logo.png")}
          bottomOverlap={null}
        />
        <Text className="app-name">海胆相册</Text>
      </View>

      <View className="login-buttons">
        {isShowHarmonyLogin() && (
          <Button
            className="huawei-login"
            icon={
              <Image
                bottomOverlap={null}
                src={require("@/assets/images/login/huawei_icon.png")}
                className="huawei-icon"
              />
            }
            //   shape='round'
            onClick={handleHarmonyLoginCheck}
          >
            华为账号登录
          </Button>
        )}
        {isShowIosLogin() && (
          <Button
            className="ios-login"
            icon={
              <Image
                bottomOverlap={null}
                src={require("@/assets/images/login/ios_icon.png")}
                className="ios-icon"
              />
            }
            type="ghost"
            onClick={handleIosLoginCheck}
          >
            通过Apple登录
          </Button>
        )}
        {/* iOS平台需要检查微信是否安装，其他平台直接显示 */}
        {(platform !== "IOS" || isWechatInstalled) && (
          <Button
            className="wechat-login"
            icon={<IconWechat color="#fff" className="wechat-icon" />}
            //   shape='round'
            onClick={handleWechatLoginCheck}
          >
            微信登录
          </Button>
        )}

        <Button
          className="phone-login"
          //   shape='round'
          type="ghost"
          onClick={() => {
            Taro.navigateTo({ url: "/pageLogin/phoneLogin/index" });
          }}
        >
          手机号登录
        </Button>
      </View>

      <View className="agreement">
        <Checkbox
          checked={check}
          onChange={() => {
            setCheck(!check);
          }}
          value={""}
        ></Checkbox>
        <Text>已阅读并同意</Text>
        <Text
          className="link"
          onClick={() => {
            Taro.navigateTo({
              url: `/pageSetting/webView/index?url=${USER_AGREEMENT}&name=用户协议`,
            });
          }}
        >
          《用户协议》
        </Text>
        <Text>和</Text>
        <Text
          className="link"
          onClick={() => {
            Taro.navigateTo({
              url: `/pageSetting/webView/index?url=${PRIVACY_POLICY}&name=隐私政策`,
            });
          }}
        >
          《隐私政策》
        </Text>
      </View>

      <UserAgreementPopup
        visible={visible}
        onClose={() => setVisible(false)}
        onConfirm={handleAutoLogin}
        onSetCheck={setCheck}
      />

      {/* 修复 ProtocolMask 组件缺少 title 属性 */}
      <ProtocolMask
        platformRef={platform}
        visible={protocolVisible}
        close={() => setProtocolVisible(false)}
        onConfirm={handleProtocolConfirm}
        title="隐私政策"
      />
    </View>
  );
}
