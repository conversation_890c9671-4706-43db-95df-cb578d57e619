@import '@arco-design/mobile-react/style/mixin.less';

/* 全屏遮罩 */
.fullscreen-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    .use-var(background-color, background-color);
    .use-dark-mode-query({
        background-color: @dark-background-color;
    });
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999; /* 确保遮罩层在最上层 */
  }
  
  .loading-container {
    text-align: center;
  }
  
  .loading-text {
    .rem(margin-top,12); 
    .rem(font-size,16);  
    .use-var(color, primary-color);
    .use-dark-mode-query({
        color: @dark-primary-color; 
    });
  }
  