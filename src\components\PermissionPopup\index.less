@import "@arco-design/mobile-react/style/mixin.less";

.permission-popup {
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  .use-var(background-color, background-color);

  .use-dark-mode-query({
    background-color: @dark-background-color;
  });

  .popupBox {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    .use-var(background-color, background-color);

    .use-dark-mode-query({
      background-color: @dark-background-color;
    });

    &-icon {
      .rem(font-size, 36);
      .rem(margin-top, 166);
      display: block;
    }

    &-container {
      width: 100%;
      .rem(margin-top, 20);
      display: flex;
      align-items: center;
      flex-direction: column;

      &-title {
        .rem(font-size, 17);
        // color: #CBCBCB;
        .use-var(color, font-color);

        .use-dark-mode-query({
          color: @dark-font-color;
        });
        text-align: center;
      }

      &-hint {
        .rem(margin-left, 15);
        .rem(margin-right, 15);
        .rem(margin-top, 7);
        .rem(font-size, 16);
        // color: #999;
        .use-var(color, sub-font-color);
        .use-dark-mode-query({
          color: @dark-sub-font-color;
        });
        text-align: center;
      }

      // &-hint2 {
      //   .rem(margin-top, 7);
      //   .rem(font-size, 16);
      //   // color: #999;
      //   .use-var(color, sub-font-color);

      //   .use-dark-mode-query({
      //     color: @dark-sub-font-color;
      //   });
      //   text-align: center;
      // }
    }

    &-bottom {
      .rem(width, 345);
      .rem(height, 110);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      position: absolute;
      .rem(bottom, 0);
      // background-color: #FFFFFF;
      // .use-var(background-color, background-color);

      // .use-dark-mode-query({
      //   background-color: @dark-background-color;
      // });
      .rem(padding, 10);
      .rem(font-size, 18);
      // color: #007AFF;
      // .use-var(color, font-color);

      // .use-dark-mode-query({
      //   color: @dark-font-color;
      // });
      text-align: center;

      &-cancel {
        border-radius: 0;
        width: 100%;
        .rem(height, 50);
        .rem(line-height, 50);
        .use-var(color, font-color);
        .rem(border-bottom-left-radius, 13);
        .rem(border-bottom-right-radius, 13);
        .use-dark-mode-query({
          color: @dark-font-color;
        });
        .use-var(background-color, primary-color);

        .use-dark-mode-query({
          background-color: @dark-primary-color;
        });
      }

      &-confirm {
        border-radius: 0;
        width: 100%;
        .rem(height, 50);
        .rem(line-height, 50);
        .rem(border-top-left-radius, 13);
        .rem(border-top-right-radius, 13);
        .use-var(color, font-color);
        .use-dark-mode-query({
          color: @dark-font-color;
        });
        .use-var(background-color, primary-color);

        .use-dark-mode-query({
          background-color: @dark-primary-color;
        });
      }
    }
  }
}
