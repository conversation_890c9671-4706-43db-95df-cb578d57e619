@import "@arco-design/mobile-react/style/mixin.less";

// 联系商家弹框样式
.contact-popup {
  z-index: 4000;
}

.popup-bottom-contact-show {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  border-radius: 15px 15px 0 0;
  background-color: #ffffff;
  .use-dark-mode-query({
    background-color: @dark-card-background-color !important;
  });
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  &-qrcode {
    width: 163px;
    height: 163px;
    margin-top: 32px;
    display: block;
    align-items: center;

    &-img {
      border: 1px solid rgba(#999999, 0.2);
      border-radius: 4px;
      display: block;
      width: 163px;
      height: 163px;
    }
  }

  &-download {
    margin-bottom: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f8f9fa;
    .use-dark-mode-query({
      background-color: @dark-cell-background-color !important;
    });
    border-radius: 50px;
    width: 58px;
    height: 36px;
    margin-top: 8px;

    &-img {
      display: block;
      width: 16px;
      height: 16px;
    }
  }

  &-num {
    width: 100%;
    border-top: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-top: 1px solid @dark-line-color;
  });
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-left {
      margin-left: 15px;
      display: flex;
      flex-direction: column;

      &-title {
        margin-top: 12px;
        margin-bottom: 5px;
        font-size: 11px;
        color: #999999;
        .use-dark-mode-query({
          color: @dark-font-color !important;
        });
      }

      &-text {
        margin-bottom: 12px;
        font-weight: bold;
        font-size: 14px;
        color: #000000;
        .use-dark-mode-query({
          color: @dark-font-color !important;
        });
      }
    }

    &-right {
      display: flex;
      align-items: center;
      justify-content: center;

      &-img {
        margin: 0 15px;
        display: block;
        width: 16px;
        height: 16px;
        cursor: pointer;
      }
    }
  }

  &-chat {
    display: flex;
    width: 100%;
    justify-content: center;

    &-text {
      text-align: center;
      width: 100%;
      padding: 9px 0;
      border-radius: 50px;
      background-color: #f8f8fa;
      .use-dark-mode-query({
        background-color: @dark-cell-background-color !important;
      });
      margin: 15px;
      font-size: 13px;
      color: #6CBE70;
    }
  }

  &-line {
    display: flex;
    width: 100%;
    height: 8px;
    background-color: rgba(#999999, 0.3);
    .use-dark-mode-query({
      background-color: @dark-line-color !important;
    });
  }

  &-cancel {
    text-align: center;
    width: 100%;
    padding: 15px;
    font-size: 14px;
    color: #333333;
    .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    cursor: pointer;
  }
}

// 拨打电话弹框样式
.call-phone-popup {
  z-index: 4010;
}

.popup-bottom-phone-show {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  border-radius: 15px 15px 0 0;
  background-color: #ffffff;
  .use-dark-mode-query({
    background-color: @dark-card-background-color !important;
  });

  &-phone {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px 0;
    cursor: pointer;

    &-img {
      display: block;
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }

    &-text {
      font-size: 16px;
      color: #333333;
      .use-dark-mode-query({
        color: @dark-font-color !important;
      });
    }
  }

  &-cancel {
    text-align: center;
    width: 100%;
    padding: 15px;
    font-size: 14px;
    color: #333333;
    .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    cursor: pointer;
    border-top: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-top: 1px solid @dark-line-color;
  });
  }
}
