@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageLogin/codeLogin/code"] {
  .new-login {
    // padding: 0 32px;
    // min-height: 100vh;
    .use-var(background-color, background-color);
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });

    .logo-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      .rem(margin-top, 60);

      .logo {
        .rem(width, 70);
        .rem(height, 70);
        .rem(margin-bottom, 20);
      }

      .app-name {
        .rem(font-size, 12);
        .use-var(color, font-color);
        .use-dark-mode-query({
        color: @dark-font-color;
      });
      }

      .phone-number {
        .rem(font-size, 15);
        .use-var(color, font-color);
        font-weight: bold;
        .use-dark-mode-query({
        color: @dark-font-color;
      });
      }
    }

    // .code-input-container {
    //   display: flex;
    //   justify-content: space-between;
    //   .rem(margin-top, 70);
    //   .rem(padding-left, 29);
    //   .rem(padding-right, 29);

    //   // padding: 0 20px;

    //   .code-input {
    //     .rem(width, 60);
    //     .rem(height, 60);
    //     .rem(border-radius, 10);
    //     border: none;
    //     .rem(margin-right, 7);
    //     text-align: center;
    //     .rem(font-size, 24);
    //     font-weight: bold;
    //     background: #F2F4F5;
    //     .use-dark-mode-query({
    //       background: #2B2B2B;
    //     })
    //   }
    // }

    .resend-code {
      display: flex;
      align-items: center;
      justify-content: center;
      .rem(margin-top, 20);
      // .rem(margin-left, 29);
      // text-align: center;

      .resend-text {
        .use-var(color, primary-color);
        .rem(font-size, 14);
      }
    }
  }
}
