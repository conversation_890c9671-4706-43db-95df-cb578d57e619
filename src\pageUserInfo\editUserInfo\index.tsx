import { useState, useEffect, useRef } from "react";
import { Icon, View } from "@tarojs/components";
import {
  Avatar,
  Cell,
  NavBar,
  Image,
  Button,
  Dialog,
} from "@arco-design/mobile-react";
import { IconQrcode } from "@/components/YkIcons";
import FullScreenLoading from "../../components/FullSrceenLoading"; // 引入全屏遮罩组件
import "./index.less";
import Taro from "@tarojs/taro";
import {
  IconPicture,
  IconUpload,
  IconUser,
} from "@arco-design/mobile-react/esm/icon";

import {
  getUserInfo,
  uploadHeadImg,
  uploadWxCodeImg,
  uploadBgImg,
  uploadFile,
  editUser
} from "@/utils/api/common/common_user";
import BottomPopup from "@/components/BottomPopup";
import { useSetPermission } from "@/stores/permissionStore";
import { AuthTypes } from "@/utils/config/authTypes";
import PermissionPopup from "@/components/PermissionPopup";
import { createFromIconfontCN } from "@arco-design/mobile-react/icon";
import { toast } from "@/utils/yk-common";
import { usePermission } from "@/hooks/usePermission";

const EditUserInfo = () => {
  // 工具函数：处理null值显示
  const formatDisplayValue = (value: any): string => {
    if (value === null || value === undefined || value === "null") {
      return "";
    }
    return String(value);
  };

  const [isPopupVisible, setPopupVisible] = useState(false); // 控制选择图片方式弹窗的显示状态
  const [userInfo, setUserInfo] = useState(
    () => Taro.getStorageSync("userInfo").userInfo || {}
  );
  const [loading, setLoading] = useState(false); //  loading 状态
  const popupType = useRef(null);

  
  // 选择图片的方法
  const chooseImage = (sourceType: "album" | "camera") => {
    console.log("选择图片方式:", sourceType);
    console.log("platformRef.current:", platformRef.current);
    if(platformRef.current === "Android"){
      window.setPhotoNum?.setPhotoNum(1);
    }
    if(platformRef.current === "HM"){
      window.harmony.setPhotoNum(1);
    }
    Taro.chooseImage({
      count: 1,
      sourceType: [sourceType], // 'album' 为从相册选择，'camera' 为拍照
      success: (res) => {
        const imagePath = res.tempFilePaths[0];
        console.log(imagePath)
        console.log("图片路径: ", imagePath);
        // 根据 popupType 更新不同的字段
        let updatedUserInfo;
        if (popupType.current === "headImg") {
          updatedUserInfo = { ...userInfo, avatar: imagePath };

          uploadFile(imagePath)
            .then((response) => {
              if (response && response.code === 0) {
                console.log("上传成功", response.data);
                handleSave(response.data)
              } else {
                console.error("上传失败", response.message);
              }
            })
            .catch((error) => {
              console.error("上传错误", error);
            });
        } else if (popupType.current === "bg") {
          updatedUserInfo = { ...userInfo, backgroundImage: imagePath };

          uploadFile(imagePath)
            .then((response) => {
              if (response && response.code === 0) {
                console.log("上传成功", response.data);
                handleSave(response.data)
              } else {
                console.error("上传失败", response.message);
              }
            })
            .catch((error) => {
              console.error("上传错误", error);
            });
        } else if (popupType.current === "qrCode") {
          updatedUserInfo = { ...userInfo, wechatQrCode: imagePath };

          uploadFile(imagePath)
            .then((response) => {
              if (response && response.code === 0) {
                console.log("上传成功", response.data);
                handleSave(response.data)
              } else {
                console.error("上传失败", response.message);
              }
            })
            .catch((error) => {
              console.error("上传错误", error);
            });
        }

        // 获取完整的用户信息，确保不丢失accessToken等重要字段
        const completeUserInfo = Taro.getStorageSync("userInfo") || {};
        const mergedUserInfo = { ...completeUserInfo, ...updatedUserInfo };

        console.log("合并前的完整用户信息:", JSON.stringify(completeUserInfo));
        console.log("合并后的用户信息:", JSON.stringify(mergedUserInfo));

        setUserInfo(mergedUserInfo); // 更新状态
        Taro.setStorageSync("userInfo", mergedUserInfo);
      },
      fail: (err) => {},
    });
  };

  // 自定义权限同意处理，处理 editUserInfo 页面特有的逻辑
  const customWebPermissonConsent = () => {
    console.log("editUserInfo customWebPermissonConsent");
    console.log("authType:", authConfirmType.current);

    // 根据权限类型执行不同的后续处理
    if (authConfirmType.current === AuthTypes.CAMERA) {
      // 拍照
      chooseImage("camera");
    } else if (authConfirmType.current === AuthTypes.GALLERY_PHOTO) {
      // 从相册选择
      chooseImage("album");
    }

    return true;
  };

  // 使用全局权限管理
  const {
    initPermissions,
    hasPermission,
    requestPermission,
    webPermissonConsent,
    webPermissonDeny,
    permissionPopupProps,
    platformRef,
    authConfirmType,
  } = usePermission(customWebPermissonConsent);
  // const permissionStatus = useHasPermission(authConfirmType);
  // 页面加载时调用 getUserInfo
  useEffect(() => {
    // 初始化权限管理
    const cleanup = initPermissions();

    Taro.eventCenter.on("refreshUserInfo", fetchUserInfo);
    fetchUserInfo();

    return () => {
      cleanup && cleanup();
      Taro.eventCenter.off("refreshUserInfo", fetchUserInfo);
    };
  }, []);

  // 权限相关函数已移至全局权限管理

  // 权限相关函数已移至全局权限管理

  const fetchUserInfo = () => {
    const storedUserInfo = Taro.getStorageSync("userInfo") || {};
    getUserInfo()
      .then((res) => {
        console.log("用户信息" + JSON.stringify(res));
        if (res && res.code === 0) {
          // 成功处理
          const fetchedUserInfo = res.data || {};

          // 合并本地存储和接口返回的数据
          const updatedUserInfo = { ...storedUserInfo, ...fetchedUserInfo };
          console.log("更新后的用户信息" + JSON.stringify(updatedUserInfo));
          // 更新状态和本地存储
          setUserInfo(updatedUserInfo);
          Taro.setStorageSync("userInfo", updatedUserInfo);
        } else {
          // 错误处理
          console.log("错误处理");
        }
      })
      .catch((err) => {})
      .finally(() => {
        setTimeout(() => {
          setLoading(false);
        }, 500);
      });
  };

  // 打开选择图片方式弹窗
  const openPopup = (type) => {
    popupType.current = type;
    if (platformRef.current === "WX") {
      chooseImage("album");
      return;
    }
    setPopupVisible(true);
  };

  // 关闭选择图片方式弹窗
  const handleClose = () => {
    setPopupVisible(false);
  };

  const handleConfirm = (index: number) => {
    if (index === 0) {
      if(platformRef.current === "HM"){
        chooseImage("camera");
      }else{
        console.log("检查相机权限:", hasPermission(AuthTypes.CAMERA));
      // 请求相机权限
      if (!hasPermission(AuthTypes.CAMERA)) {
        console.log("没有相机权限:");
      requestPermission(AuthTypes.CAMERA);
      return;
      }
      console.log("有相机权限:");
      chooseImage("camera");
      }
    } else if (index === 1) {
      // 请求相册权限
      if(platformRef.current === "HM"){
        chooseImage("album");
      }else{
        if (!hasPermission(AuthTypes.GALLERY_PHOTO)) {
          console.log("没有相册权限:");
        requestPermission(AuthTypes.GALLERY_PHOTO);
        return; 
        }
        console.log("有相册权限:");
        chooseImage("album");
      }
    }
  };

  // 权限相关函数已移至全局权限管理


  const handleSave = (path: string) => {
    let formData = {}
    if (popupType.current === "headImg") {
        formData["avatar"] = path
    } else if (popupType.current === "bg") {
      formData["backgroundImage"] = path
    } else if (popupType.current === "qrCode") {
      formData["wechatQrCode"] = path
    }

    console.log("准备调用editUser接口，formData:", formData);

    // 在调用editUser前检查用户信息
    const currentUserInfo = Taro.getStorageSync("userInfo");
    console.log("调用editUser前的用户信息:", JSON.stringify(currentUserInfo));

    // 调用 editUser 接口
    editUser(formData)
        .then((res) => {
            console.log("editUser接口响应:", JSON.stringify(res))
            if (res && res.code === 0) {
                // 成功处理
                toast("success", {
                    content: '保存成功',
                    duration: 2000,
                });
            } else {
                // 错误处理
                toast("error", {
                    content: '保存失败',
                    duration: 2000,
                });
            }
        })
        .catch((err) => {
            console.error("editUser接口错误:", err);
            toast("error", {
                content: '保存失败',
                duration: 2000,
            });
        });

};

  return (
    <View className="edit">
      <View className="bg">
        {/* {userInfo.bg_img ? (
          <Image className="bg-img" src={userInfo.bg_img} />
        ) : (
          <View className="bg-default" />
        )} */}
        <Image className="bg-img" src={userInfo.backgroundImage} />
        <View className="bg-cover" />

{platformRef.current !== "WX" &&
        <NavBar
          className={`navBar setPageContent-navbar ${
            platformRef.current !== "H5" ? "navbarPaddingTop" : ""
          }`}
          fixed={false}
          hasBottomLine={false}
          style={{
            color: "white",
            background: "#00000000",
            height: "2.2rem",
            ...(platformRef.current === "H5" ? {} : { paddingTop: "2rem" }),
          }}
          onClickLeft={() => Taro.navigateBack()}
        />}

        <Button
          className="edit-bg"
          inline
          icon={
            <IconPicture className="edit-bg-icon" />
            // <img
            //   className="edit-bg-icon"
            //   src={require("../../assets/images/userinfo/change_bg_icon.png")}
            // />
          }
          onClick={() => openPopup("bg")}
        >
          换背景
        </Button>
      </View>

      <View className="cell-container">
        <Cell.Group className="cell-group" bordered={false}>
          <Cell
            className="cell-label-head"
            label="头像"
            showArrow
            text={
              userInfo.avatar ? (
                <Avatar
                  className="cell-label-head-img"
                  src={userInfo.avatar}
                  size="medium"
                  avatarStyle={{
                    width: '50px',
                    height: '50px'
                  }}
                />
              ) : null
            }
            onClick={() => openPopup("headImg")}
          />
          <Cell
            className="cell-label"
            label="昵称"
            text={formatDisplayValue(userInfo.nickname)}
            showArrow
            onClick={() =>
              Taro.navigateTo({
                url: `/pageUserInfo/editUserInfoItem/index?type=${1}&content=${encodeURIComponent(
                  formatDisplayValue(userInfo.nickname)
                )}`,
              })
            }
          />
          <Cell
            className="cell-label"
            label="手机号"
            text={formatDisplayValue(userInfo.contactMobile)}
            showArrow
            onClick={() =>
              // Taro.navigateTo({
              //   url: "/pageSetting/bindMobilePhone/index",
              // })
              Taro.navigateTo({
                url: `/pageUserInfo/editUserInfoItem/index?type=${2}&content=${
                  formatDisplayValue(userInfo.contactMobile)
                }`,
              })
            }
          />
          <Cell
            className="cell-label"
            label="微信号"
            text={formatDisplayValue(userInfo.wechatNumber)}
            showArrow
            onClick={() =>
              Taro.navigateTo({
                url: `/pageUserInfo/editUserInfoItem/index?type=${3}&content=${encodeURIComponent(
                  formatDisplayValue(userInfo.wechatNumber)
                )}`,
              })
            }
          />
          <Cell
            className="cell-label-code"
            label="微信二维码"
            showArrow
            text={
              userInfo.wechatQrCode ? (
                <Image
                  className="cell-label-code-img"
                  src={userInfo.wechatQrCode}
                />
              ) : (
                // <Image
                //   className="cell-label-code-img"
                //   src={require("@/assets/images/common/qrcode.png")}
                // />
                <IconQrcode className="cell-label-code-img" />
              )
            }
            onClick={() => openPopup("qrCode")}
          />
          <Cell
            className="cell-label"
            label="个性签名"
            text={formatDisplayValue(userInfo.personalProfile)}
            showArrow
            onClick={() =>
              Taro.navigateTo({
                url: `/pageUserInfo/editUserInfoItem/index?type=${4}&content=${encodeURIComponent(
                  formatDisplayValue(userInfo.personalProfile)
                )}`,
              })
            }
          />
          <Cell
            className="cell-label"
            label="账号ID"
            text={formatDisplayValue(userInfo.id)}
          />
          <Cell
            className="cell-label"
            label="注册方式"
            text={formatDisplayValue(userInfo.platform)||'微信'}
          />
        </Cell.Group>
      </View>

      {/* 底部弹出对话框 */}
      <BottomPopup
        // title="请选择操作"
        options={["拍照", "从相册选择"]}
        btnCloseText="取消"
        onConfirm={handleConfirm}
        onClose={handleClose}
        visible={isPopupVisible}
      />

      <PermissionPopup {...permissionPopupProps} />

      {/* <FullScreenLoading visible={loading} /> */}
    </View>
  );
};

export default EditUserInfo;
