import { View, Text,Input  } from "@tarojs/components";
import { <PERSON><PERSON>, <PERSON>wipeAction, Checkbox, SearchBar } from "@arco-design/mobile-react";
import Taro, { useDidShow } from "@tarojs/taro";
import React, { useState, useEffect,useRef } from "react";
import YkNavBar from "@/components/ykNavBar/index";
import { IconSearch, IconMore, IconArrowUp, IconDelete  } from "@arco-design/mobile-react/esm/icon";
import { getTagList, delTag, editTag } from "@/utils/api/common/common_user";
import "./index.less";
import { userInfo } from "os";
import { toast } from "@/utils/yk-common";
// 正确导入 IconSort
const IconSort = ({ style }) => (
  <svg style={style} width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
    <path d="M5 3v2H3V3h2zm6 0v2H7V3h4zM5 7v2H3V7h2zm6 0v2H7V7h4zM5 11v2H3v-2h2zm6 0v2H7v-2h4z"/>
  </svg>
);

interface Tag {
  id: number;
  name: string;
  //count: number;
  pinned?: boolean;
}

export default function TagManage() {
  const userInfo = Taro.getStorageSync("userInfo");
  const [tags, setTags] = useState<Tag[]>([]);
  const [originTags, setOriginTags] = useState<Tag[]>([]);
  const [searchValue, setSearchValue] = useState("");
  const [editing, setEditing] = useState(false);
  const [sortVisible, setSortVisible] = useState(false);
  const [selectMode, setSelectMode] = useState(false);
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [loading, setLoading] = useState(true); // 添加加载状态
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  const handleDelete = async (id) => {
    let res: any = await delTag({ id: id });
    if (res && res.code == 0) {
      toast("success", {
        content: "删除成功",
        duration: 2000
      });
      setTags(originTags.filter(tag => tag.id !== id));
      setOriginTags(originTags.filter(tag => tag.id !== id));
    } else {
      toast("info", {
        content: res.msg,
        duration: 2000
      });
    }


  };
  const handlePin = async (id,isTop) => {
    let res: any = await editTag({ id: id, isTop: isTop ? 0 : 1 });
    if (res && res.code == 0) {
      toast("success", {
        content: isTop== 0?'置顶成功':'取消置顶成功', 
        duration: 1500
      });

      const updatedTags = originTags.map(tag => {
        if (tag.id === id) {
          return { ...tag, isTop: isTop ? 0 : 1   }; 
        }else {
          return tag;
        }
      });
      
      // 按 isTop 降序排序（1 置顶在前，0 非置顶在后）
      const sortedTags = [...updatedTags].sort((a, b) => b.isTop - a.isTop);
      setOriginTags(sortedTags);
    } else {
      toast("info", {
        content: res.msg,
        duration: 2000
      });
    }
    // setTags(tags.map(tag => 
    //   tag.id === id ? { ...tag, pinned: !tag.pinned } : tag
    // ));
  };


  const getTagListData = async () => {
    setLoading(true); // 开始加载
    const data = {
      userId: userInfo.id,
      type: 2,
      parentId: 0,
      sortType: Taro.getStorageSync('selectedTagSort') || 0,
    }
    const tagRes: any = await getTagList(data);
    if (tagRes && tagRes.code == 0) {
      if (tagRes.data.length > 0) {
        const tagsList = tagRes.data.map((item: any) => {
          // 统计 dynamicsId 的数量（逗号分隔的ID个数）
          // const dynamicsCount = item.dynamicsId
          //   ? item.dynamicsId.split(',').filter((id: string) => id.trim() !== '').length
          //   : 0;
          return {
            id: item.id,
            name: item.name,
            //count: dynamicsCount, // 使用实际的动态ID数量
            labelsName	: item.labelsName,
            isTop: item.isTop,
          };
        });
        const sortedTags = [...tagsList].sort((a, b) => b.isTop - a.isTop);
        setOriginTags(sortedTags);
        setTags(sortedTags);
      } else {
        setTags([]);
        setOriginTags([]);
      }
    }
    setLoading(false); // 加载完成
  }

  // 监听刷新事件
  useEffect(() => {
    const handleRefresh = () => {
      getTagListData();
    };

    Taro.eventCenter.on('refreshCatalogList', handleRefresh);

    return () => {
      Taro.eventCenter.off('refreshCatalogList', handleRefresh);
    };
  }, []);

  useEffect(() => {
    // 获取标签列表
    getTagListData()
  }, []);

  return (
    <View>
      {loading ? (
        // 加载状态
        <View className="catalog-loading-page">
          {platform !== "WX" &&<YkNavBar title="目录管理" />}
          <View className="catalog-loading-content">
            <Text>加载中...</Text>
          </View>
        </View>
      ) : tags.length === 0 ? (
        // 空状态
        <View className="tag-empty-page">
          {platform !== "WX" &&<YkNavBar title="目录管理" /> }
          <View className="catalog-empty-content">
            <View className="catalog-empty-icon">
              <svg width="96" height="96" viewBox="0 0 48 48" fill="none">
              <rect x="10" y="8" width="28" height="32" rx="4" className="rect" />
              <path d="M24 28l-6 4V12a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v20l-6-4z" fill="#C9CDD4"/>
              <path d="M24 20.5l1.763 3.574 3.947.574-2.855 2.784.675 3.942L24 28.5l-3.53 1.874.675-3.942-2.855-2.784 3.947-.574L24 20.5z" fill="#fff"/>
              </svg>
            </View>
            <Text className="catalog-empty-text">暂无目录</Text>
            <Button inline size="medium"  className="catalog-btn-primary" 
              onClick={() => {Taro.setStorageSync('selectedTag', []);Taro.navigateTo({ url: `/pageDynamic/tagAndCategoryManage/form/index?type=catalog&mode=create` })}}
            >
              新建目录</Button>
            <Button inline size="medium" className="catalog-btn-outline" type="ghost" 
              onClick={() => {Taro.setStorageSync('selectedArticles', []);Taro.navigateTo({ url: `/pageDynamic/tagAndCategoryManage/form/index?type=tag&mode=create` })}}
          >
              新建标签</Button>
          </View>
      </View>
      ):(
        // 列表状态
        <View className="tag-manage-page">
      	    {platform !== "WX" &&<YkNavBar title="目录管理" />}
            <SearchBar 
              actionButton={<span className="demo-search-btn"></span>} 
              placeholder="搜索"
              onChange={(e) => 
                {
                const value =  e.target.value;
                setSearchValue(value)
                if (!value) {
                  setOriginTags(tags);
                } else {
                  setOriginTags(tags.filter(item => item.name.includes(value)));     
                }
              }}
              className="demo-input-btn-input"
              clearable
              onClear={() => {
                setOriginTags(tags);              
              }}
            />      
        
          <View className="tag-list">
            {originTags.map(tag => (
              selectMode ? (
                <View className="tag-item tag-item-select" key={tag.id}>
                  <Checkbox
                    checked={selectedIds.includes(tag.id)}
                    onChange={checked => {
                      if (checked) {
                        setSelectedIds([...selectedIds, tag.id]);
                      } else {
                        setSelectedIds(selectedIds.filter(id => id !== tag.id));
                      }
                    }}
                  >
                  <View className="tag-info">
                    <Text className="tag-name">
                      {tag.name}</Text>
                  </View>
                </Checkbox>  
                </View>
              ) : (
                <SwipeAction
                  style={{marginRight:'-1px'}}
                  key={tag.id}
                  rightActions={[
                    {
                      icon: <IconArrowUp />,
                      text: tag.isTop ? "取消置顶" : "置顶",
                      style: { 
                        backgroundColor: '#FF7D00',
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                      },
                      onClick: () => handlePin(tag.id,tag.isTop),
                    },
                    {
                      icon: <IconDelete  />,
                      text: '删除',
                      style: { 
                        backgroundColor: '#F53F3F',
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                       },
                      onClick: () => handleDelete(tag.id),
                    }
                  ]}
                >
                  <View className="tag-item-c">
                    <View className="tag-item-main">
                      <Text className="tag-item-title"
                        onClick={() => Taro.navigateTo({ url: `/pageDynamic/tagAndCategoryManage/form/index?type=catalog&mode=edit&id=${tag.id}` })}
                      >{tag.name}{tag.isTop ? <Text className="tag-pinned">置顶</Text> : null}</Text>
                      
                      <Text className="tag-item-sub">
                        {tag.labelsName}
                      </Text>
                    </View>
                    <View className="tag-item-drag">
                      <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                        <circle cx="5" cy="5" r="1.2" fill="#D8D8D8"/>
                        <circle cx="5" cy="9" r="1.2" fill="#D8D8D8"/>
                        <circle cx="5" cy="13" r="1.2" fill="#D8D8D8"/>
                        <circle cx="13" cy="5" r="1.2" fill="#D8D8D8"/>
                        <circle cx="13" cy="9" r="1.2" fill="#D8D8D8"/>
                        <circle cx="13" cy="13" r="1.2" fill="#D8D8D8"/>
                      </svg>
                    </View>
                  </View>
                </SwipeAction>
              )
            ))}
          </View>
          <View className="footer-actions">
            <View className="left-actions">

              <View
                className="sort-btn"
                onClick={() => {Taro.navigateTo({ url: `/pageDynamic/tagAndCategoryManage/tag/setSort` })}}
                style={{       
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'left'
                }}
              >
                <IconArrowUp style={{ margin: 0 }} />
                <Text className="sort-text" style={{ fontSize: '3vw', margin: 0, marginTop: 4 }}>排序</Text>
              </View>
              

              <View
                className="sort-btn"
                onClick={() => {
                  setSelectMode(true);
                  setSelectedIds([]);
                }}
                style={{
                  width: 60,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'left'
                }}
              >
                <IconDelete style={{ margin: 0 }} />
                <Text className="sort-text" style={{ fontSize: '3vw', margin: 0, marginTop: 4 }}>管理/删除</Text>
              </View>

            </View>
            <View className="right-actions">
            <Button 
              inline 
              size="medium" 
              style={{borderRadius: 8,width: 100}}
              onClick={() => {Taro.setStorageSync('selectedArticles', []);Taro.navigateTo({ url: `/pageDynamic/tagAndCategoryManage/form/index?type=tag&mode=create` })}}
            >
              新建标签
            </Button>
            <Button 
              inline 
              size="medium" 
              style={{borderRadius: 8,width: 100}}
              onClick={() => {Taro.setStorageSync('selectedTag', []);Taro.navigateTo({ url: `/pageDynamic/tagAndCategoryManage/form/index?type=catalog&mode=create` })}}
            >
              新建目录
            </Button> </View>
           
          </View>
          {selectMode && (
            <View className="multi-select-footer">
              <Button className="footer-cancel" onClick={() => { setSelectMode(false); setSelectedIds([]); }}>关闭</Button>
              <Button className="footer-delete" disabled={selectedIds.length === 0} onClick={() => {
                if (selectedIds.length === 0) return;
                setShowDeleteConfirm(true);
              }}>删除({selectedIds.length})</Button>
            </View>
          )}
          {showDeleteConfirm && (
            <View className="delete-confirm-mask">
              <View className="delete-confirm-modal">
                <View className="delete-confirm-title">温馨提示</View>
                <View className="delete-confirm-content">
                  确定删除{selectedIds.length}个选中的目录？<br/>
                  <Text className="delete-confirm-desc">（标签中的商品不会被删除）</Text>
                </View>
                <View className="delete-confirm-actions">
                  <View className="delete-confirm-cancel" onClick={() => setShowDeleteConfirm(false)}>再想想</View>
                  <View className="delete-confirm-delete" onClick={async () => {
                    // 将选中的ID拼接成字符串，用逗号分隔
                    const idsString = selectedIds.join(',');
                    let res: any = await delTag({ id: idsString });
                    if (res && res.code == 0) {
                      toast("success", {
                        content: "删除成功",
                        duration: 2000
                      });
                      setSelectedIds([]);
                      setSelectMode(false);
                      setShowDeleteConfirm(false);
                      getTagListData();
                    } else {
                      toast("info", {
                        content: res.msg,
                        duration: 2000
                      });
                    }
                  }}>确定删除</View>
                </View>
              </View>
            </View>
          )}

    </View>
      )
    }
    </View>
  )
}