import { View } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import { Toast, Image, LoadMore } from "@arco-design/mobile-react";
import Taro from "@tarojs/taro";
import { IconSettings } from "@arco-iconbox/react-yk-arco";
import React, { useEffect, useState } from "react";
import wx from "weixin-webview-jssdk";
// 组件
import YkNavBar from "@/components/ykNavBar/index";
import { LoadMoreStatus } from "@arco-design/mobile-react/cjs/load-more";
import YkSwitchTabBar from "@/components/ykSwitchTabBar/index";
import { usePermission } from "@/hooks/usePermission";

export default function Friends() {
  // 提示框
  const toast = (func, options) => {
    let _window: any = window;
    if (!!_window.toastInstance) {
      _window.toastInstance.close();
    }
    _window.toastInstance = Toast[func](options);
  };

  const [isOpenNotice, setIsOpenNotice] = React.useState(false);
  const [friendsList, setFriendsList] = React.useState<any>([]);
  const [loadStatus, setLoadStatus] = React.useState<LoadMoreStatus>("prepare");

  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);

  // 打开通知设置
  const openNotice = () => {
    // 处理通知权限逻辑
  };

  // 获取好友列表
  const getFriendsList = async () => {
    // API调用获取好友列表
  };

  // 初始化
  useLoad(() => {
    console.log("Page loaded.");
    getFriendsList();
  });

  useEffect(() => {
    const cleanup = initPermissions();
    return () => {
      cleanup && cleanup();
    };
  }, []);

  return (
    <View className="friendsBox">
      {platform !== "WX" &&<YkNavBar switchTab title="消息">
        <View className="navTop">
          <View className="navTop-left">
            <View className="navTop-left-title">消息</View>
          </View>
          <View className="navTop-right">
            <IconSettings className="navTop-right-icon" onClick={() => {
              if (platform === "WX") {
                wx.miniProgram.navigateTo({ url: "/pages/web?path=/pageSetting/settings/index" });
              } else {
                Taro.navigateTo({ url: "/pageSetting/settings/index" });
              }
            }} />
          </View>
        </View>
      </YkNavBar>}

      {isOpenNotice && (
        <View className="notice">
          <View className="notice-title">
            <Image src="/static/image/common/message_notice.png" className="notice-title-img" />
            <View className="notice-title-text">为避免错过好友消息，请开启系统通知</View>
          </View>
          <View className="notice-open" onClick={openNotice}>
            <View className="notice-open-text">去开启</View>
          </View>
        </View>
      )}

      <View className="friendsList">
        {friendsList.map((item, index) => (
          <View key={index} className="friendsList-item">
            <Image src={item.avatar} className="friendsList-item-avatar" />
            <View className="friendsList-item-content">
              <View className="friendsList-item-name">{item.name}</View>
              <View className="friendsList-item-message">{item.lastMessage}</View>
            </View>
            <View className="friendsList-item-time">{item.time}</View>
          </View>
        ))}

        <LoadMore status={loadStatus} />
      </View>
      <YkSwitchTabBar activeTab={3} />
    </View>
  );
}