import { View, Text } from "@tarojs/components";
import "./result.less";
import Taro, { useRouter } from "@tarojs/taro";
import { useState, useEffect } from "react";
import {
  Button,
  Toast,
  Loading
} from "@arco-design/mobile-react";

import { IconCircleChecked } from '@arco-design/mobile-react/esm/icon';

// 组件
import YkNavBar from "@/components/ykNavBar/index";
import React, { useRef } from "react";
// 类型定义
interface WithdrawalResultState {
  loading: boolean;
  amount: string;
  accountName: string;
  accountNumber: string;
  status: 'success' | 'failed' | 'pending';
}

export default function WithdrawalResult() {
  const router = useRouter();
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  // ==================== 状态管理 ====================
  const [pageState, setPageState] = useState<WithdrawalResultState>({
    loading: true,
    amount: '0.00',
    accountName: '',
    accountNumber: '',
    status: 'pending'
  });

  // ==================== 生命周期 ====================
  useEffect(() => {
    initPage();
  }, []);

  // ==================== 页面初始化 ====================
  const initPage = async () => {
    try {
      const { status, amount, accountName, accountNumber } = router.params;
      setPageState(prev => ({
        ...prev,
        accountName: decodeURIComponent(accountName|| ''),
        accountNumber: accountNumber || '',
        status: (status as any) || 'pending',
        amount: amount || '0.00',
        loading: false
      }));
    } catch (error) {
      console.error('页面初始化失败:', error);
      Toast.error('网络异常，请重试');
      setPageState(prev => ({ ...prev, loading: false }));
    }
  };

  // ==================== 事件处理 ====================
  const handleComplete = () => {
    Taro.switchTab({ url: '/pageOnlinePayment/wallet/index' });
  };

  // ==================== 渲染函数 ====================

  const renderWithdrawalDetails = () => (
    <View className="withdrawal-details">
      <View className="detail-row">
        <Text className="detail-label">提现金额</Text>
        <Text className="detail-value">¥{Number(pageState.amount).toFixed(2)}</Text>
      </View>
      <View className="detail-row">
        <Text className="detail-label">手续费</Text>
        <Text className="detail-value">¥0.00</Text>
      </View>
      <View className="detail-row">
        <Text className="detail-label">到款账户</Text>
        <Text className="detail-value">{pageState.accountName} | {pageState.accountNumber}</Text>
      </View>
    </View>
  );

  if (pageState.loading) {
    return (
      <View className="withdrawal-result">
        {platform !== "WX" &&<YkNavBar title="提现" />}
        <View className="loading-container">
          <Loading />
        </View>
      </View>
    );
  }

  return (
    <View className="withdrawal-result">
      {platform !== "WX" &&<YkNavBar title="提现" />}

      <View className="result-content">
        {/* 成功图标 */}
         <IconCircleChecked className="success-icon"/>

        {/* 状态文本 */}
        <View className="status-container">
          <Text className="status-title">提现申请已提交</Text>
          <Text className="status-subtitle">预计3个小时内到账</Text>
        </View>

        {/* 提现详情 */}
        {renderWithdrawalDetails()}
      </View>

      {/* 底部按钮 */}
      <View className="bottom-actions">
        <Button
          type="primary"
          size="large"
          onClick={handleComplete}
          className="complete-button"
        >
          完成
        </Button>
      </View>
    </View>
  );
}