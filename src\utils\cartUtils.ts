import Taro from "@tarojs/taro";
import { getCartCount } from "@/utils/api/common/common_user";

/**
 * 获取购物车数量的通用工具函数
 * @returns Promise<number> 购物车数量
 */
export const fetchCartCountUtil = async (): Promise<number> => {
  try {
    const userInfo = Taro.getStorageSync('userInfo') || {};
    if (!userInfo.id) {
      console.warn('用户未登录，无法获取购物车数量');
      return 0;
    }
    
    const res = await getCartCount({ userId: userInfo.id });
    console.log('获取购物车数量:', res);
    
    if (res && res.code === 0) {
      const count = res.data || 0;
      // 更新本地存储
      Taro.setStorageSync('goodCarNum', count);
      // 触发全局事件，通知其他页面更新
      Taro.eventCenter.trigger('updateCartCount', count);
      return count;
    } else {
      console.error('获取购物车数量失败:', res.msg);
      return 0;
    }
  } catch (error) {
    console.error('获取购物车数量出错:', error);
    return 0;
  }
};

/**
 * 更新购物车数量到全局状态
 * 这个函数会获取最新的购物车数量并同步到所有页面
 */
export const updateCartCountGlobal = async (): Promise<void> => {
  await fetchCartCountUtil();
};
