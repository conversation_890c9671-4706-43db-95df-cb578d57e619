import { View, Text } from "@tarojs/components";
import "./index.less";
import Taro from "@tarojs/taro";
import { useState, useEffect } from "react";
import {
  Cell,
  Toast,
  Button,
  Loading,
  Popover
} from "@arco-design/mobile-react";
import React, { useRef } from "react";
import { WalletInfo } from "./types";
// 组件
import YkNavBar from "@/components/ykNavBar/index";

import {IconQuestionCircle } from '@arco-design/mobile-react/esm/icon';
import { IconWithdrawal, IconTransactions } from "@/components/YkIcons";
import YkCellLabel from "@/components/YkCellLabel";

// API
import { 
  // 导入相关API方法
} from "@/utils/api/common/common_user";


import {
  getBalance
} from "@/utils/api/common/common_wechat";

// 类型定义
interface PageState {
  loading: boolean;
  data: any[];
}

export default function Wallet() {
  // ==================== 状态管理 ====================
  const [pageState, setPageState] = useState<PageState>({
    loading: false,
    data: []
  });

  const [walletInfo, setWalletInfo] = useState<WalletInfo>({});
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  // ==================== 生命周期 ====================
  useEffect(() => {
    initPage();
  }, []);

  // ==================== 页面初始化 ====================
  const initPage = async () => {
    setPageState(prev => ({ ...prev, loading: true }));
    try {
      // 初始化逻辑
      await loadData();
    } catch (error) {
      console.error('页面初始化失败:', error);
      Toast.error('网络异常，请重试');
    } finally {
      setPageState(prev => ({ ...prev, loading: false }));
    }
  };

  // ==================== 数据加载 ====================
  const loadData = async () => {
    try {
      // 实际的数据加载逻辑
      // TODO: 调用实际的API获取钱包数据
      const res: any = await getBalance({ userId: Taro.getStorageSync("userInfo")?.id});
      if (res) {
        const data = (res && typeof res === 'object' && 'data' in res) ? (res as any).data : res;
        setWalletInfo(data || {});
      }
    } catch (error) {
      console.error('加载数据失败:', error);
      Toast.error('网络错误，请重试');
    }
  };


  // ==================== 事件处理 ====================
  const handleBack = () => {
    Taro.navigateBack({ delta: 1 });
  };

  const handleNavigation = (url: string) => {
    Taro.navigateTo({ url });
  };

  // ==================== 渲染 ====================
  return (
    <View className="wallet">
      {platform !== "WX" &&<YkNavBar title="我的钱包" onClickLeft={handleBack} />}
      
      {pageState.loading ? (
        <View className="loading-container">
          <Loading />
        </View>
      ) : (
        <View className="wallet-content">
          <View className="balance-info">
            <View className="balance-total">    
                <Text className="balance-total-title">总金额(元)</Text>
                <Text className="balance-total-value">{(((Number(walletInfo?.available_amount ?? 0) + Number(walletInfo?.pending_amount ?? 0)) / 100)).toFixed(2)}</Text>
            </View>
            <View className="balance-extra"> 
                <View className="balance-extra-item">
                    <View className="balance-extra-item-label">
                        <Text className="balance-extra-item-title">可提现</Text>
                        <Popover
                          className="question-popover"
                          content={
                            <View className="question-popover-wrapper">
                              <Text className="question-popover-content">
                                可提现到银行卡的资金，每天提现次数不限制，提现限额以你的钱包账户限额为准。提交提现申请后1-3个工作日内到账，具体到账时效以你的收款行处理时效为准
                              </Text>
                            </View>
                          }
                          direction="topLeft"
                          // onChange={visible => {
                          //     console.log('The bubble state is switched to', visible);
                          // }}
                        >
                          <IconQuestionCircle />
                        </Popover>
                    </View>
                    <Text className="balance-extra-item-value">{(Number(walletInfo?.available_amount ?? 0) / 100).toFixed(2)}</Text>
                </View>
                <View className="balance-extra-item">
                    <View className="balance-extra-item-label">
                        <Text className="balance-extra-item-title">待结算</Text>
                        <Popover
                          className="question-popover"
                          content={
                            <View className="question-popover-wrapper">
                              <Text className="question-popover-content">
                                等待结算的资金，交易成功后自动结算至可提现账户
                              </Text>
                            </View>
                          }
                          direction="topRight"
                          // onChange={visible => {
                          //     console.log('The bubble state is switched to', visible);
                          // }}
                        >
                          <IconQuestionCircle />
                        </Popover>
                    </View>
                    <Text className="balance-extra-item-value">{(Number(walletInfo?.pending_amount ?? 0) / 100).toFixed(2)}</Text>
                </View>
            </View>
            <View className="balance-btn">
                <Button className="withdrawal-btn" type="primary" size="large" onClick={() => handleNavigation('/pageOnlinePayment/wallet/withdrawal/index')}>
                    提现
                </Button>
            </View>
          </View>

        { false && ( // next-todo: 提现记录和收支明细开发中
          <View className="wallet-menu">
            <Cell.Group className="yk-cell-group" bordered={false}>
            <Cell
              className="wallet-cell-transactions"
              icon={<IconTransactions className="wallet-cell-icon" size={20} />}
              label={<YkCellLabel label="收支明细" />}
              onClick={() => handleNavigation('/pageOnlinePayment/wallet/transactions/history')}
              showArrow
            />
            <Cell
              className="wallet-cell-withdraw"
              icon={<IconWithdrawal className="wallet-cell-icon" size={20} />}
              label={<YkCellLabel label="提现记录" />}
              onClick={() => handleNavigation('/pageOnlinePayment/wallet/withdrawal/history')}
              showArrow
            />
            <Cell
              className="wallet-cell-help"
              icon={<IconQuestionCircle className="wallet-cell-icon"/>}
              label={<YkCellLabel label="帮助中心" />}
              onClick={() => Toast.info('帮助中心开发中')}
              // onClick={() => handleNavigation('/pageOnlinePayment/wallet/help')}
              showArrow
            />
          </Cell.Group>
          </View>
          )}
        </View>
      )}
    </View>
  );
}