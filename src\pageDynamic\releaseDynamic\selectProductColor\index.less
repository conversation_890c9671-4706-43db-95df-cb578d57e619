@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pageDynamic/releaseDynamic/selectProductColor/index"] {
  .product-color-page {
    min-height: 100vh;
    background: #f7f8fa;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
    display: flex;
    flex-direction: column;
    padding-bottom: 70px;
    .header {
      font-size: 18px;
      font-weight: bold;
      text-align: center;
      padding: 16px 0 12px 0;
      background: #fff;
      .use-dark-mode-query({
      background-color: @dark-background-color;
    });
    }
    .search-input {
      width: 90%;
      margin: 0 auto 0 auto;
      display: block;
      border-radius: 8px;
      border: none;
      background: #f2f3f5;
      padding: 10px 16px;
      font-size: 15px;
    }
    .section {
      margin-top: 10px;
      background: #fff;
      .use-dark-mode-query({
      background-color: @dark-card-background-color;
    });
      padding: 0 16px 16px 16px;
      border-radius: 8px;
      .section-title {
        font-size: 15px;
        font-weight: 500;
        margin: 16px 0 12px 0;
        .use-var(color, font-color);
        .use-dark-mode-query({
        color: @dark-font-color;
      });
        display: flex;
        align-items: center;
        .section-tip {
          font-size: 12px;
          color: #bcbcbc;
          margin-left: 10px;
        }
      }
      .tag-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        .tag {
          font-size: 14px;
          color: #222;
          background: #f2f3f5;
          border-radius: 8px;
          padding: 0 16px;
          height: 24px;
          line-height: 24px;
          margin-bottom: 8px;
          cursor: pointer;
          user-select: none;
          &.selected {
            background: var(--primary-color);
            color: #fff;
          }
          &.add-tag {
            border: 1px dashed var(--primary-color);
            color: var(--primary-color);
            background: #fff;
          }
        }
      }
    }
    .confirm-btn {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      width: calc(100% - 32px);
      height: 44px;
      margin: 0 16px 16px 16px;
      padding: 0;
      background: var(--primary-color);
      color: #fff;
      font-size: 18px;
      font-weight: 500;
      border: none;
      border-radius: 8px;
      z-index: 1000;
      box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: background-color 0.2s ease;
    }

    .popover {
      width: 102px;
      height: 42px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0px;
      z-index: 2;
    }
  
    .content {
      /* 自动布局子元素 */
      width: 78px;
      height: 20px;
      z-index: 0;
      /* 14/Medium */
      font-family: PingFang SC;
      font-size: 14px;
      font-weight: normal;
      line-height: 140%;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      /* 文字 Text/文字text-1 白色 */
      color: var(--text-1); /* #FFFFFF */
    }
  

  }
}

.dialog-input {
  width: 100%;
  padding: 0 12px;
  border-radius: 4px;
  background-color: #f7f8fa !important;
  font-size: 14px;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
  box-sizing: border-box;
}
