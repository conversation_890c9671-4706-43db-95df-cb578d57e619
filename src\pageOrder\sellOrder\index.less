@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pageOrder/sellOrder/index"] {
  .order-page {
    background: #f7f8fa;
    .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
    min-height: 100vh;
  }
  .order-tabs {
    display: flex;
    background: #fff;
    .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
    // border-bottom: 1px solid #f0f0f0;
    padding: 0 12px;
    position: fixed;
    // top: 84px; /* 使用更大的top值，确保完全在导航栏下方 */
    left: 0;
    right: 0;
    z-index: 50; /* 低于导航栏的z-index */
    height: 44px; /* 明确设置tabs的高度 */
  }
  .tab {
    flex: 1;
    text-align: center;
    padding: 14px 0 10px 0;
    position: relative;
  }
  .tab.active {
    color: #222;
    font-weight: 500;
  }
  .tab.highlight {
    color: var(--primary-color);
    font-weight: 600;
    border-bottom: 2px solid var(--primary-color);
  }
  .order-list {
    padding: 12px;
  }
  .order-card {
    background: #fff;
    .use-dark-mode-query({
    background-color: @dark-cell-background-color !important;
  });
    border-radius: 10px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
    padding: 10px 0 10px 0;
  }
  /* 商家信息样式 */
  .shop-info {
    display: flex;
    align-items: center;
  }

  .shop-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 8px;
    background: #f5f5f5;
  }

  .shop-name {
    font-weight: 500;
    margin-right: 4px;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .verified-icon {
    width: 16px;
    height: 16px;
  }

  .order-status {
    flex-shrink: 0;
    color: var(--primary-color);
    font-size: 13px;
  }
  .goods-list {
    padding: 0 12px;
  }
  .goods-item {
    display: flex;
    align-items: flex-start;
    margin-top: 12px;
  }
  .goods-img {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    background: #f5f5f5;
    margin-right: 10px;
  }
  .goods-info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
  }

  .goods-title-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    // margin-bottom: 8px;
  }

  .goods-title {
    flex: 1;
    font-size: 14px;
    color: #222;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    margin-right: 8px;
    /* 显示两行，超出省略号 */
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    // line-height: 20px;
  }
  .goods-price-section {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    flex-shrink: 0;
  }

  .goods-price {
    color: #f53f3f;
    font-weight: 600;
    font-size: 16px;
  }

  .goods-quantity {
    color: #888;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-size: 13px;
    // margin-top: 2px;
  }

  .goods-sku {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // margin-bottom: 4px;
  }

  .sku-text {
    color: #666;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-size: 13px;
  }

  .sku-count {
    gap: 8px;
    display: flex;
    align-items: center;
  }

  .refund-quantity {
    color: #f53f3f;
    font-size: 12px;
  }

  .normal-quantity {
    color: #86909c;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-size: 12px;
  }
  .order-summary {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 10px 12px 0 12px;
  }

  .order-summary-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }

  .order-total {
    text-align: right;
  }

  .refund-amount {
    display: flex;
    align-items: center;
  }

  .refund-label {
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    color: #86909c;
    font-size: 12px;
    margin-right: 4px;
  }

  .express {
    color: #86909c;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-size: 12px;
  }

  .time {
    color: #86909c;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-size: 12px;
  }

  .refund-value {
    color: #f53f3f;
    font-size: 12px;
  }

  .total-price {
    color: #f53f3f;
    font-size: 12px;
  }

  .goods-count {
    color: #86909c;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-size: 12px;
  }

  .freight-text {
    color: #86909c;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-size: 12px;
  }

  .order-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0 0 0;
    border-top: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-top: 1px solid @dark-line-color;
  });
    background: #fff;
    .use-dark-mode-query({
    background-color: @dark-cell-background-color !important;
  });
    height: 44px;
    margin-top: 10px;
  }
  .order-btn {
    flex: 1;
    min-width: 0;
    border-radius: 0;
    font-size: 16px;
    padding: 0;
    height: 44px;
    background: #fff !important;
    .use-dark-mode-query({
    background-color: @dark-cell-background-color !important;
  });
    border: none;
    color: #222;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    border-right: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-right: 1px solid @dark-line-color;
  });
    box-shadow: none;
    margin: 0;
  }

  .order-btn:last-child {
    border-right: none;
  }

  .order-btn.primary {
    color: #000000;
    font-weight: bold;
    background: #fff !important;
    .use-dark-mode-query({
    background-color: @dark-cell-background-color !important;
  });
    border: none;
  }

  /* 立即支付按钮 - 蓝色文字 */
  .order-btn.pay-btn {
    color: var(--primary-color) !important;
    font-weight: bold;
    background: #fff !important;
    .use-dark-mode-query({
    background-color: @dark-cell-background-color !important;
  });
  }

  /* 取消订单按钮 - 普通样式 */
  .order-btn.cancel-btn {
    color: #000000;
    font-weight: normal;
    background: #fff !important;
    .use-dark-mode-query({
    background-color: @dark-cell-background-color !important;
  });
  }

  /* 普通按钮样式 */
  .order-btn.normal-btn {
    color: #000000;
    font-weight: normal;
    background: #fff !important;
    .use-dark-mode-query({
    background-color: @dark-cell-background-color !important;
  });
  }

  .order-btn.logistics-btn {
    color: var(--primary-color) !important;
    font-weight: bold;
    background: #fff !important;
    .use-dark-mode-query({
    background-color: @dark-cell-background-color !important;
  });
  }
  .empty-order {
    text-align: center;
    color: #bbb;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-size: 15px;
    padding: 48px 0 32px 0;
    min-height: calc(100vh - 250px);
  }

  /* 下拉刷新样式 */
  .pull-refresh-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 0;
  }

  .loading-text {
    margin-left: 8px;
    font-size: 14px;
    color: #666;
  }

  .pull-refresh-success {
    font-size: 14px;
    color: #00b42a;
    text-align: center;
    padding: 10px 0;
  }


  /* 搜索栏样式 */
  .searchLine {
    background: #fff;
    .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
    // border-bottom: 1px solid #f0f0f0;
    position: fixed;
    // top: 126px; 
    left: 0;
    right: 0;
    z-index: 49; 
  }

  .search-icon {
    width: 16px;
    height: 16px;
    margin-left: 8px;
  }

  .demo-search-btn {
    display: flex;
    align-items: center;
    color: var(--primary-color);
    font-size: 14px;
    padding: 0 8px;
    cursor: pointer;
  }

  /* 备注信息样式 */
  .remark-section {
    background: #f7f8fa;
    .use-dark-mode-query({
    background-color: @dark-card-background-color !important;
  });
    border-radius: 10px;
    margin: 10px;
    padding: 12px;
    // border-top: 1px solid #f0f0f0;
  }

  .remark-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
  }

  .remark-row:last-child {
    margin-bottom: 0;
  }

  .remark-label {
    color: #666;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-size: 13px;
    width: 80px;
    flex-shrink: 0;
    margin-right: 8px;
  }

  .remark-label-block {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .remark-img-btn {
    display: flex;
    align-items: center;
    color: var(--primary-color);
    font-size: 12px;
    margin-bottom: 4px;
    cursor: pointer;
    align-self: flex-start;
  }

  .order-img-icon {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    stroke: var(--primary-color);
    fill: var(--primary-color);
  }

  .img-btn-text {
    color: var(--primary-color);
    font-size: 12px;
  }

  .remark-content {
    display: flex;
    color: #333;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-size: 13px;
    // line-height: 1.4;
  }

  // 筛选条件显示
  .filter {
    width: max-content;
    margin: 0 16px 10px 16px;
    padding: 4px 12px;
    border: 1px solid var(--primary-color);
    border-radius: 50px;
    align-items: center;
    display: flex;

    &-text {
      display: inline-block;
      text-align: center;
      font-size: 11px;
      color: var(--primary-color);
    }

    &-img {
      display: inline-block;
      margin-left: 8px;
      width: 7px;
      height: 7px;
    }
  }

  .clickOpacity:active {
    opacity: 0.8;
  }

  // 空状态样式
.not_content_order {
  width: 100%;
  height: 70vh;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  font-size: 14px;
  color: #999999;

  &-image {
    width: 100px;
    height: 100px;
    display: block;
    margin-bottom: 16px;
  }
}
}




