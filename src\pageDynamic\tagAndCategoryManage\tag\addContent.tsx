import { View, Text, Image, ScrollView } from "@tarojs/components";
import React, { useState, useRef, useEffect } from "react";
import YkNavBar from "@/components/ykNavBar/index";
import { Cell, Button, Checkbox, Input, SearchBar } from "@arco-design/mobile-react";
import { getMyAlbumList, getSearchAlbumList } from "@/utils/api/common/common_user";
import Taro,{ useLoad } from "@tarojs/taro";
import IconSearch from "@arco-design/mobile-react/esm/icon/IconSearch";
import { toast } from "@/utils/yk-common";

import "./addContent.less";

export default function AddContent() {
  const userInfo = Taro.getStorageSync("userInfo");
 
  const [search, setSearch] = useState("");
  const [selected, setSelected] = useState<number[]>([]);
  const [allIds, setAllIds] = useState<number[]>([]);
  const allSelected = selected.length === allIds.length;
  const [albumLimit, setAlbumLimit] = React.useState(10);
  const albumPageRef = useRef(1);
  const [originAlbumList, setOriginAlbumList] = useState<any[]>([]);
  const [albumList, setAlbumList] = useState<any[]>([]);
  const [isLoadEnd, setIsLoadEnd] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);
  const [pageNo, setPageNo] = React.useState(1);
  const [hasMore, setHasMore] = React.useState(true);
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
    // 获取相册列表
  const getAlbumListData = async (isLoadMore = false) => {
            if (isLoading) return;

            setIsLoading(true);

            let data = {
                pageNo: isLoadMore ? pageNo : 1,
                pageSize: albumLimit,
                userId: userInfo.id,
                content: search,
                homePageCountType: 1
            };

            let res: any;
            console.log(data, "data");
            res = await getMyAlbumList(data);
            if (res && res.code == 0) {
                const updatedAlbumList = res.data.list.map((album) => {
                    // 如果 pictures 是字符串，按逗号分割成数组
                    const picturesArray = (album.pictures === '' || album.pictures === ',')
                      ?  []
                      : album.pictures.split(','); // 如果已经是数组，保持不变

                    return {
                      ...album, // 保留其他字段
                      pictures: picturesArray, // 更新 pictures 为数组
                    };
                  });

                if (isLoadMore) {
                  // 加载更多时，追加数据
                  const newOriginList = [...originAlbumList, ...updatedAlbumList];
                  const newAllIds = [...allIds, ...res.data.list.map(i => i.id)];
                  setAllIds(newAllIds);
                  setOriginAlbumList(newOriginList);
                  setAlbumList(newOriginList);
                } else {
                  // 首次加载时，替换数据
                  setAllIds(res.data.list.map(i => i.id));
                  setOriginAlbumList(updatedAlbumList);
                  setAlbumList(updatedAlbumList);
                }

                // 判断是否还有更多数据
                setHasMore(res.data.list.length >= albumLimit);
                setIsLoadEnd(res.data.list.length < albumLimit);

            } else {
                toast("info", {
        content: "网络异常，请重试",
        duration: 2000
      });
            }
            setIsLoading(false);
  };

  const handleSelectAll = () => {
    setSelected(allSelected ? [] : allIds);
  };

  // 加载更多
  const loadMore = () => {
    console.log('loadMore triggered', { isLoading, hasMore });
    if (!isLoading && hasMore) {
      console.log('开始加载更多');
      setPageNo(prev => prev + 1);
    }
  };

  // 监听页码变化，加载数据
  useEffect(() => {
    if (pageNo > 1) {
      getAlbumListData(true);
    }
  }, [pageNo]);

  useLoad(() => {
    setPageNo(1);
    setIsLoadEnd(false);
    setHasMore(true);
    setIsLoading(false);

    // 初始化已选择的图文
    const selectedArticles = Taro.getStorageSync('selectedArticles') || [];
    if (selectedArticles.length > 0) {
      const selectedIds = selectedArticles.map((article: any) => article.id);
      setSelected(selectedIds);
    }

    getAlbumListData();

  });

  useEffect(() => {

    console.log(albumList, "albumList");
  }, [albumList]);


  return (
    <View className="add-content-page">
      <View className="nav-bar-row">
      {platform !== "WX" &&<YkNavBar title="添加图文" />}
      </View>
        <SearchBar 
          actionButton={<span className="demo-search-btn"></span>} 
          placeholder="筛选"
          onChange={e => {
            const value =  e.target.value;
            //setSearch(value);
            if (!value) {
              setAlbumList(originAlbumList);
            } else {
              setAlbumList(originAlbumList.filter(item => item.content.includes(value)));
            }
          }}
          className="demo-input-btn-input"
          clearable
          onClear={() => {
            //setSearch(''); // 清空输入框
            setAlbumList(originAlbumList);              
          }}
        //   suffix={<div className="demo-input-btn-wrap">
        //     <Button inline  size="mini" onClick={() => {
        //         getAlbumListData();
        //     }}>Send</Button>
        // </div>}
        //   border="none"
        />


     
      <ScrollView
        className="content-scroll"
        scrollY
        onScrollToLower={loadMore}
        lowerThreshold={50}
      >
        <View className="content-list">
          {albumList.map(item => (
              <View key={item.id} className="dline">
                <View className="dline-left">
                  <Checkbox
                    checked={selected.includes(item.id)}
                    className="dline-left-change"
                    onChange={() => {
                      if (selected.includes(item.id)) {
                        setSelected(selected.filter(id => id !== item.id));
                      } else {
                        setSelected([...selected, item.id]);
                      }
                    }}
                  />
                </View>
                <View className="dline-right">
                  {item.content && (
                    <View className={`dline-right-top ${!item.pictures || item.pictures.length === 0 ? 'topBg' : ''}`}>
                      <View className="dynamic-title">{item.content}</View>
                    </View>
                  )}
                  {item.pictures && item.pictures.length > 0 && (
                    <View className="dline-right-bottom">
                      {item.pictures.slice(0, 5).map((img, idx) => (
                        <View key={idx} className="imageItem">
                          <Image
                            src={img}
                            mode="aspectFill"
                            className="imageItem-image"
                          />
                          {idx === 4 && item.pictures.length > 5 && (
                            <View className="imageItem-mask">
                              <Text>+{item.pictures.length - 5}</Text>
                            </View>
                          )}
                        </View>
                      ))}
                    </View>
                  )}
                </View>
              </View>
          ))}

          {/* 加载更多提示 */}
          {isLoading && albumList.length > 0 && (
            <View className="loading-more">
              <Text>加载中...</Text>
            </View>
          )}

          {isLoadEnd && albumList.length > 0 && (
            <View className="no-more">
              <Text>没有更多了</Text>
            </View>
          )}
        </View>
      </ScrollView>
      <View className="add-content-footer">
        <View className="footer-left" onClick={handleSelectAll}>
          <Checkbox checked={allSelected} value="全选" />
          {/* {selected.length > 0 && <Text className="footer-count">（{selected.length}）</Text>} */}
          {<Text className="footer-count">（{selected.length}）</Text>}
        </View>
        <Button className="footer-btn" onClick={() => {
          // 获取所有已加载的图文数据（包括之前页面的数据）
          const allLoadedArticles = [...originAlbumList];

          // 从所有已加载的数据中筛选出当前选中的图文
          const finalSelectedArticles = allLoadedArticles.filter(item => selected.includes(item.id));

          // 直接用当前选中的图文替换缓存
          Taro.setStorageSync('selectedArticles', finalSelectedArticles);
          Taro.navigateBack();
        }}>添加</Button>
      </View>
    </View>
  );
}
