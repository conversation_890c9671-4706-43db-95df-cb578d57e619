@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pageOrder/delivery/index"] {
  .out {
    height: 100vh;
    overflow-y: auto;
    background-color: #f8f9fa;
    .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
  }

  .error-message {
    padding: 40px 20px;
    text-align: center;
    color: #999;
  }

  .line-del {
    margin: 0 15px;
    height: 1px;
    background-color: rgba(248, 248, 248, 0.8);
  }

  .receive {
    display: flex;
    align-items: flex-start;
    padding: 15px;

    &-container {
      display: flex;
      align-items: flex-start;
      flex-shrink: 0;
    }

    &-img {
      width: 12px;
      height: 14px;
      display: block;
      margin-top: 1px; // 微调图标位置
      flex-shrink: 0;
    }

    &-name {
      font-size: 12px;
      color: #333333;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      margin-left: 8px;
      flex-shrink: 0;
    }

    &-phone {
      font-size: 12px;
      color: #333333;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      margin-left: 8px;
      flex-shrink: 0;
    }

    &-address {
      font-size: 12px;
      color: #333333;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      margin-left: 8px;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2; // 限制最多两行
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      flex: 1; // 占据剩余空间
    }
  }

  .emsNum {
    display: flex;
    align-items: center;
    padding: 15px;

    &-title {
      width: 60px;
      font-size: 15px;
      color: #333333;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    }

    &-input {
      margin-left: 30px;
      font-size: 15px;
      color: #333333;
      flex: 1;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    }
  }

  .input-placeholder {
    color: #999999 !important;
  }

  .delivery {
    display: flex;
    align-items: center;
    padding: 15px;

    &-title {
      width: 60px;
      font-size: 15px;
      color: #333333;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    }

    &-company {
      align-items: center;
      margin-left: 30px;
      display: flex;
      flex: 1;
      justify-content: space-between;

      &-inputHint {
        font-size: 15px;
        color: #999999;
        .use-dark-mode-query({
        color: @dark-font-color !important;
      });
      }

      &-input {
        font-size: 15px;
        color: #333333;
        .use-dark-mode-query({
        color: @dark-font-color !important;
      });
      }

      &-img {
        width: 6px;
        height: 11px;
        display: block;
      }
    }
  }

  .freight-del {
    background-color: #ffffff;
    .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
    display: flex;
    align-items: center;
    padding: 15px;

    &-title {
      width: 60px;
      font-size: 15px;
      color: #333333;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    }

    &-text {
      margin-left: 30px;
      font-size: 15px;
      color: #333333;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    }
  }

  .container {
    padding-bottom: 8px;
    background-color: #ffffff;
    .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
    margin-top: 10px;

    &-good {
      position: relative;
      align-items: center;
      width: calc(100% - 13px);
      padding-left: 13px;
      display: flex;

      &-item {
        align-items: center;
        width: 100%;
        display: flex;
        justify-content: space-between;
        padding: 10px 0;

        &-left {
          display: flex;
          align-items: center;
          flex: 1;

          &-check {
            flex-shrink: 0;
            display: block;
            width: 17px;
            height: 17px;
          }

          &-img {
            flex-shrink: 0;
            margin-left: 13px;
            border-radius: 7px;
            display: block;
            width: 66px;
            height: 66px;
          }

          &-text {
            margin-right: 13px;
            margin-left: 9px;
            text-align: left;
            font-size: 13px;
            color: #000000;
            .use-dark-mode-query({
            color: @dark-font-color !important;
          });
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            line-clamp: 2;
          }
        }

        &-right {
          margin-right: 13px;
          margin-left: 13px;
          font-size: 14px;
          color: #eb483f;
        }

        &-right::first-letter {
          font-size: 11px;
          color: #eb483f;
        }
      }
    }

    &-opera {
      position: relative;
      width: calc(100% - 26px);
      padding: 0 13px;
      display: flex;

      &-item {
        width: 100%;
        display: flex;
        justify-content: space-between;
        height: 40px;
        align-items: center;

        &-left {
          display: flex;
          align-items: center;

          &-check {
            display: block;
            width: 17px;
            height: 17px;
          }

          &-format {
            margin-left: 13px;
            display: flex;
            flex-direction: column;

            &-style {
              font-size: 12px;
              color: #000000;
              .use-dark-mode-query({
              color: @dark-font-color !important;
            });
            }
          }
        }

        &-right {
          align-items: center;
          width: 87px;
          text-align: center;
          border-radius: 50px;
          height: 25px;
          border: 1px solid rgba(153, 153, 153, 0.2);
          display: flex;
          justify-content: space-between;

          &-subGood {
            padding: 0 10px;
            font-size: 15px;
            color: #333333;
          }

          .unclick {
            color: rgba(153, 153, 153, 0.5);
          }

          &-num {
            font-size: 13px;
            color: #000000;
          }

          &-addGood {
            padding: 0 10px;
            font-size: 15px;
            color: #333333;
          }
        }
      }
    }
  }

  .foot_holder {
    height: 60px;
  }

  .footerbtn {
    z-index: 100;
    background-color: #ffffff;
    .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.1);

    &-left {
      position: absolute;
      left: 15px;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      align-items: center;

      &-img {
        width: 20px !important;
        height: 20px !important;
        vertical-align: middle;
      }

      &-all {
        display: flex;
        align-items: center;
        justify-content: center;
        &-text {
          margin-left: 8px;
          font-size: 13px;
          color: #666666;
          .use-dark-mode-query({
          color: @dark-font-color !important;
        });
        }
      }
    }

    &-right {
      margin-right: 15px;
      display: flex;
      position: relative;

      &-send {
        justify-content: center;
        align-items: center;
        display: flex;
        border-radius: 2px;
        background-color: var(--primary-color);
        opacity: 1;
        font-size: 14px;
        padding: 6px 16px;
        color: #ffffff;
        border: none;
        cursor: pointer;
      }

      &-sendN {
        align-items: center;
        justify-content: center;
        display: flex;
        border-radius: 2px;
        background-color: var(--primary-color);
        opacity: 0.3;
        font-size: 14px;
        padding: 6px 16px;
        color: #ffffff;
        border: none;
        cursor: pointer;
      }
    }
  }

  // 发货选项弹框样式
  .delivery-options-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 999;
  }

  .delivery-options {
    position: absolute;
    bottom: 35px;
    right: 15px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    min-width: 80px;

    &-item {
      padding: 12px 16px;
      font-size: 14px;
      color: #333333;
      cursor: pointer;
      border-bottom: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-bottom: 1px solid @dark-line-color;
    });

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background-color: #f8f9fa;
      }
    }
  }

  // 留言样式 - 参考 sellOrder details
  .order-remark {
    background: #fff;
    .use-dark-mode-query({
    background-color: @dark-cell-background-color !important;
  });
    border-radius: 10px;
    margin: 0 16px 16px 16px;
    box-shadow: 0 2px 8px rgba(36, 104, 242, 0.03);
  }

  .contact-info-row {
    display: flex;
    padding: 16px;
    border-bottom: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-bottom: 1px solid @dark-line-color;
  });

    &:last-child {
      border-bottom: none;
    }

    // 发件人行 - 图标和内容垂直居中
    &:first-child {
      align-items: center;
    }

    // 收件人行 - 图标顶对齐
    &:last-child {
      align-items: flex-start;
    }
  }

  .contact-label {
    font-size: 12px;
    color: #1d2129;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    width: 60px;
    flex-shrink: 0;
    margin-top: 0;
    font-weight: 500;
  }

  .contact-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: 1;
  }

  .contact-value {
    font-size: 12px;
    color: #86909c;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    flex: 1;
    line-height: 1.4;
  }

  .order-info-card-delivery {
    display: flex;
    flex-direction: column;

    background: #fff;
    .use-dark-mode-query({
      background-color: @dark-cell-background-color !important;
    });
    border-radius: 10px;
    margin: 16px;
    box-shadow: 0 2px 8px rgba(36, 104, 242, 0.03);
  }

  .contact-info-remark-row-del {
    display: flex;
    flex-direction: column;

    background: #fff;
    .use-dark-mode-query({
    background-color: @dark-cell-background-color !important;
  });
    border-radius: 10px;
    margin: 16px;
    box-shadow: 0 2px 8px rgba(36, 104, 242, 0.03);
    // padding:0 10px;
    padding-bottom: 16px;
  }

  .contact-info-remark-row1 {
    display: flex;
    padding: 16px 16px 0 16px;
    align-items: center;
  }

  .order-remark-header-del {
    display: flex;
    flex: 1;
    justify-content: space-between;
    align-items: center;
  }

  .order-remark-content {
    flex: 1;
    margin-right: 12px;
  }

  .order-remark-text {
    font-size: 15px;
    font-weight: 500;
    color: #222;
    line-height: 22px;
    margin-bottom: 4px;
    display: block;
    .use-dark-mode-query({
    color: var(--dark-font-color);
  });

    &:last-child {
      margin-bottom: 0;
    }
  }

  .order-remark-input-sell {
    width: 100%;
    flex: 1;
    font-size: 14px;
    color: #222;
    .use-dark-mode-query({
    color: var(--dark-font-color);
  });
    padding: 8px 0;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    &::placeholder {
      color: #999;
      .use-dark-mode-query({
      color: #666;
    });
    }
  }

  .order-remark-camera {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    &:active {
      opacity: 0.7;
    }
  }

  .camera-icon {
    width: 20px;
    height: 20px;
  }

  // 上传图片区域
  .uploaded-images-detail {
    display: flex;
    flex-wrap: nowrap; // 不换行
    gap: 8px;
    margin-top: 8px;
    overflow-x: auto; // 横向滚动
    overflow-y: hidden; // 禁止垂直滚动
    padding: 0 16px 4px 16px;
    height: 68px; // 固定高度，与图片高度(60px) + padding(4px) + gap(4px) 一致

    // 隐藏滚动条但保持滚动功能
    &::-webkit-scrollbar {
      display: none;
    }
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .uploaded-image-item {
    position: relative;
    width: 60px;
    height: 60px;
    flex-shrink: 0; // 防止图片被压缩
  }

  .uploaded-image {
    width: 100%;
    height: 100%;
    border-radius: 6px;
    object-fit: cover;
  }

  .delete-image-btn {
    position: absolute;
    top: 0;
    right: 0;
    width: 18px;
    height: 18px;
    background: rgba(0, 0, 0, 0.2);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0 6px 0 0;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    z-index: 10;

    &:active {
      opacity: 0.8;
    }
  }

  // 买家留言图片样式
  .remark-images-container {
    display: flex;
    align-items: flex-start;
    padding: 16px;
  }

  .remark-images-scroll {
    display: flex;
    gap: 8px;
    overflow-x: auto;
  }

  .remark-image {
    width: 60px;
    height: 60px;
    border-radius: 6px;
    object-fit: cover;
    flex-shrink: 0;
  }

  // 发货页面商品列表样式 - 参考购物车
  .delivery-list {
    margin-bottom: 16px;
    padding: 0 16px;
  }

  .delivery-shop {
    background: #fff;
    .use-dark-mode-query({
    background: @dark-cell-background-color !important;
  });
    margin-top: 12px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    padding-bottom: 8px;
  }

  .delivery-shop-header {
    display: flex;
    align-items: center;
    padding: 12px 12px 0 12px;
    min-height: 36px;
  }

  .delivery-checkbox {
    width: 20px !important;
    height: 20px !important;
    margin-right: 8px;
    vertical-align: middle;
    cursor: pointer;
  }

  .delivery-shop-info {
    display: flex;
    align-items: center;
    flex: 1;
    cursor: pointer;
    padding: 4px 0;
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  .delivery-shop-avatar-wrapper {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 8px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
  }

  .delivery-shop-avatar {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    display: block !important;
  }

  .delivery-shop-name-group {
    display: flex;
    align-items: center;
    flex: 1;
  }

  .delivery-shop-name {
    font-size: 14px;
    color: #222;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-weight: 500;
    line-height: 24px;
    margin-right: 6px;
  }

  .delivery-shop-wx-icon {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }

  .delivery-shop-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    font-size: 12px;
    color: #888;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
  }

  .delivery-express-info {
    margin-bottom: 2px;
  }

  .delivery-time-info {
    color: #666;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
  }

  // 商品列表样式 - 参考购物车
  .delivery-shop-products {
    padding: 0 12px;
  }

  .delivery-product-card {
    background: transparent;
    border-radius: 10px;
    margin-top: 12px;
    // padding: 8px;
  }

  .delivery-product-main {
    display: flex;
    align-items: center;
  }

  .delivery-product-img-wrapper {
    width: 64px;
    height: 64px;
    border-radius: 8px;
    margin-right: 12px;
    overflow: hidden;
    display: block;
    background: #f5f5f5;
  }

  .delivery-product-img {
    width: 100%;
    height: 100%;
    display: block;
    border-radius: 8px;
    object-fit: cover;
  }

  .delivery-product-info {
    flex: 1;
    min-width: 0;
  }

  .delivery-product-title {
    font-size: 14px;
    color: #222;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-weight: 500;
    margin-bottom: 4px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    line-height: 20px;
    max-height: 40px;
  }

  .delivery-product-meta {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 2px;
    margin-top: 4px;
  }

  .delivery-product-price {
    font-size: 16px;
    color: #f53f3f;
    font-weight: 600;
    line-height: 22px;
  }

  // SKU样式 - 参考购物车
  .delivery-product-sku {
    margin-top: 8px;
    padding-left: 0; // 选择框左对齐，不需要额外padding
  }

  .delivery-product-sku-main {
    display: flex;
    align-items: center;
    // padding: 8px 0; // 与商品卡片保持相同的左右padding
    // border-top: 1px solid #f5f5f5;
    // .use-dark-mode-query({
    //   border-top: 1px solid #333;
    // });
  }

  .delivery-product-sku-info {
    flex: 1;
    margin-left: 8px;
  }

  .delivery-product-sku-text {
    font-size: 13px;
    color: #666;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    line-height: 20px;
  }

  .delivery-product-sku-controls {
    display: flex;
    align-items: center;
  }

  // 步进器样式 - 参考购物车
  .delivery-stepper {
    display: flex;
    align-items: center;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    overflow: hidden;
    background: #fff;
    .use-dark-mode-query({
    background: @dark-cell-background-color !important;
    border-color: #333;
  });
  }

  .delivery-stepper-btn {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f8f8;
    color: #333;
    font-size: 16px;
    cursor: pointer;
    user-select: none;
    border: none;
    .use-dark-mode-query({
    background: #444 !important;
    color: @dark-font-color !important;
  });

    &.disabled {
      color: #ccc;
      cursor: not-allowed;
      .use-dark-mode-query({
      color: #666 !important;
    });
    }

    &:hover:not(.disabled) {
      background: #e8e8e8;
      .use-dark-mode-query({
      background: #555 !important;
    });
    }
  }

  .delivery-stepper-input {
    width: 40px;
    height: 28px;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 14px;
    background: #f8f8f8;
    .use-dark-mode-query({
    background: @dark-cell-background-color !important;
    color: @dark-font-color !important;
  });
  }
}
