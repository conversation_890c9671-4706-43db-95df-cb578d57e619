import { View, Text, Image } from "@tarojs/components";
import React from "react";
import { Collapse, Ellipsis } from "@arco-design/mobile-react";
import { IconTriDown } from "@arco-design/mobile-react/esm/icon";
import { DynamicItem, GroupedItem } from "./AlbumCore";
import { formatDate } from "@/utils/utils";
import AlbumActions from "./AlbumActions";

export interface AlbumListProps {
  items: GroupedItem[];
  // 独立传入的置顶数据（已分组或未分组都可；未分组时直接渲染为一个组）
  pinnedItems?: DynamicItem[];
  expandedKeys: string[];
  attrExpandedKeys: Array<string | number>;
  pinnedAttrExpandedKeys?: Array<string | number>;
  isOwnAlbum?: boolean;
  hasOpenOnlinePayment?: number;
  showPinned?: boolean; // 是否显示置顶区域
  onCollapseChange?: (value: string) => void;
  onAttrCollapseChange?: (value: string | number) => void;
  onPinnedAttrCollapseChange?: (value: string | number) => void;
  onDelete?: (id: string | number) => void;
  onEdit?: (item: DynamicItem) => void;
  onDownload?: (item: DynamicItem) => void;
  onRefresh?: (id: string | number) => void;
  onToggleTop?: (id: string | number, isTop: number) => void;
  onShare?: (item: DynamicItem) => void;
  onDetail?: (item: DynamicItem) => void;
  onAddCart?: (item: DynamicItem) => void;
}

const AlbumList: React.FC<AlbumListProps> = ({
  items,
  pinnedItems,
  expandedKeys,
  attrExpandedKeys,
  pinnedAttrExpandedKeys = [],
  isOwnAlbum = false,
  hasOpenOnlinePayment,
  showPinned = true,
  onCollapseChange,
  onAttrCollapseChange,
  onPinnedAttrCollapseChange,
  onDelete,
  onEdit,
  onDownload,
  onRefresh,
  onToggleTop,
  onShare,
  onDetail,
  onAddCart,
}) => {
  const PINNED_KEY = "__PINNED__";

  // 渲染单个商品项
  const renderProductItem = (item: DynamicItem, isPinned: boolean = false) => {
    const currentAttrExpandedKeys = isPinned
      ? pinnedAttrExpandedKeys
      : attrExpandedKeys;
    const currentOnAttrCollapseChange = isPinned
      ? onPinnedAttrCollapseChange
      : onAttrCollapseChange;

    return (
      <View key={item.id} className="move-item-wrap">
        <View className="move-item" onClick={() => onDetail?.(item)}>
          {item.coverImage && (
            <Image className="move-img" src={item.coverImage} />
          )}
          <View className="move-info">
            <View className="move-desc">
              <Ellipsis
                className="move-title-text selectable-text"
                maxLine={2}
                text={item.content}
              />
              <Text className="move-price">
                {" "}
                {item.price > 0 ? `¥${item.price}` : ""}{" "}
              </Text>
            </View>

            {/* 操作按钮 */}
            <AlbumActions
              item={item}
              isOwnAlbum={isOwnAlbum}
              hasOpenOnlinePayment={hasOpenOnlinePayment}
              onDelete={onDelete}
              onEdit={onEdit}
              onDownload={onDownload}
              onRefresh={onRefresh}
              onToggleTop={onToggleTop}
              onShare={onShare}
              onDetail={onDetail}
              onAddCart={onAddCart}
            />
          </View>
        </View>

        {/* 商品属性区域 */}
        <View className="move-attrs-wrap">
          {(((item as any).productSpecificationsNames &&
            (item as any).productSpecificationsNames.trim()) ||
            ((item as any).productColorNames &&
              (item as any).productColorNames.trim())) && (
            <Collapse.Group
              activeItems={currentAttrExpandedKeys as any}
              onCollapse={currentOnAttrCollapseChange as any}
              className="attr-collapse"
            >
              <Collapse
                key={item.id}
                value={item.id as any}
                header={<Text className="move-attrs">商品属性</Text>}
                active
                icon={
                  <IconTriDown
                    className="collapse-icon"
                    style={{
                      width: "10px",
                      height: "10px",
                      transform: `rotate(${
                        currentAttrExpandedKeys.includes(item.id)
                          ? "0deg"
                          : "-90deg"
                      })`,
                      transition: "transform 0.2s",
                    }}
                  />
                }
                content={
                  <View className="card-attr-content">
                    {(item as any).productColorNames &&
                    (item as any).productColorNames.trim() ? (
                      <View className="attr-row" key="颜色">
                        <Text className="attr-label">颜色：</Text>
                        <View className="attr-values">
                          {(item as any).productColorNames
                            .split(",")
                            .map((val, index) => (
                              <Text className="attr-value" key={index}>
                                {val.trim()}
                              </Text>
                            ))}
                        </View>
                      </View>
                    ) : null}
                    {(item as any).productSpecificationsNames &&
                    (item as any).productSpecificationsNames.trim() ? (
                      <View className="attr-row" key="规格">
                        <Text className="attr-label">规格：</Text>
                        <View className="attr-values">
                          {(item as any).productSpecificationsNames
                            .split(",")
                            .map((val, index) => (
                              <Text className="attr-value" key={index}>
                                {val.trim()}
                              </Text>
                            ))}
                        </View>
                      </View>
                    ) : null}
                  </View>
                }
              />
            </Collapse.Group>
          )}
        </View>
      </View>
    );
  };

  // 渲染置顶商品列表
  const renderPinnedProductList = () => {
    // 优先使用外部传入的 pinnedItems；否则兼容从 items 中提取 isTop===1 的旧用法
    const pinned: DynamicItem[] = pinnedItems && pinnedItems.length > 0
      ? pinnedItems
      : (() => {
          const list: DynamicItem[] = [];
          items.forEach((group) => {
            group.items.forEach((item) => {
              if (item.isTop === 1) list.push(item);
            });
          });
          return list;
        })();

    // 如果没有置顶商品，不渲染任何内容
    if (pinned.length === 0) {
      return null;
    }

    return (
      <View className="move-list pinned-section">
        <Collapse.Group
          activeItems={Array.from(new Set([...(expandedKeys || []), PINNED_KEY]))}
          onCollapse={onCollapseChange}
          className="dynamic-collapse"
        >
          <Collapse
            key={PINNED_KEY}
            value={PINNED_KEY}
            header={<Text className="pinned-title">置顶</Text>}
            active
            icon={
              <IconTriDown
                className="collapse-icon"
                style={{
                  width: "10px",
                  height: "10px",
                  transform: `rotate(${
                    expandedKeys.includes(PINNED_KEY) ? "0deg" : "-90deg"
                  })`,
                  transition: "transform 0.2s",
                }}
              />
            }
            content={
              <View>
                {pinned.map((item) => renderProductItem(item, true))}
              </View>
            }
          />
        </Collapse.Group>
      </View>
    );
  };

  // 渲染普通商品列表
  const renderNormalProductList = () => {
    // 过滤掉置顶项，只保留非置顶项；并剔除过滤后为空的分组
    const normalGroups = items
      .map((group) => ({
        ...group,
        items: group.items.filter((item) => item.isTop !== 1),
      }))
      .filter((group) => group.items.length > 0);

    return (
      <View className="move-list">
        <Collapse.Group
          activeItems={expandedKeys}
          onCollapse={onCollapseChange}
          className="dynamic-collapse"
        >
          {normalGroups.map((group) => (
            <Collapse
              key={group.group}
              value={group.group}
              header={<Text className="move-group-title">{group.group}</Text>}
              active
              icon={
                <IconTriDown
                  className="collapse-icon"
                  style={{
                    width: "10px",
                    height: "10px",
                    transform: `rotate(${
                      expandedKeys.includes(group.group) ? "0deg" : "-90deg"
                    }`,
                    transition: "transform 0.2s",
                  }}
                />
              }
              content={
                <View>
                  {group.items.map((item) => renderProductItem(item, false))}
                </View>
              }
            />
          ))}
        </Collapse.Group>
      </View>
    );
  };

  return (
    <View className="album-list-container">
      {showPinned && renderPinnedProductList()}
      {renderNormalProductList()}
    </View>
  );
};

export default AlbumList;
