@import "@arco-design/mobile-react/style/mixin.less";
@import "@/utils/css/variables.less";
[id^="/pageVip/benefits/index"] {
  // 会员权益页面样式
.vip-benefits-page {
  width: 100%;
  min-height: 100vh;
  .use-var(background-color, background-color);
  .use-dark-mode-query({
      background-color: @dark-background-color;
  });

  .benefits-container {
    width: 100%;
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    align-items: center;

    // 顶部横幅
    .benefits-header {
      width: 100%;
      height: 21px;
      // 金色渐变背景 - linear-gradient(270deg, #FFD28D 0%, #FFEFCC 100%)
      background: linear-gradient(270deg, #FFD28D 0%, #FFEFCC 100%);
      border-radius: 15px 15px 0 0;
      padding: 15px 0 15px 15px;
      display: flex;
      align-items: center;

      .header-title {
        // 字体：PingFang SC Bold 30px
        font-family: PingFang SC, sans-serif;
        font-size: 15px;
        font-weight: bold;
        line-height: 21px;
        // 文字颜色 #5A4124
        color: #5A4124;
      }
    }

    // 权益列表容器
    .benefits-list {
      width: 100%;
      .use-var(background-color, background-color);
      .use-dark-mode-query({
          background-color: @dark-background-color;
      });
    border-radius: 30px 30px 0 0;

      // 单个权益项
      .benefit-item {
        width: 100%;
        height: 43px;
        padding: 16px;
        display: flex;
        flex-direction: row;
        align-items: center;
        .use-var(background-color, background-color);
        .use-dark-mode-query({
            background-color: @dark-background-color;
        });

        // 图标包裹容器
        .benefit-icon-wrapper {
          width: 60px;
          height: 40px;
          display: flex;
          align-items: center;

          // 图标背景
          .benefit-icon-bg {
            width: 40px;
            height: 40px;
            border-radius: 200px; // 圆形背景
            // 金色背景 #FFD28D
            background-color: #FFD28D;
            display: flex;
            justify-content: center;
            align-items: center;

            // SVG图标样式
            .benefit-icon-svg {
              width: 24px;
              height: 24px;
            }
          }
        }

        // 文字内容
        .benefit-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;

          // 权益标题
          .benefit-title {
            // 字体：PingFangSC-Medium 32px
            font-family: PingFangSC-Medium, PingFang SC, sans-serif;
            font-size: 16px;
            font-weight: 500;
            line-height: 140%;
            letter-spacing: auto;
            // 文字颜色 #1D2129 - 文字 Text/5-文字-基础-@font-color
            .use-var(color, font-color);
            .use-dark-mode-query({
                color: @dark-font-color;
            });
            margin-bottom: 0;
          }

          // 权益描述
          .benefit-description {
            margin-top: 6px;
            // 字体：PingFangSC-Regular 28px
            font-family: PingFangSC-Regular, PingFang SC, sans-serif;
            font-size: 14px;
            font-weight: 400;
            line-height: 140%;
            letter-spacing: auto;
            // 文字颜色 #86909C - 文字 Text/3-文字-附加信息-@sub-info-font-color
            color: #86909C;
          }
        }
      }

      // 分割线
      .benefit-divider {
        width: 100%;
        height: 1px;
        padding-left: 16px;
        
        &::after {
          content: '';
          display: block;
          width: calc(100% - 16px);
          height: 1px;
          margin-left: 16px;
          // 分割线颜色 #F2F3F5 - 线条 Line/更浅的线条色 @lighter-line-color
          background-color: #F2F3F5;
        }
      }
    }
  }
}

}