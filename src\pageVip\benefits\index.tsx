import { View, Text } from '@tarojs/components';
import { useState, useEffect } from 'react';
import Taro from '@tarojs/taro';
import YkNavBar from '@/components/ykNavBar';
import './index.less';

// 会员权益列表数据
interface BenefitItem {
  id: string;
  icon: string;
  title: string;
  description: string;
}

const BENEFIT_LIST: BenefitItem[] = [
  {
    id: '1',
    icon: 'dynamic', // 图文动态图标
    title: '发布动态',
    description: '添加图文动态到相册中'
  },
  {
    id: '2',
    icon: 'move', // 素材搬家图标
    title: '素材搬家',
    description: '通过链接一键转发动态'
  },
  {
    id: '3',
    icon: 'payment', // 微信支付图标
    title: '在线收款',
    description: '提交资料申请微信商户'
  },
  {
    id: '4',
    icon: 'edit', // 批量编辑图标
    title: '批量编辑',
    description: '一键批量编辑您的动态'
  },
  {
    id: '5',
    icon: 'forward', // 批量转发图标
    title: '批量转发',
    description: '一键批量转发好友动态'
  }
];

const VipBenefits = () => {
  const [platform, setPlatform] = useState<string>('H5');

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform('Android');
    } else if (isIos) {
      setPlatform('IOS');
    } else if (isHM) {
      setPlatform('HM');
    } else {
      setPlatform('H5');
    }
    if (window.__wxjs_environment === 'miniprogram') {
      setPlatform('WX');
    }
  }, []);

  // 渲染权益图标（使用SVG路径）
  const renderBenefitIcon = (iconType: string) => {
    const iconPaths: Record<string, string> = {
      // 图文动态图标路径
      dynamic: 'M8.69565 10.5495Q10.0687 10.5495 11.0526 11.4945Q12.0366 12.4396 12.0366 13.8022Q12.0366 15.1209 11.0526 16.0659Q10.0687 17.011 8.69565 17.011Q7.27689 17.011 6.31579 16.0659Q5.35469 15.1209 5.35469 13.8022Q5.35469 12.4396 6.31579 11.4945Q7.27689 10.5495 8.69565 10.5495ZM35.7437 0Q37.6659 0 38.833 1.23077Q40 2.46154 40 4.04396L40 27.6923Q40 28.7033 39.2449 29.4066Q38.4897 30.1099 37.4371 30.1099L34.2792 30.1099L34.2792 24.7912L36.2471 24.7912Q36.7506 24.7912 37.1167 24.4396Q37.4828 24.0879 37.4828 23.5604Q37.4828 23.0769 37.1167 22.7253Q36.7506 22.3736 36.2471 22.3736L34.2792 22.3736L34.2792 18.9451L36.2471 18.9451Q36.7506 18.9451 37.1167 18.5934Q37.4828 18.2418 37.4828 17.7582Q37.4828 17.2747 37.1167 16.9011Q36.7506 16.5275 36.2471 16.5275L34.2792 16.5275L34.2792 13.4066L36.2471 13.4066Q36.7506 13.4066 37.1167 13.0549Q37.4828 12.7033 37.4828 12.1758Q37.4828 11.6923 37.1167 11.3407Q36.7506 10.989 36.2471 10.989L34.2792 10.989L34.2792 7.29671Q34.2792 4.83517 33.0892 3.82418Q31.8993 2.81319 29.0618 2.81319L11.4416 2.81319L11.4416 2.41758Q11.4416 1.4066 12.1968 0.703299Q12.9519 2.30559e-06 14.0046 2.30559e-06L35.7437 0ZM28.0549 5.45055Q29.8398 5.45055 30.6407 6.43956Q31.4417 7.42857 31.4417 9.01099L31.4417 33.2747Q31.4417 34.2857 30.5721 35.1429Q29.7025 36 28.4211 36L2.83753 36Q1.73913 36 0.869565 35.1209Q-2.18232e-07 34.2418 0 33.055L0 8.35165Q0 7.07692 0.732265 6.26374Q1.46453 5.45055 2.65446 5.45055L28.0549 5.45055ZM28.5584 10.0659Q28.5584 9.31868 28.0778 8.76923Q27.5973 8.21978 26.5446 8.21978L4.71396 8.21978Q4.53089 8.21978 4.21052 8.28571Q3.89016 8.35165 3.56979 8.50549Q3.24943 8.65934 3.02059 8.92308Q2.79176 9.18681 2.79176 9.58242L2.79176 21.3626Q3.15789 21.7582 3.82151 22.3077Q4.48512 22.8571 5.37757 23.3187Q6.27002 23.7802 7.36842 24.1099Q8.46682 24.4396 9.74828 24.4396Q11.6705 24.4396 13.1121 23.8462Q14.5538 23.2527 15.7895 22.3516Q17.0252 21.4505 18.2151 20.4176Q19.405 19.3846 20.8467 18.4615Q22.2883 17.5385 24.1419 16.9011Q25.9954 16.2637 28.5584 16.1758L28.5584 10.0659Z',
      // 素材搬家图标路径
      move: 'M0.232929 6.50043C0.706104 5.70885 1.73127 5.45067 2.52288 5.92372L3.91285 6.75711C5.46282 7.68051 6.58946 9.1706 7.05612 10.9141L12.406 30.8687L12.5593 30.8687C13.876 30.8687 15.126 31.4521 15.9859 32.4621L37.9822 26.5484L38.0521 26.5317C38.9145 26.3416 39.7679 26.8862 39.9588 27.7485C40.16 28.6627 39.6063 29.5733 38.7021 29.8153L17.1826 35.5923C17.1112 37.6126 15.7175 39.3444 13.7593 39.846C11.3251 40.4923 8.82712 39.0456 8.17609 36.6124C7.73944 35.039 8.1861 33.3455 9.34274 32.2021L4.07618 12.5675C3.68535 11.1097 2.73076 9.86714 1.4229 9.11394L0.822912 8.77059C0.0278774 8.31227 -0.237778 7.29158 0.232924 6.50377L0.232929 6.50043ZM31.019 0L31.2256 0.0166683C31.8789 0.0300032 32.4589 0.416694 32.7356 1.00007L32.8356 1.26008L38.4021 22.2048C38.5216 22.6171 38.4712 23.06 38.2621 23.4349C38.0892 23.7468 37.8237 23.9973 37.5022 24.1516L37.2522 24.2449L16.3959 29.8153C16.26 29.8358 16.1218 29.8358 15.9859 29.8153C15.3358 29.8013 14.7496 29.4204 14.4726 28.8319L14.3726 28.5685L8.79275 7.78051C8.67502 7.36955 8.72413 6.92874 8.92941 6.55377C9.10319 6.24133 9.37 5.99082 9.69273 5.83705L9.93939 5.74038L30.8123 0.0166683L31.019 0ZM29.619 14.9643L29.339 15.011L20.1825 17.4878C19.4357 17.6896 18.992 18.4565 19.1892 19.2046L19.1892 19.2113C19.3659 19.8613 19.9725 20.278 20.6225 20.238L20.8992 20.1913L30.0556 17.7145C30.8025 17.5127 31.2462 16.7458 31.049 15.9977L31.049 15.9911C30.9027 15.4505 30.4471 15.05 29.8923 14.9743L29.619 14.9643ZM27.8357 8.96392L27.5557 9.01059L18.3992 11.4874C17.7576 11.6612 17.3253 12.2605 17.3626 12.9242L17.4059 13.2042C17.5774 13.8456 18.1766 14.2776 18.8392 14.2376L19.1159 14.1909L28.2757 11.7141C28.9159 11.539 29.3467 10.9401 29.309 10.2773L29.2657 9.99732C29.1201 9.45513 28.6649 9.0523 28.109 8.97392L27.8357 8.96392Z',
      // 微信支付图标路径
      payment: 'M44.0000229609375,28.9873603125C44.0000229609375,23.3005524125,38.3343559609375,18.6689453125,31.9750119609375,18.6689453125C25.2387590609375,18.6689453125,19.9339599609375,23.3045797125,19.9339599609375,28.9873603125C19.9339599609375,34.6822223125,25.2387590609375,39.3057743125,31.9750119609375,39.3057743125C33.3864179609375,39.3057743125,34.8098529609375,38.9473303125,36.2252669609375,38.592910312499995L40.1066319609375,40.7274783125L39.0400579609375,37.1752373125C41.8829169609375,35.032613312500004,44.000026960937504,32.1932383125,44.0000229609375,28.9873603125ZM28.2500238609375,27.408587412499998C27.3638858609375,27.408587412499998,26.646153460937498,26.6876659125,26.646153460937498,25.7975922125C26.646153460937498,24.9075189125,27.3638858609375,24.1865988125,28.2500238609375,24.1865988125C29.1361589609375,24.1865988125,29.8538932609375,24.9075189125,29.8538932609375,25.7975922125C29.8538932609375,26.6836395125,29.1361589609375,27.408587412499998,28.2500238609375,27.408587412499998ZM36.040820960937495,27.3965053125C35.1546799609375,27.3965053125,34.4369509609375,26.6755838125,34.4369509609375,25.7855110125C34.4369509609375,24.895437712499998,35.1546799609375,24.1745171125,36.040820960937495,24.1745171125C36.9269579609375,24.1745171125,37.6446879609375,24.895437712499998,37.6446879609375,25.7855110125C37.6446879609375,26.6755838125,36.9269579609375,27.3965053125,36.040820960937495,27.3965053125Z M18.158162,8C10.3753829,8,4,13.3283634,4,20.094535999999998C4,24.001196,6.1211183,27.207075,9.6656709,29.69606L8.2502556,33.973248L13.1981926,31.480234C14.970471,31.830627,16.389895,32.193098,18.158162,32.193098C18.603237999999997,32.193098,19.044301,32.172962,19.481358,32.136714999999995C19.204688,31.186228,19.044301,30.191441,19.044301,29.156374C19.044301,22.941966999999998,24.357121,17.899557100000003,31.081347,17.899557100000003C31.542459,17.899557100000003,31.995552,17.9317741,32.444632999999996,17.984132799999998C31.217676,12.2570515,25.118961,8,18.158162,8ZM13.402688,17.758596400000002C12.3401251,17.758596400000002,11.478045,16.8926868,11.478045,15.8254032C11.478045,14.758121,12.3401251,13.892211,13.402688,13.892211C14.465254,13.892211,15.327333,14.758121,15.327333,15.8254032C15.327333,16.8926868,14.465254,17.758596400000002,13.402688,17.758596400000002ZM23.290546,17.758596400000002C22.227983,17.758596400000002,21.365904,16.8926868,21.365904,15.8254032C21.365904,14.758121,22.227983,13.892211,23.290546,13.892211C24.353113,13.892211,25.215189,14.758121,25.215189,15.8254032C25.215189,16.8926868,24.353113,17.758596400000002,23.290546,17.758596400000002Z',
      // 批量编辑图标路径
      edit: 'M28.4545 4.45202L4.79545 4.45202L4.77273 32.368L2.02273 32.368C0.909091 32.368 0 31.4594 0 30.3464L0 2.02158C0 0.908575 0.909091 0 2.02273 0L26.4318 0C27.5455 0 28.4545 0.908575 28.4545 2.02158L28.4545 4.45202ZM9.36364 6.36002L33.4545 6.36002C34.5682 6.36002 35.4773 7.2686 35.4773 8.3816L35.4773 19.8978C34.0455 19.3072 32.4773 18.9665 30.8409 18.9665C27.9773 18.9665 25.3409 19.9659 23.2955 21.6695L11.5227 21.6695L11.5227 23.6911L21.3864 23.6911C19.8864 25.6672 19 28.1204 19 30.8007C19 33.8671 20.1591 36.6156 22.0455 38.728L9.36364 38.728C8.25 38.728 7.34091 37.8194 7.34091 36.7064L7.34091 8.3816C7.34091 7.2686 8.25 6.36002 9.36364 6.36002ZM11.5227 14.2192L30.5 14.2192L30.5 12.1976L11.5227 12.1976L11.5227 14.2192ZM21.6818 30.8461C21.6818 25.7808 25.7955 21.6922 30.8409 21.6922C35.9091 21.6922 40 25.7808 40 30.8461C40 35.9114 35.9091 40 30.8409 40C25.7727 40 21.6818 35.8887 21.6818 30.8461ZM34.9773 28.1933Q34.9545 28.0836 34.9181 28.0014Q34.7907 27.7272 34.5587 27.4758Q34.3267 27.2244 34.1356 27.1239Q34.0446 27.0782 33.9218 27.0416Q33.799 27.005 33.667 27.0005Q33.5351 26.9959 33.3986 27.037Q33.2621 27.0782 33.1347 27.1787Q33.0346 27.261 32.8845 27.4027Q32.7344 27.5443 32.6252 27.6449L34.3267 29.3542Q34.3904 29.2994 34.4632 29.2263Q34.5269 29.1623 34.6133 29.08Q34.6997 28.9977 34.8089 28.888Q34.909 28.7784 34.9545 28.6595Q35 28.5407 35 28.4219Q35 28.303 34.9773 28.1933ZM28.0849 32.2016Q27.8847 32.4073 27.8301 32.4621Q27.7027 32.5901 27.6345 32.6952Q27.5662 32.8003 27.5116 32.9283Q27.4661 33.0106 27.3934 33.2299Q27.3206 33.4493 27.2432 33.7098Q27.1659 33.9704 27.0976 34.2126Q27.0294 34.4548 27.0112 34.5828Q26.9748 34.8204 27.0567 34.921Q27.1386 35.0215 27.3843 34.9941Q27.5025 34.9758 27.7437 34.9119Q27.9848 34.8479 28.2532 34.761Q28.5216 34.6742 28.7673 34.5828Q29.0129 34.4914 29.1403 34.4274Q29.2677 34.3725 29.386 34.2811Q29.5043 34.1897 29.5953 34.1075Q29.6317 34.08 29.8227 33.8881Q30.0138 33.6961 30.305 33.399Q30.5961 33.102 30.9601 32.7455L31.688 32.0051L33.6443 30.0398L31.9428 28.3213L29.9865 30.2866L29.2495 31.027L28.5853 31.6943Q28.285 31.9959 28.0849 32.2016Z',
      // 批量转发图标路径
      forward: 'M28.4545 4.45202L4.79545 4.45202L4.77273 32.368L2.02273 32.368C0.909091 32.368 0 31.4594 0 30.3464L0 2.02158C0 0.908575 0.909091 0 2.02273 0L26.4318 0C27.5455 0 28.4545 0.908575 28.4545 2.02158L28.4545 4.45202ZM9.36364 6.36002L33.4545 6.36002C34.5682 6.36002 35.4773 7.2686 35.4773 8.3816L35.4773 19.8978C34.0455 19.3072 32.4773 18.9665 30.8409 18.9665C27.9773 18.9665 25.3409 19.9659 23.2955 21.6695L11.5227 21.6695L11.5227 23.6911L21.3864 23.6911C19.8864 25.6672 19 28.1204 19 30.8007C19 33.8671 20.1591 36.6156 22.0455 38.728L9.36364 38.728C8.25 38.728 7.34091 37.8194 7.34091 36.7064L7.34091 8.3816C7.34091 7.2686 8.25 6.36002 9.36364 6.36002ZM11.5227 14.2192L30.5 14.2192L30.5 12.1976L11.5227 12.1976L11.5227 14.2192ZM21.6818 30.8461C21.6818 25.7808 25.7955 21.6922 30.8409 21.6922C35.9091 21.6922 40 25.7808 40 30.8461C40 35.9114 35.9091 40 30.8409 40C25.7727 40 21.6818 35.8887 21.6818 30.8461ZM31.6761 33.4595C31.605 33.3881 31.5714 33.2876 31.5853 33.1878L31.5798 31.2548C29.4017 31.2548 27.4309 32.8153 27.0399 34.8697C26.7686 34.2504 26.6286 33.5816 26.6288 32.9055C26.6288 30.1809 28.848 27.973 31.5853 27.973L31.5853 26.0356C31.5714 25.9359 31.6048 25.8354 31.6756 25.7639C31.8069 25.6347 32.0177 25.6347 32.1491 25.7639L35.4462 29.3614C35.5124 29.4274 35.5469 29.5187 35.5409 29.6119C35.5469 29.7052 35.5124 29.7965 35.4462 29.8625L32.1674 33.44C32.1067 33.5129 32.0187 33.563 31.9157 33.563C31.8262 33.5628 31.7408 33.5263 31.6789 33.4617L31.6761 33.4595Z'
    };

    return (
      <svg
        className="benefit-icon-svg"
        viewBox="0 0 40 40"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d={iconPaths[iconType]} fill="#FFFFFF" />
      </svg>
    );
  };

  return (
    <View className="vip-benefits-page">
      {/* 导航栏 */}
      {platform !== 'WX' && <YkNavBar title="会员权益" />}

      {/* 主内容容器 */}
      <View className="benefits-container">
        {/* 顶部横幅 */}
        <View className="benefits-header">
          <Text className="header-title">会员尊享以下权益</Text>
        </View>

        {/* 权益列表 */}
        <View className="benefits-list">
          {BENEFIT_LIST.map((item, index) => (
            <View key={item.id}>
              {/* 权益项 */}
              <View className="benefit-item">
                {/* 图标区域 */}
                <View className="benefit-icon-wrapper">
                  <View className="benefit-icon-bg">
                    {renderBenefitIcon(item.icon)}
                  </View>
                </View>

                {/* 文字内容 */}
                <View className="benefit-content">
                  <Text className="benefit-title">{item.title}</Text>
                  <Text className="benefit-description">{item.description}</Text>
                </View>
              </View>

              {/* 分割线 - 最后一项不显示 */}
              {index < BENEFIT_LIST.length - 1 && (
                <View className="benefit-divider" />
              )}
            </View>
          ))}
        </View>
      </View>
    </View>
  );
};

export default VipBenefits;

