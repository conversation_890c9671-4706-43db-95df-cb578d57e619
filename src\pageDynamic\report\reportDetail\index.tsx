import { View, Text, Textarea, Image } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import { <PERSON><PERSON>, Dialog, Toast } from "@arco-design/mobile-react";
import Taro from "@tarojs/taro";
import { useState, useRef, useEffect } from "react";
import { IconClose } from "@arco-iconbox/react-yk-arco";

// 组件
import YkNavBar from "@/components/ykNavBar/index";
import BottomPopup from "@/components/BottomPopup";
import PermissionPopup from "@/components/PermissionPopup";

// 接口
import { reportUser, uploadFile } from "@/utils/api/common/common_user";

// 权限相关
import { usePermission } from "@/hooks/usePermission";
import { AuthTypes } from "@/utils/config/authTypes";

export default function ReportDetail() {
  const [reportContent, setReportContent] = useState("");
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [typeId, setTypeId] = useState("");
  const [typeName, setTypeName] = useState("");
  const [reportedType, setReportedType] = useState("");
  const [reportedId, setReportedId] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [popupVisible, setPopupVisible] = useState(false);
  const [userInfo, setUserInfo] = useState({});

  // 选择图片的方法
  const chooseImage = (sourceType: "album" | "camera") => {
    if (platformRef.current === "Android") {
      window.setPhotoNum?.setPhotoNum(4 - selectedImages.length);
    }
    if (platformRef.current === "HM") {
      window.harmony.setPhotoNum(4 - selectedImages.length);
    }
    Taro.chooseImage({
      count: 4 - selectedImages.length,
      sourceType: [sourceType], // 'album' 为从相册选择，'camera' 为拍照
      success: (res) => {
        const tempFilePaths = res.tempFilePaths;
        uploadImages(tempFilePaths);
      },
      fail: (err) => {
        console.error("选择图片失败", err);
        Toast.error({ content: "选择图片失败" });
      },
    });
  };

  // 自定义权限同意处理
  const customWebPermissonConsent = () => {
    console.log("reportDetail customWebPermissonConsent");
    console.log("authType:", authConfirmType.current);

    // 根据权限类型执行不同的后续处理
    if (authConfirmType.current === AuthTypes.CAMERA) {
      // 拍照
      chooseImage("camera");
    } else if (authConfirmType.current === AuthTypes.GALLERY_PHOTO) {
      // 从相册选择
      chooseImage("album");
    }

    return true;
  };

  // 使用全局权限管理
  const {
    initPermissions,
    hasPermission,
    requestPermission,
    webPermissonConsent,
    webPermissonDeny,
    permissionPopupProps,
    platformRef,
    authConfirmType,
  } = usePermission(customWebPermissonConsent);

  useLoad(() => {
    // 获取页面参数
    const router = Taro.getCurrentInstance().router;
    const params = router?.params;
    const userInfo = Taro.getStorageSync("userInfo") || {};
    setUserInfo(userInfo);
    if (params) {
      setTypeId(params.typeId || "");
      setTypeName(decodeURIComponent(params.typeName || ""));
      setReportedId(params.id || "");
      setReportedType(params.type || "");
    }

    console.log("ReportDetail loaded", params);
  });

  // 权限初始化
  useEffect(() => {
    // 初始化权限管理
    const cleanup = initPermissions();

    return () => {
      cleanup && cleanup();
    };
  }, [initPermissions]);

  // 显示选择图片方式弹窗
  const handleChooseImage = () => {
    if (selectedImages.length >= 4) {
      Toast.info({ content: "最多只能选择4张图片" });
      return;
    }
    if (platformRef.current === "WX") {
      chooseImage("album");
      return;
    }
    setPopupVisible(true);
  };

  // 关闭选择图片方式弹窗
  const handleClose = () => {
    setPopupVisible(false);
  };

  const handleConfirm = (index: number) => {
    setPopupVisible(false);
    if (index === 0) {
      if (platformRef.current === "HM") {
        chooseImage("camera");
      } else {
        // 请求相机权限
        // 请求相机权限
        if (!hasPermission(AuthTypes.CAMERA)) {
          // 如果没有权限，请求权限
          requestPermission(AuthTypes.CAMERA);
          return;
        }
        chooseImage("camera");
      }
    } else if (index === 1) {
      if (platformRef.current === "HM") {
        chooseImage("album");
      } else {
        // 请求相册权限
        if (!hasPermission(AuthTypes.GALLERY_PHOTO)) {
          // 如果没有权限，请求权限
          requestPermission(AuthTypes.GALLERY_PHOTO);
          return;
        }
        chooseImage("album");
      }
    }
  };

  // 上传图片
  const uploadImages = async (filePaths: string[]) => {
    try {
      const uploadPromises = filePaths.map(async (filePath) => {
        const uploadRes = await uploadFile(filePath);
        if (uploadRes && uploadRes.code === 0) {
          return uploadRes.data; // 直接返回 data，不是 data.url
        } else {
          throw new Error(uploadRes.message || "上传失败");
        }
      });

      const uploadedUrls = await Promise.all(uploadPromises);
      console.log("上传成功的图片URLs:", uploadedUrls);
      setSelectedImages((prev) => {
        const newImages = [...prev, ...uploadedUrls];
        console.log("更新后的图片列表:", newImages);
        return newImages;
      });

      Toast.success({ content: "图片上传成功" });
    } catch (error) {
      console.error("上传图片失败", error);
      Toast.error({ content: "图片上传失败" });
    }
  };

  // 删除图片
  const handleRemoveImage = (index: number) => {
    setSelectedImages((prev) => prev.filter((_, i) => i !== index));
  };

  // 提交举报
  const handleSubmitReport = async () => {
    // 验证：举报内容和图片至少要填一样
    if (!reportContent.trim() && selectedImages.length === 0) {
      Toast.info({ 
        content: reportedType === "feedback" 
          ? "请填写反馈内容或上传相关图片" 
          : "请填写举报内容或上传相关图片" 
      });
      
      return;
    }

    if (isSubmitting) return;

    setIsSubmitting(true);

    if(reportedType==='feedback'){
      Dialog.alert({
        title: "温馨提示",
        children:
          "您的反馈已提交，我们会尽快处理，感谢您对我们的支持与信任！",
        okText: "知道了",
        onOk: () => {
          // 返回上一页
          Taro.navigateBack({ delta: 2 }); 
        },
        platform: "ios",
      });
    }else{
      try {
        const reportData = {
          // id: "", // 新建举报，id为空
          type: reportedType === "dynamic" ? 2 : 1,
          whistleblowerUserId: userInfo.id, // 需要从用户信息获取
          reportType: parseInt(typeId),
          reportContent: reportContent.trim(),
          reportPhoto: selectedImages.join(","),
        };
  
        reportedType === "dynamic"
          ? (reportData.dynamicId = reportedId)
          : (reportData.reportedPersonUserId = reportedId);
  
        const res = await reportUser(reportData);
  
        if (res && res.code === 0) {
          // 显示温馨提示对话框
          Dialog.alert({
            title: "温馨提示",
            children:
              "您的举报已提交，我们会尽快处理，感谢您对我们的支持与信任！",
            okText: "知道了",
            onOk: () => {
              // 返回上一页
              Taro.navigateBack({ delta: 2 }); // 返回到首页，跳过举报类型页面
            },
            platform: "ios",
          });
        } else {
          Toast.error({ content: res.message || "举报提交失败" });
        }
      } catch (error) {
        console.error("提交举报失败", error);
        Toast.error({ content: "举报提交失败" });
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  return (
    <View className="report-detail-page">
      {platformRef.current !== "WX" && (
        <YkNavBar title={reportedType === "feedback" ? "意见反馈" : "举报"} />
      )}

      <View className="report-content">
        <View className="report-section">
          <Text className="section-title">
            {reportedType === "feedback" ? "反馈类型" : "投诉类型"}：{typeName}
          </Text>
        </View>

        <View className="report-section">
          <Textarea
            className="report-textarea"
            placeholder={reportedType === "feedback" ? "请输入反馈内容" : "请输入投诉理由"}
            value={reportContent}
            onInput={(e) => setReportContent(e.detail.value)}
            maxlength={500}
          />
        </View>

        <View className="report-section">
          <View className="image-upload-container">
            {selectedImages.map((image, index) => (
              <View key={index} className="image-item">
                <Image
                  src={image}
                  className="uploaded-image"
                  mode="aspectFill"
                />
                <View
                  className="remove-btn"
                  onClick={() => handleRemoveImage(index)}
                >
                  <IconClose className="remove-icon" />
                </View>
              </View>
            ))}

            {selectedImages.length < 4 && (
              <View className="add-image-btn" onClick={handleChooseImage}>
                <Text className="add-image-text">+</Text>
              </View>
            )}
          </View>
        </View>
      </View>

      <View className="submit-container">
        <Button
          className="submit-btn"
          type="primary"
          loading={isSubmitting}
          onClick={handleSubmitReport}
        >
          提交
        </Button>
      </View>

      {/* 选择图片方式弹窗 */}
      <BottomPopup
        options={["拍照", "从相册选择"]}
        btnCloseText="取消"
        onConfirm={handleConfirm}
        onClose={handleClose}
        visible={popupVisible}
      />

      {/* 权限弹窗 */}
      <PermissionPopup {...permissionPopupProps} />
    </View>
  );
}
