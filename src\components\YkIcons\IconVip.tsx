import { IconProps } from "./types";
const IconVip: React.FC<IconProps> = ({
  color = 'var(--primary-color)',
  size = 32,
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    fill="none"
    version="1.1"
    width={size}
    height={size}
    viewBox="0 0 32 32"
    className={className}
  >
    {" "}
    <defs>
      <linearGradient
        x1="0"
        y1="0.5"
        x2="1"
        y2="0.5"
        id="master_svg0_408_040318"
      >
        {" "}
        <stop offset="0%" stopColor="#FFE8BD" stopOpacity="1" />
        <stop offset="100%" stopColor="#FE9763" stopOpacity="1" />{" "}
      </linearGradient>
    </defs>{" "}
    <g>
      <g>
        {" "}
        <g>
          <path
            d="M29.3333259765625,23.9999259765625C29.3333259765625,26.9453259765625,26.9453259765625,29.3333259765625,23.9999259765625,29.3333259765625L7.9999559765625,29.3333259765625C5.0546259765625,29.3333259765625,2.6666259765625,26.9453259765625,2.6666259765625,23.9999259765625L2.6666259765625,7.9999559765625C2.6666259765625,5.0546259765625,5.0546259765625,2.6666259765625,7.9999559765625,2.6666259765625L23.9999259765625,2.6666259765625C26.9453259765625,2.6666259765625,29.3333259765625,5.0546259765625,29.3333259765625,7.9999559765625L29.3333259765625,23.9999259765625Z"
            fill="#FFF7E8"
            fillOpacity="1"
          />{" "}
        </g>
        <g>
          {" "}
          <rect
            x="8"
            y="11.3333740234375"
            width="16"
            height="9.333333969116211"
            rx="0"
            fill="#9B0000"
            fillOpacity="1"
          />
        </g>
        <g>
          {" "}
          <path
            d="M22.8196740234375,10.6666259765625L9.1889640234375,10.6666259765625C7.9491020234375,10.6666259765625,7.3333740234375,11.3297269765625,7.3333740234375,12.6653959765625L7.3333740234375,19.3345159765625C7.3333740234375,20.6702259765625,7.9491020234375,21.3333259765625,9.1889640234375,21.3333259765625L22.8110740234375,21.3333259765625C24.0508740234375,21.3333259765625,24.6665740234375,20.6702259765625,24.6665740234375,19.3345159765625L24.6665740234375,12.6651659765625C24.6752740234375,11.3294959765625,24.0594740234375,10.6666259765625,22.8196740234375,10.6666259765625ZM12.0459140234375,18.5582459765625C11.8638640234375,18.5582459765625,11.699054023437501,18.3713959765625,11.560314023437499,17.9884559765625L9.9561440234375,13.5144259765625L11.1960040234375,13.5144259765625L12.3145640234375,16.5874159765625L14.1094040234375,13.5144259765625L15.3492540234375,13.5144259765625L12.661424023437501,17.9979259765625C12.4274540234375,18.3716259765625,12.2193440234375,18.5582459765625,12.0459140234375,18.5582459765625ZM14.9939940234375,18.4836459765625L15.6444140234375,13.5144259765625L16.7976640234375,13.5144259765625L16.1386340234375,18.4836459765625L14.9939940234375,18.4836459765625ZM22.0780740234375,15.373235976562501C22.0087740234375,15.9056059765625,21.748674023437502,16.3820959765625,21.358274023437502,16.708905976562498C20.9767740234375,17.0357259765625,20.499974023437503,17.2133359765625,20.014374023437497,17.2133359765625L18.080874023437502,17.2133359765625L17.9159740234375,18.4649359765625L16.7713840234375,18.4649359765625L17.109624023437497,15.9337859765625L20.300474023437502,15.9337859765625C20.4478740234375,15.9337859765625,20.5952740234375,15.8776659765625,20.7167740234375,15.7751159765625C20.829374023437502,15.6818059765625,20.907374023437498,15.5416059765625,20.933474023437498,15.3827059765625C20.9681740234375,15.2332659765625,20.9248740234375,15.0743659765625,20.829374023437502,14.9623459765625C20.7253740234375,14.8595659765625,20.5952740234375,14.8034459765625,20.4478740234375,14.8129159765625L17.2744340234375,14.8129159765625L18.1934740234375,13.5146559765625L20.5172740234375,13.5146559765625C20.9767740234375,13.4867159765625,21.4276740234375,13.6640959765625,21.7570740234375,14.0096159765625C22.0520740234375,14.3830859765625,22.1735740234375,14.8875159765625,22.0780740234375,15.373235976562501Z"
            fill="url(#master_svg0_408_040318)"
            fillOpacity="1"
          />{" "}
        </g>
      </g>{" "}
    </g>
  </svg>
);

export default IconVip;