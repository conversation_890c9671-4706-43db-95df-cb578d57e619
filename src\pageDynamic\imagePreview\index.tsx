import { View, Text, Swiper, SwiperI<PERSON>, Movable<PERSON><PERSON>, MovableView, <PERSON>rollView } from "@tarojs/components";
import { useState, useEffect, useRef, useCallback } from "react";
import { Toast, Image, Dialog } from "@arco-design/mobile-react";
import Taro from "@tarojs/taro";
import "./index.less";
import wx from "weixin-webview-jssdk";
import { toast } from "@/utils/yk-common";
import { addCart } from "@/utils/api/common/common_user";
import { fetchCartCountUtil } from "@/utils/cartUtils";
import CartModal from "@/components/CartModal";
import { IconDownload, IconLeft } from "@arco-iconbox/react-yk-arco";
import { IconArrowBack } from "@arco-design/mobile-react/esm/icon";

import { useSetPermission } from "@/stores/permissionStore";
import {
  AuthTypes,
  AuthStatusAndroid,
  AuthStatusIos,
} from "@/utils/config/authTypes";
import PermissionPopup from "@/components/PermissionPopup";
import { usePermission } from "@/hooks/usePermission";
import { useGlobalCallbacks } from "@/utils/globalCallbackManager";

export default function ImagePreview() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [images, setImages] = useState<string[]>([]);
  const [dynamic, setDynamic] = useState<any>({});
  const [userInfo, setUserInfo] = useState<any>({});
  const [showCar, setShowCar] = useState(true);
  const [showBottom, setShowBottom] = useState(true);
  
  // 购物车相关状态
  const [actionSheetVisible, setActionSheetVisible] = useState(false);

 // 自定义权限同意处理，处理首页特有的逻辑
 const customWebPermissonConsent = () => {
  // 根据操作类型执行不同的后续处理
      handleDownloadConfirm();
  return true;
};

// 使用全局权限管理
const {
  initPermissions,
  hasPermission,
  requestPermission,
  webPermissonConsent,
  webPermissonDeny,
  permissionPopupProps,
  platformRef,
  authConfirmType,
} = usePermission(customWebPermissonConsent);
  useEffect(() => {
    // 获取页面参数
    const params = Taro.getCurrentInstance().router?.params;
    if (params) {
      const { images: imagesStr, initialIndex, dynamic: dynamicStr, showCar: showCarStr, showBottom: showBottomStr } = params;

      if (imagesStr) {
        setImages(JSON.parse(decodeURIComponent(imagesStr)));
      }

      if (initialIndex) {
        setCurrentIndex(parseInt(initialIndex));
      }

      if (dynamicStr) {
        setDynamic(JSON.parse(decodeURIComponent(dynamicStr)));
      }

      if (showCarStr) {
        setShowCar(showCarStr === 'true');
      }

      if (showBottomStr) {
        setShowBottom(showBottomStr === 'true');
      }
    }
        // 初始化权限管理
        const cleanup = initPermissions();

    // 获取用户信息
    const userInfoData = Taro.getStorageSync('userInfo') || {};
    setUserInfo(userInfoData);

    return () => {
      cleanup && cleanup();
    };
  }, []);

  // 下载成功回调 - 使用 useCallback 确保能访问最新状态
  const webDownloadSuc = useCallback(() => {
    console.log('webDownloadSuc 被调用');
    console.log('dynamic.content:', dynamic.content);
    console.log('images:', images);
    console.log('条件判断:', images.length > 0 && dynamic.content !== "");

    if (images.length > 0 && dynamic.content !== "") {
      toast("success", {
        content: "图片下载成功，动态文案已复制",
        duration: 2000,
      });
    } else if (images.length > 0 && dynamic.content === "") {
      toast("success", {
        content: "图片下载成功",
        duration: 2000,
      });
    } else {
      toast("success", {
        content: "下载成功",
        duration: 2000,
      });
    }
  }, [dynamic.content, images]);

  // 下载失败回调
  const webDownloadFail = useCallback(() => {
    toast("error", {
      content: "下载失败",
      duration: 2000,
    });
  }, []);

  // 使用全局回调管理器注册下载回调
  const callbackCleanup = useGlobalCallbacks('imagePreview', {
    webDownloadSuc: webDownloadSuc,
    webDownloadFail: webDownloadFail,
  });





  // 处理 Swiper 切换
  const handleSwiperChange = (e: any) => {
    const newIndex = e.detail.current;
    if (newIndex >= 0 && newIndex < images.length && newIndex !== currentIndex) {
      setCurrentIndex(newIndex);
    }
  };

  // 处理关闭
  const handleClose = () => {
    Taro.navigateBack();
  };


  const handleDownload = () => {
    if (images.length > 0) {
      if (platformRef.current === "HM"||platformRef.current === "WX") {
        handleDownloadConfirm();
      }else{
      // 检查存储权限
      if (!hasPermission(AuthTypes.STORAGE)) {
        console.log("没有权限");
        // 如果没有权限，请求权限
        requestPermission(AuthTypes.STORAGE);
        return;
      }
      // 有权限则执行下载
      handleDownloadConfirm();
    }
    } else {
      Taro.setClipboardData({
        data: dynamic.content,
        success: () => {
          Taro.hideToast();
          toast("info", {
            content: "文字已复制",
            duration: 2000,
          });
        },
        fail: () => {
          Taro.hideToast();
        },
      });
    }
  };

  // 处理下载
  const handleDownloadConfirm = () => {
    if (platformRef.current === "Android") {
      (window as any).downloadImg?.downloadImg(images.join(','));
    }else if(platformRef.current === "HM"){
      window.harmony.downloadImg(images.join(','));
    }else if(platformRef.current === "WX"){
      wx.miniProgram.navigateTo({ url: "/pages/downloadImgs/index?imgs=" + encodeURIComponent(images.join('|')) });
    }
    else{
    for (let i = 0; i < images.length; i++) {
      // if (platformRef.current === "Android") {
      //   (window as any).downloadImg?.downloadImg(images[i]);
      // } else 
      if (platformRef.current === "IOS") {
        (window as any).webkit?.messageHandlers?.saveImgWithUrlStr?.postMessage(images[i]);
      } 
      // else if (platformRef.current === "HM") {
      //   (window as any).harmony?.downloadImg(images[i]);
      // }
    }
  }
    if (dynamic.content) {
      Taro.setClipboardData({
        data: dynamic.content,
        success: () => {
          Taro.hideToast();
        },
        fail: () => {
          Taro.hideToast();
        },
      });
    }
  };

  // 处理分享
  const handleShare = () => {
    if (dynamic.userId !== userInfo.userId) {
      Taro.setStorageSync('releaseDynamicList', dynamic);
      Taro.navigateTo({
        url: '/pageDynamic/releaseDynamic/index?type=4'
      });
    }
  };

  // 加入购物车
  const handleAddCar = () => {
    setActionSheetVisible(true);
  };

  return (
    <View className="image-preview-page">
      {/* 顶部导航 */}
      {platformRef.current !== "WX" && (
      <View className="nav-top">
        <View
          className="nav-content"
          style={
            platformRef.current == "H5"
              ? { padding: '16px' }
              : { paddingTop: "2rem", paddingLeft: "16px", paddingRight: "16px", paddingBottom: "16px" }
          }
        >
          <View
            className="back-button"
            onClick={(e) => {
              e.stopPropagation();
              console.log('返回按钮被点击');
              handleClose();
            }}
          >
            {/* <Image
              className="back-icon"
              src={require('@/assets/images/common/left_return_white.png')}
              bottomOverlap={null}
            /> */}
            <IconArrowBack className="back-icon" />

          </View>
          <Text className="page-indicator">
            {(currentIndex || 0) + 1} / {images.length}
          </Text>
        </View>
      </View>
      )}

      {/* 图片预览区域 */}
      <View className="carousel">
        <Swiper
          style={{ height: '100vh', background: '#000' }}
          onChange={handleSwiperChange}
          current={currentIndex}
          circular={false}
          autoplay={false}
        >
          {images.map((img, i) => (
            <SwiperItem key={i}>
              <MovableArea style={{ width: '100%', height: '100vh', background: '#000' }} scaleArea>
                <MovableView
                  style={{ 
                    width: '100%', 
                    height: '100vh', 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'center', 
                    background: '#000' 
                  }}
                  direction="all"
                  scale
                  scaleMin={1}
                  scaleMax={4}
                >
                  <Image
                    key={`image-${i}`}
                    className="image"
                    src={img}
                    fit="contain"
                    width="100%"
                    height="100%"
                    style={{ background: '#000' }}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleClose();
                    }}
                  />
                </MovableView>
              </MovableArea>
            </SwiperItem>
          ))}
        </Swiper>
      </View>

      {/* 动态文字显示 */}
      {dynamic && dynamic.content && (
        <View className="title">
          <Text>{dynamic.content}</Text>
        </View>
      )}

      {/* 底部控制栏 */}
      {showBottom && (
        <View
          className="footer-img"
          onClick={(e) => e.stopPropagation()}
        >
          <View className="controls">
            {
              images.length>0&&(
            <View className="download" onClick={handleDownload}>
              <Image
                className="icon"
                bottomOverlap={null}
                src={require('@/assets/images/common/trend_download_white.png')}
              />
              {/* <IconDownload className="icon" /> */}

              <Text>下载</Text>
            </View>
            )}

            <View className="right-controls">
              {dynamic.price && Number(dynamic.price) > 0 && showCar ? (
                <>
                {userInfo.userId !== dynamic.userId && (
                  <Text className="share-btn" onClick={handleShare}>
                    {userInfo.userId === dynamic.userId ? '一键分享' : '一键转发'}
                  </Text>
                )}
                  <Text className="cart-btn" onClick={handleAddCar}>
                    加入购物车
                  </Text>
                </>
              ) : (
                <>
                {userInfo.userId !== dynamic.userId && (
                <Text className="share-btn" onClick={handleShare}>
                  {userInfo.userId === dynamic.userId ? '一键分享' : '一键转发'}
                </Text>
                )}
                </>
              )}
            </View>
          </View>
        </View>
      )}

      {/* 购物车弹窗 */}
      <CartModal
        visible={actionSheetVisible}
        onClose={() => setActionSheetVisible(false)}
        productData={{
          id: dynamic.id,
          pictures: images.join(','),
          content: dynamic.content,
          dynamic_title: dynamic.dynamic_title,
          price: dynamic.price,
          colors: dynamic.colors,
          specifications: dynamic.specifications,
          userId: dynamic.userId, // 商家ID
        }}
        merchantInfo={{
          nickname: dynamic.nickname,
          avatar: dynamic.avatar,
        }}
        onAddCart={async (cartData) => {
          try {
            const res: any = await addCart(cartData);
            if (res && res.code === 0) {
              Toast.success({ content: '加入购物车成功' });
              // 重新获取购物车数量，确保数据准确
              await fetchCartCountUtil();
            } else {
              Toast.error({ content: res.msg || '加入购物车失败' });
            }
          } catch (error) {
            console.error('加入购物车失败:', error);
            Toast.error({ content: '加入购物车失败' });
          }
        }}
        onBuyNow={(buyNowData) => {
          // 构建选中商品数据并存储
          Taro.setStorageSync('selectedItems', buyNowData);
          // 跳转到确认订单页面
          Taro.navigateTo({
            url: '/pageOrder/order/pay/index?from=detail'
          });
        }}
      />
    </View>
  );
}