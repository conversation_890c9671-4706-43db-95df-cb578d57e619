import { View, Text } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import { Cell } from "@arco-design/mobile-react";
import Taro from "@tarojs/taro";
import { IconRight } from "@arco-iconbox/react-yk-arco";
import React, { useEffect, useRef, useState } from "react";
// 组件
import YkNavBar from "@/components/ykNavBar/index";

export default function ReportType() {
  const [reportedId, setReportedId] = React.useState("");
  const [reportedType, setReportedType] = React.useState("");
  const [platform, setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);

  const reportList = [
    { id: 1, title: "欺诈", description: "" },
    { id: 2, title: "骚扰", description: "" },
    { id: 3, title: "侵权（冒充他人、侵犯名誉等）", description: "" },
    { id: 4, title: "违法犯罪", description: "" },
    { id: 5, title: "诱导行为", description: "" },
    { id: 6, title: "不实行为", description: "" },
    { id: 7, title: "色情", description: "" },
    { id: 8, title: "其他", description: "" },
  ];

  const feedbackList = [
    { id: 1, title: "页面闪退", description: "" },
    { id: 2, title: "无法打开", description: "" },
    { id: 3, title: "卡顿", description: "" },
    { id: 4, title: "黑屏白屏", description: "" },
    { id: 5, title: "死机", description: "" },
    { id: 6, title: "加载缓慢", description: "" },
    { id: 7, title: "页面显示错误", description: "" },
    { id: 8, title: "操作体验", description: "" },
    { id: 9, title: "注册登录问题", description: "" },
    { id: 10, title: "其他问题", description: "" },
  ];

  const reportTypes = reportedType === "feedback" ? feedbackList : reportList;

  useLoad(() => {
    // 获取页面参数
    const router = Taro.getCurrentInstance().router;
    const params = router?.params;

    if (params?.type) {
      setReportedType(params.type);
    }
    if (params?.id) {
      setReportedId(params.id);
    }

    console.log("ReportType loaded", params);
  });

  // 处理举报类型选择
  const handleTypeSelect = (type: any) => {
    // 跳转到举报详情页面，传递举报类型和用户ID
    if (reportedType === "feedback") {
      Taro.navigateTo({
        url: `/pageDynamic/report/reportDetail/index?typeName=${encodeURIComponent(
          type.title
        )}&type=${reportedType}`,
      });
    } else {
      Taro.navigateTo({
        url: `/pageDynamic/report/reportDetail/index?typeId=${
          type.id
        }&typeName=${encodeURIComponent(
          type.title
        )}&type=${reportedType}&id=${reportedId}`,
      });
    }
  };

  return (
    <View className="report-type-page">
      {platform !== "WX" && (
        <YkNavBar title={reportedType === "feedback" ? "意见反馈" : "举报"} />
      )}

      <View className="report-type-header">
        <Text className="report-type-header-text">
          {reportedType === "feedback"
            ? "请选择反馈的问题类型"
            : reportedType === "dynamic"
            ? "请详细填写该动态的违规内容"
            : "请详细填写该用户的违规内容"}
        </Text>
      </View>

      <View className="report-type-list">
        {reportTypes.map((type) => (
          <Cell
            border={false}
            key={type.id}
            className="report-type-item"
            label={type.title}
            showArrow
            onClick={() => handleTypeSelect(type)}
          />
        ))}
      </View>
    </View>
  );
}
