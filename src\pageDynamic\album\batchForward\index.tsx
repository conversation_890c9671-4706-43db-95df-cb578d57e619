import React, { useState, useEffect, useRef } from 'react';
import { View, Text } from "@tarojs/components";
import {
  Dialog,
  Checkbox,
  Image,
  Button,
  SearchBar,
} from '@arco-design/mobile-react';
import { getMyAlbumList as getUserAlbumList, batchForwardDynamics } from '@/utils/api/common/common_user';
import './index.less';
import YkNavBar from '@/components/ykNavBar';
import { IconLoadEmpty } from "@/components/YkIcons";
import SelectQuantityManager from '@/components/SelectQuantityPopup/SelectQuantityManager';
import Taro from '@tarojs/taro';
import { toast } from "@/utils/yk-common";
import { isMemberValid } from '@/pages/my/utils'
import wx from "weixin-webview-jssdk";

interface DynamicItem {
  id: string;
  pictures?: string;
  content?: string;
  price?: number;
  time?: string;
  createTime?: string;
  updateTime?: string;
}

interface PageInfo {
  page: number;
  limit: number;
  isLoad: boolean;
  pageTurn: boolean;
}

const BatchForward: React.FC = () => {
  const [showPage, setShowPage] = useState(false);
  const [pages, setPages] = useState<PageInfo>({
    page: 1,
    limit: 20,
    isLoad: false,
    pageTurn: true
  });
  const [isLoading, setIsLoading] = useState(false);
  const [forwardLoading, setForwardLoading] = useState(false);
  const [dynamicList, setDynamicList] = useState<DynamicItem[]>([]);
  const [selectedIdList, setSelectedIdList] = useState<string[]>([]);
  const [allSelected, setAllSelected] = useState(false);
  const [paddingBottom, setPaddingBottom] = useState('0rpx');
  const [popupVisible, setPopupVisible] = useState(false);
  const [pageTitle, setPageTitle] = useState('批量转发');
  const searchRef = useRef('');
  const [search, setSearch] = useState('');
  
  const lookImageListRef = useRef<any>(null);


  const loginUserInfo = Taro.getStorageSync('userInfo');
  const targetUserId = useRef<number>(0);
  const [platform,setPlatform] = useState<string>("H5");

  const openVip = ()=>{
    Dialog.confirm({
      title: "提示",
      children: "请先开通会员",
      okText: "去开通",
      cancelText: "取消",
      platform: 'ios',
      onOk: () => {
        if (platform === "WX") {
          wx.miniProgram.navigateTo({ url: "/pages/web?path=/pageVip/vipCenter/index" });
        } else {
          Taro.navigateTo({ url: "/pageVip/vipCenter/index" });
        }
      }
    })
  }

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  useEffect(() => {
    // 获取URL参数中的userId
    const params = Taro.getCurrentInstance().router?.params;
    if (params?.userId) {
      targetUserId.current = parseInt(params.userId);
      // 如果是转发别人的动态，更新页面标题
      setPageTitle('批量转发');
    }

    // 检测平台类型设置底部边距
    const uaAll = navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    
    if (isIos) {
      setPaddingBottom('68rpx');
    } else if (isAndroid) {
      setPaddingBottom('0rpx');
    }

    getDynamicList();
  }, []);


  const getDynamicList = () => {
    // 使用传入的userId，如果没有则使用当前登录用户的userId
    const userId = targetUserId.current;
    console.log('userId', userId);
    // 如果传入了searchContent参数，使用传入的值；否则使用当前的search状态
    // const contentValue = searchContent !== undefined ? searchContent : search;
    
    let data = {
      pageNo: pages.page,
      pageSize: pages.limit,
      userId: userId,
      isListed: 1, // 只获取已上架的动态
      content: searchRef.current || '',
      homePageCountType:1,
    };
    
    setIsLoading(true);
    getUserAlbumList(data).then((res: any) => {
      setIsLoading(false);
      if (res && res.code === 0) {
        setPages(prev => ({ ...prev, isLoad: true }));
        let newList;
        
        if (pages.page === 1) {
          newList = res.data.list;
        } else {
          if (res.data.list.length > 0) {
            newList = [...dynamicList, ...res.data.list];
          } else {
            setPages(prev => ({ ...prev, page: prev.page - 1 }));
            newList = dynamicList;
          }
        }
        
        if (res.data.list.length < pages.limit) {
          setPages(prev => ({ ...prev, pageTurn: false }));
        } else {
          setPages(prev => ({ ...prev, pageTurn: true, page: prev.page + 1 }));
        }
        
        setDynamicList(newList);
        checkAllSelected(newList);
        
        setTimeout(() => {
          setShowPage(true);
        }, 500);
      } else {
        toast("info", {
          content: res.message,
          duration: 2000
        });
      }
    });
  };


  const openLookImageList = (item: DynamicItem, index: number) => {
    if (lookImageListRef.current) {
      lookImageListRef.current.open(item, index, false, true);
    }
  };


  const getRightsamllImage = (item: DynamicItem) => {
    let list: string[] = [];
    if (item.pictures) {
      list = item.pictures.split(',').filter(img => img.trim() !== '');
    }
    return list;
  };

  const handleItemSelect = (item: DynamicItem) => {
    const currentIdList = [...selectedIdList];
    const index = currentIdList.indexOf(item.id);
    
    if (index === -1) {
      currentIdList.push(item.id);
    } else {
      currentIdList.splice(index, 1);
    }
    
    setSelectedIdList(currentIdList);
    setAllSelected(currentIdList.length === dynamicList.length);
  };

  const selectNum = (num: number) => {
    let newIds: string[] = [];
    const selectCount = Math.min(num, dynamicList.length);

    for (let i = 0; i < selectCount; i++) {
      newIds.push(dynamicList[i].id);
    }

    setSelectedIdList(newIds);
    setAllSelected(newIds.length === dynamicList.length);
  };

  const handleSelectAll = () => {
    if (selectedIdList.length < dynamicList.length) {
      const newList: string[] = [];
      dynamicList.forEach(item => {
        newList.push(item.id);
      });
      setSelectedIdList(newList);
      setAllSelected(true);
    } else {
      setSelectedIdList([]);
      setAllSelected(false);
    }
  };

  const checkAllSelected = (list: DynamicItem[]) => {
    if (selectedIdList.length === 0) {
      setAllSelected(false);
      return;
    }
    
    const allSelected = list.every(item => 
      selectedIdList.includes(item.id)
    );
    
    setAllSelected(allSelected);
  };


  // 搜索处理
  const handleSearch = () => {
    const newPages = {
      page: 1,
      limit: 20,
      isLoad: false,
      pageTurn: true
    };
    setPages(newPages);
    setSelectedIdList([]);
    setDynamicList([]);
    getDynamicList();
  };

  // 清除搜索
  const handleClearSearch = () => {
    setSearch('');
    searchRef.current = '';
    const newPages = {
      page: 1,
      limit: 20,
      isLoad: false,
      pageTurn: true
    };
    setPages(newPages);
    setSelectedIdList([]);
    setDynamicList([]);
    getDynamicList();
  };

  const handleBatchForward = async () => {
    if (forwardLoading || selectedIdList.length === 0) return;
    
    setForwardLoading(true);
    
    try {
      const data = {
        userId: loginUserInfo.id,
        dynamicsIds: selectedIdList.map(id => parseInt(id))
      };
      
      const res: any = await batchForwardDynamics(data);
      
      if (res && res.code === 0) {
        // toast("info", {
        //   content: '添加至相册成功',
        //   duration: 2000
        // });
        
        // 跳转到结果页面，传递转发的条数
        Taro.navigateTo({
          url: `/pageDynamic/album/batchForward/result?count=${selectedIdList.length}`
        });
      } else {
        toast("info", {
          content: res.message,
          duration: 2000
        });
      }
    } catch (err) {
      console.error(err);
      toast("info", {
        content: '操作失败，请重试',
        duration: 2000
      });
    } finally {
      setForwardLoading(false);
    }
  };



  const renderDynamicItem = (item: DynamicItem) => {
    const images = getRightsamllImage(item);

    return (
      <View key={item.id} className="dline">
        <View className="dline-left">
          <Checkbox
            checked={selectedIdList.includes(item.id)}
            className="dline-left-change"
            value={''}
            onChange={() => handleItemSelect(item)}
          />
        </View>
        <View className="dline-right">
          {item.content && (
            <View className={`dline-right-top ${!item.pictures || item.pictures === '' ? 'topBg' : ''}`}>
              <View className="dynamic-title">{item.content}</View>
            </View>
          )}
          {item.pictures && (
            <View className="dline-right-bottom">
              {images.slice(0, 5).map((imgUrl, imgIndex) => (
                <View
                  key={imgIndex}
                  className="imageItem"
                  onClick={() => openLookImageList(item, imgIndex)}
                >
                  <Image
                    src={imgUrl}
                    fit="cover"
                    className="imageItem-image"
                  />
                  {imgIndex === 4 && images.length > 5 && (
                    <View className="imageItem-mask">
                      <Text>+{images.length - 5}</Text>
                    </View>
                  )}
                </View>
              ))}
            </View>
          )}
        </View>
      </View>
    );
  };

  const renderEmptyContent = () => (
    <View className="not_content">
      {/* <Image
        className="not_content-image"
        src={require("@/assets/images/common/not_content_trend.png")}
      /> */}
      <IconLoadEmpty className="not_content-image" />

      <Text>暂无动态内容</Text>
    </View>
  );

  return (
    <View>
      {platform !== "WX" &&<YkNavBar
        className={'navTop'}
        title={pageTitle}
      />}
      
      {showPage && (
        <>
          {/* Tab栏 */}
          {/* <Tabs
            tabs={tabList.map((item) => ({ title: item.title }))}
            type="line-divide"
            defaultActiveTab={0}
            tabBarHasDivider={false}
            onChange={(_, index) => handleTabChange(index)}
          /> */}

          {/* 搜索栏 */}
          <View className="user-header-searchbar-wrap">
            <SearchBar
              actionButton={
                <Text className="user-header-filter" onClick={handleSearch}>
                  搜索
                </Text>
              }
              placeholder="请输入要搜索的内容"
              onChange={(_, value) => {setSearch(value); searchRef.current = value;}}
              value={search}
              className="user-header-searchbar"
              clearable
              onClear={handleClearSearch}
            />
          </View>

          {/* 内容区域 */}
          <View className="boxContent">
            {dynamicList.length === 0 && !pages.pageTurn ? (
              renderEmptyContent()
            ) : (
              <>
                {dynamicList.map((item) => renderDynamicItem(item))}

                {dynamicList.length > 0 && isLoading && pages.pageTurn && (
                  <View className="notmorelist">
                    <Text>正在加载...</Text>
                  </View>
                )}
              </>
            )}
          </View>
          
          <View className="footer_content_z"></View>
          
          <View className="footerBtnBox" style={{ paddingBottom }}>
            <View className="footerBtnBox-change">
              <Checkbox
                checked={allSelected}
                onChange={handleSelectAll}
                className="footerBtnBox-change-image"
                value={''}
              />
              <View className="footerBtnBox-change-c" onClick={() => setPopupVisible(true)}>
                <Text className="footerBtnBox-change-c-text">
                  选中{selectedIdList.length}条
                </Text>
                <Image
                bottomOverlap={null}
                  src={require("@/assets/images/common/check_all_icon.png")}
                  className="footerBtnBox-change-c-img"
                />
              </View>
            </View>
            
            <Button
              type="primary"
              className={selectedIdList.length > 0 ? "footerBtnBox-btn" : "footerBtnBox-notbtn"}
              disabled={selectedIdList.length === 0 || forwardLoading}
              onClick={() => {
                if(!isMemberValid(Taro.getStorageSync("userInfo"))){
                  openVip();
                  return;
                }
                Dialog.confirm({
                  platform: 'ios',
                  title: "温馨提示",
                  children: `确定要转发选中的 ${selectedIdList.length} 件商品？`,
                  okText: "确定",
                  cancelText: "取消",
                  onOk: handleBatchForward
                });
              }}
            >
              {forwardLoading ? '添加中...' : '添加至相册'}
            </Button>
          </View>
        </>
      )}
      
      <SelectQuantityManager
        visible={popupVisible}
        onClose={() => setPopupVisible(false)}
        onSelectAll={handleSelectAll}
        onSelectCustom={selectNum}
        totalCount={dynamicList.length}
      />
    </View>
  );
};

export default BatchForward;
