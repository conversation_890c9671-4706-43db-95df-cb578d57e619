@import '@arco-design/mobile-react/style/mixin.less';

.clickOpacity:active {
  opacity: 0.8;
}

.rightBox {
  width: 320px;
  height: 100%;
  background-color: #ffffff;
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });
  overflow-y: scroll;
  
  .rightContentBox {
    padding-top: 84px;
    
    &-timeTitle {
      padding: 15px;
      background-color: #f8f9fa;
      .use-dark-mode-query({
        background-color: @dark-cell-background-color;
      });
      
      &-text {
        font-size: 13px;
        color: #999999;
        .use-dark-mode-query({
          color: @dark-font-color;
        });
      }
    }

    &-timeStart {
      border-bottom: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-bottom: 1px solid @dark-line-color;
    });
      padding: 10px 0;
      margin: 0 15px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      &-title {
        font-size: 13px;
        color: #999999;
        .use-dark-mode-query({
          color: @dark-font-color;
        });
      }

      &-right {
        display: flex;
        align-items: center;
        
        &-text {
          margin-right: 30px;
          font-size: 13px;
          color: #000000;
          .use-dark-mode-query({
            color: @dark-font-color;
          });
        }
        
        &-img {
          display: block;
          width: 5px;
          height: 9px;
        }
      }
    }

    &-timeEnd {
      padding: 10px 15px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      &-title {
        font-size: 13px;
        color: #999999;
        .use-dark-mode-query({
          color: @dark-font-color;
        });
      }

      &-right {
        display: flex;
        align-items: center;
        
        &-text {
          margin-right: 30px;
          font-size: 13px;
          color: #000000;
          .use-dark-mode-query({
            color: @dark-font-color;
          });
        }
        
        &-img {
          display: block;
          width: 5px;
          height: 9px;
        }
      }
    }

    &-sort {
      display: flex;
      justify-content: space-between;
      padding: 15px;
      background-color: #f8f9fa;
      
      &-text {
        font-size: 13px;
        color: #999999;
        .use-dark-mode-query({
          color: @dark-font-color;
        });
      }

      &-right {
        &-text {
          font-size: 13px;
          color: #576b95;
          .use-dark-mode-query({
            color: @dark-font-color;
          });
        }
      }
    }

    &-list {
      background-color: #ffffff;
      .use-dark-mode-query({
        background-color: @dark-background-color;
      });
      margin: 15px 10px;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      
      &-item {
        display: flex;
        margin-right: 5px;
        margin-bottom: 10px;
        padding: 4px 12px;
        border-radius: 50px;
        border: 1px solid rgba(153, 153, 153, 0.4);
        
        &-text {
          text-align: center;
          font-size: 11px;
          color: #333333;
        }
        
        .textactive {
          text-align: center;
          font-size: 11px;
          color: #6cbe70;
        }
      }
      
      .active {
        border-radius: 50px;
        border: 1px solid rgba(108, 190, 112, 0.4);
      }
    }

    &-foot {
      background-color: #ffffff;
      .use-dark-mode-query({
        background-color: @dark-cell-background-color;
      });
      position: fixed;
      bottom: 0;
      right: 0;
      width: 320px;
      height: 50px;
      padding: 0 15px;
      z-index: 3090;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 12px;

      &-reset {
        flex: 1;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 7px;
        background-color: #f8f9fa;
        .use-dark-mode-query({
          background-color: @dark-card-background-color;
        });
        &:active {
          opacity: 0.8;
        }

        &-text {
          font-size: 13px;
          text-align: center;
          color: #333333;
          .use-dark-mode-query({
            color: @dark-font-color;
          });
        }
      }

      &-comfirm {
        flex: 1;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 7px;
        background-color: var(--primary-color);

        &-text {
          text-align: center;
          color: #ffffff;
          font-size: 13px;
        }

        &:active {
          opacity: 0.8;
        }
      }
    }
  }
}

.foot_holder {
  height: 60px;
}
