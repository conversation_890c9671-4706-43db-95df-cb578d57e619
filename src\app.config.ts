export default defineAppConfig({
  pages: [
    'pages/login/index',
    'pages/index/index',
    "pages/friends/index",
    "pages/control/index",
    "pages/message/index",
    "pages/my/index",
  ],

  subPackages: [
    {
      root: 'pageLogin',
      pages:[
        "phoneLogin/index",
        "codeLogin/index",
        "codeLogin/code",
        "resetPassword/index",
      ]
    },
    {
      root: 'pageOrder',
      pages:[
        "orderSetting/index",
        "orderSetting/deliveryManager/index",
        "delivery/index",
        "deliveryCompany/index",
        "orderSetting/emsTemplate/index",
        "orderSetting/emsTemplate/editEmsTemplate",
        "orderSetting/emsTemplate/emsArea",
        "orderSetting/emsTemplate/emsRule",
        "refundList/index",
        "refundDetail/index",
        "skuEms/index",
        "refund/index",
        "emsDetail/index",
        "order/index",
        "order/details/index",
        "sellOrder/index",
        "sellOrder/details/index",
        "cart/index",
        "order/pay/index",
        "order/pay/address",
      ]
    },
    {
      root: 'pageDynamic',
      pages:[
        "releaseDynamic/selectTag/index",
        "releaseDynamic/selectProductColor/index",
        "detail/index",
        "moveMaterials/selectMaterials/index",
        "moveMaterials/selectMaterials/edit",
        "moveMaterials/selectMaterials/result",
        "moveMaterials/collectLink/index",
        "releaseDynamic/index",
        "tagAndCategoryManage/index",
        "tagAndCategoryManage/tag/index",
        "tagAndCategoryManage/tag/setSort",
        "tagAndCategoryManage/tag/addContent",
        "tagAndCategoryManage/catalog/index",
        "tagAndCategoryManage/catalog/addTag",
        "tagAndCategoryManage/form/index",
        "album/index",
        "album/batchDelete/index",
        "album/batchEdit/index",
        "album/editContent/index",
        "album/editPrice/index",
        "album/upAndDownShelves/index",
        "album/batchForward/index",
        "album/batchForward/result",
        "myCollect/index",
        "categoryDynamic/tagResult/index",
    "imagePreview/index",
    "report/reportType/index",
    "report/reportDetail/index",
      ]
    },
    {
      root: 'pageUserInfo',
      pages:[
        'editUserInfoItem/index',   
        'editUserInfo/index',  
    "qrcode/index",

    "followUser/index",
    "followUser/result",
    "userDetail/index",
    "fans/index",
      ]
    },
    {
      root: 'pageOnlinePayment',
      pages:[
        "onlinePayment/index",
        "onlinePayment/merchantApplication/index",
        "onlinePayment/information/index",
        "onlinePayment/information/editContact",
        "onlinePayment/information/editSettlement",
        "onlinePayment/verifyAccount",
        "onlinePayment/signAgreement",
        "wallet/index",
        "wallet/withdrawal/index",
        "wallet/withdrawal/result",
        "wallet/withdrawal/history",
        "wallet/withdrawal/updateAccount",
        "wallet/transactions/history",
        "wallet/transactions/detail",
        "wallet/help",
      ]
    },
    {
      root: 'pageSetting',
      pages:[
        "aboutUs/index",
    "PICC/index",
    "PICCAvatar/index",
    "PICCNickName/index",
    "PICCPhone/index",
    "colorSettings/index",
    "permissionSetting/index",
    'settings/index',
    'accountCancellation/index',
    "bindMobilePhone/index",
    "loginedResetPassword/index",
    "webView/index",
    "darkModeSettings/index",
      ]
    },
    {
      root: 'pageVip',
      pages:[
        "vipCenter/index",
        "vipCenter/paySuccess",
        "vipCenter/payOrder",
        "benefits/index",
      ]
    },
    {
      root: 'pageCustomerService',
      pages:[
    "mobileCustomerServer/index",
    "ai/chat",
      ]
    }
  ],
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: 'WeChat',
    navigationBarTextStyle: 'black',
    onReachBottomDistance: 80
  },
  animation: {
    duration: 300,
    delay: 50,
  },

})
