@import '@arco-design/mobile-react/style/mixin.less';
@import '@/utils/css/variables.less';

.yk-notice-bar {
  width: 100%;
  // padding: 6px 16px;
  &__inner {
    // width: 100%;
    padding: 8px 12px;
    font-size: 14px;
    line-height: 1.4;
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  &__title {
    font-weight: 500;
    font-size: 14px;
    line-height: 1.4;
  }

  &__text {
    font-size: 13px;
    line-height: 1.4;
    opacity: 0.9;
  }

  .yk-notice-bar__inner {
    display: flex;
  }

  // // 信息类型样式
  // &--info .yk-notice-bar__inner {
  //   color: @arcoblue-6;
  //   background-color: @arcoblue-1;
  //   .use-dark-mode-query({
  //     color: @dark-arcoblue-6;
  //     background-color: @dark-arcoblue-1;
  //   });
  // }

  // 成功类型样式
  &--success .yk-notice-bar__inner {
    color: @--success-6;
    background-color: @--success-1;
    // .use-dark-mode-query({
    //   color: @--success-6;
    //   background-color: @--success-1;
    // });
  }

  // 警告类型样式
  &--warning .yk-notice-bar__inner {
    color: @--danger-6;
    background-color: @--danger-1;
    // .use-dark-mode-query({
    //   color: @--warning-6;
    //   background-color:  @--warning-1;
    // });
  }

  // 日程类型样式
  &--schedule .yk-notice-bar__inner {
    color: @--warning-6;
    background-color: @--warning-1;
    // .use-dark-mode-query({
    //   color: @--warning-6;
    //   background-color: @--warning-1;
    // });
  }

    // 日程类型样式
  &--info .yk-notice-bar__inner {
    color: @--warning-6;
    background-color: @--warning-1;
    // .use-dark-mode-query({
    //   color: @--warning-6;
    //   background-color: @--warning-1;
    // });
  }

  // 图标样式
  .arco-notice-bar-left {
    margin-right: 8px;

    .arco-icon {
      font-size: 16px;
    }
  }
}
