@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pageOrder/refund/index"] {
  .refund-page {
    min-height: 100vh;
    background-color: #f8f9fa;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
    padding-bottom: 80px; // 给底部操作栏留出空间

    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 50vh;
    }
  }

  // 商品区域样式
  .goods-section {
    background-color: #ffffff;
    .use-dark-mode-query({
    background-color: @dark-card-background-color;
  });
    margin-bottom: 12px;

    .goods-section-header {
      padding: 16px;

      .goods-section-title {
        font-size: 16px;
        color: var(--primary-color);
        // .use-dark-mode-query({
        //   color: @dark-primary-color;
        // });
        font-weight: 500;
      }

      .goods-section-line {
        height: 1px;
        background-color: #f5f5f5;
        .use-dark-mode-query({
        background-color: #333;
      });
        margin-top: 12px;
      }
    }
  }

  // 商品卡片样式 - 参考delivery页面
  .refund-product-card {
    background: transparent;
    border-radius: 10px;
    margin: 0 12px 12px 12px;
    padding: 8px 0;
  }

  .refund-product-main {
    display: flex;
    align-items: center;
  }

  .refund-checkbox {
    margin-right: 12px;
  }

  .refund-product-img-wrapper {
    width: 64px;
    height: 64px;
    border-radius: 8px;
    margin-right: 12px;
    overflow: hidden;
    display: block;
    background: #f5f5f5;
  }

  .refund-product-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .refund-product-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 64px;
  }

  .refund-product-title {
    font-size: 14px;
    color: #333;
    .use-dark-mode-query({
    color: @dark-font-color;
  });
    line-height: 20px;
    margin-bottom: 8px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .refund-product-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .refund-product-price {
    font-size: 16px;
    color: #eb483f;
    font-weight: 500;
  }

  // SKU样式 - 参考delivery页面
  .refund-product-sku {
    margin-top: 8px;
    padding-left: 0;
  }

  .refund-product-sku-main {
    display: flex;
    align-items: center;
  }

  .refund-product-sku-info {
    display: flex;
    flex-direction: column;
    flex: 1;
    margin-left: 8px;
  }

  .refund-product-sku-quantity {
    font-size: 12px;
    color: #86909c;
    .use-dark-mode-query({
    color: @dark-font-color;
  });
    margin-top: 4px;
  }

  .refund-product-sku-text {
    font-size: 13px;
    color: #666;
    .use-dark-mode-query({
    color: @dark-font-color;
  });
    line-height: 20px;
  }

  .refund-product-sku-controls {
    display: flex;
    align-items: center;
  }

  // 退款金额输入区域
  .refund-amount-section {
    background-color: #ffffff;
    .use-dark-mode-query({
    background-color: @dark-card-background-color;
  });
    margin-bottom: 12px;
    padding: 0;

    .amount-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-bottom: 1px solid @dark-line-color;
    });

      &:last-child {
        border-bottom: none;
      }

      .amount-item-left {
        flex: 1;
        display: flex;
        flex-direction: column;

        .amount-item-title {
          font-size: 15px;
          color: #333;
          .use-dark-mode-query({
          color: @dark-font-color;
        });
          margin-bottom: 4px;
        }

        .amount-item-hint {
          font-size: 12px;
          color: #999;
          .use-dark-mode-query({
          color: @dark-font-color;
        });
        }
      }

      .amount-item-right {
        .amount-input-wrapper {
          display: flex;
          align-items: center;
          border: 1px solid rgba(153, 153, 153, 0.3);
          border-radius: 6px;
          padding: 8px 12px;
          width: 120px;

          .amount-input {
            flex: 1;
            text-align: center;
            font-size: 14px;
            color: #333;
            .use-dark-mode-query({
            color: @dark-font-color;
          });
            border: none;
            background: transparent;
            width: 60px;
          }

          .amount-unit {
            font-size: 14px;
            color: #666;
            .use-dark-mode-query({
            color: @dark-font-color;
          });
            margin-left: 4px;
            flex-shrink: 0;
          }
        }
      }
    }

    .remark-section {
      padding: 16px;

      margin-bottom: 16px;
      .remark-input {
        width: 100%;
        min-height: 80px;
        background-color: #f8f9fa;
        .use-dark-mode-query({
        background-color: #333;
      });
        border-radius: 6px;
        font-size: 14px;
        color: #333;
        .use-dark-mode-query({
        color: @dark-font-color;
      });
        border: none;
        resize: none;
      }
    }
  }

  // 底部操作栏
  .bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #ffffff;
    .use-dark-mode-query({
    background-color: @dark-card-background-color;
  });
  border-top: 1px solid var(--line-color);
  .use-dark-mode-query({
  border-top: 1px solid @dark-line-color;
});
    padding: 12px 16px;
    display: flex;
    align-items: center;
    z-index: 100;

    .bottom-bar-left {
      display: flex;
      align-items: center;
      flex: 1;

      .select-all-checkbox {
        margin-right: 8px;
      }

      .select-all-text {
        font-size: 14px;
        color: #333;
        .use-dark-mode-query({
        color: @dark-font-color;
      });
        margin-right: 16px;
      }
    }

    .bottom-bar-center {
      display: flex;
      align-items: center;
      margin-right: 16px;

      .selected-text {
        font-size: 14px;
        color: #666;
        .use-dark-mode-query({
        color: @dark-font-color;
      });
      }

      .selected-count {
        font-size: 16px;
        color: #eb483f;
        font-weight: 500;
        margin: 0 2px;
      }

      .selected-price {
        font-size: 16px;
        color: #eb483f;
        font-weight: 500;
      }
    }

    .bottom-bar-right {
      .refund-btn {
        color: #fff;
        min-width: 100px;
        height: 40px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }

  // 确认弹窗内容
  .confirm-content {
    padding: 0;
    padding-top: 16px;
    font-size: 14px;
    text-align: left;

    .confirm-amount {
      font-size: 14px;
      color: #666;
      .use-dark-mode-query({
      color: @dark-font-color;
    });
      line-height: 20px;
      margin-bottom: 16px;
      text-align: left;
    }

    .confirm-method {
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-top: 1px solid @dark-line-color;
    });

      .method-row {
        display: flex;
        align-items: center;

        .method-label {
          font-size: 14px;
          color: #666;
          .use-dark-mode-query({
          color: @dark-font-color;
        });
          margin-right: 8px;
        }

        .method-value {
          font-size: 14px;
          color: #eb483f;
          font-weight: normal;
        }
      }
    }
  }

  // 弹窗标题中的数字颜色
  :global {
    .arco-dialog-title {
      .refund-count {
        color: #eb483f;
        font-weight: 500;
      }
    }
  }

  // 自定义弹窗样式
  :global {
    .arco-dialog {
      border-radius: 12px;
      overflow: hidden;

      .arco-dialog-header {
        padding: 20px 20px 0 20px;
        text-align: center;
        border-bottom: none;

        .arco-dialog-title {
          font-size: 16px;
          font-weight: 500;
          color: #333;
          margin-bottom: 0;
        }
      }

      .arco-dialog-body {
        padding: 16px 20px 20px 20px;
      }

      .arco-dialog-footer {
        padding: 0 20px 20px 20px;
        display: flex;
        justify-content: space-between;
        gap: 16px;
        border-top: none;

        .arco-btn {
          flex: 1;
          height: 36px;
          border-radius: 6px;
          font-size: 14px;
          border: none;

          &.arco-btn-secondary {
            background-color: #f5f5f5;
            color: #333;
          }

          &.arco-btn-primary {
            background-color: #007aff;
            color: #fff;
          }
        }

        // 取消按钮样式
        .arco-btn:first-child {
          background-color: #f5f5f5;
          color: #333;
        }

        // 确认按钮样式
        .arco-btn:last-child {
          background-color: #007aff;
          color: #fff;
        }
      }
    }
  }
}
