@import "@arco-design/mobile-react/style/mixin.less";

// 页面根容器样式
[id^="/pageOnlinePayment/wallet/withdrawal/result"] {
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });

  // 页面主容器
  .withdrawal-result {
    position: relative;
    min-height: 100vh;
    background-color: var(--page-primary-background-color);
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });

    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }

    .result-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 30px 16px 10px;
      gap: 40px;

      .success-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 48px;
        height: 48px;
        color: var(--primary-color);
        .use-dark-mode-query({
        color: var(--dark-primary-color);
      });
      }

      .status-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;

        .status-title {
          font-size: 15px;
          font-weight: bold;
          line-height: 1.4;
          color: var(--font-color);
          .use-dark-mode-query({
          color: var(--dark-font-color);
        });
        }

        .status-subtitle {
          font-size: 14px;
          font-weight: 500;
          line-height: 1.4;
          color: var(--sub-info-font-color);
          opacity: 0.9;
          .use-dark-mode-query({
          color: var(--dark-sub-info-font-color);
        });
        }
      }

      .withdrawal-details {
        width: 100%;
        max-width: 343px;
        border-top: 0.5px solid var(--line-color);
        .use-dark-mode-query({
        border-top: 0.5px solid @dark-line-color;
      });
        padding: 20px 0;

        .detail-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 5px;

          &:last-child {
            margin-bottom: 0;
          }

          .detail-label {
            font-size: 12px;
            font-weight: 500;
            line-height: 1.4;
            color: var(--sub-info-font-color);
            .use-dark-mode-query({
            color: var(--dark-sub-info-font-color);
          });
          }

          .detail-value {
            font-size: 12px;
            font-weight: 500;
            line-height: 1.4;
            color: var(--font-color);
            text-align: right;
            .use-dark-mode-query({
            color: var(--dark-font-color);
          });
          }
        }
      }
    }

    .bottom-actions {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 16px;
      background-color: var(--container-background-color);
      .use-dark-mode-query({
      background-color: var(--dark-container-background-color);
    });

      .confirm-button,
      .complete-button {
        width: 100%;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
