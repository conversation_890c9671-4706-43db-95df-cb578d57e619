@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pageDynamic/album/batchEdit/index"] {
  // 批量删除页面样式 - 参考批量上下架
  .batch-delete-page {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f7f8fa;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
  }

  // 搜索栏样式
  .searchLine-batchEdit {
    background: #fff;
    // padding: 10px 15px;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });

    .search-icon {
      width: 20px;
      height: 20px;
      margin-left: 8px;
    }

    .demo-search-btn {
      color: var(--primary-color);
      font-size: 14px;
      padding: 0 8px;
      cursor: pointer;
    }
  }

  // 筛选条件显示 - 对照订单列表样式
  .filter {
    width: max-content;
    margin: 0 16px 10px 16px;
    padding: 4px 12px;
    border: 1px solid var(--primary-color);
    border-radius: 50px;
    align-items: center;
    display: flex;
    .use-dark-mode-query({
    border-color: var(--primary-color);
  });

    &-text {
      display: inline-block;
      text-align: center;
      font-size: 11px;
      color: var(--primary-color)  !important;
    }

    &-img {
      display: inline-block;
      margin-left: 8px;
      width: 7px;
      height: 7px;
    }
  }

  .clickOpacity:active {
    opacity: 0.8;
  }

  // 内容滚动区域
  .content-scroll {
    flex: 1;
    background: #fff;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
  }

  // 列表容器
  .boxContent {
    position: relative;
    width: 100%;
    // padding-bottom: 50px; // 为底部操作栏留出空间
  }

  // 列表项样式 - 参考批量上下架的dline
  .dline {
    position: relative;
    width: 100%;
    padding: 15px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });

  border-bottom: 1px solid var(--line-color);
  .use-dark-mode-query({
  border-bottom: 1px solid @dark-line-color;
});

    &-left {
      margin-left: 15px;
      display: flex;
      align-items: center;
      justify-content: center;

      &-change {
        width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    &-right {
      flex: 1;
      margin-right: 15px;
      margin-left: 10px;
      display: flex;
      flex-direction: column;

      &-top {
        margin-bottom: 8px;

        &.topBg {
          background-color: #f3f4f6;
          padding: 8px;
          border-radius: 4px;
          .use-dark-mode-query({
          background-color: #2a2a2a;
        });
        }

        .dynamic-title {
          font-size: 14px;
          color: #222;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
          .use-dark-mode-query({
          color: var(--dark-font-color);
        });
        }
      }

      &-bottom {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;

        .imageItem {
          position: relative;
          width: 60px;
          height: 60px;
          border-radius: 4px;
          overflow: hidden;

          &-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            background: #f7f8fa;
          }

          &-mask {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 12px;
          }
        }
      }
    }
  }

  // 空状态样式
  .not_content {
    position: relative;
    width: 100%;
    height: 50vh;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    font-size: 14px;
    color: #999999;

    &-image {
      width: 100px;
      height: 100px;
      display: block;
      margin-bottom: 16px;
    }
  }

  // 底部操作栏样式 - 完全照搬批量上下架
  .footerBtnBox {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 50px;
    padding: 0 15px;
    background-color: #ffffff;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
    z-index: 3090;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top: 1px solid #f7f7f7;
    box-sizing: border-box;

    &-change {
      flex: 1;
      display: flex;
      align-items: center;

      &-image {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
      }

      &-c {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        &-img {
          width: 8px;
          height: 12px;
          display: block;
          margin-left: 10px;
        }

        &-text {
          font-size: 13px;
          color: #333333;
          .use-dark-mode-query({
          color: var(--dark-font-color);
        });
        }
      }
    }

    &-share {
      margin-right: 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      &-img {
        width: 16px;
        height: 16px;
        display: block;
      }

      &-text {
        margin-top: 4px;
        font-size: 10px;
        color: #666666;
        .use-dark-mode-query({
        color: @dark-font-color;
      });
      }
    }

    &-btn {
      width: 130px !important;
      height: 36px !important;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6px !important;
      background-color: var(--primary-color) !important; // 蓝色主题
      opacity: 1;
      color: #ffffff !important;
      font-size: 13px !important;
      border: none !important;
      padding: 0 !important;
      margin: 0 !important;
      min-width: 130px !important;
      max-width: 130px !important;
      flex-shrink: 0;
      box-sizing: border-box;
    }

    &-notbtn {
      width: 130px !important;
      height: 36px !important;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6px !important;
      background-color: var(--primary-color) !important; // 蓝色半透明
      opacity: 0.3;
      color: #ffffff !important;
      font-size: 13px !important;
      border: none !important;
      padding: 0 !important;
      margin: 0 !important;
      min-width: 130px !important;
      max-width: 130px !important;
      flex-shrink: 0;
      box-sizing: border-box;
    }
  }

  // 加载更多样式
  .loading-more {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16px 0;
    background: #fff;
    .use-dark-mode-query({
    background: @dark-background-color;
  });

    .loading-text {
      font-size: 14px;
      color: #86909c;
      display: flex;
      align-items: center;

      &::before {
        content: "";
        width: 16px;
        height: 16px;
        margin-right: 8px;
        border: 2px solid #e5e6eb;
        border-top-color: #165dff;
        border-radius: 50%;
        animation: loading-spin 0.8s linear infinite;
      }
    }
  }

  @keyframes loading-spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .no-more {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 16px 0;
    background: #fff;
    .use-dark-mode-query({
    background: @dark-background-color;
  });

    .no-more-text {
      font-size: 14px;
      color: #86909c;
      position: relative;
      padding: 0 16px;

      &::before,
      &::after {
        content: "";
        position: absolute;
        top: 50%;
        width: 60px;
        height: 1px;
        background: #e5e6eb;
        .use-dark-mode-query({
        background: #333;
      });
      }

      &::before {
        right: 100%;
        margin-right: 16px;
      }

      &::after {
        left: 100%;
        margin-left: 16px;
      }
    }
  }
}
