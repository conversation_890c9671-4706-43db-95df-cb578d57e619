import { View, Text } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import {
  Cell,
  Image
} from "@arco-design/mobile-react";
import React from "react";
import Taro from "@tarojs/taro";

// 组件
import YkNavBar from "@/components/ykNavBar/index";
import { url } from "inspector";
import { platform } from "os";

export default function aboutUs() {

  const [httpPlatform, setHttpPlatform] = React.useState("H5");
  // 在 App 内部用原生 WebView 打开外链（顶层加载，不被 iframe 限制）
  const openExternal = (rawUrl: string) => {
    try {
      const uaAll = window.navigator.userAgent;
      const isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
      const isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;

      if (isIos && (window as any).webkit?.messageHandlers?.openExternal) {
        (window as any).webkit.messageHandlers.openExternal.postMessage(rawUrl);
        return;
      }
      if (isAndroid && (window as any).Android?.openExternal) {
        (window as any).Android.openExternal(rawUrl);
        return;
      }
      // H5 回退：新窗口打开
      window.open(rawUrl, '_blank');
    } catch (e) {
      // 兜底
      window.open(rawUrl, '_blank');
    }
  }

  // 不允许被内嵌加载的域名（存在 X-Frame-Options/CSP frame-ancestors 限制）
  const DISALLOW_EMBED_HOSTS = [
    'beian.miit.gov.cn'
  ];

  const getInLink = (url:string,name:string) => {
    // 命中禁止内嵌的域名，直接走原生 WebView / 系统浏览器
    try {
      const u = new URL(url);
      if (DISALLOW_EMBED_HOSTS.includes(u.hostname)) {
        openExternal(url);
        return;
      }
    } catch (e) {
      // URL 解析失败，直接外部打开
      openExternal(url);
      return;
    }

    const encodedUrl = encodeURIComponent(url);
    const encodedName = encodeURIComponent(name);
    Taro.navigateTo({
      url: `/pageSetting/webView/index?url=${encodedUrl}&name=${encodedName}`,
    });
  }

  // 给我们评分
  const scoreforUs = () => {
    let _window:any = window;
    _window.webkit.messageHandlers.storeReview.postMessage('');
  }

  useLoad(() => {
    let uaAll = window.navigator.userAgent;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setHttpPlatform("Android");
    } else if (isIos) {
      setHttpPlatform("IOS");
    } else if (isHM) {
      setHttpPlatform("HM");
    } else {
      setHttpPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setHttpPlatform("WX");
    }
  });

  return (
    <View className="aboutUsPageContent">
      {httpPlatform !== "WX" && <YkNavBar title="关于我们" />}

      <View className="aboutUsTop">
        <Image
          className="aboutUsTop-logo"
          src={require("@/assets/images/login/logo.png")}
          bottomOverlap={null}
        />
        <Text className="aboutUsTop-name">海胆相册</Text>
      </View>
      <View className="module">
        <View className="module-content">
          <Cell.Group bordered={false}>
            {/* 关于我们 */}
            <Cell
              label="关于我们"
              className="module-content-cell"
              text=""
              showArrow
              onClick={()=>getInLink(`${ABOUT_US}`,'关于我们')}
            ></Cell>
            {/* 给我们评分 */}
           {httpPlatform == "IOS" && <Cell
              label="给我们评分"
              className="module-content-cell"
              text=""
              showArrow
              onClick={scoreforUs}
            ></Cell>}
            {/* 用户协议 */}
            <Cell
              label="用户协议"
              className="module-content-cell"
              text=""
              showArrow
              onClick={()=>getInLink(`${USER_AGREEMENT}`,'用户协议')}
            ></Cell>
            {/* 隐私政策 */}
            <Cell
              label="隐私政策"
              className="module-content-cell"
              text=""
              showArrow
              onClick={()=>getInLink(`${PRIVACY_POLICY}`,'隐私政策')}
            ></Cell>
            {/* 消费者权益保障说明 */}
            <Cell
              label="消费者权益保障说明"
              className="module-content-cell"
              text=""
              showArrow
              onClick={()=>getInLink(`${CONSUMER_AGREEMENT}`,'消费者权益保障说明')}
            ></Cell>
            {/* 商户入驻协议 */}
            <Cell
              label="商户入驻协议"
              className="module-content-cell"
              text=""
              showArrow
              onClick={()=>getInLink(`${SALE_SETTLED_AGREEMENT}`,'商户入驻协议')}
            ></Cell>
          </Cell.Group>
        </View>
      </View>

      {/* <Text className="icp" onClick={()=>window.location.href='https://beian.miit.gov.cn'}>ICP备案号: 闽ICP备2023012710号-9A</Text> */}
      <Text className="icp" onClick={()=>getInLink('https://beian.miit.gov.cn/#/home','ICP备案')}>{httpPlatform === "WX" ? "闽ICP备2023012710号-7X  >" : "ICP备案号: 闽ICP备2023012710号-9A  >"}  </Text>
    </View>
  );
}
