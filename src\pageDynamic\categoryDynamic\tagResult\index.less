  @import "@arco-design/mobile-react/style/mixin.less";
.tag-result-page {
  min-height: 100vh;
  background-color: #f5f5f5;

  .tag-info {
    background: #fff;
    padding: 24px 32px 20px;
    border-bottom: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-bottom: 1px solid @dark-line-color;
  });
    position: sticky;
    top: 0;
    z-index: 100;

    .tag-info-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      .back-btn {
        color: #1890ff;
        font-size: 28px;
        cursor: pointer;
        
        &:active {
          opacity: 0.7;
        }
      }

      .page-title {
        font-size: 32px;
        font-weight: 600;
        color: #333;
      }
    }

    .selected-tags {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 8px;

      .tags-label {
        font-size: 28px;
        color: #666;
        flex-shrink: 0;
      }

      .tags-text {
        font-size: 28px;
        color: #1890ff;
        font-weight: 500;
        background: rgba(24, 144, 255, 0.1);
        padding: 8px 16px;
        border-radius: 16px;
        border: 1px solid rgba(24, 144, 255, 0.2);
      }
    }
  }

  // 重写Album组件的一些样式，适配当前页面
  :global {
    .album-core {
      .album-tabs {
        margin-top: 0;
      }
    }
  }
} 