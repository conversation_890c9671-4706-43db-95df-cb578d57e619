@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageDynamic/moveMaterials/selectMaterials/result"] {
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });

  .move-result {
    .main-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 100px 24px 40px;
    }

    .success-icon {
      font-size: 48px;
      color: var(--primary-color);
    }

    .success-text {
      margin-top: 16px;
      font-size: 15px;
      font-weight: 600;
    //   color: #1d2129;
    .use-var(color, font-color);
    .use-dark-mode-query({
      color: var(--dark-font-color);
    });

    }

    .success-desc {
      margin-top: 10px;
      font-size: 12px;
      color: #959595;
      text-align: center;
      padding: 0 8px;
    }

    .highlight {
      color: var(--primary-color);
      font-weight: 600;
    }

    .action-line {
      margin-top: 40px;
      display: flex;
      align-items: center;
      gap: 12px;
      color: var(--primary-color);
    }

    .action {
      color: var(--primary-color);
      font-size: 14px;
    }

    .divider {
      color: var(--primary-color);
      opacity: 0.8;
    }

    .bottom-bar {
      position: fixed;
      left: 0;
      right: 0;
      bottom: 34px;
      padding: 16px;
      display: flex;
      justify-content: center;
      align-items: center;

      .primary-btn {
        padding: 7.5px 16px;
      }
    }
  }
}
