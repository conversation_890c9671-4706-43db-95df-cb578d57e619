import { IconProps } from './types';

const IconPayment: React.FC<IconProps> = ({
  color = '#54B736',
  size = 16,
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    fill="none"
    version="1.1"
    width={size}
    height={size}
    viewBox="0 0 16 16"
    className={className}
  >
    <g>
      <g>
        <path
          d="M6.416666873069763,9.625294700000001Q5.617999873069763,10.062603,5.499999873069763,9.3797297L4.499999373069763,7.0835304Q4.114666373069763,5.99228,4.833333173069763,6.5917273Q5.448666973069763,7.049891,5.916666373069763,7.3290949Q6.383333573069764,7.6089716,6.916666873069763,7.4111748L13.455333373069763,4.442194000000001C12.248668373069764,2.96544003,10.255334273069764,2,7.999333773069763,2C4.317333373069763,2,1.3333333730697632,4.5700216000000005,1.3333333730697632,7.7401638C1.3333333730697632,9.5633993,2.322000083069763,11.1861458,3.8613332730697634,12.238375L3.583333373069763,13.807972Q3.447999873069763,14.266809,3.9166668730697634,14.053537C4.236666773069763,13.908889,5.050666673069763,13.389503,5.536666773069763,13.073296C6.330172873069763,13.344101,7.162350073069764,13.481607,8.000000373069764,13.48033C11.681334373069763,13.48033,14.666667373069764,10.9103069,14.666667373069764,7.7401652C14.664510373069763,6.8408628,14.423724373069764,5.9585552,13.969334373069763,5.1849446C11.886001373069764,6.4181509,7.0406673730697635,9.283523599999999,6.416000773069763,9.6252952L6.416666873069763,9.625294700000001Z"
          fill={color}
          fillOpacity="1"
        />
      </g>
    </g>
  </svg>
);

export default IconPayment;
