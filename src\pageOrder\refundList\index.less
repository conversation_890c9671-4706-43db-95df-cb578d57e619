@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pageOrder/refundList/index"] {
  .refund-list-page {
    background: #f7f8fa;
    .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
    //   min-height: 100vh;
  }

  .order-list-refund {
    padding: 12px;
  }

  .order-card {
    background: #fff;
    .use-dark-mode-query({
    background-color: @dark-cell-background-color !important;
  });
    border-radius: 10px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
    padding: 10px 0 10px 0;
  }

  /* 商家信息样式 */
  .shop-info {
    display: flex;
    align-items: center;
  }

  .shop-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 8px;
    background: #f5f5f5;
  }

  .shop-name {
    font-weight: 500;
    margin-right: 4px;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .verified-icon {
    width: 16px;
    height: 16px;
  }

  .order-status {
    flex-shrink: 0;
    color: var(--primary-color);
    font-size: 13px;
  }

  /* 商品列表样式 */
  .goods-list {
    padding: 0 12px;
  }

  .goods-item {
    display: flex;
    align-items: flex-start;
    margin-top: 12px;
  }

  .goods-img {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    background: #f5f5f5;
    margin-right: 10px;
  }

  .goods-info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
  }

  .goods-title-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .goods-title {
    flex: 1;
    font-size: 14px;
    color: #222;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    margin-right: 8px;
    /* 显示两行，超出省略号 */
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .goods-price-section {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    flex-shrink: 0;
  }

  .goods-price {
    color: #f53f3f;
    font-weight: 600;
    font-size: 16px;
  }

  .goods-quantity {
    color: #888;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-size: 13px;
  }

  .goods-sku {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .sku-text {
    color: #666;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-size: 13px;
  }

  .sku-count {
    gap: 8px;
    display: flex;
    align-items: center;
  }

  .refund-quantity-refund {
    color: #86909c;
    font-size: 12px;
  }

  .normal-quantity {
    color: #86909c;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-size: 12px;
  }

  /* 订单汇总信息 */
  .order-summary {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 10px 12px 0 12px;
  }

  .order-summary-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }

  .order-total {
    text-align: right;
  }

  .refund-amount {
    display: flex;
    align-items: center;
  }

  .refund-label {
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    color: #86909c;
    font-size: 12px;
    margin-right: 4px;
  }

  .express {
    color: #86909c;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-size: 12px;
  }

  .time {
    color: #86909c;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-size: 12px;
  }

  .refund-value {
    color: #f53f3f;
    font-size: 12px;
  }

  .total-price {
    color: #f53f3f;
    font-size: 12px;
  }

  .goods-count {
    color: #86909c;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-size: 12px;
  }

  .freight-text {
    color: #86909c;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-size: 12px;
  }

  /* 备注信息样式 */
  .remark-section {
    background: #f7f8fa;
    .use-dark-mode-query({
    background-color: @dark-card-background-color !important;
  });
    border-radius: 10px;
    margin: 10px;
    padding: 12px;
  }

  .remark-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
  }

  .remark-row:last-child {
    margin-bottom: 0;
  }

  .remark-label {
    color: #666;
    font-size: 13px;
    width: 80px;
    flex-shrink: 0;
    margin-right: 8px;
  }

  .remark-label-block {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  

  .order-img-icon {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }


  .remark-content {
    display: flex;
    color: #333;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-size: 13px;
  }

  /* 空状态 */
  .empty-order {
    text-align: center;
    color: #bbb;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-size: 15px;
    min-height: calc(100vh - 250px);
    padding: 48px 0 32px 0;
  }

  /* 加载状态 */
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50vh;
  }

  /* 下拉刷新样式 */
  .pull-refresh-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 0;
  }

  .loading-text {
    margin-left: 8px;
    font-size: 14px;
    color: #666;
  }

  .pull-refresh-success {
    font-size: 14px;
    color: #00b42a;
    text-align: center;
    padding: 10px 0;
  }
}
