import { View, Text, RichText, WebView } from "@tarojs/components";
import { Button, Image, Input, NavBar } from "@arco-design/mobile-react";
import "./index.less";
import Taro, { useLoad } from "@tarojs/taro";
import { getAgreement } from "@/utils/api/common/common_user";
import React, { useState, useEffect,  useRef } from "react";
// 组件
import YkNavBar from "@/components/ykNavBar/index";
import { toast } from "@/utils/yk-common";
// interface AgreementResponse {
//   data: {
//     content: string;
//   }
// }

export default function WebViewPage() {
  const [content, setContent] = useState("");
  // const [aid, setAid] = useState("");
  const [name, setName] = useState("");
  const [webUrl, setWebUrl] = useState("");
  const [useWebView, setUseWebView] = useState(false);
  const [platform,setPlatform] = useState<string>("H5");

  useLoad((e) => {
    console.log(e,)
    // setAid(e.id)
    setName(decodeURIComponent(e.name))

    // 检查是否传入了外部URL参数
    if (e.url) {
      setWebUrl(decodeURIComponent(e.url));
      setUseWebView(true);
    }

    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  });

  // useEffect(() => {
  //   if(aid && !useWebView){
  //     console.log('aid',aid)
  //     getAgreementInfo(aid);
  //   }
  // }, [aid, useWebView]);

  // 获取协议内容
  const getAgreementInfo = (id: string) => {
    let data = {
      id,
    };
    getAgreement(data).then((res:any) => {
      if (res && res.code == 0) {
        setContent(res.data);
      }else{
        toast("error", {
          content: res.msg,
          duration: 2000,
        });
      }
    })
  };

  // WebView消息处理
  const handleWebViewMessage = (e: any) => {
    console.log('WebView消息:', e.detail);
  };

  // WebView加载完成
  const handleWebViewLoad = (e: any) => {
    console.log('WebView加载完成:', e.detail);
  };

  // WebView加载失败
  const handleWebViewError = (e: any) => {
    console.log('WebView加载失败:', e.detail);
    toast('error', {
      content: '网络异常，请重试',
      duration: 2000,
    });
  };

  return (
    <View className='webview'>
      {platform !== "WX" &&<YkNavBar title={name} />}
        <WebView
          src={webUrl}
          onMessage={handleWebViewMessage}
          onLoad={handleWebViewLoad}
          onError={handleWebViewError}
          className='webview-container'
        />
    </View>
  );
}
