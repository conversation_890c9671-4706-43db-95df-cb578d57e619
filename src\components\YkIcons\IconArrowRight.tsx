import { IconProps } from './types';

const IconArrowRight: React.FC<IconProps> = ({
  color = '#07C160',
  size = 24,
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    version="1.1"
    width={size}
    height={size}
    viewBox="0 0 15 24"
    className={className}
  >
    <g>
      <path
        d="M0,4.16125L0,19.8387C0,21.5158,1.93986,22.4481,3.24939,21.4005L13.0478,13.5617C14.0486,12.7611,14.0486,11.2389,13.0478,10.4383L3.24939,2.59951C1.93986,1.55189,0,2.48424,0,4.16125Z"
        fill={color}
        fillOpacity="1"
      />
    </g>
  </svg>
);

export default IconArrowRight;
