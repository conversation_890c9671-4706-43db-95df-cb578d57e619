import { MerchantType } from '../types';
import {
  BusinessLicenseInfo,
  MerchantBasicInfo,
  IdentityInfo,
  WithdrawalAccountInfo,
  ContactInfo,
  MerchantEntryApplicationData,
  MerchantApplicationFormData
} from './types';
import { MERCHANT_TYPE_DICT } from '../types';
import { generateOutRequestNo } from '../utils';
import type { ImagePickItem } from '@arco-design/mobile-react/cjs/image-picker/type';

// 重新导出字典，保持向后兼容
export { MERCHANT_TYPE_DICT };

// 辅助函数：将URL字符串转换为ImagePickItem对象
const createImagePickItemFromUrl = (url: string): ImagePickItem | null => {
  if (!url) return null;

  return {
    url: url,
    file: undefined, // 编辑模式下没有原始文件
    status: 'loaded', // 表示图片已经加载完成
    uid: `image_${Date.now()}_${Math.random()}`, // 生成唯一ID
    name: url.split('/').pop() || 'image', // 从URL提取文件名
    size: 0, // 编辑模式下无法获取文件大小
    type: 'image/jpeg' // 默认类型，实际类型可能不同
  } as ImagePickItem;
};

/**
 * 根据商户类型获取默认账户类型
 * @param merchantType 商户类型
 * @returns 默认账户类型
 */
const getDefaultAccountType = (merchantType: MerchantType): 'grzh' | 'dgzh' => {
  if (merchantType === MerchantType.ENTERPRISE) {
    // 企业默认使用对公账户
    return 'dgzh';
  } else if (merchantType === MerchantType.PERSONAL) {
    // 个人默认使用个人账户
    return 'grzh';
  } else {
    // 个体工商户默认使用对公账户（可选择）
    return 'dgzh';
  }
};

/**
 * 初始化表单数据
 * @param merchantType 商户类型，用于设置合适的默认值
 */
export const initFormData = (merchantType: MerchantType = MerchantType.ENTERPRISE): MerchantApplicationFormData => ({
  merchantType,
  businessLicenseInfo: {
    image: null,
    name: '',
    creditCode: '',
    registerDate: '',
    registerArea: '',
    businessAddress: '',
    wechat: {}
  } as BusinessLicenseInfo,
  merchantBasicInfo: {
    shortName: '',
    industry: '',
    industryPickerVisible: false
  } as MerchantBasicInfo,
  identityInfo: {
    images: { front: null, back: null },
    basic: {
      name: '',
      idNumber: '',
      startDate: '',
      endDate: '',
      address: ''
    },
    wechat: {}
  } as IdentityInfo,
  withdrawalAccountInfo: {
    type: getDefaultAccountType(merchantType),
    image: null,
    bankCard: {
      name: '',
      number: '',
      branch: ''
    },
    wechat: {},
    local: {}
  } as WithdrawalAccountInfo,
  beneficiaryInfo: {
    images: { front: null, back: null },
    basic: {
      name: '',
      idNumber: '',
      startDate: '',
      endDate: '',
      address: ''
    },
    wechat: {}
  } as IdentityInfo,
  contactInfo: {
    name: '',
    idNumber: '',
    phone: '',
    verificationCode: '',
    servicePhone: ''
  } as ContactInfo,
  owner: true // 默认为法人受益人
});

/**
 * 将API响应数据转换为表单数据
 */
export const convertApiDataToFormData = (apiData: any): MerchantApplicationFormData => {
  console.log('[convertApiDataToFormData] 开始转换API数据:', apiData);

  if (!apiData) {
    console.log('[convertApiDataToFormData] API数据为空，返回默认表单数据');
    return initFormData();
  }

  // 首先解析商户类型，用于初始化合适的默认值
  let merchantType = MerchantType.ENTERPRISE; // 默认值
  if (apiData.organizationType !== undefined) {
    const orgType = parseInt(apiData.organizationType);
    if (Object.values(MerchantType).includes(orgType)) {
      merchantType = orgType as MerchantType;
      console.log('[convertApiDataToFormData] 商户类型转换:', apiData.organizationType, '->', orgType, '(',
        orgType === MerchantType.ENTERPRISE ? '企业' :
        orgType === MerchantType.INDIVIDUAL ? '个体工商户' :
        orgType === MerchantType.PERSONAL ? '小微商户' :
        orgType === MerchantType.SELLER ? '个人卖家' : '未知', ')');
    } else {
      console.warn('[convertApiDataToFormData] 无效的商户类型:', apiData.organizationType, '使用默认值:', merchantType);
    }
  } else {
    console.warn('[convertApiDataToFormData] API数据中缺少 organizationType 字段，使用默认值:', merchantType);
  }

  // 使用解析出的商户类型初始化表单数据
  const formData = initFormData(merchantType);

  // 营业执照信息
  if (apiData.businessLicenseCopyLocal) {
    formData.businessLicenseInfo.image = createImagePickItemFromUrl(apiData.businessLicenseCopyLocal);
  }
  if (apiData.merchantName) {
    formData.businessLicenseInfo.name = apiData.merchantName;
  }
  if (apiData.businessLicenseNumber) {
    formData.businessLicenseInfo.creditCode = apiData.businessLicenseNumber;
  }
  if (apiData.companyAddress) {
    formData.businessLicenseInfo.businessAddress = apiData.companyAddress;
  }
  if (apiData.businessTime) {
    formData.businessLicenseInfo.registerDate = apiData.businessTime;
  }

  // 商户基本信息
  if (apiData.merchantShortname) {
    formData.merchantBasicInfo.shortName = apiData.merchantShortname;
  }
  if (apiData.qualificationType) {
    formData.merchantBasicInfo.industry = apiData.qualificationType;
  }

  // 身份证信息
  if (apiData.idCardName) {
    formData.identityInfo.basic.name = apiData.idCardName;
  }
  if (apiData.idCardNumber) {
    formData.identityInfo.basic.idNumber = apiData.idCardNumber;
  }
  if (apiData.idCardValidTimeBegin) {
    formData.identityInfo.basic.startDate = apiData.idCardValidTimeBegin;
  }
  if (apiData.idCardValidTime) {
    formData.identityInfo.basic.endDate = apiData.idCardValidTime;
  }
  if (apiData.idCardCopyLocal) {
    formData.identityInfo.images.front = createImagePickItemFromUrl(apiData.idCardCopyLocal);
  }
  if (apiData.idCardNationalLocal) {
    formData.identityInfo.images.back = createImagePickItemFromUrl(apiData.idCardNationalLocal);
  }

  // 提现账户信息
  console.log('[convertApiDataToFormData] 提现账户原始数据:', {
    accountName: apiData.accountName,
    accountNumber: apiData.accountNumber,
    accountBank: apiData.accountBank,
    bankName: apiData.bankName,
    bankAccountType: apiData.bankAccountType
  });

  if (apiData.accountName) {
    formData.withdrawalAccountInfo.bankCard.name = apiData.accountName;
    console.log('[convertApiDataToFormData] 设置开户名称:', apiData.accountName);
  }
  if (apiData.accountNumber) {
    formData.withdrawalAccountInfo.bankCard.number = apiData.accountNumber;
    console.log('[convertApiDataToFormData] 设置账号:', apiData.accountNumber);
  }
  // 优先使用 accountBank，如果没有则使用 bankName
  if (apiData.accountBank) {
    formData.withdrawalAccountInfo.bankCard.branch = apiData.accountBank;
    console.log('[convertApiDataToFormData] 设置开户银行(accountBank):', apiData.accountBank);
  } else if (apiData.bankName) {
    formData.withdrawalAccountInfo.bankCard.branch = apiData.bankName;
    console.log('[convertApiDataToFormData] 设置开户银行(bankName):', apiData.bankName);
  }

  // 根据 bankAccountType 设置账户类型
  if (apiData.bankAccountType) {
    // 74: 对公账户(企业), 75: 个人账户
    const accountType = apiData.bankAccountType === '75' ? 'grzh' : 'dgzh';
    formData.withdrawalAccountInfo.type = accountType;
    console.log('[convertApiDataToFormData] 设置账户类型:', apiData.bankAccountType, '->', accountType);
  }

  console.log('[convertApiDataToFormData] 转换后的提现账户信息:', formData.withdrawalAccountInfo);

  // 设置受益人类型（owner 状态）
  if (apiData.owner !== undefined) {
    formData.owner = apiData.owner;
  }

  // 受益人信息（仅在非法人受益人时填充）
  if (!formData.owner) {
    if (apiData.uboIdDocName) {
      formData.beneficiaryInfo.basic.name = apiData.uboIdDocName;
    }
    if (apiData.uboIdDocNumber) {
      formData.beneficiaryInfo.basic.idNumber = apiData.uboIdDocNumber;
    }
    if (apiData.uboIdDocPeriodBegin) {
      formData.beneficiaryInfo.basic.startDate = apiData.uboIdDocPeriodBegin;
    }
    if (apiData.uboIdDocPeriodEnd) {
      formData.beneficiaryInfo.basic.endDate = apiData.uboIdDocPeriodEnd;
    }
    if (apiData.uboIdDocCopyLocal) {
      formData.beneficiaryInfo.images.front = createImagePickItemFromUrl(apiData.uboIdDocCopyLocal);
    }
    if (apiData.uboIdDocCopyBackLocal) {
      formData.beneficiaryInfo.images.back = createImagePickItemFromUrl(apiData.uboIdDocCopyBackLocal);
    }
  }

  // 联系人信息
  if (apiData.contactName) {
    formData.contactInfo.name = apiData.contactName;
  }
  if (apiData.contactIdCardNumber) {
    formData.contactInfo.idNumber = apiData.contactIdCardNumber;
  }
  if (apiData.mobilePhone) {
    formData.contactInfo.phone = apiData.mobilePhone;
  }
  if (apiData.customerServicePhone) {
    formData.contactInfo.servicePhone = apiData.customerServicePhone;
  }

  return formData;
};



/**
 * 日期格式转换：将 YYYY/MM/DD 格式转换为 YYYY-MM-DD 格式
 * @param dateString 输入的日期字符串，格式为 YYYY/MM/DD
 * @param secondValue 第二个值，如果提供则返回数组格式 ["dateString", "secondValue"]
 * @returns 转换后的日期字符串，格式为 YYYY-MM-DD。如果有第二个参数，返回数组格式的 JSON 字符串
 */
const formatDateForApi = (dateString: string, secondValue?: string): string => {
  // 将 / 替换为 -
  const formattedDate = dateString ? dateString.replace(/\//g, '-') : '';
  
  // 如果有第二个参数，返回数组格式
  if (secondValue !== undefined) {
    return JSON.stringify([formattedDate, secondValue]);
  }
  
  // 否则返回单个日期（如果为空则返回空字符串）
  return formattedDate || '';
};

/**
 * 根据商户类型和账户类型确定银行账户类型
 * @param merchantType 商户类型
 * @param accountType 账户类型 ('grzh' | 'dgzh')
 * @returns 银行账户类型代码
 */
const getBankAccountType = (merchantType: MerchantType, accountType: 'grzh' | 'dgzh'): string => {
  // 根据微信支付文档要求：
  // 74-对公账户：适用于企业/政府机关/事业单位/社会组织，以及个体工商户选择对公账户
  // 75-对私账户：适用于小微/个人卖家，以及个体工商户选择对私账户

  if (merchantType === MerchantType.ENTERPRISE) {
    // 企业只能使用对公账户
    return '74';
  } else if (merchantType === MerchantType.INDIVIDUAL) {
    // 个体工商户可以选择对公或对私账户
    return accountType === 'dgzh' ? '74' : '75';
  } else if (merchantType === MerchantType.PERSONAL) {
    // 个人只能使用对私账户
    return '75';
  }

  // 默认返回对私账户
  return '75';
};

/**
 * 将表单数据转换为API请求数据
 */
export const convertFormDataToApiData = (
  formData: MerchantApplicationFormData,
  userId: number,
  id?: number
): MerchantEntryApplicationData => {
  const apiData: MerchantEntryApplicationData = {
    userId: userId,
    // 生成唯一的业务申请编号
    outRequestNo: generateOutRequestNo(formData.merchantType, userId),
    organizationType: formData.merchantType.toString(),
    // certType: '1', //企业或个体工商户不需要设置

    // 营业执照信息
    businessLicenseCopy: formData.businessLicenseInfo.wechat?.mediaId,
    businessLicenseNumber: formData.businessLicenseInfo.creditCode,
    merchantName: formData.businessLicenseInfo.name,
    companyAddress: formData.businessLicenseInfo.businessAddress,
    legalPerson: formData.businessLicenseInfo.legalPerson,
    businessTime: formatDateForApi(formData.businessLicenseInfo.registerDate, '长期'), // 注册日期映射到营业期限，默认为长期有效
    // 商户基本信息
    merchantShortname: formData.merchantBasicInfo.shortName,
    //qualificationType: formData.merchantBasicInfo.industry, //暂时不传：微信会默认

    // 身份证信息
    idCardName: formData.identityInfo.basic.name,
    idCardNumber: formData.identityInfo.basic.idNumber,
    idCardValidTimeBegin: formatDateForApi(formData.identityInfo.basic.startDate),
    idCardValidTime: formatDateForApi(formData.identityInfo.basic.endDate),
    idCardCopy: formData.identityInfo.wechat?.frontMediaId,
    idCardNational: formData.identityInfo.wechat?.backMediaId,

    // 提现账户信息
    bankAccountType: getBankAccountType(formData.merchantType, formData.withdrawalAccountInfo.type),
    accountName: formData.withdrawalAccountInfo.bankCard.name,
    accountNumber: formData.withdrawalAccountInfo.bankCard.number,
    bankName: formData.withdrawalAccountInfo.bankCard.branch,
    accountBank: formData.withdrawalAccountInfo.bankCard.shortName,

    //经营场景信息
    storeName: formData.merchantBasicInfo.shortName,
    storeUrl: "https://hdkf.haidanyun.com",

    // 联系人信息
    contactType: "65", //法人
    contactName: formData.identityInfo.basic.name,
    contactIdCardNumber: formData.contactInfo.idNumber,
    mobilePhone: formData.contactInfo.phone,

    // 客服电话
    customerServicePhone: formData.contactInfo.servicePhone,
    businessAdditionDesc: formData.merchantType===MerchantType.SELLER ? '该商户已持续从事电子商务经营活动满6个月，且期间经营收入累计超过20万元。' : '',
  };

  // 受益人信息（仅企业类型需要）
  if (formData.merchantType === MerchantType.ENTERPRISE && formData.beneficiaryInfo) {
    // 受益人类型：从 formData 中获取 owner 字段
    apiData.owner = formData.owner;

    // 受益人基本信息
    if (formData.beneficiaryInfo.basic) {
      apiData.uboIdDocName = formData.beneficiaryInfo.basic.name;
      apiData.uboIdDocNumber = formData.beneficiaryInfo.basic.idNumber;
      apiData.uboIdDocPeriodBegin = formatDateForApi(formData.beneficiaryInfo.basic.startDate);
      apiData.uboIdDocPeriodEnd = formatDateForApi(formData.beneficiaryInfo.basic.endDate);
      apiData.uboIdDocAddress = formData.beneficiaryInfo.basic.address;
    }

    // 受益人证件照片
    if (formData.beneficiaryInfo.wechat) {
      apiData.uboIdDocCopy = formData.beneficiaryInfo.wechat.frontMediaId;
      apiData.uboIdDocCopyBack = formData.beneficiaryInfo.wechat.backMediaId;
    }
  }

  // 如果是修改，添加ID
  if (id) {
    (apiData as any).id = id;
  }

  return apiData;
};

/**
 * 调试模式相关工具函数
 */

// 调试场景枚举
export enum DebugScenario {
  NONE = '',
  BASIC = 'basic',           // 基础调试
  FORM_VALIDATION = 'form',  // 表单验证调试
  API_CALLS = 'api',         // API调用调试
  OCR_TESTING = 'ocr',       // OCR识别测试
  MOCK_DATA = 'mock',        // 模拟数据测试
  ENTERPRISE = 'enterprise', // 企业场景测试
  INDIVIDUAL = 'individual', // 个体工商户场景测试
  PERSONAL = 'personal'      // 个人场景测试
}

// 获取URL参数的通用函数
const getUrlParam = (paramName: string): string | null => {
  if (typeof window === 'undefined') {
    return null;
  }

  // 在Taro H5环境中，URL参数可能在hash后面
  const searchUrl = window.location.search;
  const hashUrl = window.location.hash;

  // 尝试从search参数中获取
  let urlParams = new URLSearchParams(searchUrl);
  let param = urlParams.get(paramName);

  // 如果search中没有，尝试从hash中获取
  if (param === null && hashUrl.includes('?')) {
    const hashSearch = hashUrl.split('?')[1];
    urlParams = new URLSearchParams(hashSearch);
    param = urlParams.get(paramName);
  }

  return param;
};

// 检查URL中是否存在指定参数（无论是否有值）
const hasUrlParam = (paramName: string): boolean => {
  if (typeof window === 'undefined') {
    return false;
  }

  // 在Taro H5环境中，URL参数可能在hash后面
  const searchUrl = window.location.search;
  const hashUrl = window.location.hash;

  // 检查search参数中是否存在
  let urlParams = new URLSearchParams(searchUrl);
  if (urlParams.has(paramName)) {
    return true;
  }

  // 检查hash中是否存在
  if (hashUrl.includes('?')) {
    const hashSearch = hashUrl.split('?')[1];
    urlParams = new URLSearchParams(hashSearch);
    if (urlParams.has(paramName)) {
      return true;
    }
  }

  return false;
};

export const validateFormData = (formData: any): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];


  const merchantType: MerchantType | undefined = formData?.merchantType;
  const isEnterprise = merchantType === MerchantType.ENTERPRISE;
  const isIndividual = merchantType === MerchantType.INDIVIDUAL;
  const isPersonal = merchantType === MerchantType.PERSONAL;
  const isSeller = merchantType === MerchantType.SELLER;

  // 营业执照验证（企业、个体工商户需要；）
  if (isEnterprise || isIndividual) {
    if (!formData?.businessLicenseInfo?.image) {
      errors.push('请上传营业执照');
    }
    const businessName = formData?.businessLicenseInfo?.name;
    if (!businessName || (typeof businessName === 'string' && !businessName.trim())) {
      errors.push('请输入商户名称');
    }
    const creditCode = formData?.businessLicenseInfo?.creditCode;
    if (!creditCode || (typeof creditCode === 'string' && !creditCode.trim())) {
      errors.push('请输入统一社会信用代码');
    }
    const registerDate = formData?.businessLicenseInfo?.registerDate;
    if (!registerDate || (typeof registerDate === 'string' && !registerDate.trim())) {
      errors.push('请选择注册日期');
    }
    const registerArea = formData?.businessLicenseInfo?.registerArea;
    if (!registerArea || (typeof registerArea === 'string' && !registerArea.trim())) {
      errors.push('请选择注册地区');
    }
    const businessAddress = formData?.businessLicenseInfo?.businessAddress;
    if (!businessAddress || (typeof businessAddress === 'string' && !businessAddress.trim())) {
      errors.push('请输入经营地址');
    }
  }

  // 商户基本信息验证
  const shortName = formData?.merchantBasicInfo?.shortName;
  if (!shortName || (typeof shortName === 'string' && !shortName.trim())) {
    errors.push('请输入商户简称');
  }
  const industry = formData?.merchantBasicInfo?.industry;
  if (!industry || (typeof industry === 'string' && !industry.trim())) {
    errors.push('请选择所属行业');
  }

  // 身份证信息验证
  const idName = formData?.identityInfo?.basic?.name;
  if (!idName || (typeof idName === 'string' && !idName.trim())) {
    errors.push('请输入姓名');
  }
  const idNumber = formData?.identityInfo?.basic?.idNumber;
  if (!idNumber || (typeof idNumber === 'string' && !idNumber.trim())) {
    errors.push('请输入身份证号');
  }
  const startDate = formData?.identityInfo?.basic?.startDate;
  if (!startDate || (typeof startDate === 'string' && !startDate.trim())) {
    errors.push('请选择身份证有效期开始日期');
  }
  const endDate = formData?.identityInfo?.basic?.endDate;
  if (!endDate || (typeof endDate === 'string' && !endDate.trim())) {
    errors.push('请选择身份证有效期结束日期');
  }
  // 证件住址：仅企业类型要求填写；个体、个人不校验证件住址（UI也不显示）
  const address = formData?.identityInfo?.basic?.address;
  if (isEnterprise) {
    if (!address || (typeof address === 'string' && !address.trim())) {
      errors.push('请输入证件住址');
    }
  }
  if (!formData?.identityInfo?.images?.front) {
    errors.push('请上传身份证正面');
  }
  if (!formData?.identityInfo?.images?.back) {
    errors.push('请上传身份证反面');
  }

  // 受益人验证：仅企业且 owner=false（非法人受益人）时要求
  if (isEnterprise && formData?.owner === false) {
    const bName = formData?.beneficiaryInfo?.basic?.name;
    if (!bName || (typeof bName === 'string' && !bName.trim())) {
      errors.push('请输入受益人姓名');
    }
    const bId = formData?.beneficiaryInfo?.basic?.idNumber;
    if (!bId || (typeof bId === 'string' && !bId.trim())) {
      errors.push('请输入受益人身份证号');
    }
    const bStart = formData?.beneficiaryInfo?.basic?.startDate;
    if (!bStart || (typeof bStart === 'string' && !bStart.trim())) {
      errors.push('请选择受益人有效期开始日期');
    }
    const bEnd = formData?.beneficiaryInfo?.basic?.endDate;
    if (!bEnd || (typeof bEnd === 'string' && !bEnd.trim())) {
      errors.push('请选择受益人有效期结束日期');
    }
    const bAddr = formData?.beneficiaryInfo?.basic?.address;
    if (!bAddr || (typeof bAddr === 'string' && !bAddr.trim())) {
      errors.push('请输入受益人证件住址');
    }
    if (!formData?.beneficiaryInfo?.images?.front) {
      errors.push('请上传受益人身份证正面');
    }
    if (!formData?.beneficiaryInfo?.images?.back) {
      errors.push('请上传受益人身份证反面');
    }
  }

  // 提现账户验证
  const bankCardName = formData?.withdrawalAccountInfo?.bankCard?.name;
  if (!bankCardName || (typeof bankCardName === 'string' && !bankCardName.trim())) {
    errors.push('请输入开户名称');
  }
  const bankCardNumber = formData?.withdrawalAccountInfo?.bankCard?.number;
  if (!bankCardNumber || (typeof bankCardNumber === 'string' && !bankCardNumber.trim())) {
    errors.push('请输入银行账号');
  }
  const bankCardBranch = formData?.withdrawalAccountInfo?.bankCard?.branch;
  if (!bankCardBranch || (typeof bankCardBranch === 'string' && !bankCardBranch.trim())) {
    errors.push('请输入所属支行');
  }

  // 联系人信息验证
  const phone = formData?.contactInfo?.phone;
  if (!phone || (typeof phone === 'string' && !phone.trim())) {
    errors.push('请输入手机号');
  }
  if (phone && typeof phone === 'string' && !/^1[3-9]\d{9}$/.test(phone)) {
    errors.push('请输入正确的手机号');
  }
  const verificationCode = formData?.contactInfo?.verificationCode;
  if (!verificationCode || (typeof verificationCode === 'string' && !verificationCode.trim())) {
    errors.push('请输入验证码');
  }

  // 客服电话验证
  const servicePhone = formData?.contactInfo?.servicePhone;
  if (servicePhone && typeof servicePhone === 'string' && servicePhone.trim()) {
    // 如果填写了客服电话，验证格式（支持固话和手机号）
    const phoneRegex = /^(1[3-9]\d{9}|0\d{2,3}-?\d{7,8})$/;
    if (!phoneRegex.test(servicePhone.replace(/\s+/g, ''))) {
      errors.push('请输入正确的客服电话格式');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * 生成调试模拟数据
 */
export const generateMockData = (scenario: DebugScenario): Partial<MerchantApplicationFormData> | null => {

  switch (scenario) {
    case DebugScenario.ENTERPRISE:
      return {
        merchantType: MerchantType.ENTERPRISE,
        businessLicenseInfo: {
          name: '测试企业有限公司',
          creditCode: '91110000000000000X',
          registerDate: '2020/01/01',
          registerArea: '北京市朝阳区',
          businessAddress: '北京市朝阳区测试街道123号',
          legalPerson: '张三',
          image: null,
          wechat: {}
        },
        merchantBasicInfo: {
          shortName: '测试企业',
          industry: '软件和信息技术服务业',
          industryPickerVisible: false
        },
        identityInfo: {
          basic: {
            name: '张三',
            idNumber: '110101199001011234',
            startDate: '2020/01/01',
            endDate: '2030/01/01',
            address: '北京市朝阳区测试街道456号'
          },
          images: { front: null, back: null },
          wechat: {}
        },
        withdrawalAccountInfo: {
          type: 'dgzh',
          bankCard: {
            name: '测试企业有限公司',
            number: '621226********90123',
            branch: '中国工商银行北京朝阳支行'
          },
          image: null,
          wechat: {}
        },
        contactInfo: {
          name: '张三',
          idNumber: '110101199001011234',
          phone: '***********',
          verificationCode: '123456',
          servicePhone: '010-********'
        },
        owner: true
      };

    case DebugScenario.INDIVIDUAL:
      return {
        merchantType: MerchantType.INDIVIDUAL,
        businessLicenseInfo: {
          name: '张三个体工商户',
          creditCode: '92110000000000000Y',
          registerDate: '2021/06/01',
          registerArea: '上海市浦东新区',
          businessAddress: '上海市浦东新区测试路789号',
          legalPerson: '张三',
          image: null,
          wechat: {}
        },
        merchantBasicInfo: {
          shortName: '张三小店',
          industry: '零售业',
          industryPickerVisible: false
        },
        identityInfo: {
          basic: {
            name: '张三',
            idNumber: '310115199205151234',
            startDate: '2022/05/15',
            endDate: '2032/05/15',
            address: '上海市浦东新区测试路456号'
          },
          images: { front: null, back: null },
          wechat: {}
        },
        withdrawalAccountInfo: {
          type: 'grzh',
          bankCard: {
            name: '张三',
            number: '622848********90123',
            branch: '中国农业银行上海浦东支行'
          },
          image: null,
          wechat: {}
        },
        contactInfo: {
          name: '张三',
          idNumber: '310115199205151234',
          phone: '***********',
          verificationCode: '654321',
          servicePhone: '021-********'
        },
        owner: true
      };

    case DebugScenario.PERSONAL:
      return {
        merchantType: MerchantType.PERSONAL,
        identityInfo: {
          basic: {
            name: '李四',
            idNumber: '******************',
            startDate: '2023/12/12',
            endDate: '2033/12/12',
            address: '广东省深圳市福田区测试大道888号'
          },
          images: { front: null, back: null },
          wechat: {}
        },
        withdrawalAccountInfo: {
          type: 'grzh',
          bankCard: {
            name: '李四',
            number: '621700********90123',
            branch: '中国建设银行深圳福田支行'
          },
          image: null,
          wechat: {}
        },
        contactInfo: {
          name: '李四',
          idNumber: '******************',
          phone: '***********',
          verificationCode: '789012',
          servicePhone: ''
        },
        owner: true
      };

    default:
      return null;
  }
};

/**
 * 获取审核状态显示文本
 */
export const getStatusText = (status: number): { text: string; color: string } => {
  switch (status) {
    case 0:
      return { text: '待审核', color: '#FF7D00' };
    case 1:
      return { text: '审核通过', color: '#00B42A' };
    case 2:
      return { text: '审核不通过', color: '#F53F3F' };
    default:
      return { text: '未知状态', color: '#86909C' };
  }
};
