@import "@arco-design/mobile-react/style/mixin.less";
@import "@/utils/css/variables.less";

// 页面根容器样式
[id^="/pageCustomerService/ai/chat"] {
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    // background-color: #07C160 !important;
    background-color: @dark-background-color !important;
  });

  // AI聊天页面主容器
  .ai-chat-page {
    position: relative;
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;

    // 加载容器
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      flex: 1;
      gap: 16px;

      .arco-loading {
        color: var(--primary-color);
      }

      text {
        font-size: 14px;
        .use-var(color, sub-font-color);
        .use-dark-mode-query({
        color: var(--dark-sub-font-color) !important;
      });
      }
    }

    // 聊天容器
    .chat-container {
      flex: 1;
      overflow: hidden;
      padding: 0 10px;

      .message-list {
        height: 100%;
        padding: 16px 0;

        // 底部占位，避免输入框遮挡
        .holder {
          height: 100px; // 与输入容器高度一致或略大
          flex-shrink: 0;
        }

        .empty-messages {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 200px;

          text {
            font-size: 14px;
            .use-var(color, sub-info-font-color);
            .use-dark-mode-query({
            color: var(--dark-sub-info-font-color) !important;
          });
          }
        }
      }
    }

    // 消息项
    .message-item {
      margin-bottom: 16px;
      text-align: center; // 使 system-message 可通过 inline-* 居中

      // 时间显示
      .message-time {
        text-align: center;
        margin-bottom: 12px;
        font-size: 12px;
        .use-var(color, sub-info-font-color);
        .use-dark-mode-query({
        color: var(--dark-sub-info-font-color) !important;
      });
      }
      .system-message {
        // tokens from design
        // --sysmsg-bg: paint_525:79244 -> rgba(0, 0, 0, 0.0314)
        // --sysmsg-text: paint_525:79229 -> #52596C
        // --sysmsg-font-size: font_525:79249.size -> 20px
        // --sysmsg-font-family: font_525:79249.family -> PingFang SC
        // --sysmsg-font-weight: font_525:79249.style -> Medium(500)
        // --sysmsg-padding: flexContainer.padding -> 8px 20px
        // --sysmsg-radius: borderRadius -> 100px
        --sysmsg-bg: rgba(0, 0, 0, 0.0314);
        --sysmsg-text: #52596c;
        --sysmsg-font-size: 12px;
        --sysmsg-font-family: "PingFang SC", -apple-system, "Helvetica Neue",
          Arial, "Noto Sans", "Microsoft YaHei", sans-serif;
        --sysmsg-font-weight: 500;
        --sysmsg-padding: 8px 20px;
        --sysmsg-radius: 100px;

        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: var(--sysmsg-padding);
        background-color: var(--sysmsg-bg);
        border-radius: var(--sysmsg-radius);
        width: max-content;
        opacity: 1;

        .use-dark-mode-query({
        --sysmsg-bg: rgba(255, 255, 255, 0.08);
        --sysmsg-text: var(--dark-sub-info-font-color);
      });

        .system-text,
        text {
          font-size: var(--sysmsg-font-size);
          color: var(--sysmsg-text);
          font-family: var(--sysmsg-font-family);
          font-weight: var(--sysmsg-font-weight);
          line-height: 1.4;
        }
      }
      // 消息内容
      .message-content {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        // 统一头像与箭头尺寸变量
        --avatar-size: 36px;
        --arrow-size: 16px;

        // 用户消息（右侧）
        &.user-message {
          flex-direction: row-reverse;

          .bubble-wrapper {
            position: relative;
            display: inline-flex; // 让箭头与气泡作为一个整体对齐
            align-items: center; // 让箭头垂直居中
            // 让同级箭头也能读取到变量
            --bubble-bg: #07c160;
            --bubble-text-color: #fff;
          }

          .message-bubble {
            background-color: var(--bubble-bg);
            // background-color: var(--primary-color);
            // .use-dark-mode-query({
            //   background-color: var(--dark-primary-color) !important;
            // });

            .message-text {
              font-size: 14px;
              color: var(--bubble-text-color);
            }
          }

          .bubble-arrow.right {
            position: absolute;
            right: -8px; // 轻微压住，避免缝隙
            top: calc(
              (var(--avatar-size) - var(--arrow-size)) / 2
            ); // 固定到头像中心
            color: var(--bubble-bg);
            pointer-events: none;
            // z-index: auto;
          }

          // 右侧头像占位，等宽于头像
          .avatar-placeholder {
            width: var(--avatar-size);
            height: var(--avatar-size);
            flex-shrink: 0;
          }
        }

        // AI消息（左侧）
        &.ai-message {
          .bubble-wrapper {
            position: relative;
            display: inline-flex; // 让箭头与气泡作为一个整体对齐
            align-items: center; // 让箭头垂直居中
            // 让同级箭头也能读取到变量
            --bubble-bg: var(--cell-background-color);
            --bubble-text-color: var(--font-color);
            .use-dark-mode-query({
            // 在暗黑模式下仅覆盖变量，保持气泡与箭头同步使用同一背景色
            --bubble-bg: var(--dark-cell-background-color);
            --bubble-text-color: var(--dark-font-color);
          });
          }

          .message-bubble {
            .message-text {
              font-size: 14px;
              color: var(--bubble-text-color);
              // 文本颜色与变量保持一致，暗黑模式通过上方变量覆盖
            }
          }

          .bubble-arrow.left {
            position: absolute;
            left: -8px; // 轻微压住，避免缝隙
            top: calc(
              (var(--avatar-size) - var(--arrow-size)) / 2
            ); // 固定到头像中心
            color: var(--bubble-bg);
            pointer-events: none;
            // z-index: auto;
          }

          // 右侧头像占位，等宽于头像
          .avatar-placeholder {
            width: var(--avatar-size);
            height: var(--avatar-size);
            flex-shrink: 0;
          }
        }
      }

      // 头像
      .message-avatar {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        width: var(--avatar-size);
        height: var(--avatar-size);
        border-radius: 6px;
        overflow: hidden;
        .use-var(background-color, background-color) !important;
        .use-dark-mode-query({
        background-color: var(--dark-background-color) !important;
      });
        .kefu-avatar {
          width: 100%;
          height: 100%;
          .use-var(background-color, background-color) !important;
          .use-dark-mode-query({
          background-color: var(--dark-background-color) !important;
        });
        }
        // overflow: hidden;
      }

      // 消息气泡
      .message-bubble {
        position: relative;
        max-width: 70vw; // 改用视口宽，避免父容器影响
        padding: 6px 10px;
        border-radius: 4px;
        word-wrap: break-word;
        word-break: break-all;
        color: var(--bubble-text-color, inherit);
        background-color: var(--bubble-bg, inherit);
        display: inline-block; // 保证宽高由内容决定，箭头按容器中线定位
        text-align: left; // 强制消息文本左对齐，避免继承 .message-item 的居中样式

        .message-text {
          font-size: 15px;
          line-height: 22px;
          margin: 0;
          text-align: left;
        }

        // 加载状态
        .message-loading {
          display: flex;
          align-items: center;
          gap: 8px;

          .arco-loading {
            color: var(--sub-font-color);
            .use-dark-mode-query({
            color: var(--dark-sub-font-color) !important;
          });
          }

          text {
            font-size: 14px;
            .use-var(color, sub-font-color);
            .use-dark-mode-query({
            color: var(--dark-sub-font-color) !important;
          });
          }
        }
      }
    }

    // 输入容器
    .input-container {
      position: fixed;
      left: 0;
      right: 0;
      .use-var(background-color, background-color);
      .use-dark-mode-query({
      background-color: var(--dark-background-color) !important;
    });
      // border-top: 1px solid var(--line-color);
      // .use-dark-mode-query({
      //   border-top-color: var(--dark-line-color) !important;
      // });
      // padding: 12px 16px;
      transition: bottom 0.3s ease;
      z-index: 100;

      .input-wrapper {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 10px 15px;

        gap: 12px;

        .arco-input-wrap {
          padding: 0;
          width: 100%;
        }

        .message-input {
          flex: 1;
          min-width: 0;
          min-height: 40px;
          max-height: 120px;
          background-color: #f8f8f8;
          .use-var(background-color, container-background-color);
          .use-dark-mode-query({
          background-color: var(--dark-container-background-color) !important;
        });
          border-radius: 6px;
          padding: 5px 15px;
          font-size: 15px;
          .use-var(color, font-color);
          .use-dark-mode-query({
          color: var(--dark-font-color) !important;
        });

          &::placeholder {
            .use-var(color, sub-info-font-color);
            .use-dark-mode-query({
            color: var(--dark-sub-info-font-color) !important;
          });
          }

          &:focus {
            border-color: var(--primary-color);
            .use-dark-mode-query({
            border-color: var(--dark-primary-color) !important;
          });
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }

        .send-button {
          flex-shrink: 0;
          height: 40px;
          // padding: 0 20px;
          border-radius: 6px;
          font-size: 14px;
          transition: all 0.3s ease;

          &:not(.active) {
            background-color: var(--primary-color);
            .use-dark-mode-query({
            background-color: var(--dark-primary-color) !important;
          });
            color: #fff;
            border: none;
            opacity: 0.6;
          }

          &.active {
            background-color: var(--primary-color);
            .use-dark-mode-query({
            background-color: var(--dark-primary-color) !important;
          });
            color: #fff;
            border: none;
          }
        }
      }
    }

    // 调试面板
    .debug-panel {
      position: fixed;
      top: 80px;
      right: 16px;
      z-index: 1000;

      .debug-indicator {
        background-color: #007aff;
        color: #fff;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
      }

      .debug-buttons {
        margin-top: 8px;
        display: flex;
        flex-direction: column;
        gap: 4px;

        .arco-btn {
          font-size: 12px;
          padding: 4px 8px;
          height: auto;
          min-height: 24px;
          background-color: rgba(0, 122, 255, 0.1);
          border: 1px solid #007aff;
          color: #007aff;

          &:hover {
            background-color: rgba(0, 122, 255, 0.2);
          }

          &:active {
            background-color: rgba(0, 122, 255, 0.3);
          }
        }
      }
    }
  }
}
