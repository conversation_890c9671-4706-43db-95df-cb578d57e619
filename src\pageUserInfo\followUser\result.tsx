import { View, Text } from '@tarojs/components'
import { Button, Image, Loading, Avatar } from '@arco-design/mobile-react'
import './result.less'
import Taro from '@tarojs/taro'
import { useState, useEffect } from 'react'
import { getFollowUserInfo } from '@/utils/api/common/common_user'
import YkNavBar from '@/components/ykNavBar'

export default function FollowResult() {
  const [friendInfo, setFriendInfo] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);

  useEffect(() => {
    // 从路由参数获取用户ID
    const params = Taro.getCurrentInstance().router?.params;
    console.log("[FollowResult] 路由参数:", JSON.stringify(params));
    
    if (params && params.uid) {
      // 🔧 直接通过API获取用户信息
      fetchFriendInfo(params.uid);
    } else {
      console.error("[FollowResult] 缺少用户ID参数");
      setDataLoading(false);
    }
  }, []);

  // 获取被关注用户信息
  const fetchFriendInfo = async (userId: string) => {
    try {
      setDataLoading(true);
      console.log("[FollowResult] 开始获取用户信息, userId:", userId);
      
      const res = await getFollowUserInfo({ userId });
      console.log("[FollowResult] 获取用户信息返回:", JSON.stringify(res));
      
      if (res && res.code === 0) {
        setFriendInfo(res.data);
        console.log("[FollowResult] 成功设置用户信息:", JSON.stringify(res.data));
      } else {
        console.error("[FollowResult] 获取用户信息失败:", res.msg);
      }
    } catch (err) {
      console.error("[FollowResult] 获取用户信息异常:", err);
    } finally {
      setDataLoading(false);
    }
  };

  // 跳转到首页
  const goToHome = () => {
    setIsLoading(true);
    Taro.reLaunch({ url: '/pages/index/index' });
  };

  const useDays = friendInfo.useDays || 0;
  const postCount = friendInfo.postCount || 0;
  
  return (
    <View className="follow-result">
      <YkNavBar switchTab title="关注成功" />

      {dataLoading ? (
        <View className="loading-container">
          <Loading />
          <Text className="loading-text">正在获取用户信息...</Text>
        </View>
      ) : (
        <View className="follow-info-container">
          {/* <Image 
            className='avatar' 
            src={friendInfo.avatar || require('../../assets/images/login/logo.png')} 
            bottomOverlap={null}
          /> */}
          <Avatar
            src={
              friendInfo.avatar || require("../../assets/images/login/logo.png")
            }
            size="medium"
          />
          <Text className="nickname">{friendInfo.nickname ? `${friendInfo.nickname} ` : ''}</Text>
          <Text className="user-info">
            Ta已经使用<Text className="user-info-number">{useDays}</Text>
            天，累计发布<Text className="user-info-number">{postCount}</Text>
            条图文
          </Text>
        <Button
            inline 
            className="goto-button"
            onClick={goToHome}
            loading={isLoading}
            size="medium"
        >
            立即前往
        </Button>
        </View>
      )}
    </View>
  );
}
