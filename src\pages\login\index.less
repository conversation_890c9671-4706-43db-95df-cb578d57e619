@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pages/login/index"] {
  .login {
    // min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    .rem(padding-top, 84);
    .use-var(background-color, background-color);
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });

    .logo-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      .rem(margin-top, 65);
      text-align: center;

      .logo {
        .rem(width, 70);
        .rem(height, 70);
        .rem(margin-bottom, 10);
      }

      .app-name {
        .text-medium();
        .rem(font-size, 18);
        .use-var(color, font-color);
        .use-dark-mode-query({
        color: @dark-font-color;
      });
      }
    }

    .login-buttons {
      // width: 100%;
      .rem(margin-top, 130);

      .wechat-login {
        .wechat-icon {
          .rem(width, 25);
          .rem(height, 25);
        }
        display: flex;
        align-items: center;
        justify-content: center;
        .rem(width, 290);
        .rem(height, 44);
        .rem(border-radius, 6);
        .rem(margin-bottom, 20);
      }

      .huawei-login {
        background-color:  #FF7391;
        .huawei-icon {
          .rem(width, 25);
          .rem(height, 25);
        }
        display: flex;
        align-items: center;
        justify-content: center;
        .rem(width, 290);
        .rem(height, 44);
        .rem(border-radius, 6);
        .rem(margin-bottom, 20);
      }

      .phone-login {
        .rem(width, 290);
        .rem(height, 44);
        .rem(border-radius, 6);
      }

      .ios-login {
        margin-bottom: 20px;
        background: #000;
        .ios-icon {
          .rem(width, 19);
          .rem(height, 22);
        }
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        .rem(width, 290);
        .rem(height, 44);
        .rem(border-radius, 6);
      }
    }

    .agreement {
      .rem(font-size, 10);
      .use-var(color, sub-info-font-color);
      .use-dark-mode-query({
      color: @dark-sub-info-font-color;
    });
      display: flex;
      align-items: center;
      .rem(margin-top, 15);

      .link {
        .use-var(color, primary-color);
      }
    }
  }


}
