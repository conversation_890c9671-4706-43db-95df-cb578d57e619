import { View, Text } from "@tarojs/components";
import "./detail.less";
import Taro, { useRouter } from "@tarojs/taro";
import { useState, useEffect } from "react";
import React from "react";
// 组件
import YkNavBar from "@/components/ykNavBar/index";
import  { useRef } from "react";
// 类型定义
interface DetailParams {
  id: string;
  type: string;
  amount: string;
  createTime: string;
  merchantName: string;
  accountNumber?: string;
  userNameBy?: string;
  orderId?: string;
  withdrawalId?: string;
  // 其他参数
}

interface DetailField {
  label: string;
  key?: string;
  formatter?: (value: any) => string;
  renderType?: 'text' | 'multiline' | 'custom';
  multilineData?: Array<{
    key: string;
    formatter?: (value: any) => string;
  }>;
  customRenderer?: (params: DetailParams) => React.ReactNode;
}

interface DetailConfig {
  title: string;
  amountColor: 'income' | 'expense';
  fields: Array<DetailField>;
}

export default function TransactionDetail() {
  const router = useRouter();
  const [params, setParams] = useState<DetailParams | null>(null);
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  // ==================== 生命周期 ====================
  useEffect(() => {
    const routerParams = router.params;
    if (routerParams.id) {
      setParams({
        id: decodeURIComponent(routerParams.id),
        type: decodeURIComponent(routerParams.type || 'income'),
        amount: decodeURIComponent(routerParams.amount || '0'),
        createTime: decodeURIComponent(routerParams.createTime || ''),
        merchantName: routerParams.merchantName ? decodeURIComponent(routerParams.merchantName) : '',
        accountNumber: routerParams.accountNumber ? decodeURIComponent(routerParams.accountNumber) : '',
        userNameBy: routerParams.userNameBy ? decodeURIComponent(routerParams.userNameBy) : '',
        orderId: routerParams.orderId ? decodeURIComponent(routerParams.orderId) : '',
        withdrawalId: routerParams.withdrawalId ? decodeURIComponent(routerParams.withdrawalId) : ''
        // 其他参数
      });
    } else {
      console.error('参数错误');
      Taro.navigateBack({ delta: 1 });
    }
  }, [router.params]);

  // ==================== 事件处理 ====================
  const handleBack = () => {
    Taro.navigateBack({ delta: 1 });
  };

  // ==================== 工具函数 ====================
  const formatTime = (timestamp: string): string => {
    const date = new Date(parseInt(timestamp));
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
  };

  const formatAmount = (amount: string): string => {
    const num = parseFloat(amount);
    return num.toFixed(2);
  };

  const generateOrderNumber = (id: string): string => {
    // 生成模拟订单号
    return `**************************`;
  };

  const generateFlowNumber = (id: string): string => {
    // 生成模拟流水号
    return `**************************`;
  };

  const maskBankCard = (accountNumber: string): string => {
    // 银行卡号脱敏处理
    if (!accountNumber) return '';
    const len = accountNumber.length;
    if (len <= 8) return accountNumber;
    return accountNumber.substring(0, 4) + '****' + accountNumber.substring(len - 4);
  };

  // ==================== 配置映射 ====================
  const getDetailConfig = (type: string): DetailConfig => {
    switch (type) {
      case 'withdraw_success':
        return {
          title: '提现',
          amountColor: 'expense',
          fields: [
            { label: '提现时间', key: 'createTime', formatter: formatTime },
            {
              label: '收款账户',
              renderType: 'multiline',
              multilineData: [
                { key: 'merchantName' },
                { key: 'accountNumber', formatter: maskBankCard }
              ]
            },
            { label: '提现单号', key: 'id', formatter: generateOrderNumber },
            { label: '流水单号', key: 'id', formatter: generateFlowNumber }
          ]
        };
      
      case 'withdraw_failed':
        return {
          title: '提现失败退回',
          amountColor: 'income',
          fields: [
            { label: '退回时间', key: 'createTime', formatter: formatTime },
            {
              label: '退回账户',
              renderType: 'multiline',
              multilineData: [
                { key: 'merchantName' },
                { key: 'accountNumber', formatter: maskBankCard }
              ]
            },
            { label: '提现单号', key: 'id', formatter: generateOrderNumber }
          ]
        };
      
      case 'transaction_fee':
        return {
          title: '交易手续费',
          amountColor: 'expense',
          fields: [
            { label: '交易时间', key: 'createTime', formatter: formatTime },
            { label: '订单编号', key: 'id', formatter: generateOrderNumber },
            { label: '流水单号', key: 'id', formatter: generateFlowNumber },
            { label: '交易说明', key: 'userNameBy', formatter: (value) => `"${value || '张三'}"购买商品的交易手续费` },
            // 示例：使用自定义渲染器
            {
              label: '费用详情',
              renderType: 'custom',
              customRenderer: (params) => (
                <View className="custom-fee-info">
                  <Text className="multiline-text">手续费率: 0.6%</Text>
                  <Text className="multiline-text">基础费用: ¥{(parseFloat(params.amount) * 0.006).toFixed(2)}</Text>
                </View>
              )
            }
          ]
        };
      
      case 'platform_fee':
        return {
          title: '平台手续费',
          amountColor: 'expense',
          fields: [
            { label: '交易时间', key: 'createTime', formatter: formatTime },
            { label: '订单编号', key: 'id', formatter: generateOrderNumber },
            { label: '流水单号', key: 'id', formatter: generateFlowNumber },
            { label: '交易说明', key: 'userNameBy', formatter: (value) => `"${value || '张三'}"购买商品的平台手续费` }
          ]
        };
      
      case 'income':
        return {
          title: '订单收入',
          amountColor: 'income',
          fields: [
            { label: '交易时间', key: 'createTime', formatter: formatTime },
            { label: '订单编号', key: 'id', formatter: generateOrderNumber },
            { label: '流水单号', key: 'id', formatter: generateFlowNumber },
            { label: '交易说明', key: 'userNameBy', formatter: (value) => `"${value || '张三'}"购买商品` }
          ]
        };
      
      case 'refund':
        return {
          title: '订单退款',
          amountColor: 'expense',
          fields: [
            { label: '退款时间', key: 'createTime', formatter: formatTime },
            { label: '订单编号', key: 'id', formatter: generateOrderNumber },
            { label: '流水单号', key: 'id', formatter: generateFlowNumber },
            { label: '退款说明', key: 'userNameBy', formatter: (value) => `"${value || '张三'}"购买商品退款` }
          ]
        };
      
      default:
        return {
          title: '交易详情',
          amountColor: 'income',
          fields: [
            { label: '交易时间', key: 'createTime', formatter: formatTime },
            { label: '交易说明', key: 'merchantName' }
          ]
        };
    }
  };

  // ==================== 渲染 ====================
  if (!params) {
    return (
      <View className="transactionDetailPage">
        {platform !== "WX" &&<YkNavBar title="交易详情" onClickLeft={handleBack} />}
        <View className="loading">
          <Text>加载中...</Text>
        </View>
      </View>
    );
  }

  const config = getDetailConfig(params.type);
  const amount = parseFloat(params.amount);
  const isIncome = config.amountColor === 'income';

  return (
    <View className="transactionDetailPage">
      {platform !== "WX" &&<YkNavBar title="交易详情" onClickLeft={handleBack} />}
      
      <View className="content">
        {/* 金额展示区域 */}
        <View className="amount-section">
          <Text className="transaction-type">{config.title}</Text>
          <Text className={`amount-text ${config.amountColor}`}>
            {isIncome ? '+' : '-'}{formatAmount(params.amount)}
          </Text>
        </View>

        {/* 详情信息区域 */}
        <View className="detail-section">
          <View className="detail-group">
            {config.fields.map((field, index) => {
              const renderFieldValue = () => {
                switch (field.renderType) {
                  case 'multiline':
                    return (
                      <View className="multiline-info">
                        {field.multilineData?.map((lineData, lineIndex) => {
                          let lineValue = params[lineData.key as keyof DetailParams] || '';
                          if (lineData.formatter) {
                            lineValue = lineData.formatter(lineValue);
                          }
                          // 为第一行提供默认值
                          if (lineIndex === 0 && !lineValue) {
                            lineValue = '福建朱雀科技有限公司';
                          }
                          return (
                            <Text key={lineIndex} className="multiline-text">{lineValue}</Text>
                          );
                        })}
                      </View>
                    );

                  case 'custom':
                    return field.customRenderer ? field.customRenderer(params) : null;

                  case 'text':
                  default:
                    let value = field.key ? (params[field.key as keyof DetailParams] || '') : '';
                    if (field.formatter) {
                      value = field.formatter(value);
                    }
                    return <Text className="detail-item-value-text">{value}</Text>;
                }
              };

              return (
                <View key={index} className="detail-item">
                  <View className="detail-item-label"><Text className="detail-item-label-text">{field.label}</Text></View>
                  <View className="detail-item-value">
                    {renderFieldValue()}
                  </View>
                </View>
              );
            })}
          </View>
        </View>
      </View>
    </View>
  );
}
