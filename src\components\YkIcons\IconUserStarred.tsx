import { IconProps } from './types';

const IconUserStarred: React.FC<IconProps> = ({
  color = '#6BD2FA',
  size = 16,
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    fill="none"
    version="1.1"
    width={size}
    height={size}
    viewBox="0 0 16 16"
    className={className}
  >
    <defs>
      <clipPath id="master_svg0_514_80721/411_85796">
        <rect x="0" y="0" width="16" height="16" rx="0" />
      </clipPath>
    </defs>
    <g clipPath="url(#master_svg0_514_80721/411_85796)">
      <g>
        <path
          d="M7.8927682730697635,10.909209573069763C8.013720373069763,10.484873173069763,8.376572973069763,10.242486373069763,8.860377673069763,10.182048173069763L10.009572373069764,10.000098573069764L9.828304673069763,9.878906573069763C9.707036373069764,9.757712673069763,9.586084773069762,9.636518873069763,9.465134073069763,9.454887773069764C9.404816973069764,9.333376273069764,9.404816973069764,9.212182373069762,9.344182373069764,9.030551273069763L9.344182373069764,8.606215373069762C9.344182373069764,8.485021973069763,9.404500373069764,8.424584273069764,9.404500373069764,8.363828073069763C9.464816473069764,8.303391373069763,9.464816473069764,8.242634673069762,9.525451073069764,8.182196973069763C9.585768073069763,8.121441673069764,9.646402673069764,8.060685473069764,9.706718773069763,8.060685473069764L9.887986573069764,7.879053973069763C9.948621173069764,7.818617273069763,10.009254773069763,7.757861473069763,10.069572773069764,7.636349573069763C10.129888873069763,7.515156673069763,10.190523473069764,7.4547184730697635,10.190523473069764,7.272769373069763C10.250840573069762,7.151575473069763,10.250840573069762,7.030064473069763,10.311475173069763,6.848432873069763C10.371792173069764,6.848432873069763,10.493060473069763,6.787995173069763,10.553695073069763,6.727239973069763C10.614327773069764,6.666484273069763,10.674645773069763,6.606046073069763,10.734961873069762,6.484534573069763C10.795277973069764,6.363341673069764,10.795277973069764,6.242147373069763,10.855913473069764,6.000078573069763L10.855913473069764,5.696935973069763C10.855913473069764,5.636498773069763,10.795597473069764,5.515304473069763,10.795597473069764,5.454548673069763L10.735280373069763,5.394111073069763C10.735280373069763,5.0909682730697625,10.735280373069763,4.666949673069763,10.674964273069763,4.3030513730697635C10.614648173069764,4.060664273069763,10.554012673069764,3.6970838730697633,10.493696573069764,3.394259073069763C10.372428273069763,3.030360473069763,10.251477573069764,2.727535873069763,10.009891873069764,2.4851487730697635C9.949575773069764,2.363955273069763,9.828624173069763,2.2427616730697633,9.586404173069763,2.121250213069763C9.646720273069763,1.9396190630697632,9.465453473069763,1.878863153069763,9.223233573069763,1.7576696230697633C9.041965873069763,1.6972319830697633,8.800062973069764,1.5760384630697633,8.558161173069763,1.5152826730697633C8.255625173069763,1.3333333730697632,8.013722273069764,1.3333333730697632,7.771502373069763,1.3333333730697632C7.590234173069764,1.3333333730697632,7.348332273069763,1.3333333730697632,7.106112373069763,1.3937710780697632C6.924845073069763,1.3937710780697632,6.6829427730697635,1.4542087830697632,6.501357473069763,1.575720373069763C6.320089173069763,1.636158113069763,6.017552773069763,1.7573516030697631,5.835967873069763,1.8785451030697633C5.654699673069763,1.9997385730697632,5.412797373069763,2.181687893069763,5.291528773069763,2.4845127730697634C5.110260873069763,2.7272177730697633,4.928675073069764,3.030042273069763,4.8683584730697635,3.332867073069763C4.747407273069763,3.6967657730697634,4.687090273069764,3.939152573069763,4.687090273069764,4.1815393730697625C4.626456173069763,4.484364373069763,4.626456173069763,4.848262873069763,4.626456173069763,5.151087673069763C4.566139373069763,5.272598673069764,4.505504973069764,5.333036573069763,4.505504973069764,5.454230173069763C4.384553773069763,5.635861773069763,4.384553773069763,5.817811373069763,4.384553773069763,5.878566173069763C4.384553773069763,5.999759573069763,4.384553773069763,6.120953473069763,4.444870873069763,6.302584973069763C4.444870873069763,6.4842160730697636,4.505187873069763,6.545289373069763,4.565821773069763,6.726920473069764L4.686773373069763,6.848114873069763C4.747090173069763,6.9085525730697634,4.807724373069763,6.9085525730697634,4.868041173069763,6.9085525730697634C4.928357473069763,7.090183573069763,4.928357473069763,7.272450773069763,4.988991873069763,7.3933266730697635C5.049626473069763,7.514519573069763,5.049626473069763,7.574957273069763,5.109943273069764,7.696151173069763C5.170259873069764,7.817662073069763,5.230894173069763,7.8780997730697635,5.291210973069763,7.938856473069763C5.412162173069763,8.060049373069763,5.533113373069763,8.181560873069763,5.714698673069763,8.302754773069763C5.835650373069763,8.423947673069764,5.895966873069763,8.545141573069763,5.895966873069763,8.848284173069764L5.895966873069763,9.211863873069763C5.895966873069763,9.333057773069763,5.895966873069763,9.393494973069764,5.835650373069763,9.515007373069762C5.775333273069763,9.636200273069763,5.714698673069763,9.696637473069764,5.654382073069764,9.878588073069762C5.593747473069763,9.878588073069762,5.4727967730697635,9.999780073069763,5.230894973069763,10.121293473069763C5.049626673069763,10.242486373069763,4.747090473069763,10.363681173069763,4.444553473069764,10.424118373069764C4.081382673069763,10.484555573069763,2.750920173069763,10.969647773069763,2.509018073069763,11.090840673069764C2.267115653069763,11.151278873069764,1.9648965630697632,11.272472773069763,1.783311133069763,11.515180373069763C1.6020431230697632,11.696809373069764,1.4204576830697633,11.939514373069763,1.3601408560697632,12.242339373069763C1.3601408560697632,12.787869373069762,1.299824018069763,12.909062373069764,1.3601408560697632,13.272644373069763C1.4204576910697633,13.575467373069763,1.4810919930697632,13.878610373069764,1.5414088030697632,13.939368373069764C1.6020431230697632,13.999803373069764,1.9045797030697633,14.060559373069763,2.2067987930697632,14.120999373069763C2.5699697730697633,14.181435373069764,5.049626673069763,14.666528373069763,7.892453573069763,14.666528373069763L8.981014573069764,14.666528373069763C8.920697573069763,14.545335373069763,8.920697573069763,14.423823373069764,8.981014573069764,14.242192373069763L9.162282373069763,12.969501373069763L8.194672973069764,12.060707373069762C7.892771573069763,11.757565373069763,7.771502873069763,11.333228473069763,7.8927682730697635,10.909209573069763ZM14.666667373069764,11.212033673069763L12.670814373069764,10.969647773069763L11.702888373069763,9.090988973069763L10.856231073069763,10.969647773069763L8.799744973069764,11.212033673069763L10.251159073069763,12.666673373069763L9.887987473069764,14.666526373069763L11.702573373069763,13.757731373069763L13.517158373069764,14.666526373069763L13.154304373069763,12.666673373069763L14.666667373069764,11.212033673069763Z"
          fill={color}
          fillOpacity="1"
          style={{ mixBlendMode: 'normal' }}
        />
      </g>
    </g>
  </svg>
);

export default IconUserStarred;
