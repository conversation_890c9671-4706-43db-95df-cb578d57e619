@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageDynamic/imagePreview/index"] {
  background: #000 !important;
  .image-preview-page {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 4000;
    display: flex;
    flex-direction: column;

    .nav-top {
      background: #000;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      z-index: 4002;
      transition: opacity 0.3s;

      .nav-content {
        display: flex;
        align-items: center;
        padding: 16px;

        .back-button {
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;

          .back-icon {
            width: 16px;
            height: 16px;
            color: #fff;
          }
        }

        .page-indicator {
          flex: 1;
          text-align: center;
          color: #fff;
          font-size: 16px;
          font-weight: bold;
        }
      }
    }

    .title {
      background: #000;
      position: fixed;
      bottom: 108px; // 在底部控制栏上方
      left: 0;
      right: 0;
      padding: 0 15px;
      z-index: 4002;
      transition: opacity 0.3s;
      max-height: 120px; // 设置最大高度，约等于原来3行的高度
      overflow-y: auto; // 允许垂直滚动

      // 自定义滚动条样式（可选）
      &::-webkit-scrollbar {
        width: 3px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 3px;

        &:hover {
          background: rgba(255, 255, 255, 0.5);
        }
      }

      text {
        color: #fff;
        font-size: 13px;
        text-align: left;
        word-wrap: break-word; // 允许长单词换行
        white-space: pre-wrap; // 保留换行符和空格
        // 移除原来的行数限制样式
        // display: -webkit-box;
        // -webkit-line-clamp: 3;
        // line-clamp: 3;
        // -webkit-box-orient: vertical;
        // overflow: hidden;
      }
    }

    .footer-img {
      display: flex;
      align-items: center;
      position: fixed;
      bottom: 0;
      left: 0;
      width: 100%;
      padding: 16px 0;
      padding-bottom: calc(16px + env(safe-area-inset-bottom));
      padding-bottom: calc(
        16px + constant(safe-area-inset-bottom)
      ); /* 兼容iOS < 11.2 */
      transition: opacity 0.3s;
      z-index: 4002;

      .controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .download {
          margin-left: 16px;
          display: flex;
          align-items: center;
          .icon {
            width: 16px;
            height: 16px;
            margin-right: 9px;
            color: #fff;
          }

          text {
            color: #fff;
            font-size: 12px;
          }
        }

        .right-controls {
          display: flex;
          gap: 8px;

          
          .cart-btn {
            margin-right: 16px;
            padding: 8px 0;
            width: 88px;
            text-align: center;
            border-radius: 22px;
            font-size: 13px;
            color: #fff;
          }

          .share-btn{
            padding: 8px 0;
            width: 88px;
            text-align: center;
            border-radius: 22px;
            font-size: 13px;
            color: #fff;
          }

          .share-btn {
            background: #6cbe70;
          }

          .cart-btn {
            background: #f96527;
          }
        }
      }
    }

    .hidden {
      opacity: 0;
      pointer-events: none;
    }

    .carousel {
      width: 100%;
      height: 100vh;
      background: #000 !important;

      // Taro Swiper 组件样式
      .taro-swiper {
        background: #000 !important;

        .taro-swiper-container {
          background: #000 !important;
        }

        .taro-swiper-slide {
          background: #000 !important;
        }
      }

      // MovableArea 和 MovableView 的样式
      .marea,
      .taro-movable-area {
        background: #000 !important;
        height: 100vh;
        width: 100%;

        .mview,
        .taro-movable-view {
          background: #000 !important;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100vh;

          .image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
          }
        }
      }

      *{
        background: #000 !important;
      }

      // 直接图片的样式（备用）
      .image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
      }
    }
  }
}
