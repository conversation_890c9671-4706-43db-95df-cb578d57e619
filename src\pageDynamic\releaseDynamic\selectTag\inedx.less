@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pageDynamic/releaseDynamic/selectTag/index"] {
  .select-tag-page {
    min-height: 100vh;
    background: #f7f8fa;
    //暗黑模式
    .use-dark-mode-query({
    background-color: @dark-background-color;
});
    display: flex;
    flex-direction: column;
    padding-bottom: 70px;
  }

  .header {
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    padding: 16px 0 12px 0;
    background: #fff;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
  }

  .add-tag {
    background: #fff;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
    // color: #bcbcbc;
    .use-var(color, font-color);
    .use-dark-mode-query({
    color: @dark-font-color;
  });
    font-size: 15px;
    // margin-top: 10px;
    padding: 12px 16px;
  }

  .list-content {
    background: #fff;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
    color: #bcbcbc;
    font-size: 15px;
    margin-top: 10px;
    padding: 12px 16px;
  }

  .select-title {
    background: #f7f8fa;
    .use-dark-mode-query({
    background-color: @dark-card-background-color;
  });
    color: #8c8c8c;
    font-size: 15px;
    padding: 12px 16px 8px 16px;
  }

  .tag-group-list {
    background: #fff;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
    flex: 1;
    padding-bottom: 16px;
  }

  .tag-group {
    margin-bottom: 12px;
  }

  .group-title {
    color: #8c8c8c;
    font-size: 15px;
    margin: 12px 0 0 16px;
  }

  .tags-row {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
    padding-left: 16px;
    align-items: center;
  }

  .tag-item {
    margin-top: 10px;
    font-size: 14px;
    color: #222;
    background: #fff;
    border: 1px solid #e5e6eb;
    border-radius: 12px;
    padding: 0 16px;
    height: 32px;
    line-height: 32px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s;
    user-select: none;
  }
  .tag-item.selected {
    background: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
  }

  .tag-item-s {
    margin-top: 10px;
    font-size: 14px;
    color: #222;
    background: #fff;
    border: 1px solid #e5e6eb;
    border-radius: 12px;
    padding: 0 16px;
    height: 32px;
    line-height: 32px;
    margin-bottom: 8px;

    transition: all 0.2s;
    user-select: none;
  }
  .tag-item-s.selected {
    background: var(--primary-color);
    color: #fff;
    border-color: var(--primary-color);
  }

  .footer-btns {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
    display: flex;
    justify-content: space-between;
    padding: 12px 16px 24px 16px;
    z-index: 10;
  }

  .btn {
    flex: 1;
    height: 44px;
    border-radius: 8px;
    font-size: 16px;
    border: none;
    margin: 0 4px;
    cursor: pointer;
  }
  .btn-grey {
    background: #e5e6eb;
    color: #8c8c8c;
  }
  .btn-primary {
    background: var(--primary-color);
    color: #fff;
  }
}
