@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageUserInfo/editUserInfoItem/index"] {
  .edit {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    .use-var(background-color, background-color);
    .use-dark-mode-query({
        background-color: @dark-background-color;
    });
    .input-container {
      width: 100%;
      .rem(margin-top, 25);
      flex-direction: column;
      display: flex;

      .custom-input {
        box-sizing: border-box;
        flex: 1;
        .rem(margin-right, 15);
        .rem(margin-left, 15);
        .rem(font-size, 14);
        // color: #444444;
        .use-var(color, font-color);
        .use-dark-mode-query({
              color: @dark-font-color;
            });
        // .rem(border-width, 1);
        // border-style: solid;
        // border-color: #E7E9ED;
        // .use-var(border-color, line-color);
        // .use-dark-mode-query({
        //     border-color: @dark-line-color;
        // });
        // .rem(border-radius, 6);
        width: calc(100% - 30px);
        background: #f7f8fa;
        .use-dark-mode-query({
              background: @dark-card-background-color;
            });
        .rem(height, 49.5);
        // transition: border-color 0.3s;
        // &.focused {
        //     // border-color: #EB483F;
        //     .use-var(border-color, danger-color);
        //     .use-dark-mode-query({
        //         border-color: @dark-danger-color;
        //     });
        // }
      }

      .custom-textarea {
        flex: 1;
        box-sizing: border-box;
        .rem(margin-right, 15);
        .rem(margin-left, 15);
        .rem(margin-bottom, 15);
        .rem(font-size, 14);
        // color: #444444;
        .use-var(color, font-color);
        .use-dark-mode-query({
              color: @dark-font-color;
            });
        background: #f7f8fa;
        .use-dark-mode-query({
              background: @dark-card-background-color;
            });
        // .rem(border-width, 1);
        // border-style: solid;
        // border-color: #E7E9ED;
        // .use-var(border-color, line-color);
        // .use-dark-mode-query({
        //     border-color: @dark-line-color;
        // });
        // .rem(border-radius, 6);
        width: calc(100% - 30px);
        .rem(height, 107.5);
        // transition: border-color 0.3s;
      }

      .hint {
        width: 100%;
        visibility: hidden;
        margin-bottom: 15px;
        margin-top: 15px;
        // color: #eb483f;
        .use-var(color, danger-color);
        .use-dark-mode-query({
                color: @dark-danger-color;
            });
        .rem(margin-left, 15);
        .rem(margin-top, 5);
        .rem(font-size, 12);
        transition: visibility 0.3s, opacity 0.3s;
        opacity: 0;

        &.visible {
          visibility: visible;
          opacity: 1;
        }
      }
    }

    .footer-container {
      width: 100vw;
      display: flex;
      align-items: center;
      justify-content: center;

      .footer-button {
        .rem(margin-right, 15);
        .rem(margin-left, 15);
        .rem(height, 46);
        // .rem(border-radius, 50);
        // color: #ffffff;
        // .use-var(color, font-color);
        // .use-dark-mode-query({
        //     color: @dark-font-color;
        // });
        .rem(font-size, 15);
        font-weight: bold;
        // background-color: #6cbe70;
        // .use-var(background-color, primary-color);
        // .use-dark-mode-query({
        //     background-color: @dark-primary-color;
        // });

        // &.disabled {
        //     .use-var(background-color, disabled-color);
        //     .use-dark-mode-query({
        //       background-color: @dark-disabled-color;
        //     });
        //     // background-color: rgba(108, 190, 112, 0.3);
        //     // color: rgba(255, 255, 255, 0.6);
        // }
      }
    }
  }
}
