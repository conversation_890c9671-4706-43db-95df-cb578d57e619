import { View } from "@tarojs/components";
import { But<PERSON>, Mask<PERSON> } from "@arco-design/mobile-react";
import "./index.less";
import { Text } from "@tarojs/components";
import Taro from "@tarojs/taro";
const ProtocolMask = ({
  platformRef,
  visible,
  close,
  onConfirm,
  options = [],
  btnCloseText = "取消",
  title,
}) => {
  const handleUserAgreementClick = () => {
    Taro.navigateTo({
      url: `/pageSetting/webView/index?url=${USER_AGREEMENT}&name=用户协议`,
    });
    close();
  };
  const handlePrivacyPolicyClick = () => {
    Taro.navigateTo({
      url: `/pageSetting/webView/index?url=${PRIVACY_POLICY}&name=隐私政策`,
    });
    close();
  };

  const exitApp = () => {
    if (platformRef === "Android") {
      window.exitApp.exitApp();
    } else if (platformRef === "HM") {
      window.harmony.exitApp();
    }else if (platformRef === "IOS") {
      
    }
  };
  return (
    // <Popup
    //   visible={visible}
    //   direction="bottom"
    //   className="bottom-popup"
    //   maskClosable={true}
    //   close={onClose}
    // >
    //   <View className="popup-content">
    //     {title && <View className="popup-header">{title}</View>}
    //     <View className="popup-options">
    //       {options.map((item, index) => (
    //         <View
    //           key={index}
    //           className="popup-option"
    //           onClick={() => {
    //             if (onConfirm) {
    //               onClose();
    //               onConfirm(index);
    //             }
    //           }}
    //         >
    //           {item}
    //         </View>
    //       ))}
    //     </View>
    //     <View
    //       className="popup-cancel-btn"
    //       onClick={onClose}
    //     >
    //       {btnCloseText}
    //     </View>
    //   </View>
    // </Popup>

    <Masking
      maskClosable={false}
      className="demo-masking-img"
      visible={visible}
      close={close}
      // mountOnEnter={false}
      // unmountOnExit={false}
      onOpen={() => console.log("onOpen")}
      onClose={(scene) => console.log("onClose", scene)}
      contentAtCenter={true}
    >
      <View className="popup-content">
        <View className="popup-title">隐私政策</View>
        <View className="popup-desc2">
          <Text>欢迎您使用本软件。</Text>
          <Text>
            我们非常重视您的个人信息和隐私保护。为了更好地保障您的个人权益，在您使用我们的产品前，请您认真阅读
          </Text>
          <Text className="link" onClick={handleUserAgreementClick}>
            《用户协议》
          </Text>
          <Text>和</Text>
          <Text className="link" onClick={handlePrivacyPolicyClick}>
            《隐私政策》
          </Text>
          <Text>的全部内容。</Text>
          <Text>
            同意并接受全部条款后，开始使用我们的产品和服务。若选择不同意，将无法使用我们的产品和服务，并会退出应用。
          </Text>
        </View>
        <Button className="popup-confirm" onClick={onConfirm}>
          同意并继续
        </Button>
        <View className="popup-reject" onClick={exitApp}>
          狠心拒绝
        </View>
      </View>
    </Masking>
  );
};

export default ProtocolMask;
