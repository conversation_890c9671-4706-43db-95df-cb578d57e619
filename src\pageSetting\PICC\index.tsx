import { View, Text } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import React, { useEffect,useRef ,useState } from "react";
import { Cell, Image } from "@arco-design/mobile-react";
import Taro from "@tarojs/taro";
const userInfo = Taro.getStorageSync('userInfo') || {};
// 组件
import YkNavBar from "@/components/ykNavBar/index";


export default function aboutUs() {
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  return (
    <View className="aboutUsPageContent">
      {platform !== "WX" &&<YkNavBar title="" />}
      <View className="aboutUsPageContent-title">
        <View className="aboutUsPageContent-title-name">个人信息收集清单</View>
        <View className="aboutUsPageContent-title-desc">
          你可以查阅{APP_NAME_CN}对你的个人信息的收集情况。
        </View>
      </View>
      <View className="module">
        <View className="module-title">
          <Text className="module-title-text">用户基本信息</Text>
        </View>
        <View className="module-content">
          <Cell.Group bordered={false}>
            {/* 头像 */}
            <Cell
              label="头像"
              className="module-content-cell"
              text=""
              showArrow
              onClick={() => {
                Taro.navigateTo({
                  url: "/pageSetting/PICCAvatar/index",
                });
              }}
            ></Cell>
            {/* 昵称 */}
            <Cell
              label="昵称"
              className="module-content-cell"
              text=""
              showArrow
              onClick={() => {
                Taro.navigateTo({
                  url: "/pageSetting/PICCNickName/index",
                });
              }}
            ></Cell>
            {/* 手机号 */}
            {userInfo.mobile && (
            <Cell
              label="手机号"
              className="module-content-cell"
              text=""
              showArrow
              onClick={() => {
                Taro.navigateTo({
                  url: "/pageSetting/PICCPhone/index",
                });
              }}
            ></Cell>
            )}
          </Cell.Group>
        </View>
      </View>
    </View>
  );
}
