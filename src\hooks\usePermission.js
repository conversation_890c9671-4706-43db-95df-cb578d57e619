import { useState, useRef, useEffect } from "react";
import { Dialog } from "@arco-design/mobile-react";
import { useSetPermission, permissionsAtomArray } from "@/stores/permissionStore";
import { useRecoilValue } from 'recoil';
import wx from "weixin-webview-jssdk";
import {
  permissionMapping,
  initPermissionManager,
  hasPermission,
  checkPermissionCallBack,
  requestPermissionWeb,
  generatePermissionText,
  openSetting,
} from "@/utils/permission";

export const usePermission = (customWebPermissonConsent) => {
  // 权限相关状态
  const [isPermissionPopupVisible, setPermissionPopupVisible] = useState(false);
  const [permissionsState, setPermissionsState] = useState([]);
  const popupTitle = useRef("");
  const popupText = useRef("");
  const permissionHint = useRef("");
  const platformRef = useRef("H5");
  const authConfirmType = useRef(-1);
  const allPermissions = useRef([]);
  const setPermission = useSetPermission();
  const isRequestPermission = useRef(true);
  const customConsentCallback = useRef(customWebPermissonConsent);

  // 获取当前权限状态
  const currentPermissions = useRecoilValue(permissionsAtomArray);

  // 初始化权限管理器
  useEffect(() => {
    initPermissionManager(setPermission, setPermissionsState, currentPermissions);
  }, [setPermission, currentPermissions]);

  // 权限初始化
  const initPermissions = () => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    let isWx = window.__wxjs_environment === 'miniprogram';


    // 设置权限回调函数
    window.checkPermissionCallBack = (e) => {
      checkPermissionCallBack(
        e,
        authConfirmType,
        isRequestPermission,
        permissionHint,
        setPermissionPopupVisible,
        webPermissonConsent,
        webPermissonDeny,
        platformRef
      );
    };

    isRequestPermission.current = true;
    if (isAndroid) {
      platformRef.current = "Android";
      window.webPermissonDeny = webPermissonDeny;
      window.webPermissonConsent = webPermissonConsent;
    } else if (isIos) {
      platformRef.current = "IOS";
    } else if (isHM) {
      platformRef.current = "HM";
    } else if (isWx) {
      platformRef.current = "WX";
    } else {
      platformRef.current = "H5";
    }

    return () => {
      delete window.checkPermissionCallBack;
      delete window.webPermissonDeny;
      delete window.webPermissonConsent;
    };
  };

  // 权限被拒绝的处理
  const webPermissonDeny = () => {
    console.log("webPermissonDeny");
    isRequestPermission.current = false;
    handlePermissionClose();
    openSetPopup();
  };

  // 权限被同意的处理
  const webPermissonConsent = () => {
    console.log("webPermissonConsent");
    handlePermissionClose();

    // 调用自定义的权限同意回调
    if (customConsentCallback.current && typeof customConsentCallback.current === 'function') {
      return customConsentCallback.current();
    }

    return true;
  };

  // 打开权限设置弹框
  const openSetPopup = () => {
    const { title, text } = generatePermissionText(authConfirmType.current, platformRef.current);
    popupTitle.current = title;
    popupText.current = text;
    showDialog();
  };

  // 显示权限设置对话框
  const showDialog = () => {
    Dialog.confirm({
      title: popupTitle.current ? popupTitle.current : "",
      children: popupText.current ? popupText.current : "",
      okText: "去设置",
      cancelText: "取消",
      onOk: () => {
        openSetting(platformRef.current);
      },
      platform: "ios",
    });
  };

  // 关闭权限说明弹窗
  const handlePermissionClose = () => {
    setPermissionPopupVisible(false);
  };

  // 权限确认处理
  const handlePermissionConfirm = (type) => {
    if (platformRef.current === "Android") {
      window.requestPermission.requestPermission(type);
    } else {
      window.webkit.messageHandlers.requestPermission.postMessage(type);
    }
  };

  // 请求权限的封装函数
  const requestPermission = (type) => {
    return requestPermissionWeb(type, authConfirmType, isRequestPermission, platformRef);
  };

  return {
    // 状态
    isPermissionPopupVisible,
    permissionsState,
    permissionHint,
    platformRef,
    authConfirmType,
    
    // 函数
    initPermissions,
    hasPermission,
    requestPermission,
    handlePermissionClose,
    handlePermissionConfirm,
    webPermissonConsent,
    webPermissonDeny,
    
    // 组件需要的 props
    permissionPopupProps: {
      visible: isPermissionPopupVisible,
      type: authConfirmType.current,
      hintText: permissionHint.current,
      onClose: handlePermissionClose,
      onConfirm: handlePermissionConfirm,
    },
  };
};
