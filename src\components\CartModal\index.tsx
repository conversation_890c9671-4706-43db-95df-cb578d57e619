import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView } from '@tarojs/components';
import { ActionSheet, Image, Stepper, Cell, Tabs, Textarea, Button, Toast } from '@arco-design/mobile-react';
import Taro from '@tarojs/taro';
import './index.less';

interface CartModalProps {
  visible: boolean;
  onClose: () => void;
  productData: {
    id?: string;
    pictures?: string;
    content?: string;
    dynamic_title?: string;
    price?: number;
    colors?: Record<string, string>; // 新格式：{"132": "红色", "133": "白色"}
    specifications?: Record<string, string>; // 新格式：{"32": "38.5", "33": "40.5"}
    userId?: string; // 商家ID
  };
  merchantInfo?: {
    nickname?: string;
    avatar?: string;
  };
  onAddCart: (data: any) => Promise<void>;
  onBuyNow: (data: any) => void;
}

const CartModal: React.FC<CartModalProps> = ({
  visible,
  onClose,
  productData,
  merchantInfo,
  onAddCart,
  onBuyNow
}) => {
  // 状态管理
  const [stepperValues, setStepperValues] = useState<number[][]>([]);
  const [activeColorIndex, setActiveColorIndex] = useState(0);
  const [forceUpdateKey, setForceUpdateKey] = useState(0);
  const [batchPrevValue, setBatchPrevValue] = useState(0);
  const [remark, setRemark] = useState('');

  // 解析商品数据 - 支持新旧两种格式
  const getColorsData = () => {
    if (productData.colors && Object.keys(productData.colors).length > 0) {
      return {
        colorNames: Object.values(productData.colors),
        colorIds: Object.keys(productData.colors)
      };
    }
    return { colorNames: [], colorIds: [] };
  };

  const getSpecificationsData = () => {
    if (productData.specifications && Object.keys(productData.specifications).length > 0) {
      return {
        specificationNames: Object.values(productData.specifications),
        specificationIds: Object.keys(productData.specifications)
      };
    }
    return { specificationNames: [], specificationIds: [] };
  };

  const { colorNames: productColorArr, colorIds: productColorIds } = getColorsData();
  const { specificationNames: productSpecificationsArr, specificationIds: productSpecificationsIds } = getSpecificationsData();

  // 初始化stepperValues数组
  const initStepperValues = () => {
    console.log("productData", productData);
    const colorNum = productColorArr.length;
    const specificationsNum = productSpecificationsArr.length;

    // 如果既没有颜色也没有规格，创建一个1x1的数组
    if (colorNum === 0 && specificationsNum === 0) {
      setStepperValues([[0]]);
      return;
    }

    // 如果只有规格没有颜色，创建1xN的数组
    if (colorNum === 0 && specificationsNum > 0) {
      setStepperValues([Array(specificationsNum).fill(0)]);
      return;
    }

    // 创建二维数组：颜色数量 x 规格数量
    const newStepperValues = Array(Math.max(colorNum, 1)).fill(null).map(() => Array(specificationsNum).fill(0));
    setStepperValues(newStepperValues);
  };

  // 批量操作：对当前颜色的所有规格进行加减
  const handleBatchOperation = (operation: 'add' | 'subtract') => {
    const newStepperValues = [...stepperValues];
    const specificationsNum = productSpecificationsArr.length;

    // 当没有规格时，至少要有一个默认规格位置
    const actualSpecNum = specificationsNum === 0 ? 1 : specificationsNum;

    // 确保当前颜色的数组存在
    if (!newStepperValues[activeColorIndex]) {
      newStepperValues[activeColorIndex] = Array(actualSpecNum).fill(0);
    }

    // 如果当前数组长度不够，扩展到正确长度
    if (newStepperValues[activeColorIndex].length < actualSpecNum) {
      const currentArray = newStepperValues[activeColorIndex];
      newStepperValues[activeColorIndex] = [...currentArray, ...Array(actualSpecNum - currentArray.length).fill(0)];
    }

    // 直接对所有规格增加或减少1
    newStepperValues[activeColorIndex] = newStepperValues[activeColorIndex].map(value => {
      if (operation === 'add') {
        return Math.min(value + 1, 9999);
      } else {
        return Math.max(value - 1, 0);
      }
    });

    setStepperValues(newStepperValues);
    // 强制触发重新渲染，确保步进器组件同步
    setForceUpdateKey(prev => prev + 1);
  };

  // 计算总数量和总价格
  const getTotalQuantityAndPrice = () => {
    let totalQuantity = 0;
    stepperValues.forEach(colorSpecs => {
      colorSpecs.forEach(quantity => {
        totalQuantity += quantity;
      });
    });
    const totalPrice = totalQuantity * parseFloat(((productData.price || 0)/100).toFixed(2)) || 0;
    return { totalQuantity, totalPrice };
  };

  // 构建购物车数据
  const buildCartData = () => {
    const format: any[] = [];
    const colorNum = productColorArr.length;
    const specificationsNum = productSpecificationsArr.length;

    // 遍历每个颜色和规格的组合
    stepperValues.forEach((colorSpecs, colorIndex) => {
      colorSpecs.forEach((quantity, specIndex) => {
        if (quantity > 0) {
          const item: any = {
            quantity: quantity,
            priceSnapshot: (productData.price || 0)*100,//单位：分
          };

          // 如果有规格，添加规格ID
          if (specificationsNum > 0 && productSpecificationsIds.length > 0) {
            item.specificationId = productSpecificationsIds[specIndex] || '';
          }else{
            item.specificationId = 0;
          }

          // 如果有颜色，添加颜色ID
          if (colorNum > 0 && productColorIds.length > 0) {
            item.colorId = productColorIds[colorIndex] || '';
          }else{
            item.colorId = 0;
          }

          format.push(item);
        }
      });
    });

    return {
      userId: Taro.getStorageSync("userInfo").id,
      sellerUserId: productData.userId,
      dynamicId: productData.id,
      remark: remark,
      // type: 1,
      detail: format,
    };
  };

  // 构建立即购买数据
  const buildBuyNowData = () => {
    const details: any[] = [];

    // 遍历每个颜色和规格的组合
    stepperValues.forEach((colorSpecs, colorIndex) => {
      colorSpecs.forEach((quantity, specIndex) => {
        if (quantity > 0) {
          details.push({
            id: Date.now() + Math.random(), // 临时ID
            productColorId: productColorIds.length > 0 ? productColorIds[colorIndex] : null,
            productSpecificationsId: productSpecificationsIds.length > 0 ? productSpecificationsIds[specIndex] : null,
            productSpecificationsName: productSpecificationsArr.length > 0 ? productSpecificationsArr[specIndex] : null,
            productColorName: productColorArr.length > 0 ? productColorArr[colorIndex] : null,
            quantity: quantity,
            price: (productData.price || 0) * quantity,
            checked: true
          });
        }
      });
    });

    // 构建按商家分组的数据结构
    return [{
      shoppingCartList: [{
        id: Date.now(),
        shoppingCartSellerId: productData.userId, // 商家ID
        dynamicsId: productData.id,
        dynamicsContent: productData.content || productData.dynamic_title,
        dynamicsImage: productData.pictures ? productData.pictures.split(',')[0] : '',
        remark: remark,
        type: 1,
        price: productData.price, // 单价
        checked: true,
        details: details,
        isCollect: 0
      }],
      shoppingCartSellerId: productData.userId, // 商家ID
      shoppingCartSellerUserId: productData.userId, // 商家用户ID
      dynamicsUserName: merchantInfo?.nickname || '',
      dynamicsUserAvatar: merchantInfo?.avatar || '',
      checked: true
    }];
  };

  // 处理加入购物车
  const handleAddToCart = async () => {
    if (getTotalQuantityAndPrice().totalQuantity > 0) {
      const cartData = buildCartData();
      console.log("cartData", cartData);
      await onAddCart(cartData);
      // 重置表单
      initStepperValues();
      setRemark('');
      onClose();
    } else {
      Toast.error({ content: '请选择商品规格和数量' });
    }
  };

  // 处理立即购买
  const handleBuyNow = () => {
    if (getTotalQuantityAndPrice().totalQuantity > 0) {
      const buyNowData = buildBuyNowData();
      console.log("buyNowData", buyNowData);
      onBuyNow(buyNowData);
      onClose();
    } else {
      Toast.error({ content: '请选择商品规格和数量' });
    }
  };

  // 当弹框打开时初始化数据
  useEffect(() => {
    if (visible) {
      initStepperValues();
      setRemark('');
      setActiveColorIndex(0);
      setBatchPrevValue(0);
      setForceUpdateKey(0);
    }
  }, [visible, productData]);

  return (
    <ActionSheet
      className='demo-action-sheet'
      visible={visible}
      onClose={onClose}
      close={onClose}
      maskClosable
      subTitle={(
        <View className="move-item-my">
          <Image 
            radius={10} 
            className="move-img" 
            src={productData.pictures ? productData.pictures.split(',')[0] : ''} 
          />
          <View className="move-info-my-s">
            <Text className="move-title-text-my">
              {productData.content || productData.dynamic_title || ''}
            </Text>
            <Text className="move-price">¥{ ((productData.price || 0)/100).toFixed(2) || 0}</Text>
          </View>
        </View>
      )}
      items={[
        {
          content: (
            <View className="sku-container">
              {/* 颜色Tab切换 */}
              {productColorArr.length > 0 && (
                <View className="color-tabs">
                  <View className="color-tab-containerOut">
                  <Tabs
                    tabs={productColorArr.map(color => ({ title: color }))}
                    defaultActiveTab={activeColorIndex}
                    underlineAdaptive={true}
                    tabBarHasDivider={false}
                    duration={400}
                    tabBarArrange="start"
                    useCaterpillar
                    transitionDuration={400}
                    onAfterChange={(_, index) => {
                      setActiveColorIndex(index);
                    }}
                    className="color-tab-container"
                  >
                    {productColorArr.map((color, colorIndex) => (
                      <View key={colorIndex} className="color-tab-content">
                        {/* 这里可以放每个颜色对应的内容，现在暂时为空 */}
                      </View>
                    ))}
                  </Tabs>
                  </View>

                  {/* Tab内容 */}
                  <View className="color-tab-content">
                    <ScrollView
                      className="spec-list"
                      scrollY
                      style={{ height: '200px' }}
                      onTouchMove={(e) => e.stopPropagation()}
                    >
                      {/* 批量操作区域 */}
                      <Cell
                        label="批量"
                        className="no-border-cell"
                      >
                        <View className="batch-stepper-wrapper">
                          <Stepper
                            className='stepper-style'
                            min={-9999}
                            max={9999}
                            step={1}
                            value={0}
                            onChange={(val) => {
                              // 根据变化方向调用批量操作
                              if (val !== null && typeof val === 'number') {
                                if (val > batchPrevValue) {
                                  handleBatchOperation('add');
                                } else if (val < batchPrevValue) {
                                  handleBatchOperation('subtract');
                                }
                                setBatchPrevValue(val);
                              }
                            }}
                          />
                        </View>
                      </Cell>
                      <>  
                      {productSpecificationsArr.length>0&&(
                      productSpecificationsArr.map((spec, specIndex) => (
                        <Cell
                          label={spec}
                          className='no-border-cell'
                          key={`${productColorArr[activeColorIndex]}-${spec}-${specIndex}`}
                        >
                          <Stepper
                            key={`stepper-${activeColorIndex}-${specIndex}-${forceUpdateKey}`}
                            className='stepper-style'
                            min={0}
                            max={9999}
                            step={1}
                            defaultValue={stepperValues[activeColorIndex]?.[specIndex] ?? 0}
                            value={stepperValues[activeColorIndex]?.[specIndex] ?? 0}
                            onChange={val => {
                              if (val !== null && typeof val === 'number') {
                                // 限制输入值不超过 9999
                                const clampedVal = Math.min(Math.max(val, 0), 9999);
                                const newValues = [...stepperValues];
                                if (!newValues[activeColorIndex]) {
                                  newValues[activeColorIndex] = Array(productSpecificationsArr.length).fill(0);
                                }
                                newValues[activeColorIndex][specIndex] = clampedVal;
                                setStepperValues(newValues);
                                // 强制触发重新渲染，确保 defaultValue 同步
                                // setForceUpdateKey(prev => prev + 1);
                              }
                            }}
                          />
                        </Cell>
                      )))}

                      {productSpecificationsArr.length === 0 && (
                         <View className="batch-only-container">
                         <Cell
                           label='默认'
                           className='no-border-cell'
                         >
                           <Stepper
                             key={`stepper-default-${activeColorIndex}-${forceUpdateKey}`}
                             className='stepper-style'
                             min={0}
                             max={9999}
                             step={1}
                             defaultValue={stepperValues[activeColorIndex]?.[0] ?? 0}
                             value={stepperValues[activeColorIndex]?.[0] ?? 0}
                             onChange={val => {
                               if (val !== null && typeof val === 'number') {
                                 // 限制输入值不超过 9999
                                 const clampedVal = Math.min(Math.max(val, 0), 9999);
                                 const newValues = [...stepperValues];
                                 if (!newValues[activeColorIndex]) {
                                   newValues[activeColorIndex] = [0];
                                 }
                                 newValues[activeColorIndex][0] = clampedVal;
                                 setStepperValues(newValues);
                                 // 强制触发重新渲染，确保 defaultValue 同步
                                 // setForceUpdateKey(prev => prev + 1);
                               }
                             }}
                           />
                         </Cell>
                       </View>
                      )}
                      </>
                      


                    </ScrollView>
                  </View>
                </View>
              )}

              {/* 如果没有颜色，只显示规格 */}
              {productColorArr.length === 0 && productSpecificationsArr.length > 0 && (
                <ScrollView
                  className="spec-list"
                  scrollY
                  style={{ height: '200px' }}
                  onTouchMove={(e) => e.stopPropagation()}
                >
                  {/* 批量操作区域 */}
                  <Cell
                    label="批量"
                    className="no-border-cell"
                  >
                    <View className="batch-stepper-wrapper">
                      <Stepper
                        className='stepper-style'
                        min={-9999}
                        max={9999}
                        step={1}
                        value={0}
                        onChange={(val) => {
                          // 根据变化方向调用批量操作
                          if (val !== null && typeof val === 'number') {
                            if (val > batchPrevValue) {
                              handleBatchOperation('add');
                            } else if (val < batchPrevValue) {
                              handleBatchOperation('subtract');
                            }
                            setBatchPrevValue(val);
                          }
                        }}
                      />
                    </View>
                  </Cell>
                  {productSpecificationsArr.map((spec, specIndex) => (
                    <Cell
                      label={spec}
                      className='no-border-cell'
                      key={`spec-${specIndex}`}
                    >
                      <Stepper
                        key={`stepper-0-${specIndex}-${forceUpdateKey}`}
                        className='stepper-style'
                        min={0}
                        max={9999}
                        step={1}
                        defaultValue={stepperValues[0]?.[specIndex] ?? 0}
                        value={stepperValues[0]?.[specIndex] ?? 0}
                        onChange={val => {
                          if (val !== null && typeof val === 'number') {
                            // 限制输入值不超过 9999
                            const clampedVal = Math.min(Math.max(val, 0), 9999);
                            const newValues = [...stepperValues];
                            if (!newValues[0]) {
                              newValues[0] = Array(productSpecificationsArr.length).fill(0);
                            }
                            newValues[0][specIndex] = clampedVal;
                            setStepperValues(newValues);
                            // 强制触发重新渲染，确保 defaultValue 同步
                            // setForceUpdateKey(prev => prev + 1);
                          }
                        }}
                      />
                    </Cell>
                  ))}
                </ScrollView>
              )}

              {/* 如果既没有颜色也没有规格，显示批量操作 */}
              {productColorArr.length === 0 && productSpecificationsArr.length === 0 && (
                <View className="batch-only-container">
                  <Cell
                    label='默认'
                    className='no-border-cell'
                  >
                    <Stepper
                      key={`stepper-default-${activeColorIndex}-${forceUpdateKey}`}
                      className='stepper-style'
                      min={0}
                      max={9999}
                      step={1}
                      defaultValue={stepperValues[activeColorIndex]?.[0] ?? 0}
                      value={stepperValues[activeColorIndex]?.[0] ?? 0}
                      onChange={val => {
                        if (val !== null && typeof val === 'number') {
                          // 限制输入值不超过 9999
                          const clampedVal = Math.min(Math.max(val, 0), 9999);
                          const newValues = [...stepperValues];
                          if (!newValues[activeColorIndex]) {
                            newValues[activeColorIndex] = [0];
                          }
                          newValues[activeColorIndex][0] = clampedVal;
                          setStepperValues(newValues);
                          // 强制触发重新渲染，确保 defaultValue 同步
                          // setForceUpdateKey(prev => prev + 1);
                        }
                      }}
                    />
                  </Cell>
                </View>
              )}
            </View>
          ),
          style: {height:'100%'},
          onClick:() => true
        },
        {
          content: (
            <Textarea
              value={remark}
              onChange={(_, value) => setRemark(value)}
              onErrStatusChange={hasError => console.log('hasError', hasError)}
              autosize
              placeholder="可备注颜色，尺码..."
              border="none"
            />
          ),
          style: {height:'100%'},
          onClick:() => true
        },
        {
          content: (
            <View className='bottom-container'>
              <View className='total-info-container'>
                {/* 统计总件数 */}
                <Text className='cart-total-summary' style={{fontSize: 16, color: '#222', fontWeight: 500}}>
                  共<Text className='highlight-text' style={{color: '#e35848'}}>{getTotalQuantityAndPrice().totalQuantity}</Text>件
                  <Text className='highlight-text' style={{color: '#e35848'}}>￥{getTotalQuantityAndPrice().totalPrice.toFixed(2)}</Text>
                </Text>
              </View>
              <View className='button-container-out'>
                <Button
                  className={`cart-modal-add-btn ${getTotalQuantityAndPrice().totalQuantity > 0 ? 'active' : ''}`}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleAddToCart();
                  }}
                >加入购物车</Button>
                <Button
                  className={`cart-modal-buy ${getTotalQuantityAndPrice().totalQuantity > 0 ? 'cart-modal-buy-active' : ''}`}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleBuyNow();
                  }}
                >立即购买</Button>
              </View>
            </View>
          ),
          style: {height:120,marginTop:-30},
        }
      ]}
    />
  );
};

export default CartModal;
