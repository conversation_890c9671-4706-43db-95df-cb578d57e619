import React, { useState, useEffect, useRef } from 'react';
import { View, Text } from "@tarojs/components";
import {
  Dialog,
  Checkbox,
  Image,
  Button,
  Tabs,
  Sticky
} from '@arco-design/mobile-react';
import { getMyAlbumList, dynamicBatchOperate } from '@/utils/api/common/common_user';
import './index.less';
import YkNavBar from '@/components/ykNavBar';
import { IconLoadEmpty } from "@/components/YkIcons";
import SelectQuantityManager from '@/components/SelectQuantityPopup/SelectQuantityManager';
import Taro from '@tarojs/taro';
import { toast } from "@/utils/yk-common";

interface DynamicItem {
  id: string;
  pictures?: string;
  content?: string;
  price?: number;
  time?: string;
  createTime?: string;
  updateTime?: string;
}

interface PageInfo {
  page: number;
  limit: number;
  isLoad: boolean;
}

interface TabItem {
  type: number;
  name: string;
  pages: PageInfo;
}

const BatchOnOffShelf: React.FC = () => {
  const [sortType, setSortType] = useState(1);
  const [tabList, setTabList] = useState<TabItem[]>([
    {
      type: 1,
      name: '已上架',
      pages: {
        page: 1,
        limit: 20,
        isLoad: false
      }
    },
    {
      type: 2,
      name: '已下架',
      pages: {
        page: 1,
        limit: 20,
        isLoad: false
      }
    }
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const [current, setCurrent] = useState(0);
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  // 转换为Arco Tabs组件所需的格式
  const tabData = tabList.map((tab, index) => ({ title: tab.name, index }));
  const [delLoading, setDelLoading] = useState(false);
  const [dynamicList, setDynamicList] = useState<DynamicItem[]>([]);
  const [delIdlist, setDelIdlist] = useState<string[]>([]);
  const [allchange, setAllchange] = useState(false);
  const [dynamicList2, setDynamicList2] = useState<DynamicItem[]>([]);
  const [delIdlist2, setDelIdlist2] = useState<string[]>([]);
  const [allchange2, setAllchange2] = useState(false);
  const [paddingBottom, setPaddingBottom] = useState('0rpx');
  const [popupVisible, setPopupVisible] = useState(false);
  const [onOffPopupVisible, setOnOffPopupVisible] = useState(false);
  
  const lookImageListRef = useRef<any>(null);

  const loginUserInfo = Taro.getStorageSync('userInfo')
  useEffect(() => {
    // 获取用户信息

    // 检测平台类型设置底部边距
    const uaAll = navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    
    if (isIos) {
      setPaddingBottom('68rpx');
    } else if (isAndroid) {
      setPaddingBottom('0rpx');
    }

    getDynamicList();
  }, []);

  const getDynamicList = (tabIndex?: number) => {
    let pages, currDynamicList;
    const currentIndex = tabIndex !== undefined ? tabIndex : current;

    pages = tabList[currentIndex].pages;
    currDynamicList = currentIndex === 0 ? dynamicList : dynamicList2;

    let data = {
      pageNo: pages.page,
      pageSize: pages.limit,
      userId: loginUserInfo.id,
      isListed: tabList[currentIndex].type,
      sort: sortType,
      homePageCountType:1,
    };


    setIsLoading(true);
    getMyAlbumList(data).then((res: any) => {
      setIsLoading(false);
      if (res && res.code === 0) {
        let newList;
        console.log(res.data)

        // 确保res.data和res.data.list存在
        const dataList = res.data?.list || [];

        if (pages.page === 1) {
          newList = dataList;
        } else {
          if (dataList.length > 0) {
            newList = [...currDynamicList, ...dataList];
          } else {
            pages.page = pages.page - 1;
            newList = currDynamicList;
          }
        }

        // 更新tabList状态，确保React能检测到变化
        setTabList(prev => {
          const newTabList = [...prev];
          newTabList[currentIndex].pages.isLoad = true;

          if (dataList.length >= pages.limit) {
            newTabList[currentIndex].pages.page = pages.page + 1;
          }

          return newTabList;
        });

        if (currentIndex === 0) {
          setDynamicList(newList);
        } else {
          setDynamicList2(newList);
        }

        isNewAllChange(currentIndex);

      } else {
        toast("info", {
        content: res.message,
        duration: 2000
      })
      }
    }).catch((error) => {
      // 网络错误或其他异常时的处理
      setIsLoading(false);
      console.error('获取动态列表失败:', error);
      toast("info", {
        content: '网络异常，请稍后重试',
        duration: 2000
      });
    });
  };

  // 滚动到底部加载更多
  const handleScroll = (e: any) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target;
    const threshold = 50; // 距离底部50px时触发加载

    if (scrollHeight - scrollTop - clientHeight < threshold) {
      if (!isLoading) {
        getDynamicList();
      }
    }
  };

  const handleTabChange = (index: number) => {
    setCurrent(index);
    let isLoad = true;

    if (index === 0) {
      if (!tabList[index].pages.isLoad) {
        // 更新tabList状态
        setTabList(prev => {
          const newTabList = [...prev];
          newTabList[index].pages = {
            page: 1,
            limit: 20,
            isLoad: false
          };
          return newTabList;
        });
        setDelIdlist([]);
        isLoad = false;
      }
    } else {
      if (!tabList[index].pages.isLoad) {
        // 更新tabList状态
        setTabList(prev => {
          const newTabList = [...prev];
          newTabList[index].pages = {
            page: 1,
            limit: 20,
            isLoad: false
          };
          return newTabList;
        });
        setDelIdlist2([]);
        isLoad = false;
      }
    }

    if (!isLoad) {
      getDynamicList(index);
    }
  };

  const openLookImageList = (item: DynamicItem, index: number) => {
    if (lookImageListRef.current) {
      lookImageListRef.current.open(item, index, false, true);
    }
  };


  const refreshAll = () => {
    tabList[0].pages = {
      page: 1,
      limit: 20,
      isLoad: false
    };

    tabList[1].pages = {
      page: 1,
      limit: 20,
      isLoad: false
    };

    setDelIdlist([]);
    setDelIdlist2([]);
    // 通知相册列表刷新
    Taro.eventCenter.trigger('refreshAlbumList');
    getDynamicList();
  };

  const getRightsamllImage = (item: DynamicItem) => {
    let list: string[] = [];
    if (item.pictures) {
      list = item.pictures.split(',').filter(img => img.trim() !== '');
    }
    return list;
  };

  const getDelIdlist = (item: DynamicItem) => {
    const currentIdList = current === 0 ? [...delIdlist] : [...delIdlist2];
    const currentDynamicList = current === 0 ? dynamicList : dynamicList2;
    
    const index = currentIdList.indexOf(item.id);
    if (index === -1) {
      currentIdList.push(item.id);
    } else {
      currentIdList.splice(index, 1);
    }
    
    if (current === 0) {
      setDelIdlist(currentIdList);
      setAllchange(currentIdList.length === currentDynamicList.length);
    } else {
      setDelIdlist2(currentIdList);
      setAllchange2(currentIdList.length === currentDynamicList.length);
    }
  };

  const selectNum = (num: number) => {
    const currentDynamicList = current === 0 ? dynamicList : dynamicList2;
    let newIds: string[] = [];
    
    const selectCount = Math.min(num, currentDynamicList.length);
    for (let i = 0; i < selectCount; i++) {
      newIds.push(currentDynamicList[i].id);
    }
    
    if (current === 0) {
      setDelIdlist(newIds);
      setAllchange(newIds.length === currentDynamicList.length);
    } else {
      setDelIdlist2(newIds);
      setAllchange2(newIds.length === currentDynamicList.length);
    }
  };

  const getAllChange = () => {
    setPopupVisible(false);
    
    const currentIdList = current === 0 ? [...delIdlist] : [...delIdlist2];
    const currentDynamicList = current === 0 ? dynamicList : dynamicList2;
    
    if (currentIdList.length < currentDynamicList.length) {
      const newList: string[] = [];
      currentDynamicList.forEach(item => {
        newList.push(item.id);
      });
      
      if (current === 0) {
        setDelIdlist(newList);
        setAllchange(true);
      } else {
        setDelIdlist2(newList);
        setAllchange2(true);
      }
    } else {
      if (current === 0) {
        setDelIdlist([]);
        setAllchange(false);
      } else {
        setDelIdlist2([]);
        setAllchange2(false);
      }
    }
  };

  const isNewAllChange = (tabIndex?: number) => {
    const currentIndex = tabIndex !== undefined ? tabIndex : current;
    const currentIdList = currentIndex === 0 ? delIdlist : delIdlist2;
    const currentDynamicList = currentIndex === 0 ? dynamicList : dynamicList2;
    
    if (currentIdList.length === 0) {
      if (currentIndex === 0) {
        setAllchange(false);
      } else {
        setAllchange2(false);
      }
      return;
    }
    
    const allSelected = currentDynamicList.every(item => 
      currentIdList.includes(item.id)
    );
    
    if (currentIndex === 0) {
      setAllchange(allSelected);
    } else {
      setAllchange2(allSelected);
    }
  };


  const handleBatchOperation = () => {
    if (delLoading) return;
    
    const currentIdList = current === 0 ? delIdlist : delIdlist2;
    const ids = currentIdList.join(',');
    const data = {
      status: current === 0 ? '0' : '1',
      ids
    };
    
    setDelLoading(true);
    setOnOffPopupVisible(false);
    
    dynamicBatchOperate(data).then((res: any) => {
      setTimeout(() => {
        setDelLoading(false);
        if (res && res.code === 0) {
          toast("info", {
        content: current === 0 ? '下架成功' : '上架成功',
        duration: 2000
      })
          refreshAll();
        } else {
          toast("info", {
        content: res.message,
        duration: 2000
      })
        }
      }, 300);
    });
  };



  const renderDynamicItem = (item: DynamicItem, _itemIndex: number, isList1: boolean) => {
    const currentIdList = isList1 ? delIdlist : delIdlist2;
    const images = getRightsamllImage(item);

    return (
      <View key={item.id} className="dline">
        <View className="dline-left">
          <Checkbox
            checked={currentIdList.includes(item.id)}
            className="dline-left-change"
            onChange={() => getDelIdlist(item)}
          />
        </View>
        <View className="dline-right">

       { item.content&&(
          <View className={`dline-right-top ${!item.pictures || item.pictures === '' ? 'topBg' : ''}`}>
            <Text className="dynamic-title">{item.content}</Text>
          </View>
        )}
          {item.pictures && (
            <View className="dline-right-bottom">
              {images.slice(0, 5).map((imgUrl, imgIndex) => (
                <View
                  key={imgIndex}
                  className="imageItem"
                  onClick={() => openLookImageList(item, imgIndex)}
                >
                  <Image
                    src={imgUrl}
                    fit="cover"
                    className="imageItem-image"
                  />
                  {imgIndex === 4 && images.length > 5 && (
                    <View className="imageItem-mask">
                      <Text>+{images.length - 5}</Text>
                    </View>
                  )}
                </View>
              ))}
            </View>
          )}
        </View>
      </View>
    );
  };

  const renderEmptyContent = () => (
    <View className="not_content">
      {/* <TaroImage
        className="not_content-image"
        src={require("@/assets/images/common/not_content_trend.png")}
      /> */}
      <IconLoadEmpty className="not_content-image" />

      <Text>暂无动态内容</Text>
    </View>
  );

  return (
    <View>
      
      {/* {!showPage && <Loading type="spin" />} */}
      
      {platform !== "WX" &&<YkNavBar
        className={'navTop'}
        // onBack={getNavigateBack}
        title="批量上下架"
      />}
      
            <Tabs
              style={platform !== "WX" ? { top: '84px' } : { top: '0px' }}
              className="up-down-tabs"
              tabs={tabData}
              defaultActiveTab={current}
              tabBarHasDivider={false}
              tabBarScroll
              useCaterpillar
              tabBarPadding={16}
              onAfterChange={(tab, index) => {
                console.log('[tabs]', tab, index);
                handleTabChange(index);
              }}
            >
              {tabData.map((_, index) => (
                <View key={index}>
                  {/* 空内容，实际内容在下方渲染 */}
                </View>
              ))}
            </Tabs>

          {/* Tab内容区域 */}
          <View className="tab-content-wrapper" onScroll={handleScroll} style={{marginTop:"50px"}}>
            {current === 0 ? (
              <View className="boxContent">
                {dynamicList.length === 0 && !isLoading && tabList[0].pages.isLoad ? (
                  renderEmptyContent()
                ) : (
                  <>
                    {dynamicList.map((item, index) => renderDynamicItem(item, index, true))}

                    {dynamicList.length > 0 && isLoading  && (
                      <View className="notmorelist">
                        <Text>正在加载...</Text>
                      </View>
                    )}
                  </>
                )}
              </View>
            ) : (
              <View className="boxContent2">
                {dynamicList2.length === 0 && !isLoading && tabList[1].pages.isLoad ? (
                  renderEmptyContent()
                ) : (
                  <>
                    {dynamicList2.map((item, index) => renderDynamicItem(item, index, false))}

                    {dynamicList2.length > 0 && isLoading  && (
                      <View className="notmorelist">
                        <Text>正在加载...</Text>
                      </View>
                    )}
                  </>
                )}
              </View>
            )}
          </View>
          
          <View className="footer_content_z"></View>
          
          <View className="footerBtnBox" style={{ paddingBottom }}>
            <View className="footerBtnBox-change">
              <Checkbox
                checked={current === 0 ? allchange : allchange2}
                onChange={getAllChange}
                className="footerBtnBox-change-image"
              />
              <View className="footerBtnBox-change-c" onClick={() => setPopupVisible(true)}>
                <Text className="footerBtnBox-change-c-text">
                  选中{current === 0 ? delIdlist.length : delIdlist2.length}条
                </Text>
                <Image 
                   src={require("@/assets/images/common/check_all_icon.png")}
                  className="footerBtnBox-change-c-img"
                />
              </View>
            </View>
            
            {current === 0 ? (
              <Button
                type="primary"
                className={delIdlist.length > 0 ? "footerBtnBox-btn" : "footerBtnBox-notbtn"}
                disabled={delIdlist.length === 0}
                onClick={() => {
                  Dialog.confirm({
                    platform: 'ios',
                    title: "温馨提示",
                    children: `确定要下架${delIdlist.length}件商品吗？`,
                    okText: "确定",
                    cancelText: "取消",
                    onOk: async () => {
                      try {
                        const res: any = await dynamicBatchOperate({
                          status: '2',
                          ids: delIdlist.join(',')
                        });
                        if (res && res.code === 0) {
                          toast("info", {
                            content: '下架成功',
                            duration: 2000
                          })
                          refreshAll();
                        } else {
                          toast("info", {
                            content: res.message,
                            duration: 2000
                          })
                        }
                      } catch (err) {
                        console.error(err);
                      }
                    }
                  });
                }}
              >
                下架
              </Button>
            ) : (
              <Button
                type="primary"
                className={delIdlist2.length > 0 ? "footerBtnBox-btn" : "footerBtnBox-notbtn"}
                disabled={delIdlist2.length === 0}
                onClick={() => {
                  Dialog.confirm({
                    platform: 'ios',
                    title: "温馨提示",
                    children: `确定要上架${delIdlist2.length}件商品吗？`,
                    okText: "确定",
                    cancelText: "取消",
                    onOk: async () => {
                      try {
                        const res: any = await dynamicBatchOperate({
                          status: '1',
                          ids: delIdlist2.join(',')
                        });
                        if (res && res.code === 0) {
                          toast("info", {
                            content: '上架成功',
                            duration: 2000
                          })
                          refreshAll();
                        } else {
                          toast("info", {
                            content: res.message,
                            duration: 2000
                          })
                        }
                      } catch (err) {
                        console.error(err);
                      }
                    }
                  });
                }}
              >
                上架
              </Button>
            )}
          </View>
      
      <Dialog
        visible={onOffPopupVisible}
        title="温馨提示"
        platform="ios"
        close={() => setOnOffPopupVisible(false)}
        footer={[
          {
            content: '取消',
            onClick: () => setOnOffPopupVisible(false)
          },
          {
            content: '确定',
            onClick: handleBatchOperation
          }
        ]}
      >
        <View>
          <Text>确定要{current === 0 ? '下架' : '上架'}</Text>
          <Text style={{ color: '#6CBE70' }}>
            {current === 0 ? delIdlist.length : delIdlist2.length}
          </Text>
          <Text>件商品吗？</Text>
        </View>
      </Dialog>
      
      
      <SelectQuantityManager
        visible={popupVisible}
        onClose={() => setPopupVisible(false)}
        onSelectAll={getAllChange}
        onSelectCustom={selectNum}
        totalCount={current === 0 ? dynamicList.length : dynamicList2.length}
      />
    </View>
  );
};

export default BatchOnOffShelf;