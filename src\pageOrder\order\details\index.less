@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageOrder/order/details/index"] {
  .order-details-box {
    width: 100%;
    min-height: 100vh;
    background: #f7f8fa;
    .use-dark-mode-query({
    background: var(--dark-background-color);    //白色字体
  });
    padding-bottom: 80px;

    .order-status-bar {
      background: var(--primary-color);
      color: #fff;
      font-size: 16px;
      padding: 12px 0 12px 16px;
      font-weight: bold;
    }

    .refund-quantity {
      color: #f53f3f;
      font-size: 12px;
    }

    .order-info-card {
      background: #fff;
      .use-dark-mode-query({
      background-color: @dark-card-background-color !important;
    });
      border-radius: 10px;
      margin: 16px;
      box-shadow: 0 2px 8px rgba(36, 104, 242, 0.03);
      padding: 0 10px;
    }

    .order-user-card {
      background: #fff;
      .use-dark-mode-query({
      background-color: @dark-card-background-color !important;
    });
      border-radius: 10px;
      margin: 16px;
      box-shadow: 0 2px 8px rgba(36, 104, 242, 0.03);
    }

    .order-goods-card {
      background: #fff;
      .use-dark-mode-query({
      background-color: @dark-card-background-color !important;
    });
      border-radius: 10px;
      margin: 16px;
      box-shadow: 0 2px 8px rgba(36, 104, 242, 0.03);
      padding: 16px;
    }

    // 新的订单信息行样式
    .order-info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
    }

    .order-info-label {
      font-size: 14px;
      color: #666;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      width: 80px;
      flex-shrink: 0;
    }

    .order-info-value {
      font-size: 14px;
      color: #333;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      flex: 1;
    }

    .order-info-value-row {
      display: flex;
      align-items: center;
      flex: 1;
      justify-content: space-between;

      .order-info-value-row-left {
        display: flex;
        align-items: center;
        gap: 8px; // 图标之间的间距
      }
    }

    .copy-icon {
      width: 16px;
      height: 16px;
      cursor: pointer;
      flex-shrink: 0;
    }

    .expand-icon {
      width: 16px;
      height: 16px;
      cursor: pointer;
      transition: transform 0.3s ease;
      flex-shrink: 0;

      &.expanded {
        transform: rotate(180deg);
      }
    }

    .expanded-time-info {
      margin-top: 8px;
      padding-top: 8px;
      border-top: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-top: 1px solid @dark-line-color;
    });
    }

    // 联系人信息样式
    .contact-info-row {
      display: flex;
      align-items: flex-start;
      padding: 12px 16px;
      border-bottom: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-bottom: 1px solid @dark-line-color;
    });

      &:last-child {
        border-bottom: none;
      }
    }

    .contact-label {
      font-size: 12px;
      color: #86909c;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      width: 60px;
      flex-shrink: 0;
    }

    .contact-info {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      flex: 1;
      margin-left: 16px;
    }

    .contact-details {
      flex: 1;
    }

    .contact-name {
      font-size: 12px;
      color: #1d2129;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      margin-bottom: 4px;
      display: block;
    }

    .contact-phone {
      font-size: 12px;
      color: #1d2129;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      margin-bottom: 4px;
      display: block;
    }

    .contact-address {
      font-size: 12px;
      color: #1d2129;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      line-height: 1.4;
      display: block;
    }

    .contact-value {
      font-size: 12px;
      color: #1d2129;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      flex: 1;
    }

    // 买家留言图片样式
    .remark-images-container {
      display: flex;
      align-items: flex-start;
      padding: 12px 16px;
      border-bottom: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-bottom: 1px solid @dark-line-color;
    });

      .contact-label {
        width: 60px;
        flex-shrink: 0;
        margin-top: 2px;
      }
    }

    .remark-images-scroll {
      display: flex;
      overflow-x: auto;
      gap: 8px;
      flex: 1;
      padding-bottom: 4px;

      // 隐藏滚动条但保持滚动功能
      &::-webkit-scrollbar {
        display: none;
      }
      -ms-overflow-style: none;
      scrollbar-width: none;
    }

    .remark-image {
      width: 60px;
      height: 60px;
      border-radius: 6px;
      flex-shrink: 0;
      object-fit: cover;
    }

    // 用户信息头部样式
    .user-info-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      border-bottom: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-bottom: 1px solid @dark-line-color;
    });
    }

    .user-info-content {
      display: flex;
      align-items: center;
    }

    .user-name {
      font-size: 14px;
      color: #1d2129;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      font-weight: 500;
    }

    // 商品卡片样式
    .shop-header {
      display: flex;
      align-items: center;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-bottom: 1px solid @dark-line-color;
    });
    }

    .shop-name {
      font-size: 14px;
      color: #1d2129;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      font-weight: 500;
      margin-right: 6px;
    }

    .verified-icon {
      width: 16px;
      height: 16px;
      margin-right: auto;
    }

    .shop-arrow {
      margin-left: auto;
    }

    .arrow-right {
      width: 16px;
      height: 16px;
    }

    // 使用订单列表的商品样式
    .goods-list-detail {
      // padding: 0 12px;
      padding-bottom: 10px;
    }

    .goods-item {
      display: flex;
      align-items: flex-start;
      margin-top: 12px;
    }

    .goods-img {
      width: 60px;
      height: 60px;
      border-radius: 8px;
      background: #f5f5f5;
      margin-right: 10px;
    }

    .goods-info {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
    }

    .goods-title-row {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
    }

    .goods-title {
      flex: 1;
      font-size: 14px;
      color: #222;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      margin-right: 8px;
      /* 显示两行，超出省略号 */
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .goods-price-section {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      flex-shrink: 0;
    }

    .goods-price {
      color: #f53f3f;
      font-weight: 600;
      font-size: 16px;
    }

    .goods-quantity {
      color: #888;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      font-size: 13px;
    }

    .goods-sku {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .sku-count-wrapper {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .sku-text {
      color: #666;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      font-size: 13px;
    }

    .sku-count {
      color: #888;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      font-size: 13px;
    }

    .sku-ems {
      font-size: 12px;
      color: var(--primary-color);
    }

    // 订单总计样式
    .order-summary-details {
      display: flex;
      flex-direction: column;
      align-items: space-between;
      padding-top: 10px;
      // padding: 16px;
      border-top: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-top: 1px solid @dark-line-color;
    });
    }

    .summary-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      &.total-row {
        margin-top: 8px;
        padding-top: 8px;
        border-top: 1px solid var(--line-color);
        .use-dark-mode-query({
        border-top: 1px solid @dark-line-color;
      });
      }
    }

    .summary-label {
      font-size: 12px;
      color: #86909c;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    }

    .summary-value {
      font-size: 12px;
      color: #1d2129;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    }

    .summary-total {
      font-size: 12px;
      color: #e35848;
    }

    // 物流模块样式
    .logistics-card {
      background: #fff;
      .use-dark-mode-query({
      background-color: @dark-card-background-color !important;
    });
      border-radius: 10px;
      margin: 16px;
      box-shadow: 0 2px 8px rgba(36, 104, 242, 0.03);
      padding: 12px;
    }

    .logistics-card-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .logistics-card-content-time {
        font-size: 14px;
        color: #86909c;
        .use-dark-mode-query({
        color: @dark-font-color !important;
      });
      }

      .logistics-card-content-status {
        font-size: 14px;
        color: #1d2129;
        .use-dark-mode-query({
        color: @dark-font-color !important;
      });
      }
    }

    .logistics-header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 12px;
      border-bottom: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-bottom: 1px solid @dark-line-color;
    });
    }

    .logistics-header {
      background: #f7f8fa;
      padding: 10px;
      border-radius: 4px;
      .use-dark-mode-query({
      background-color: @dark-cell-background-color !important;
    });
      margin-bottom: 12px;
    }

    .logistics-status-row {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .logistics-status {
      font-size: 14px;
      color: #1d2129;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      // 限制显示两行，超出显示省略号
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 1.4;
      max-height: 2.8em; // 2行的高度
      font-weight: 500;
    }

    .logistics-time {
      font-size: 12px;
      color: #86909c;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    }

    .logistics-tracking-row {
      display: flex;
      align-items: center;
      padding: 12px 0 0 0;
      gap: 10px;
    }

    .logistics-tracking-btn {
      padding: 4px 16px;
      font-size: 12px;
      color: #165dff;
      border-radius: 100px;
      border: 1px solid #165dff;
    }

    .logistics-label {
      font-size: 12px;
      color: #86909c;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    }

    .logistics-tracking-info {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .logistics-tracking-number {
      font-size: 12px;
      color: #1d2129;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    }

    .logistics-goods-list {
      margin-bottom: 12px;
    }

    .logistics-summary {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 12px;
      border-top: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-top: 1px solid @dark-line-color;
    });
    }

    .logistics-summary-text {
      font-size: 12px;
      color: #86909c;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    }

    .logistics-summary-count {
      font-size: 12px;
      color: #1d2129;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      font-weight: 500;
    }

    // 修改快递单号按钮样式
    .logistics-tracking-btn {
      font-size: 12px;
      color: #165dff;
      cursor: pointer;
    }

    .order-user-row {
      display: flex;
      align-items: center;
      padding: 16px 0 0 16px;
    }
    .order-user-name {
      font-size: 15px;
      font-weight: bold;
      color: @font-color;
    }
    .order-user-phone {
      font-size: 12px;
      color: #888;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      margin-top: 2px;
    }

    .order-remark {
      background: #fff;
      .use-dark-mode-query({
      background-color: @dark-card-background-color !important;
    });
      border-radius: 10px;
      margin: 0 16px 16px 16px;
      box-shadow: 0 2px 8px rgba(36, 104, 242, 0.03);
    }

    .order-img-list {
      display: flex;
      flex-wrap: wrap;
      padding: 0 0 12px 0;
      margin: 0 16px 16px 16px;
    }

    .order-goods-title {
      display: flex;
      align-items: center;
      font-size: 15px;
      font-weight: bold;
      padding: 16px 0 0 16px;
    }
    .order-goods-item {
      display: flex;
      align-items: flex-start;
      padding: 12px 16px 0 16px;
    }
    .order-goods-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
    .order-goods-name {
      font-size: 14px;
      font-weight: bold;
      color: @font-color;
      margin-bottom: 6px;
    }
    .order-goods-spec {
      font-size: 12px;
      color: #888;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      margin-bottom: 8px;
    }
    .order-goods-price {
      font-size: 13px;
      color: #e35848;
      font-weight: bold;
    }
    .order-goods-summary {
      font-size: 13px;
      color: #333;
      padding: 0 16px 12px 16px;
      display: flex;
      flex-wrap: wrap;
      gap: 12px 24px;
      .order-goods-actual {
        color: #e35848;
        font-weight: bold;
      }
    }

    .order-footer-bar {
      height: 50px;
      position: fixed;
      left: 0;
      right: 0;
      bottom: 0;
      background: #fff;
      .use-dark-mode-query({
      background-color: @dark-background-color !important;
    });
      box-shadow: 0 -2px 8px rgba(36, 104, 242, 0.03);
      display: flex;
      justify-content: space-around;
      align-items: center;
      padding: 12px 8px 12px 8px;
      z-index: 10;
      .order-footer-btn {
        flex: 1;
        margin: 0 4px;
        font-size: 15px;
        border-radius: 8px;
        &.order-footer-btn-pay {
          font-weight: bold;
        }
      }
    }
  }

  // .arco-cell .cell-content .cell-text {
  //   text-align: left;
  //   color: #000;
  // }

  // .arco-cell .cell-label {
  //   margin-right: 10px;
  //   font-family: PingFang SC;
  //   font-size: 13px;
  //   font-weight: normal;
  //   line-height: 140%;
  //   text-align: justify; /* 浏览器可能不支持 */
  //   letter-spacing: normal;
  //   color: #86909c;
  // }

  // .cell-title
  // {
  //     width: 60px;
  // }

  .demo-cell-avatar-label {
    display: flex;
    align-items: center;
    .arco-avatar {
      .rem(width, 32);
      .rem(height, 32);
      .rem-with-rtl(margin-right, 8);
    }
  }
  .demo-cell-avatar {
    .cell-text {
      font-size: 0;
    }
    .arco-avatar {
      .rem(width, 24);
      .rem(height, 24);
      display: inline-block;
    }
  }

  .demo-cell-info {
    text-align: right;
    .info {
      .use-var(color, font-color);
      .rem(font-size, 16);
    }
    .sub-info {
      .use-var(color, cell-desc-color);
      .rem(font-size, 14);
    }
  }

  .info {
    font-size: 12px;
    line-height: 18px;
    padding-bottom: 16;
    width: 180px;
    margin-right: 35px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4; /* 限制显示行数 */
    overflow: hidden;
    max-height: 6em; /* 可选，设置最大高度 */
  }

  .order-btn-detail {
    flex: 1;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    border-right: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-right: 1px solid @dark-line-color;
  });
    background: #fff;
    .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
    cursor: pointer;
    transition: background-color 0.2s;
    color: #86909c; // 默认灰色

    &:last-child {
      border-right: none;
    }

    // 所有普通按钮都是灰色
    &.normal-btn {
      color: #86909c;

      .btn-text {
        color: #86909c;
      }
    }

    // 只有立即支付按钮是主题色
    &.pay-btn {
      .btn-text {
        color: var(--primary-color);
      }
    }
  }

  .btn-text {
    font-size: 14px;
    font-weight: 400;
    color: inherit; // 继承父元素颜色
  }

  .empty-order {
    text-align: center;
    color: #bbb;
    font-size: 15px;
    padding: 48px 0 32px 0;
  }
}
