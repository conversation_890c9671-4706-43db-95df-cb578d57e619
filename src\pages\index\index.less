@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pages/index/index"] {
  /* 在这里设置样式 */
  background-color: #fff !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });

  // 下拉刷新样式
  .pull-refresh-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    .rem(padding, 12);

    .loading-text {
      .rem(margin-left, 8);
      color: var(--primary-color);
      .use-dark-mode-query({
        color: var(--dark-primary-color);
      });
    }
  }

  .pull-refresh-success {
    display: flex;
    align-items: center;
    justify-content: center;
    .rem(padding, 12);
    color: var(--primary-color);
    .use-dark-mode-query({
      color: var(--dark-primary-color);
    });
  }

  .album-tabs-home {
    display: flex;
    background: #fff;
    .use-dark-mode-query({
      background-color: @dark-background-color !important;
    });
    // border-bottom: 1px solid #f0f0f0;
    padding: 0 18px;
    font-size: 15px;
    color: #888;
    height: 44px;
    position: fixed;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    // top: 134px;
    // top: 50px;
    left: 0;
    right: 0;
    z-index: 50;
  }


  .indexBox {
    width: 100%;
    min-height: 95vh;
    position: relative;
  }

  .gridModeBoxTitle {
    width: 100%;
    box-sizing: border-box;
    padding: 8px 18px;
    padding-top: 16px;
    font-size: 12px;
    font-weight: normal;
    color: var(--sub-info-font-color);
    .use-dark-mode-query({
    color: var(--dark-sub-info-font-color);
  });
  }

  .gridModeBoxContent {
    width: 100%;
    box-sizing: border-box;
    padding: 0 12px;

    &-grid {
      box-sizing: border-box;
      border-radius: 4px;
      padding: 16px 0;
      background-color: var(--container-background-color);
      .use-dark-mode-query({
      background-color: var(--dark-container-background-color);
    });

      &-item:active {
        opacity: 0.6;
      }

      &-icon {
        font-size: 18px;
      }

      &-icon [fill] {
        fill: var(--font-color);
        .use-dark-mode-query({
          fill: var(--dark-font-color);
      });
      }
      &-icon [stroke] {
        stroke: var(--font-color);
        .use-dark-mode-query({
        stroke: var(--dark-font-color);
      });
      }

      &-text {
        font-size: 12px;
        color: var(--font-color);

        .use-dark-mode-query({
        color: var(--dark-font-color);
      });
      }
    }
  }

  .showAlbumBox {
    position: relative;
    width: 100%;
    box-sizing: border-box;
    padding-bottom: 100px; // 为底部TabBar预留空间
    top: 60px;

    &-item {
      position: relative;
      padding: 15px;
      box-sizing: border-box;
      width: 100%;

      &-title {
        width: 100%;
        margin-top: 8px;
        box-sizing: border-box;
        padding-left: 46px;
        cursor: pointer;
      }

      &-top {
        width: 100%;
        height: 40px;
        display: flex;
        &-image {
          width: 40px;
          height: 40px;
        }

        &-content {
          flex: 1;
          padding-left: 10px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          &-name {
            font-size: 13px;
            color: #576b95;
            display: flex;
            align-items: center;

            .payment-icon {
              width: 16px;
              height: 16px;
              margin-left: 6px;
            }
          }
          &-date {
            font-size: 12px;
            color: #999999;
          }
        }

        &-more {
          width: 24px;
          height: 24px;
        }
      }

      &-center {
        width: 100%;
        margin-top: 8px;
        box-sizing: border-box;
        padding-left: 46px;
        display: grid;
        gap: 4px; // 移除上下间距

        // 默认3列布局（适用于3、6、7、8、9张图）
        grid-template-columns: repeat(3, 1fr);

        // 1张图：单独显示，宽度与9图整体宽度一致，保持1:1比例
        &.images-1 {
          grid-template-columns: 1fr;

          .showAlbumBox-item-center-item {
            width: 100%;
            max-width: 299px; // 3 * 97px + 2 * 4px gap = 299px
            height: 299px; // 保持1:1比例
          }
        }

        // 2张图：一行两列，宽度与9图整体宽度一致
        &.images-2 {
          grid-template-columns: repeat(2, 1fr);

          .showAlbumBox-item-center-item {
            width: 100%;
            height: 147px; // (299px - 4px gap) / 2 = 147.5px，约147px
          }
        }

        // 4张图：2x2网格，宽度与9图整体宽度一致
        &.images-4 {
          grid-template-columns: repeat(2, 1fr);

          .showAlbumBox-item-center-item {
            width: 100%;
            height: 147px; // (299px - 4px gap) / 2 = 147.5px，约147px
          }
        }

        // 5张图：保持3列布局，但调整尺寸
        &.images-5 {
          grid-template-columns: repeat(3, 1fr);

          .showAlbumBox-item-center-item {
            width: 97px;
            height: 97px;
          }
        }

        &-item {
          width: 97px;
          height: 97px;
          background-color: #ccc;
          border-radius: 4px;
          overflow: hidden;
          margin: 0; // 重置margin
          padding: 0; // 重置padding
          display: block; // 确保为块级元素
        }
      }

      &-jiage {
        width: 100%;
        height: 44px;
        display: flex;
        box-sizing: border-box;
        padding-left: 46px;
        align-items: center;
        color: #eb483f;

        &-icon {
          font-size: 12px;
        }

        &-val {
          font-size: 18px;
          font-weight: bold;
        }
      }

      &-playMode {
        width: 100%;
        padding: 10px;
        box-sizing: border-box;
        padding-left: 46px;
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        &-link {
          color: #576b95;
          font-size: 12px;

          &:active {
            opacity: 0.8;
          }
        }
      }

      &-playBtn {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        padding-top: 10px;
        padding-left: 46px;

        &-left {
          display: flex;

          &-item {
            width: 40px;
            margin-right: 10px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;

            &:active {
              opacity: 0.8;
            }

            &-icon {
              width: 18px;
              height: 18px;
              color: #999999;
            }

            &-text {
              font-size: 12px;
              color: #999999;
            }
          }
        }

        &--right {
          height: 40px;
        }
      }
    }
  }

  .move-attrs-wrap-my {
    padding: 0;
    padding-left: 46px;
    // margin-top: -20px;
    // .use-dark-mode-query({
    //   border-bottom: 1px solid #333;
    // });
  }

  .move-attrs-my {
    font-size: 13px;
    color: #8c8c8c;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    .move-attrs-arrow {
      margin-left: 6px;
      width: 16px;
      height: 16px;
      flex-shrink: 0;
      transition: transform 0.2s ease;
    }
  }

  .card-attr-content {
    background: #f7f8fa;
    border-radius: 8px; // 根据图片调整圆角
    padding: 12px; // 调整内边距
    margin-top: 8px; // 调整与属性文字间距
    margin-right: 0px; // 移除右侧外边距
    box-shadow: none; // 移除阴影
    .use-dark-mode-query({
      background: @dark-container-background-color;
      box-shadow: none;
    });
  }
  .card-attr-content .attr-row {
    align-items: center; // 使属性 label 和 values 垂直居中
  }
  .card-attr-content .attr-row + .attr-row {
    border-top: 1px dashed #eee; // 使用虚线作为分隔线
    padding-top: 8px; // 调整上方内边距
    margin-top: 8px; // 调整上方外边距
    .use-dark-mode-query({
      border-top: 1px dashed #333;
    });
  }
  .card-attr-content .attr-label {
    font-size: 14px; // 根据图片调整字体大小
    color: #222;
    font-weight: bold; // 字体加粗
    min-width: 40px; // 调整最小宽度
    margin-top: 0px; // 移除顶部外边距
    .use-dark-mode-query({ color: var(--dark-font-color); });
  }
  .card-attr-content .attr-values {
    display: flex;
    flex-wrap: wrap;
    gap: 8px 8px; // 调整属性值之间的间距
  }
  .tag-badge {
    // 标签样式

    font-size: 13px; // 根据图片调整字体大小
    color: #165dff;
    background: #e8f3ff;
    border-radius: 4px;
    padding: 3px 8px; // 移除内边距
    margin-bottom: 0px; // 移除底部外边距
    position: relative;
    transition: none;
    border: none;
    .use-dark-mode-query({ 
      color: var(--dark-font-color); 
      background: #333;
    });
  }
  .card-attr-content .attr-value {
    font-size: 13px; // 根据图片调整字体大小
    color: #444;
    background: none;
    border: none;
    padding: 0; // 移除内边距
    margin-bottom: 0px; // 移除底部外边距
    position: relative;
    cursor: pointer;
    transition: none;
    .use-dark-mode-query({ color: var(--dark-font-color); });

    &.selected {
      color: #165dff; // 选中状态字体颜色
      font-weight: bold; // 选中状态字体加粗
    }

    // 属性值之间的分隔线，根据图片样式调整
    &:not(:last-child)::after {
      content: "|"; // 使用竖线作为分隔符
      position: static; // 移除绝对定位
      transform: none; // 移除transform
      margin-left: 8px; // 调整与属性值间距
      height: auto; // 移除固定高度
      background: none; // 移除背景
      color: #e5e6eb; // 分隔符颜色
      .use-dark-mode-query({ color: #333; });
    }
    &:last-child::after {
      // 移除最后一个属性值后面的分隔符
      display: none;
    }
  }

  // 自定义保存按钮样式
  .custom-save-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--primary-color);
    border-radius: 20px;
    background-color: transparent;
    overflow: hidden;
  }

  .custom-save-button_noboder {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    overflow: hidden;
  }

  .cart-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:active {
      background-color: rgba(22, 93, 255, 0.1);
    }

    .cart-icon {
      width: 18px;
      height: 18px;
      // stroke: var(--primary-color);
      // fill: var(--primary-color);
    }
  }

  .divider-line {
    width: 1px;
    height: 20px;
    background-color: var(--primary-color);
    opacity: 0.3;
  }

  .save-text-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;

    &:active {
      background-color: rgba(22, 93, 255, 0.1);
    }
  }

  .custom-save-button-text {
    color: var(--primary-color);
    font-size: 14px;
    font-weight: 500;
  }

  // 自定义文字展开/收起组件样式
  .custom-ellipsis-container {
    width: 100%;
    line-height: 1.5;

    .custom-ellipsis-text {
      color: #333;
      font-size: 14px;
      line-height: 1.5;
      word-break: break-word;
      .use-dark-mode-query({
      color: #fff;
    });
    }

    .custom-ellipsis-toggle {
      color: var(--primary-color);
      font-size: 14px;
      margin-left: 4px;
      cursor: pointer;

      &:active {
        opacity: 0.7;
      }
    }
  }

  // 图片包装器样式
  .image-wrapper {
    display: inline-block;
    cursor: pointer;
    margin: 0; // 重置margin
    padding: 0; // 重置padding
    vertical-align: top; // 防止inline-block的基线对齐问题

    &:active {
      opacity: 0.8;
    }
  }

  // 悬浮购物车样式
  .car {
    position: fixed;
    right: 15px;
    bottom: 138px;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 99;

    &-img {
      width: 50px;
      height: 50px;
    }

    &-text {
      position: absolute;
      top: -5px;
      right: -5px;
      min-width: 18px;
      height: 18px;
      padding: 0 4px;
      background: #eb483f;
      border-radius: 9px;
      color: #fff;
      font-size: 12px;
      text-align: center;
      line-height: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      z-index: 1;
    }
  }

  // 首页搜索框样式 - 固定在顶部
  .searchLine_home {
    background: #fff;
    .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
    position: fixed;
    // top: 84px;
    // top: 0;
    left: 0;
    right: 0;
    z-index: 50; /* 确保在内容之上，但在导航栏之下 */
    box-sizing: border-box;

    padding: 10px 16px;

    .arco-search-bar-container {
      padding: 0 !important;
    }
  }

.search-icon {
  width: 16px;
  height: 16px;
  margin-left: 8px;
}

.not_content {
  position: relative;
  width: 100%;
  height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  font-size: 14px;
  color: #999999;
  &-image {
    width: 100px;
    height: 100px;
    display: block;
    margin-bottom: 16px;
  }
}

// 未登录状态样式
.wait-login-container {
  position: relative;
  width: 100%;
  min-height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 80px 54.5px 0;
  box-sizing: border-box;
  
  &-content {
    width: 266px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }
  
  &-text-area {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 5px;
    
    &-title {
      font-size: 16px;
      font-weight: bold;
      color: #1D2129;
      line-height: 1.4;
      text-align: center;
      .use-dark-mode-query({
        color: var(--dark-font-color);
      });
    }
    
    &-subtitle {
      font-size: 14px;
      font-weight: 500;
      color: #86909C;
      line-height: 1.4;
      text-align: center;
      .use-dark-mode-query({
        color: var(--dark-sub-info-font-color);
      });
    }
  }
  
  &-image-wrapper {
    width: 144px;
    height: 179px;
    position: relative;
    overflow: hidden;
    border-radius: 4px;
    border: 0.5px solid #E5E6EB;
    background: linear-gradient(180deg, rgba(215, 255, 255, 0.16) 46%, rgba(216, 216, 216, 0) 92%);
    
    .use-dark-mode-query({
      border-color: #333;
      background: linear-gradient(180deg, rgba(215, 255, 255, 0.08) 46%, rgba(216, 216, 216, 0) 92%);
    });
    
    &-bg {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  &-login-btn {
    position: fixed;
    bottom: calc(80px + env(safe-area-inset-bottom));
    left: 50%;
    transform: translateX(-50%);
    width: 88px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #165DFF;
    color: #FFFFFF;
    font-size: 14px;
    font-weight: normal;
    border-radius: 4px;
    border: 1px solid #165DFF;
    cursor: pointer;
    transition: opacity 0.2s;
    z-index: 100;
    
    &:active {
      opacity: 0.8;
    }
    
    .use-dark-mode-query({
      background: var(--dark-primary-color);
      border-color: var(--dark-primary-color);
    });
  }
}

}
