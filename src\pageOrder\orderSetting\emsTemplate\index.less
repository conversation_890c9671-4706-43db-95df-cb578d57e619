@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageOrder/orderSetting/emsTemplate/index"] {
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });

  .emsTemplatePage {
    position: relative;
    width: 100%;
    height: 100vh;

    .title-right {
      display: flex;
      align-items: center;
      justify-content: center;

      &-text {
        font-size: 15px;
        color: var(--primary-color);
      }
    }

    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 50vh;
    }

    .ems-rule-group {
      border-radius: 4px;
      margin: 10px;
      overflow: hidden;
    }

    .container {
      display: flex;
      flex-direction: column;
      padding: 10px;

      &-rule {
        background-color: #ffffff;
        .use-dark-mode-query({
        background-color: @dark-background-color !important;
      });
        border-radius: 7px;
        // display: flex;
      }

      &-head {
        padding: 0px 16px 8px 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        &-left {
          // padding: 20px;
          font-size: 15px;
          color: #999999;
        }

        &-right {
          font-size: 14px;
          .use-var(color, primary-color);
          // .use-dark-mode-query({
          //   color: @dark-font-color !important;
          // });
        }

        &-right-text {
          font-size: 14px;
          .use-var(color, primary-color);
        }
      }

      &-box {
        margin-bottom: 20px;
        display: flex;
        flex-direction: column;
        gap: 15px;

        &-item {
          border-radius: 7px;
          padding: 0px 10px;
          display: flex;
          flex-direction: column;
          .use-var(background-color, background-color);
          .use-dark-mode-query({
          background-color: @dark-cell-background-color !important;
        });

          &-cell {
            padding: 15px 0px;
          }

          &-append {
            display: flex;
            flex-direction: column;
            gap: 15px;
          }

          &-text {
            font-weight: bold;
            font-size: 15px;
            .use-var(color, font-color);
            .use-dark-mode-query({
            color: @dark-font-color !important;
          });
          }

          &-line {
            // padding-bottom: 30px;
            display: flex;
            // margin-top: 30px;
            align-items: center;

            &-tv1 {
              flex-shrink: 0;
              font-size: 14px;
              .use-var(color, sub-info-font-color);
              .use-dark-mode-query({
              color: @dark-font-color !important;
            });
            }

            &-tv2 {
              margin-left: 15px;
              font-size: 13px;
              color: #333333;
              .use-var(color, font-color);
              .use-dark-mode-query({
              color: @dark-font-color !important;
            });
            }
          }

          &-area {
            // padding-bottom: 30px;
            display: flex;
            flex-direction: column;

            &-item {
              display: flex;

              &-tv1 {
                flex-shrink: 0;
                font-size: 14px;
                .use-var(color, sub-info-font-color);
                .use-dark-mode-query({
                color: @dark-font-color !important;
              });
              }

              &-tv2 {
                margin-left: 15px;
                font-size: 13px;
                color: #333333;
                .use-var(color, font-color);
                .use-dark-mode-query({
                color: @dark-font-color !important;
              });
              }
            }
          }

          &-areaN {
            // padding-bottom: 30px;
            display: flex;

            &-tv1 {
              flex-shrink: 0;
              font-size: 14px;
              .use-var(color, sub-info-font-color);
              .use-dark-mode-query({
              color: @dark-font-color !important;
            });
            }

            &-tv2 {
              margin-left: 15px;
              font-size: 13px;
              color: #333333;
              .use-var(color, font-color);
              .use-dark-mode-query({
              color: @dark-font-color !important;
            });
            }
          }

          &-line2 {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;

            &-left {
              display: flex;
              align-items: center;
              flex-shrink: 0;

              &-check {
                display: block;
                width: 16px;
                height: 16px;
                flex-shrink: 0;
                color: var(--primary-color);
                // background-color: var(--primary-color);
              }

              &-uncheck {
                display: block;
                width: 16px;
                height: 16px;
                flex-shrink: 0;
                color: #999999;
                .use-var(color, sub-info-font-color);
                .use-dark-mode-query({
                  color: @dark-font-color !important;
                });
              }

              &-text {
                margin-left: 10px;
                font-size: 14px;
                color: #333333;
                .use-var(color, font-color);
                .use-dark-mode-query({
                color: @dark-font-color !important;
              });
              }
            }

            &-right {
              display: flex;
              align-items: center;
              margin-left: auto;

              &-del {
                display: block;
                width: 16px;
                height: 16px;
                flex-shrink: 0;
              }

              &-edit {
                display: flex;
                align-items: center;
                margin-left: 40px;
                flex-shrink: 0;

                &-img {
                  display: block;
                  width: 16px;
                  height: 16px;
                }

                &-text {
                  margin-left: 14px;
                  font-size: 14px;
                  color: #002c8c;
                  //.use-var(color, font-color);
                  .use-dark-mode-query({
                  color: @dark-font-color !important;
                });
                }
              }
            }
          }
        }

        &-line {
          height: 1px;
          background-color: rgba(248, 248, 248, 0.8);
        }
      }
    }

    .holder {
      min-height: 140px;
    }

    .footerbtn {
      .use-var(background-color, background-color);
      .use-dark-mode-query({
        background-color: @dark-background-color !important;
      });
      position: fixed;
      bottom: 0;
      width: 100%;
      height: 64px;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 100;

      &-btn {
        width: 90%;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ffffff;
        // font-size: 14px;

        &-icon {
          width: 15px;
          height: 15px;
          font-size: 14px;
        }
        &-text {
          margin-left: 10px;
          font-size: 15px;
        }
      }
    }

    .emsPop {
      padding-bottom: constant(safe-area-inset-bottom);
      padding-bottom: env(safe-area-inset-bottom);
      border-radius: 10px 10px 0 0 !important;
      overflow: hidden;
      //.use-var(background-color, background-color);
      background-color: var(--page-primary-background-color);
      .use-dark-mode-query({
        background-color: @dark-cell-background-color;
      });
      display: flex;
      flex-direction: column;
      justify-content: center;
  
      &-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border-radius: 10px 10px 0 0 !important;
        background-color: var(--page-primary-background-color) !important;
        .use-dark-mode-query({
        // background-color: @dark-cell-background-color !important;
          background-color: @dark-background-color !important;
        });
        gap: 8px;
  
        &-group {
          width: 100%;
          overflow-y: auto;
          // border-radius: 10px 10px 0 0 !important;
          overflow: hidden;
        }
  
        &-cancel {
          // margin-top: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          padding: 12px 16px;
          .use-var(background-color, background-color);
          .use-dark-mode-query({
            background-color: @dark-cell-background-color !important;
          });
  
          &-text {
            font-size: 15px;
            .use-var(color, font-color);
            .use-dark-mode-query({
              color: @dark-font-color;
            });
          }
        }
      }
    }
  }
  
  .hiddenStyle {
    //display: none;
    visibility: hidden;
  }
}
