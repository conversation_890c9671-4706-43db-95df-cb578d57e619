import {CSSProperties, FC, useEffect, useState, useCallback, useRef} from "react";
import {View, Input} from "@tarojs/components";
import './index.less';

interface IProps {
	size?: number,
	isFocus?: boolean,
	inputType?: 'text' | 'number',
	type?: 'pane' | 'line',
	onConfirm: (code: string) => void,
	inputStyle?: CSSProperties,
	activeStyle?: CSSProperties
}

export const YiCode: FC<IProps> = (props) => {
	const { size = 6, isFocus = true, type = 'pane', inputType = 'number', onConfirm, inputStyle, activeStyle } = props;
	
	const [code, setCode] = useState<string[]>([]);
	const [focused, setFocused] = useState(isFocus);
	const hasConfirmedRef = useRef(false); // 记录是否已经确认过，防止重复调用
	
	const inputChange = useCallback((val: string | number) => {
		const newCode = val.toString().split('').slice(0, size);
		setCode(newCode);
		// 输入变化时重置确认状态
		hasConfirmedRef.current = false;
	}, [size]);
	
	const handleBoxClick = useCallback(() => {
		setFocused(true);
	}, []);
	
	useEffect(() => {
		if(code.length === size && !hasConfirmedRef.current){
			hasConfirmedRef.current = true; // 标记已确认
			onConfirm(code.join(''));
		}
	}, [code, size, onConfirm])
	
	// 处理初始焦点
	useEffect(() => {
		setFocused(isFocus);
	}, [isFocus])
	
	// 计算当前输入位置，用于定位隐藏输入框
	const currentInputIndex = code.length < size ? code.length : size - 1;
	// 每个输入框宽度60px + 右边距10px = 70px，左边距20px
	const hideInputLeft = 20 + currentInputIndex * 70;
	
	return <View className='box' onClick={handleBoxClick}>
		<View className='showInput'>
			{Array.from(new Array(size).keys()).map((i) => {
				return <View className={`inputBox ${type}`} key={i}>
					<Input disabled value={code[i] || ''} className='input' style={inputStyle} type='text' maxlength={1} />
					{code.length === i && <View className='active' style={activeStyle} />}
				</View>
			})}
		</View>
		<Input
		  className='hideInput'
		  style={{
		    height: inputStyle && inputStyle.height ? inputStyle.height: '60rpx',
		    left: `${hideInputLeft}px`,
		    width: '60px'
		  }}
		  type={inputType}
		  onInput={(e) => inputChange(e.detail.value)}
		  onFocus={() => setFocused(true)}
		  onBlur={() => setFocused(false)}
		  maxlength={size}
		  focus={focused}
		/>
	</View>
}