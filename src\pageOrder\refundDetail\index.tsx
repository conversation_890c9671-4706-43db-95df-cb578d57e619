import { View, Text } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import {
  NavBar,
  Image,
  Avatar,
  Divider,
  Tag,
  Loading,
  Dialog,
} from "@arco-design/mobile-react";
import { IconRight, IconDown, IconCopy } from "@arco-iconbox/react-yk-arco";
import { IconPayment } from "@/components/YkIcons";
import React, { useState ,useRef} from "react";
import Taro from "@tarojs/taro";
import {
  getUserOrderDetails,
} from "@/utils/api/common/common_user";
import YkNavBar from "@/components/ykNavBar/index";
import { toast } from "@/utils/yk-common";
import { useEffect } from "react";
export default function RefundDetail() {
  const [refundData, setRefundData] = useState<any>(null);
  const [orderDetails, setOrderDetails] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [isExpanded, setIsExpanded] = useState(false); // 控制时间信息展开/收起
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  // 点击原订单号跳转到对应的订单详情页面
  const handleOrderNoClick = () => {
    if (!refundData || !orderDetails) return;

    // 根据订单类型判断跳转到买家还是卖家订单详情
    const orderType = refundData.orderType || "buyer";
    const orderId = refundData.userOrderId;

    console.log('点击原订单号，订单类型:', orderType, '订单ID:', orderId);

    if (orderType === "seller") {
      // 跳转到卖家订单详情
      console.log('跳转到卖家订单详情');
      Taro.navigateTo({
        url: `/pageOrder/sellOrder/details/index?id=${orderId}`,
      });
    } else {
      // 跳转到买家订单详情（默认）
      console.log('跳转到买家订单详情');
      Taro.navigateTo({
        url: `/pageOrder/order/details/index?id=${orderId}`,
      });
    }
  };

  useLoad(() => {
    // 获取路由参数
    const router = Taro.getCurrentInstance().router;
    const refundDataStr = router?.params?.refundData;

    if (refundDataStr) {
      try {
        const data = JSON.parse(decodeURIComponent(refundDataStr));
        console.log('退款数据:', data);
        setRefundData(data);

        // 获取订单详情
        if (data.userOrderId) {
          getUserOrderDetails({ id: data.userOrderId }).then(response => {
            if (response && response.code === 0) {
              setOrderDetails(response.data);
            }
            setLoading(false);
          }).catch(error => {
            console.error("获取订单详情失败:", error);
            setLoading(false);
          });
        } else {
          setLoading(false);
        }
      } catch (error) {
        console.error("解析退款数据失败:", error);
        toast("info", {
        content: "退款数据解析失败",
        duration: 2000
      });
        setLoading(false);
      }
    } else {
      toast("info", {
        content: "退款数据不存在",
        duration: 2000
      });
      setLoading(false);
    }
  });


  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <View className="order-details-box">
       {platform !== "WX" && <YkNavBar title="退款详情" />}
        <View style={{ padding: "50px", textAlign: "center" }}>
          <Loading />
          <Text style={{ marginTop: "10px", display: "block" }}>加载中...</Text>
        </View>
      </View>
    );
  }

  // 如果没有退款数据，显示错误状态
  if (!refundData) {
    return (
      <View className="order-details-box">
        {platform !== "WX" &&<YkNavBar title="退款详情" />}
        <View style={{ padding: "50px", textAlign: "center" }}>
          <Text>退款详情不存在</Text>
        </View>
      </View>
    );
  }
  // 格式化时间
  const formatTime = (timestamp: number) => {
    if (!timestamp) return "--";
    const date = new Date(timestamp);
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // 复制订单号
  const handleCopyOrderNo = (orderNo: string) => {
    Taro.setClipboardData({
      data: orderNo,
      success: () => {
        Taro.hideToast();
        toast("success", {
          content: "订单号已复制",
          duration: 2000
        });
      },
      fail: () => {
        Taro.hideToast();
        toast("error", {
          content: "复制失败",
          duration: 2000
        });
      },
    });
  };

  // 切换展开/收起状态
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  // 复制发件人信息
  const handleCopySender = (senderInfo: string) => {
    Taro.setClipboardData({
      data: senderInfo,
      success: () => {
        Taro.hideToast();
        toast("success", {
          content: "发件人信息已复制",
          duration: 2000
        });
      },
      fail: () => {
        Taro.hideToast();
        toast("error", {
          content: "复制失败",
          duration: 2000
        });
      },
    });
  };

  // 复制收件人信息
  const handleCopyReceiver = (receiverInfo: string) => {
    Taro.setClipboardData({
      data: receiverInfo,
      success: () => {
        Taro.hideToast();
        toast("success", {
          content: "收件人信息已复制",
          duration: 2000
        });
      },
      fail: () => {
        Taro.hideToast();
        toast("error", {
          content: "复制失败",
          duration: 2000
        });
      },
    });
  };

  return (
    <View className="order-details-box">
      {platform !== "WX" &&<YkNavBar title="退款详情" />}
      <View className="order-status-bar">
        已退款
      </View>
      <View className="order-info-card">
        {/* 订单编号行 - 带复制和展开/收起功能 */}
        <View className="order-info-row">
          <Text className="order-info-label">订单编号</Text>
          <View className="order-info-value-row">
            <View className="order-info-value-row-left">
              <Text className="order-info-value">
                {refundData?.refundId || "--"}
              </Text>
              {/* <Image
                className="copy-icon"
                src={require("@/assets/images/common/copy_icon.png")}
                onClick={() =>
                  handleCopyOrderNo(refundData?.refundId || "")
                }
              /> */}
              <IconCopy className="copy-icon" onClick={() =>
                handleCopyOrderNo(refundData?.refundId || "")
              } />

            </View>
            {/* <Image
              className={`expand-icon ${isExpanded ? "expanded" : ""}`}
              src={require("@/assets/images/common/arrow_down.png")}
              onClick={toggleExpanded}
            /> */}
            <IconDown className="expand-icon" onClick={toggleExpanded} />
          </View>
        </View>

       

        {/* 可展开/收起的时间信息 */}
        {isExpanded && (
          <View className="expanded-time-info">
             <View className="order-info-row">
          <Text className="order-info-label">原订单号</Text>
          <View className="order-info-value-row">
            <View className="order-info-value-row-left">
              <Text
                className="order-info-value order-no-blue"
                onClick={handleOrderNoClick}
              >
                {orderDetails.combineOutTradeNo || "--"}
              </Text>
            </View>
            {/* <Image
              className="expand-icon"
              src={require("@/assets/images/common/arrow_right.png")}
              onClick={handleOrderNoClick}
            /> */}
            <IconRight className="expand-icon" onClick={handleOrderNoClick} />
          </View>
        </View>
            <View className="order-info-row">
              <Text className="order-info-label">开单时间</Text>
              <Text className="order-info-value">
                {orderDetails ? formatTime(orderDetails.createTime) : "--"}
              </Text>
            </View>
            <View className="order-info-row">
              <Text className="order-info-label">付款时间</Text>
              <Text className="order-info-value">
                {orderDetails ? formatTime(orderDetails.successTime) || "--" : "--"}
              </Text>
            </View>
            <View className="order-info-row">
              <Text className="order-info-label">退款时间</Text>
              <Text className="order-info-value">
                {formatTime(refundData.createTime)}
              </Text>
            </View>
          </View>
        )}
      </View>

      {refundData?.reason && (
        <View className="order-remark">
          {/* 退款原因 */}
          <View className="contact-info-row">
            <Text className="contact-label">退款原因：</Text>
            <View className="contact-info">
              <Text className="contact-value">
                {refundData?.reason}
              </Text>
            </View>
          </View>
        </View>
      )}

      <View className="order-user-card">
        {/* 用户信息 */}
        <View className="user-info-header">
          <View className="user-info-content">
            <Avatar
              size="small"
              style={{ marginRight: 8 }}
              src={Taro.getStorageSync("userInfo").avatar || ""}
            />
            <Text className="user-name">
              {Taro.getStorageSync("userInfo").nickname || ""}
            </Text>
          </View>
          {/* <Image
            className="arrow-right"
            src={require("@/assets/images/common/arrow_right.png")}
          /> */}
          <IconRight className="arrow-right" />

        </View>

        {/* 发件人信息 */}
        {orderDetails && (
          <View className="contact-info-row">
            <Text className="contact-label">发件人</Text>
            <View className="contact-info">
              <View className="contact-details">
                <Text className="contact-name">
                  {orderDetails.customizeSenderName ||
                    orderDetails.dynamicUserName ||
                    ""}{" "}
                  {orderDetails.customizeSenderPhone ||
                    orderDetails.dynamicUserPhone ||
                    ""}
                </Text>
              </View>
              {/* <Image
                className="copy-icon"
                src={require("@/assets/images/common/copy_icon.png")}
                onClick={() =>
                  handleCopySender(
                    `${
                      orderDetails.customizeSenderName ||
                      orderDetails.dynamicUserName ||
                      ""
                    } ${
                      orderDetails.customizeSenderPhone ||
                      orderDetails.dynamicUserPhone ||
                      ""
                    }`
                  )
                }
              /> */}
              <IconCopy className="copy-icon" onClick={() =>
                 handleCopySender(
                  `${
                    orderDetails.customizeSenderName ||
                    orderDetails.dynamicUserName ||
                    ""
                  } ${
                    orderDetails.customizeSenderPhone ||
                    orderDetails.dynamicUserPhone ||
                    ""
                  }`
                )
              } />

            </View>
          </View>
        )}

        {/* 收件人信息 */}
        {orderDetails && (
          <View className="contact-info-row">
            <Text className="contact-label">收件人</Text>
            <View className="contact-info">
              <View className="contact-details">
                <Text className="contact-name">
                  {orderDetails.receivingName || ""}{" "}
                  {orderDetails.receivingPhone || ""}
                </Text>
                <Text className="contact-address">
                  {orderDetails.receivingArea || ""}{" "}
                  {orderDetails.receivingAddress || ""}
                </Text>
              </View>
              {/* <Image
                className="copy-icon"
                src={require("@/assets/images/common/copy_icon.png")}
                onClick={() =>
                  handleCopyReceiver(
                    `${orderDetails.receivingName || ""} ${
                      orderDetails.receivingPhone ||
                      orderDetails.dynamicUserPhone ||
                      ""
                    } ${orderDetails.receivingArea || ""} ${
                      orderDetails.receivingAddress || ""
                    }`
                  )
                }
              /> */}
              <IconCopy className="copy-icon" onClick={() =>
                handleCopyReceiver(
                  `${orderDetails.receivingName || ""} ${
                    orderDetails.receivingPhone ||
                    orderDetails.dynamicUserPhone ||
                    ""
                  } ${orderDetails.receivingArea || ""} ${
                    orderDetails.receivingAddress || ""
                  }`
                )
              } />

            </View>
          </View>
        )}

        {/* 配送方式 */}
        {orderDetails && (
          <View className="contact-info-row">
            <Text className="contact-label">配送方式</Text>
            <View className="contact-info">
              <Text className="contact-value">
                {orderDetails.delivery || "--"}
              </Text>
            </View>
          </View>
        )}

        {/* 支付方式 */}
        {orderDetails && (
          <View className="contact-info-row">
            <Text className="contact-label">支付方式</Text>
            <View className="contact-info">
              <Text className="contact-value">
                微信
              </Text>
            </View>
          </View>
        )}

        {/* 运费 */}
        {orderDetails && (
          <View className="contact-info-row">
            <Text className="contact-label">运费</Text>
            <View className="contact-info">
              <Text className="contact-value">
                ¥{(orderDetails.freightAmount || 0).toFixed(2)}
              </Text>
            </View>
          </View>
        )}
      </View>

 

      <View className="order-goods-card">
        {/* 商家信息 */}
        <View className="shop-header">
          <Avatar
            size="small"
            style={{ marginRight: 8 }}
            src={refundData?.dynamicUserAvatar || ""}
          />
          <Text className="shop-name">
            {refundData?.shop || ""}
          </Text>
          {/* <Image
            className="verified-icon"
            src={require("@/assets/images/common/wx_pay.png")}
          /> */}
          <IconPayment className="verified-icon" />
          <View className="shop-arrow">
            {/* <Image
              className="arrow-right"
              src={require("@/assets/images/common/arrow_right.png")}
            /> */}
            <IconRight className="arrow-right" />

          </View>
        </View>

        {/* 退款商品列表 */}
        {refundData?.dynamics &&
          refundData?.dynamics.length > 0 && (
            <View className="goods-list-detail">
              {refundData?.dynamics.map(
                (dynamic: any, index: number) => {
                  const firstImage = dynamic.pictures
                    ? dynamic.pictures.split(",")[0]
                    : "";
                  return (
                    <View key={index} className="goods-item">
                      <Image src={firstImage} className="goods-img" />
                      <View className="goods-info">
                        <View className="goods-title-row">
                          <Text className="goods-title">
                            {dynamic.content || "--"}
                          </Text>
                          <View className="goods-price-section">
                            <Text className="goods-price">
                              ￥{dynamic.price || 0}
                            </Text>
                            <Text className="goods-quantity">
                              x{dynamic.orderRefundDetails?.reduce((sum: number, detail: any) => sum + detail.refundQuantity, 0) || 0}
                            </Text>
                          </View>
                        </View>
                        {/* 显示退款SKU信息 */}
                        {dynamic.orderRefundDetails &&
                          dynamic.orderRefundDetails.map(
                            (detail: any, detailIdx: number) => (
                              <View className="goods-sku" key={detailIdx}>
                                <Text className="sku-text">
                                  {detail.colorName} {detail.specName}
                                </Text>
                                <View className="sku-count-wrapper">
                                  <Text className="refund-quantity">{`退款x${detail.refundQuantity}`}</Text>
                                </View>
                              </View>
                            )
                          )}
                      </View>
                    </View>
                  );
                }
              )}
            </View>
          )}

        {/* 退款总计 */}
        <View className="order-summary-details">
          <View className="summary-row">
            <Text className="summary-label">商品数量</Text>
            <Text className="summary-value">
              {refundData?.dynamics?.reduce(
                (total: number, dynamic: any) => total + (dynamic.orderRefundDetails?.reduce((sum: number, detail: any) => sum + detail.refundQuantity, 0) || 0),
                0
              ) || 0}
            </Text>
          </View>
          <View className="summary-row">
            <Text className="summary-label">商品总金额</Text>
            <Text className="summary-value">
              ¥{refundData?.dynamics?.reduce(
                (total: number, dynamic: any) => total + (dynamic.price * (dynamic.orderRefundDetails?.reduce((sum: number, detail: any) => sum + detail.refundQuantity, 0) || 0)),
                0
              ).toFixed(2) || "0.00"}
            </Text>
          </View>
          <View className="summary-row">
            <Text className="summary-label">已退运费</Text>
            <Text className="summary-value">
              ¥{(refundData?.refundPostage || 0).toFixed(2)}
            </Text>
          </View>

          <View className="summary-row">
            <Text className="summary-label">退款金额(含运费)</Text>
            <Text className="summary-total">
              ¥{((refundData?.refundAmount || 0) + (refundData?.refundPostage || 0)).toFixed(2)}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
}
