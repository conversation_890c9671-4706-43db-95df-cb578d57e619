import { View, Text, Image } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import wx from "weixin-webview-jssdk";
import "./index.less";
import { Dialog, Toast, Checkbox } from "@arco-design/mobile-react";
import React, { useState, useRef, useEffect } from "react";
import Taro from "@tarojs/taro";
import { toast } from "@/utils/yk-common";
// 组件
import YkNavBar from "@/components/ykNavBar/index";

// API
import { logOut } from "@/utils/api/common/common_user";

export default function AccountCancellation() {
  const [isChecked, setIsChecked] = useState(false);
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);

  // 切换同意状态
  const handleCheckboxChange = () => {
    setIsChecked(!isChecked);
  };

  // 申请注销确认
  const handleLogoutConfirm = () => {
    if (!isChecked) {
      Toast.info('请阅读并同意注销协议');
      return;
    }

    // 使用 Arco Design Mobile 的 Dialog.confirm
    Dialog.confirm({
      title: '提示',
      children: '注销账号后，你的账号的所有数据会被删除，确认注销吗？',
      okText: '注销',
      cancelText: '取消',
      onOk: handleLogout,
      platform: 'ios',
    });
  };



  // 执行注销
  const handleLogout = async () => {
      const res = await logOut(Taro.getStorageSync("userInfo").id);

      if (res && res.code === 0) {
        toast("success", {
          content: "注销成功",
          duration: 2000
        })

        // 清除缓存
        Taro.clearStorageSync();

        if (platform === "WX") {
          wx.miniProgram.reLaunch({
            url: "/pages/index/index?isLoginOut=true",
          });
        } else {
        Taro.reLaunch({
            url: "/pages/login/index",
          });
        }

        // setTimeout(() => {
        //   Taro.reLaunch({
        //     url: '/pages/login/index'
        //   });
        // }, 1000);
      } else {
        // 注销失败时显示温馨提示框（延迟避免嵌套弹窗冲突）
        setTimeout(() => {
          Dialog.alert({
            title: '温馨提示',
            children: res.msg,
            okText: '知道了',
            platform: 'ios',
          });
        }, 1000); 
      }
  };

  return (
    <View className="account-cancellation">
      {platform !== "WX" &&<YkNavBar title="注销账号" />}

      {/* 头部说明 */}
      <View className="head-box">
        <Image
          className="head-box-img"
          src={require("@/assets/images/common/zhuxiao_icon.png")}
        />
        <Text className="head-box-text1">申请注销账号</Text>
        <Text className="head-box-text2">
          您提交的注销申请生效前，我们将进行以下验证，以保证您的账号数据安全
        </Text>
      </View>

      {/* 内容区域 */}
      <View className="content-box">
        <View className="content-box-item">
          <Text className="content-box-item-num">01</Text>
          <View className="content-box-item-right">
            <Text className="content-box-item-right-title">账号处于安全状态</Text>
            <Text className="content-box-item-right-hint">
              确认账号没有被盗、被封的风险。
            </Text>
          </View>
        </View>

        <View className="content-box-item">
          <Text className="content-box-item-num">02</Text>
          <View className="content-box-item-right">
            <Text className="content-box-item-right-title">账户余额全部提现</Text>
            <Text className="content-box-item-right-hint">
            自己确认该账号上的余额是否已全部提现。
            </Text>
          </View>
        </View>

        <View className="content-box-item">
          <Text className="content-box-item-num">03</Text>
          <View className="content-box-item-right">
            <Text className="content-box-item-right-title">在线收款处于未开通状态</Text>
            <Text className="content-box-item-right-hint">
            自己确认该账号是否已开通在线收款，若已开通，请先联系平台方处理。若有关联的商户号，请先解绑。
            </Text>
          </View>
        </View>

        <View className="content-box-item">
          <Text className="content-box-item-num">04</Text>
          <View className="content-box-item-right">
            <Text className="content-box-item-right-title">交易订单已全部完成</Text>
            <Text className="content-box-item-right-hint">
            自己确认该账号的交易订单是否全部完成，若有未完成的订单，请先完成进行中的订单。
            </Text>
          </View>
        </View>

        <View className="content-box-item">
          <Text className="content-box-item-num">05</Text>
          <View className="content-box-item-right">
            <Text className="content-box-item-right-title">重要提醒</Text>
            <Text className="content-box-item-right-hint">
              提交注销前，请仔细阅读以下内容：{'\n'}
              注销账号是不可恢复的操作，你应自行备份账号相关的信息和数据。{'\n'}
              包括但不限于：{'\n'}
              （1）你将无法登录；{'\n'}
              （2）个人资料与历史信息（包含昵称、头像、图文等）无法找回。
            </Text>
          </View>
        </View>
      </View>

      {/* 底部操作区域 */}
      <View className="logout-box">
        <View
          className={`logout-box-btn ${isChecked ? 'active' : ''}`}
          onClick={handleLogoutConfirm}
        >
          申请注销
        </View>

        <View className="logout-box-agreement" >
          <Checkbox
            checked={isChecked}
            onChange={handleCheckboxChange}
          />
          <Text className="logout-box-agreement-text" onClick={handleCheckboxChange}>
            我已阅读并同意以上内容
          </Text>
        </View>
      </View>

    </View>
  );
}