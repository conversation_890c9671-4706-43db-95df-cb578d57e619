import { IconProps } from './types';

const IconPaymentStroken: React.FC<IconProps> = ({
  color = 'var(--primary-color)',
  size = 16,
  className,
}) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    xmlnsXlink='http://www.w3.org/1999/xlink'
    fill='none'
    version='1.1'
    width={size}
    height={size}
    viewBox='0 0 16 16'
    className={className}
  >
    <g>
      {/* 对话气泡外围轮廓 - 线性样式 */}
      <path
        d='M7.729,2.667C4.202,2.667,1.333,4.978,1.333,7.818C1.333,9.377,2.188,10.827,3.682,11.809C3.667,11.958,3.575,12.274,3.517,12.473C3.328,13.120,3.167,13.900,3.333,13.900C3.500,13.900,3.999,13.567,4.000,13.567C4.333,13.400,4.801,13.166,5.201,12.966C5.430,12.792,5.805,12.508,5.946,12.463C6.644,12.667,7.378,12.770,8.129,12.770C11.656,12.770,14.525,10.459,14.525,7.618C14.525,4.778,11.656,2.667,7.729,2.667Z'
        stroke={color}
        strokeWidth='1.2'
        fill='none'
      />

      {/* 内部打钩符号 - 填充样式 */}
      <path
        d='M6.975,9.010C6.975,9.010,5.783,8.022,5.783,8.022C5.783,8.022,5.375,7.823,5.387,8.196C5.387,8.196,6.555,10.930,6.754,10.954C6.952,10.977,6.941,11.117,7.793,10.568C8.547,10.088,12.892,7.578,13.967,6.956C11.719,5.578,9.129,6.827,6.975,9.010Z'
        fill={color}
      />
    </g>
  </svg>
);

export default IconPaymentStroken;
