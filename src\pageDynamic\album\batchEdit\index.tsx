import { View, Text, Image, ScrollView, CoverImage } from "@tarojs/components";
import React, { useState, useEffect, useRef, useCallback } from "react";
import { createPortal } from "react-dom";
import Taro, { useDidShow, useReady } from "@tarojs/taro";
import {
  Dialog,
  SearchBar,
  ActionSheet,
  Checkbox,
  DatePicker,
  Button,
} from "@arco-design/mobile-react";
import YkNavBar from "@/components/ykNavBar/index";
import {
  getMyAlbumList
} from "@/utils/api/common/common_user";
import BottomPopup from "@/components/BottomPopup";
import PermissionPopup from "@/components/PermissionPopup";
import FilterDrawer, { FilterDrawerRef } from "@/components/FilterDrawer";
import { AuthTypes } from "@/utils/config/authTypes";
import { usePermission } from "@/hooks/usePermission";
import "./index.less";
import { toast } from "@/utils/yk-common";
import { IconLoadEmpty } from "@/components/YkIcons";
import SelectQuantityManager from "@/components/SelectQuantityPopup/SelectQuantityManager";
import { IconClose } from "@arco-iconbox/react-yk-arco";
import { isMemberValid } from '@/pages/my/utils'
import wx from "weixin-webview-jssdk";
// 定义 dynamic 类型
interface DynamicItem {
  id: string | number;
  content: string;
  price: number;
  time: string;
  pictures: string; // API returns pictures as comma-separated string
  coverImage: string[]; // Transformed pictures into array
  skus?: string[];
  color?: string[];
  digitalWatermark?: string;
  isTop?: number; // Add isTop property to the interface
}

interface GroupedItem {
  group: string;
  items: DynamicItem[];
}

interface DynamicData {
  items: GroupedItem[];
  totalCount?: number;
  hasMore?: boolean;
  allItems?: DynamicItem[]; // 添加 allItems 字段存储所有商品数据
}

export default function BatchEditPage() {
  const [dynamic, setDynamic] = useState<DynamicData>({
    items: [],
    hasMore: true,
    allItems: [],
  });
  const [selectedIds, setSelectedIds] = useState<(string | number)[]>([]);
  const [isAllSelected, setIsAllSelected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // 搜索相关状态
  const [searchKeyword, setSearchKeyword] = useState(""); // 搜索关键词
  const searchKeywordRef = useRef(""); // 实时保存搜索内容
  const searchImgRef = useRef(""); // 保存图片搜索内容
  const userInfo = Taro.getStorageSync("userInfo");

  // 弹框相关状态
  const [isPopupVisible, setIsPopupVisible] = useState(false);
  const operationTypeRef = useRef(""); // 操作类型标识：'imageSearch'
  const chooseImageRef = useRef(""); // 选择图片类型标识：'album' 或 'camera'

  // 筛选抽屉相关状态
  const [isFilterDrawerVisible, setFilterDrawerVisible] = useState(false);
  const filterDrawerRef = useRef<FilterDrawerRef>(null);

  // 日期选择器相关状态
  const [datePickerVisible, setDatePickerVisible] = useState(false);
  const [currentDateType, setCurrentDateType] = useState<1 | 2>(1); // 1: 起始时间, 2: 结束时间

  // 临时时间选择状态（在抽屉中显示，但未确认）
  const [tempBeginDate, setTempBeginDate] = useState("请选择");
  const [tempEndDate, setTempEndDate] = useState("请选择");

  // 已确认的筛选时间（用于显示筛选条件和接口调用）
  const [confirmedBeginDate, setConfirmedBeginDate] = useState("请选择");
  const [confirmedEndDate, setConfirmedEndDate] = useState("请选择");

  // 保存当前的筛选条件
  const [currentFilterTime, setCurrentFilterTime] = useState<{
    createTime?: string;
    endTime?: string;
  }>({});

  // 自定义数量相关状态
  const [popupVisible, setPopupVisible] = useState(false);
  const [pageNo, setPageNo] = useState(1);
  const [pageSize] = useState(20);
  const [sortType, setSortType] = useState(1);
  const loginUserInfo = Taro.getStorageSync("userInfo");
  console.log("loginUserInfo",loginUserInfo);

  const openVip = ()=>{
    Dialog.confirm({
      title: "提示",
      children: "请先开通会员",
      okText: "去开通",
      cancelText: "取消",
      platform: 'ios',
      onOk: () => {
        if (platformRef.current === "WX") {
          wx.miniProgram.navigateTo({ url: "/pages/web?path=/pageVip/vipCenter/index" });
        } else {
          Taro.navigateTo({ url: "/pageVip/vipCenter/index" });
        }
      }
    })
  }

  const chooseImage = async (type: "album" | "camera") => {
    if (platformRef.current === "Android") {
      window.setPhotoNum?.setPhotoNum(1);
    }
    if (platformRef.current === "HM") {
      window.harmony.setPhotoNum(1);
    }
    setIsPopupVisible(false);
    Taro.chooseImage({
      count: 1,
      sizeType: ["compressed"],
      sourceType: [type],
      success: async (res) => {
        const imagePath = res.tempFilePaths[0];
        //转为base64
        const base64Data = await blobToBase64(imagePath);
        setSearchKeyword("");
        searchKeywordRef.current = ""; // 同时清除文字搜索的ref
        searchImgRef.current = base64Data;
        setPageNo(1);
        // 图片搜索时，同时传递当前的筛选条件
        getDynamicListData(
          "",
          currentFilterTime.createTime,
          currentFilterTime.endTime
        );
      },
      fail: (err) => {},
    });
  };

  // 自定义权限同意处理，处理批量编辑页面特有的逻辑
  const customWebPermissonConsent = () => {
    console.log("batchEdit customWebPermissonConsent");
    console.log("operationType:", operationTypeRef.current);
    console.log("chooseImageType:", chooseImageRef.current);

    // 根据操作类型执行不同的后续处理
    if (operationTypeRef.current === "imageSearch") {
      // 以图搜图操作
      console.log("执行图片选择，类型:", chooseImageRef.current);
      chooseImage(chooseImageRef.current as "album" | "camera");
    }

    // 清除操作类型标识
    operationTypeRef.current = "";

    return true;
  };

  // 使用全局权限管理
  const {
    initPermissions,
    hasPermission,
    requestPermission,
    webPermissonConsent,
    webPermissonDeny,
    permissionPopupProps,
    platformRef,
    authConfirmType,
  } = usePermission(customWebPermissonConsent);
  const getDynamicListData = async (
    content = " ",
    createTime?: string,
    endTime?: string
  ) => {
    if (isLoading) return; // 防止重复加载

    setIsLoading(true);

    // 只在第一页时显示loading
    if (pageNo === 1) {
      Taro.showLoading({
        title: "加载中...",
        mask: true,
      });
    }

    const data = {
      pageNo: pageNo,
      pageSize: pageSize,
      userId: loginUserInfo.id,
      sort: sortType,
      homePageCountType:1,
    } as any;

    // 判断搜索框有值就传content
    const keyword =
      searchImgRef.current?.length > 0
        ? searchImgRef.current
        : searchKeywordRef.current;
    if (keyword && keyword.length > 0) {
      data.content = keyword;
    }

    // 添加时间筛选参数
    if (createTime) {
      data.createTime = createTime;
    }
    if (endTime) {
      data.endTime = endTime;
    }

    try {
      const res: any = await getMyAlbumList(data);

      if (pageNo === 1) {
        Taro.hideLoading();
      }

      if (res && res.code === 0 && res.data) {
        const itemsWithSkus =
          res.data.list?.map((item) => {
            const pictures = item.pictures || "";
            // 分割图片字符串，并取第一张（过滤空值）
            const pictureArray = pictures.split(",").filter(Boolean);
            const coverImage = pictureArray[0] || ""; // 如果没有图片则设为空字符串;

            return {
              ...item,
              pictures,
              coverImage: coverImage,
              pictureArray: pictureArray,
            };
          }) || [];

        // 更新状态，累加数据
        setDynamic((prevDynamic) => {
          // 合并所有商品数据
          const allItems =
            pageNo === 1
              ? itemsWithSkus
              : [...(prevDynamic.items || []), ...itemsWithSkus];
          const hasMore = res.data.list?.length === pageSize;

          return {
            items: allItems,
            hasMore,
          };
        });
      } else {
        toast("info", {
          content: res.msg || "网络异常，请重试",
          duration: 2000,
        });
      }
    } catch (error) {
      if (pageNo === 1) {
        Taro.hideLoading();
      }
      toast("info", {
        content: "网络异常，请重试",
        duration: 2000,
      });
      console.error("获取动态列表失败:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshList = () => {
    setSelectedIds([]);
    setIsAllSelected(false);
    getDynamicListData();
  };

  // 获取数据逻辑同主页面
  useEffect(() => {
    Taro.eventCenter.on("refreshAlbumList", refreshList);
    // 初始化权限管理
    const cleanup = initPermissions();
    getDynamicListData();

    // 清理事件监听
    return () => {
      cleanup && cleanup();
      Taro.eventCenter.off("refreshAlbumList", refreshList);
    };
  }, []);

  // 加载更多
  const loadMore = () => {
    if (!isLoading && dynamic.hasMore) {
      setPageNo((prev) => prev + 1);
    }
  };

  // 监听页码变化，加载数据
  useEffect(() => {
    if (pageNo > 1) {
      getDynamicListData(
        "",
        currentFilterTime.createTime,
        currentFilterTime.endTime
      );
    }
  }, [pageNo]);

  // 处理搜索
  const handleSearch = useCallback(
    (keyword: string) => {
      setSearchKeyword(keyword);
      searchKeywordRef.current = keyword;
      searchImgRef.current = "";
      // 重置分页状态
      setPageNo(1);

      // 调用搜索接口，同时传递当前的筛选条件
      getDynamicListData(
        "",
        currentFilterTime.createTime,
        currentFilterTime.endTime
      );
    },
    [currentFilterTime]
  );

  // 打开选择图片方式弹窗
  const openPopup = () => {
    if (platformRef.current === "WX") {
      chooseImageRef.current = "album";
      chooseImage("album");
      return;
    }
    setIsPopupVisible(true);
  };

  // 关闭选择图片方式弹窗
  const handleClose = () => {
    setIsPopupVisible(false);
  };

  const handleConfirm = (index: number) => {
    operationTypeRef.current = "imageSearch";
    if (index === 0) {
      chooseImageRef.current = "camera";

      if (platformRef.current === "HM") {
        chooseImage("camera");
      } else {
        // 请求相机权限
        if (!hasPermission(AuthTypes.CAMERA)) {
          // 如果没有权限，请求权限
          requestPermission(AuthTypes.CAMERA);
          return;
        }
        chooseImage("camera");
      }
    } else if (index === 1) {
      // 设置图片选择类型为相册
      chooseImageRef.current = "album";
      if (platformRef.current === "HM") {
        chooseImage("album");
      } else {
        // 请求相册权限
        if (!hasPermission(AuthTypes.GALLERY_PHOTO)) {
          console.log("相册权限请求");
          // 如果没有权限，请求权限
          requestPermission(AuthTypes.GALLERY_PHOTO);
          return;
        }
        console.log("相册权限");
        chooseImage("album");
      }
    }
  };

  const blobToBase64 = async (blobUrl: string): Promise<string> => {
    try {
      // 首先通过fetch获取blob数据
      const response = await fetch(blobUrl);
      const blob = await response.blob();

      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          const base64String = reader.result as string;
          // 移除data:image/jpeg;base64,前缀，只保留base64数据
          const base64Data = base64String.split(",")[1];
          resolve(base64Data);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error("转换base64失败:", error);
      throw error;
    }
  };

  // 搜索框onChange处理
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchKeyword(value); // 立即更新输入框的值
    searchKeywordRef.current = value; // 同时更新ref

    // 如果输入为空，立即清除搜索结果，同时传递当前的筛选条件
    if (value.length === 0) {
      searchImgRef.current = "";
      setPageNo(1);
      getDynamicListData(
        "",
        currentFilterTime.createTime,
        currentFilterTime.endTime
      );
    }
  };

  // 处理键盘按键事件
  const handleKeyDown = (e) => {
    console.log("Key pressed:", e.key);
    if (e.key === "Enter") {
      console.log("Enter key pressed, searching...");
      handleSearchSubmit();
    }
  };

  // 处理键盘搜索按钮点击
  const handleSearchSubmit = (value) => {
    console.log("handleSearchSubmit called with:", value);
    const searchValue = value || searchKeyword;
    if (searchValue.trim().length > 0) {
      handleSearch(searchValue.trim());
    }
  };

  // 搜索框清除处理
  const handleSearchClear = () => {
    setSearchKeyword("");
    searchKeywordRef.current = ""; // 同时清除ref
    searchImgRef.current = "";
    setPageNo(1);
    // 清除搜索时，保持当前的筛选条件
    getDynamicListData(
      "",
      currentFilterTime.createTime,
      currentFilterTime.endTime
    );
  };

  // 处理筛选按钮点击
  const handleFilter = useCallback(() => {
    console.log("点击筛选按钮");
    setFilterDrawerVisible(true);
  }, []);

  // 处理筛选抽屉关闭
  const handleFilterDrawerClose = useCallback(() => {
    setFilterDrawerVisible(false);
  }, []);

  // 处理筛选确认
  const handleFilterConfirm = useCallback(
    (data: any) => {
      console.log("筛选确认", data);

      // 验证时间选择的有效性
      if (tempBeginDate !== "请选择" && tempEndDate !== "请选择") {
        const beginTime = new Date(tempBeginDate).getTime();
        const endTime = new Date(tempEndDate).getTime();

        if (endTime < beginTime) {
          toast("error", {
            content: "结束时间必须大于起始时间",
            duration: 2000,
          });
          return;
        }
      }

      // 格式化时间参数
      const createTime = tempBeginDate !== "请选择" ? tempBeginDate : undefined;
      const endTime = tempEndDate !== "请选择" ? tempEndDate : undefined;

      // 更新确认的时间状态
      setConfirmedBeginDate(tempBeginDate);
      setConfirmedEndDate(tempEndDate);

      // 保存当前筛选条件
      const filterTime = { createTime, endTime };
      setCurrentFilterTime(filterTime);

      // 重置分页状态
      setPageNo(1);

      // 调用获取列表接口，传递时间参数
      getDynamicListData("", createTime, endTime);

      // 验证成功，关闭抽屉
      setFilterDrawerVisible(false);
    },
    [tempBeginDate, tempEndDate]
  );

  // 处理筛选重置
  const handleFilterReset = useCallback(() => {
    console.log("筛选重置");
    filterDrawerRef.current?.reset();
    setTempBeginDate("请选择");
    setTempEndDate("请选择");
    setConfirmedBeginDate("请选择");
    setConfirmedEndDate("请选择");

    // 清空筛选条件
    setCurrentFilterTime({});

    // 重置分页状态
    setPageNo(1);

    // 重新获取数据，不传递时间参数
    getDynamicListData("");
  }, []);

  // 处理时间选择点击
  const handleTimeClick = useCallback((type: 1 | 2) => {
    setCurrentDateType(type);
    setDatePickerVisible(true);
  }, []);

  // 日期选择确认
  const handleDateConfirm = useCallback(
    (value: string) => {
      if (currentDateType === 1) {
        setTempBeginDate(value);
        filterDrawerRef.current?.setDateRange(value, tempEndDate);
      } else {
        setTempEndDate(value);
        filterDrawerRef.current?.setDateRange(tempBeginDate, value);
      }
      setDatePickerVisible(false);
    },
    [currentDateType, tempBeginDate, tempEndDate]
  );

  // 日期选择关闭
  const handleDatePickerClose = useCallback(() => {
    setDatePickerVisible(false);
  }, []);

  // 获取筛选条件显示文本
  const getFilterText = useCallback(() => {
    const parts = [];
    if (confirmedBeginDate && confirmedBeginDate !== "请选择") {
      parts.push(`${confirmedBeginDate}`);
    }
    if (confirmedEndDate && confirmedEndDate !== "请选择") {
      parts.push(`${confirmedEndDate}`);
    }
    return parts.join("，");
  }, [confirmedBeginDate, confirmedEndDate]);

  // 处理清除筛选条件
  const handleClearFilter = useCallback(() => {
    handleFilterReset();
  }, [handleFilterReset]);

  // 自定义数量选择函数
  const selectNum = (num: number) => {
    const allItems = dynamic.items || [];
    let newIds: (string | number)[] = [];

    const selectCount = Math.min(num, allItems.length);
    for (let i = 0; i < selectCount; i++) {
      newIds.push(allItems[i].id);
    }

    setSelectedIds(newIds);
    setIsAllSelected(newIds.length === allItems.length);
  };

  // 全选/反选
  const handleSelectAll = () => {
    if (isAllSelected) {
      setSelectedIds([]);
      setIsAllSelected(false);
    } else {
      // 直接使用API返回的数组结构
      const all = (dynamic.items || []).map((item: any) => item.id);
      setSelectedIds(all);
      setIsAllSelected(true);
    }
  };

  // 单选
  const handleSelect = (id: any) => {
    let newSelected;
    if (selectedIds.includes(id)) {
      newSelected = selectedIds.filter((_id: any) => _id !== id);
    } else {
      newSelected = [...selectedIds, id];
    }

    setSelectedIds(newSelected);
    // 直接使用API返回的数组结构
    setIsAllSelected(newSelected.length === (dynamic.items || []).length);
  };

  // 批量编辑
  const handleBatchEdit = () => {
    if(!isMemberValid(Taro.getStorageSync("userInfo"))){
      openVip();
      return;
    }
    if (selectedIds.length === 0) return;

    // 获取选中的动态数据
    const selectedItems = (dynamic.items || []).filter((item: any) =>
      selectedIds.includes(item.id)
    );

    // 存储选中的动态数据
    Taro.setStorageSync("selectedDynamics", selectedItems);

    // 直接跳转到编辑价格页面
    Taro.navigateTo({
      url: `/pageDynamic/album/editPrice/index?type=batchEdit`,
    });
  };

  const firstShare = () => {
    if (selectedIds.length === 0) {
      toast("info", {
        content: "请先选择要分享的动态",
        duration: 2000,
      });
      return;
    }

    // 获取选中的动态项目
    const selectedItems = (dynamic.items || []).filter((item: any) =>
      selectedIds.includes(item.id)
    );

    // 筛选出有图片的动态，并提取第一张图片
    const itemsWithImages = selectedItems
      .filter((item: any) => {
        const images = item.pictures
          ? item.pictures.split(",").filter((img: string) => img.trim() !== "")
          : [];
        return images.length > 0;
      })
      .slice(0, 9) // 只取前9条
      .map((item: any) => {
        const images = item.pictures
          .split(",")
          .filter((img: string) => img.trim() !== "");
        return {
          ...item,
          pictures: images[0], // 只保留第一张图片
        };
      });

    if (itemsWithImages.length === 0) {
      toast("info", {
        content: "选中的动态中没有包含图片的内容",
        duration: 2000,
      });
      return;
    }

    // 创建合成的动态数据
    const combinedImages = itemsWithImages
      .map((item) => item.pictures)
      .join(",");
    const combinedDynamic = {
      items: [
        {
          pictures: combinedImages,
        },
      ],
    };

    // 存储合成的动态数据
    Taro.setStorageSync("releaseDynamicList", combinedDynamic);

    // 跳转到发布动态页面
    Taro.navigateTo({
      url: `/pageDynamic/releaseDynamic/index?isFirstShare=true`,
    });
  };

  // 渲染单个动态项目 - 参考批量上下架样式
  const renderDynamicItem = (item: any) => {
    const images = item.pictures
      ? item.pictures.split(",").filter((img: string) => img.trim() !== "")
      : [];

    return (
      <View key={item.id} className="dline">
        <View className="dline-left">
          <Checkbox
            checked={selectedIds.includes(item.id)}
            className="dline-left-change"
            onChange={() => handleSelect(item.id)}
          />
        </View>
        <View className="dline-right">
          {item.content && (
            <View
              className={`dline-right-top ${
                !item.pictures || item.pictures === "" ? "topBg" : ""
              }`}
            >
              <View className="dynamic-title">{item.content}</View>
            </View>
          )}
          {item.pictures && (
            <View className="dline-right-bottom">
              {images.slice(0, 5).map((imgUrl: string, imgIndex: number) => (
                <View key={imgIndex} className="imageItem">
                  <Image
                    src={imgUrl}
                    mode="aspectFill"
                    className="imageItem-image"
                  />
                  {imgIndex === 4 && images.length > 5 && (
                    <View className="imageItem-mask">
                      <Text>+{images.length - 5}</Text>
                    </View>
                  )}
                </View>
              ))}
            </View>
          )}
        </View>
      </View>
    );
  };

  // 渲染空内容
  const renderEmptyContent = () => (
    <View className="not_content">
      {/* <Image
        className="not_content-image"
        src={require("@/assets/images/common/not_content_trend.png")}
      /> */}
      <IconLoadEmpty className="not_content-image" />

      <Text>暂无动态内容</Text>
    </View>
  );

  // 列表渲染
  const renderList = () => {
    // 直接使用API返回的数据结构
    const allItems = dynamic.items || [];

    return (
      <View className="boxContent">
        {allItems.length === 0
          ? renderEmptyContent()
          : allItems.map((item: any) => renderDynamicItem(item))}
      </View>
    );
  };

  return (
    <View className="batch-delete-page">
      {platformRef.current !== "WX" &&<YkNavBar title="批量编辑" />}

      {/* 搜索栏 */}
      <View className="searchLine-batchEdit">
        <SearchBar
          placeholder="搜索内容"
          clearable={true}
          value={searchKeyword}
          onChange={handleSearchChange}
          onClear={handleSearchClear}
          onKeyDown={handleKeyDown}
          // suffix={
          //   <Image
          //     className="search-icon"
          //     src={require("@/assets/images/common/search_img_icon.png")}
          //     mode="aspectFit"
          //     onClick={() => openPopup()}
          //   />
          // }
          actionButton={
            <View className="demo-search-btn" onClick={handleFilter}>
              筛选
            </View>
          }
        />

        {/* 筛选条件显示 */}
        {(confirmedBeginDate !== "请选择" || confirmedEndDate !== "请选择") && (
          <View className="filter">
            <View className="filter-text">
              <Text>{getFilterText()}</Text>
            </View>
            {/* <Image
            className="filter-img clickOpacity"
            src={require("@/assets/images/common/clear_filter_icon.png")}
            mode="aspectFit"
            onClick={handleClearFilter}
          /> */}
            <IconClose className="filter-img" onClick={handleClearFilter} />
          </View>
        )}
      </View>

      {/* 列表内容区域 */}
      <ScrollView
        className="content-scroll"
        scrollY
        onScrollToLower={loadMore}
        lowerThreshold={50}
      >
        {renderList()}
        {/* 加载更多提示 */}
        {isLoading && dynamic.items && dynamic.items.length > 0 && (
          <View className="loading-more">
            <Text className="loading-text">加载中...</Text>
          </View>
        )}
        {!dynamic.hasMore && dynamic.items && dynamic.items.length > 0 &&pageNo>1&& (
          <View className="no-more">
            <Text className="no-more-text">没有更多了</Text>
          </View>
        )}
      </ScrollView>

      {/* 底部操作栏 - 完全照搬批量上下架样式 */}
      <View className="footerBtnBox">
        <View className="footerBtnBox-change">
          <Checkbox
            checked={isAllSelected}
            onChange={handleSelectAll}
            className="footerBtnBox-change-image"
          />
          <View
            className="footerBtnBox-change-c"
            onClick={() => setPopupVisible(true)}
          >
            <Text className="footerBtnBox-change-c-text">
              选中{selectedIds.length}条
            </Text>
            <Image
              src={require("@/assets/images/common/check_all_icon.png")}
              className="footerBtnBox-change-c-img"
            />
          </View>
        </View>

        <View className="footerBtnBox-share" onClick={() => firstShare()}>
          <Image
            className="footerBtnBox-share-img"
            src={require("@/assets/images/common/first_photo_share.png")}
          ></Image>
          <Text className="footerBtnBox-share-text">首图分享</Text>
        </View>

        <Button
          type="primary"
          className={
            selectedIds.length > 0 ? "footerBtnBox-btn" : "footerBtnBox-notbtn"
          }
          disabled={selectedIds.length === 0}
          onClick={handleBatchEdit}
          loading={isLoading}
        >
          编辑
        </Button>
      </View>

      <SelectQuantityManager
        visible={popupVisible}
        onClose={() => setPopupVisible(false)}
        onSelectAll={handleSelectAll}
        onSelectCustom={selectNum}
        totalCount={dynamic.items?.length || 0}
      />

      {/* 图片选择弹窗 */}
      <BottomPopup
        options={["拍照", "从相册选择"]}
        onConfirm={handleConfirm}
        onClose={handleClose}
        visible={isPopupVisible}
      />

      <PermissionPopup {...permissionPopupProps} />

      {/* 筛选抽屉 */}
      <FilterDrawer
        ref={filterDrawerRef}
        visible={isFilterDrawerVisible}
        onClose={handleFilterDrawerClose}
        onConfirm={handleFilterConfirm}
        onReset={handleFilterReset}
        onTimeClick={handleTimeClick}
        tempBeginDate={tempBeginDate}
        tempEndDate={tempEndDate}
      />

      {/* 日期选择器 - 使用Portal渲染到body顶层 */}
      {datePickerVisible &&
        typeof document !== "undefined" &&
        createPortal(
          <DatePicker
            visible={datePickerVisible}
            title={currentDateType === 1 ? "选择起始时间" : "选择结束时间"}
            maskClosable
            disabled={false}
            currentTs={
              currentDateType === 1
                ? tempBeginDate !== "请选择"
                  ? new Date(tempBeginDate).getTime()
                  : Date.now()
                : tempEndDate !== "请选择"
                ? new Date(tempEndDate).getTime()
                : Date.now()
            }
            mode="date"
            onHide={handleDatePickerClose}
            onOk={(timestamp) => {
              const date = new Date(timestamp);
              const formattedDate = `${date.getFullYear()}-${String(
                date.getMonth() + 1
              ).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
              handleDateConfirm(formattedDate);
            }}
            formatter={(value, type) => {
              if (type === "year") {
                return `${value}年`;
              } else if (type === "month") {
                return `${value}月`;
              } else if (type === "date") {
                return `${value}日`;
              }
              return `${value}`;
            }}
          />,
          document.body
        )}
    </View>
  );
}
