import { IconProps } from './types';

const IconCart: React.FC<IconProps> = ({
  color = 'var(--primary-color)',
  size = 20,
  className,
  onClick,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    fill="none"
    version="1.1"
    width={size}
    height={size}
    viewBox="0 0 20 20"
    className={className}
    onClick={onClick}
    style={{ cursor: onClick ? 'pointer' : 'default' }}
  >
    <defs>
      <clipPath id="master_svg0_601_93148/411_85820">
        <rect x="0" y="0" width="20" height="20" rx="0" />
      </clipPath>
    </defs>
    <g clipPath="url(#master_svg0_601_93148/411_85820)">
      <g>
        <path
          d="M16.530872078125,14.16528397265625L6.933781178125,14.16528397265625C6.664363378125,14.16528397265625,6.407678078125,13.92220797265625,6.373121278125,13.63436397265625L5.3670835781249995,6.54992677265625L4.7981893781250005,3.26637337265625C4.694779178125,2.41928786265625,3.950523878125,1.75689697265625,3.1031584781249997,1.75689697265625L2.350408908125,1.75689697265625C2.040459038125,1.75689697265625,1.788330078125,2.0090459626562502,1.788330078125,2.31897580265625C1.788330078125,2.6289055326562503,2.0404790681250002,2.88105467265625,2.350408908125,2.88105467265625L3.1031584781249997,2.88105467265625C3.382508978125,2.88105467265625,3.649388078125,3.13206437265625,3.6862030781249997,3.42898317265625L4.256216478124999,6.72442867265625L5.258277178125001,13.78052397265625C5.360827478125,14.62649197265625,6.096588578125,15.28886097265625,6.933761078125,15.28886097265625L16.530853078125,15.28886097265625C16.840804078125,15.28886097265625,17.092934078124998,15.03671397265625,17.092934078124998,14.72678297265625C17.092973078125,14.41741297265625,16.840825078125,14.16528397265625,16.530872078125,14.16528397265625Z"
          fill={color}
          fillOpacity="1"
        />
      </g>
      <g>
        <path
          d="M17.862697265625002,4.048233392265625C17.566917265625,3.713360582265625,17.159534265625,3.529205322265625,16.716433265625,3.529205322265625L6.486151095625,3.529205322265625C6.176201225625,3.529205322265625,5.924072265625,3.781354482265625,5.924072265625,4.091284092265625C5.924072265625,4.4012137022656255,6.176221455625,4.653362822265625,6.486151095625,4.653362822265625L16.716433265625,4.653362822265625C16.837132265625,4.653362822265625,16.939683265625,4.700390822265625,17.021268265625,4.792748322265625C17.142526265625,4.929875422265625,17.204282265625,5.154823022265625,17.178781265625,5.366159722265625L16.247832265625,10.692928322265626C16.213275265625,10.976795222265626,15.942419265625,11.210815922265624,15.644379665625,11.212515822265626L7.630994365625,11.803494422265626C7.481415465625,11.814267122265624,7.3454276656249995,11.882260322265624,7.247394465625,11.996143322265626C7.149081365625,12.109466522265626,7.101213665625,12.254527122265625,7.112245965625,12.404106122265626C7.133491365625,12.696489322265625,7.379404465625,12.925394022265625,7.672066165625,12.925394022265625C7.6853773656249995,12.925394022265625,7.699547565625,12.924834222265625,7.7134383656250005,12.923695522265625L15.685193065625,12.334414522265625C16.523783265625,12.334414522265625,17.258127265625,11.685634622265624,17.358978265624998,10.857816222265626L18.290506265624998,5.531047822265625C18.357921265625002,4.972386822265625,18.202107265625,4.431273522265625,17.862697265625002,4.048233392265625Z"
          fill={color}
          fillOpacity="1"
        />
      </g>
      <g>
        <path
          d="M7.086616390625,18.243077C7.722085490625,18.243077,8.239394890625,17.7257477,8.239394890625,17.0905777C8.239394890625,16.45482939,7.722065790625,15.9375,7.086616390625,15.9375C6.451167230625,15.9375,5.933837890625,16.45482939,5.933837890625,17.0905777C5.9338180542,17.7257675,6.451147440625,18.243077,7.086616390625,18.243077Z"
          fill={color}
          fillOpacity="1"
        />
      </g>
      <g>
        <path
          d="M15.358381665625,18.243077C15.993571365625,18.243077,16.510879265625,17.7257477,16.510879265625,17.0905777C16.510879265625,16.45482939,15.993550165625,15.9375,15.358380065625,15.9375C14.722631815625,15.9375,14.205322265625,16.45482939,14.205322265625,17.0905777C14.2053237915038,17.7257675,14.722633365625,18.243077,15.358381665625,18.243077Z"
          fill={color}
          fillOpacity="1"
        />
      </g>
    </g>
  </svg>
);

export default IconCart;
