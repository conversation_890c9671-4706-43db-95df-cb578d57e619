import { View, Text } from "@tarojs/components";
import "./editSettlement.less";
import { useEffect, useState, useCallback, ChangeEvent } from "react";
import Taro, { useRouter } from "@tarojs/taro";
import React, { useRef } from "react";
// 组件
import YkNavBar from "@/components/ykNavBar/index";
import {
  Cell,
  Toast,
  Button,
  Input,
  Radio
} from "@arco-design/mobile-react";
import type { ImagePickItem } from '@arco-design/mobile-react/cjs/image-picker/type';
import YkCellLabel from "@/components/YkCellLabel";
import YkImagePicker from '@/components/YkImagePicker';

// API
import { uploadSingleFile, uploadImageWechat } from '@/utils/api/common/common_user';
import { createAndSubmitAccount } from '@/utils/api/common/common_wechat';

// 类型和工具函数
import { MerchantType, AccountType } from '../types';
import {
  BankCardInfo,
  BankCardOCRResult,
  FormErrors
} from './types';
import {
  validateForm,
  formatBankCardNumber,
  cleanBankCardNumber
} from './utils';



export default function EditSettlement() {
  const router = useRouter();
  const [merchantType, setMerchantType] = useState<MerchantType>(MerchantType.ENTERPRISE);
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [isFormValid, setIsFormValid] = useState<boolean>(false);
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  // ==================== 银行卡信息状态 ====================
  const [bankCardInfo, setBankCardInfo] = useState<BankCardInfo>({
    type: 'grzh',
    image: null,
    bankCard: {
      name: '',
      number: '',
      branch: '',
    },
    wechat: {}
  });



  useEffect(() => {
    // 获取商户类型
    const { merchantType: mType = 0 } = router.params;
    setMerchantType(Number(mType) as MerchantType);

    // 设置默认账户类型
    if (Number(mType) === MerchantType.ENTERPRISE) {
      setBankCardInfo(prev => ({ ...prev, type: 'dgzh' }));
    } else {
      setBankCardInfo(prev => ({ ...prev, type: 'grzh' }));
    }

    // 从缓存获取用户信息
    const userInfo = Taro.getStorageSync('userInfo');
    if (userInfo?.userInfo?.name) {
      setBankCardInfo(prev => ({
        ...prev,
        bankCard: {
          ...prev.bankCard,
          name: userInfo.userInfo.name
        }
      }));
    }

  }, []);

  // 实时验证表单
  const validateFormRealtime = useCallback((info: BankCardInfo) => {
    const errors = validateForm(info);
    const isValid = Object.keys(errors).length === 0;
    setIsFormValid(isValid);
    return isValid;
  }, []);

  // 监听bankCardInfo变化，进行实时验证
  useEffect(() => {
    validateFormRealtime(bankCardInfo);
  }, [bankCardInfo, validateFormRealtime]);

  // 更新银行卡信息
  const updateBankCardInfo = useCallback((
    category: keyof BankCardInfo,
    field: string,
    value: any
  ) => {
    let newBankCardInfo: BankCardInfo;

    if (category === 'type') {
      newBankCardInfo = { ...bankCardInfo, type: value };
      setBankCardInfo(newBankCardInfo);
      // 切换账户类型时清空表单错误
      setFormErrors({});
    } else if (category === 'image') {
      newBankCardInfo = { ...bankCardInfo, image: value };
      setBankCardInfo(newBankCardInfo);
    } else {
      newBankCardInfo = {
        ...bankCardInfo,
        bankCard: {
          ...bankCardInfo.bankCard,
          [field]: value
        }
      };
      setBankCardInfo(newBankCardInfo);
      // 清除对应字段的错误
      if (formErrors[field as keyof FormErrors]) {
        setFormErrors(prev => ({ ...prev, [field]: undefined }));
      }
    }

    // 实时验证表单
    validateFormRealtime(newBankCardInfo);
  }, [bankCardInfo, formErrors, validateFormRealtime]);

  // 处理输入变化
  const handleInputChange = useCallback((
    e: ChangeEvent<HTMLInputElement>,
    field: keyof BankCardInfo['bankCard']
  ) => {
    console.log('[handleInputChange]');
    let value = e.target.value;
    if (field === 'number') {
      // 格式化银行卡号
      value = formatBankCardNumber(value);
    }
    updateBankCardInfo('bankCard', field, value);
  }, [updateBankCardInfo]);

  // 处理银行卡OCR结果（一次性批量更新，避免多次setState引发的覆盖）
  const handleBankCardOCRResult = useCallback((ocrResult?: BankCardOCRResult) => {
    console.log('[handleBankCardOCRResult]  ocrResult：' , ocrResult);
    if (ocrResult && ocrResult.words_result) {
      const data = ocrResult.words_result;

      // 预处理字段
      const nextNumber = data.bank_card_number?.words
        ? formatBankCardNumber(data.bank_card_number.words)
        : undefined;
      const nextBranch = data.bank_name?.words || undefined;

      // 批量写入，使用函数式更新，确保多个字段同时生效
      setBankCardInfo(prev => {
        const next = {
          ...prev,
          bankCard: {
            ...prev.bankCard,
            ...(nextNumber ? { number: nextNumber } : {}),
            ...(nextBranch ? { branch: nextBranch } : {}),
          },
        };
        // 异步校验，避免闭包值不一致
        setTimeout(() => validateFormRealtime(next), 0);
        return next;
      });

      Toast.success({ content: '识别成功', duration: 2000 });
    } else {
      console.log('[handleBankCardOCRResult] error! ', ocrResult);
      Toast.error({ content: '识别失败，请手动输入', duration: 2000 });
    }
  }, [validateFormRealtime]);

  useEffect(() => {
    console.log('[useEffect] bankCardInfo: ', bankCardInfo);
  }, [bankCardInfo]);

  // 处理开户证明OCR结果
  const handleAccountOpeningOCRResult = useCallback((ocrResult?: any) => {
    if (ocrResult && ocrResult.words_result) {
      console.log('[handleAccountOpeningOCRResult] OCR结果: ', ocrResult);
      const data = ocrResult.words_result;

      // 处理数组格式的字段值，参照merchantApplication但加上数组处理
      const getFieldValue = (field: any) => {
        if (!field?.word) return '';
        // 如果是数组格式，取第一个元素
        if (Array.isArray(field.word)) {
          return field.word.length > 0 ? field.word[0] : '';
        }
        // 如果是字符串格式，直接返回
        return field.word || '';
      };

      // 简化银行名称：去除第一个"银行"后面的部分
      const simplifyBankName = (bankName: string): string => {
        if (!bankName) return '';

        const bankIndex = bankName.indexOf('银行');
        if (bankIndex !== -1) {
          // 找到"银行"，截取到"银行"结束的位置
          return bankName.substring(0, bankIndex + 2);
        }

        // 如果没有找到"银行"，返回原名称
        return bankName;
      };

      const companyName = getFieldValue(data.公司名称);
      const accountNumber = getFieldValue(data.账号);
      const rawBankBranch = getFieldValue(data.开户银行);
      const bankBranch = simplifyBankName(rawBankBranch);

      console.log('[handleAccountOpeningOCRResult] 提取的字段值:', {
        companyName,
        accountNumber,
        rawBankBranch,
        bankBranch: `${rawBankBranch} -> ${bankBranch}`
      });

      // 使用函数式更新确保获取最新状态，避免图片丢失
      setBankCardInfo(currentBankCardInfo => {
        console.log('[handleAccountOpeningOCRResult] 当前状态:', currentBankCardInfo);

        const updatedBankCard = { ...currentBankCardInfo.bankCard };
        let hasUpdates = false;

        if (companyName) {
          updatedBankCard.name = companyName;
          hasUpdates = true;
        }
        if (accountNumber) {
          updatedBankCard.number = accountNumber;
          hasUpdates = true;
        }
        if (bankBranch) {
          updatedBankCard.branch = bankBranch;
          hasUpdates = true;
        }

        if (hasUpdates) {
          const newBankCardInfo = {
            ...currentBankCardInfo,  // 保留type、image等原有字段
            bankCard: updatedBankCard
          };
          console.log('[handleAccountOpeningOCRResult] 更新后状态:', newBankCardInfo);

          // 异步调用验证，避免闭包问题
          setTimeout(() => validateFormRealtime(newBankCardInfo), 0);

          return newBankCardInfo;
        }

        return currentBankCardInfo;
      });

      if (companyName || accountNumber || bankBranch) {
        Toast.success({ content: '识别成功', duration: 2000 });
      } else {
        Toast.info({ content: '未识别到有效信息', duration: 2000 });
      }
    } else {
      console.log('[handleAccountOpeningOCRResult] OCR识别失败或无结果: ', ocrResult);
    }
  }, [validateFormRealtime]);

  // 处理图片上传到微信服务器
  const handleImageUploadWechat = useCallback(async (img: ImagePickItem) => {
    try {
      if (!img.file) {
        throw new Error('文件不存在');
      }

      // 1. 上传到微信服务器
      const wechatResponse = await uploadImageWechat(img.file);
      const { mediaId } = wechatResponse.data.mediaId;
      console.log('[handleImageUploadWechat] 微信上传响应: ', wechatResponse);

      // 2. 同时上传到本地服务器进行备份，用于OCR识别
      let localUrl = '';
      try {
        const localResponse = await uploadSingleFile(img.file);
        localUrl = localResponse.data || localResponse; // 根据实际响应结构调整
        console.log('[handleImageUploadWechat] 本地备份上传响应: ', localResponse);
      } catch (localError) {
        console.warn('[handleImageUploadWechat] 本地备份上传失败: ', localError);
        // 本地备份失败不影响微信上传的结果
      }

      // 3. 更新微信相关状态
      setBankCardInfo(prev => ({
        ...prev,
        wechat: { ...prev.wechat, mediaId }
      }));

      // 返回localUrl用于OCR识别，如果没有localUrl则返回mediaId
      return localUrl || mediaId;
    } catch (error) {
      console.log('[handleImageUploadWechat] error: ', error);
      Toast.error({ content: '图片上传失败', duration: 2000 });
      throw error;
    }
  }, []);

  // 处理图片上传到自己的服务器
  const handleImageUpload = useCallback(async (img: ImagePickItem) => {
    try {
      const response = await uploadSingleFile(img.file);
      console.log('[handleImageUpload] response: ', response);

      // 返回图片URL用于OCR识别
      return response.data || response;
    } catch (error) {
      console.log('[handleImageUpload] error: ', error);
      Toast.error({ content: '上传失败', duration: 2000 });
      throw error;
    }
  }, []);

  // 提交表单
  const handleSubmit = useCallback(async () => {
    // 表单验证
    const errors = validateForm(bankCardInfo);
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      // 显示第一个错误
      const firstError = Object.values(errors)[0];
      if (firstError) {
        Toast.error({ content: firstError, duration: 2000 });
      }
      return;
    }

    try {
      // 获取用户信息
      const userInfo = Taro.getStorageSync('userInfo');
      const userId = userInfo?.userInfo?.id || userInfo?.id;

      if (!userId) {
        Toast.error({ content: '获取用户信息失败，请重新登录', duration: 2000 });
        return;
      }

      // 清理银行卡号格式
      const cleanedInfo = {
        ...bankCardInfo,
        bankCard: {
          ...bankCardInfo.bankCard,
          number: cleanBankCardNumber(bankCardInfo.bankCard.number)
        }
      };

      // 将前端账户类型转换为微信支付枚举字符串
      // 根据微信支付API文档要求：
      // ACCOUNT_TYPE_BUSINESS: 对公银行账户
      // ACCOUNT_TYPE_PRIVATE: 经营者个人银行卡
      const getAccountTypeEnum = (accountType: AccountType, merchantType: MerchantType): string => {
        if (merchantType === MerchantType.ENTERPRISE) {
          // 企业只能使用对公账户
          return 'ACCOUNT_TYPE_BUSINESS';
        } else if (merchantType === MerchantType.INDIVIDUAL) {
          // 个体工商户可以选择对公或对私账户
          return accountType === 'dgzh' ? 'ACCOUNT_TYPE_BUSINESS' : 'ACCOUNT_TYPE_PRIVATE';
        } else if (merchantType === MerchantType.PERSONAL || merchantType === MerchantType.SELLER) {
          // 个人只能使用对私账户
          return 'ACCOUNT_TYPE_PRIVATE';
        }
        // 默认返回对私账户
        return 'ACCOUNT_TYPE_PRIVATE';
      };

      // 构造API请求数据
      const requestData = {
        userId: userId,
        accountType: getAccountTypeEnum(cleanedInfo.type, merchantType), // 微信支付枚举：ACCOUNT_TYPE_BUSINESS | ACCOUNT_TYPE_PRIVATE
        accountName: cleanedInfo.bankCard.name, // 开户名称/持卡人
        accountNumber: cleanedInfo.bankCard.number, // 银行账号/银行卡号
        accountBank: cleanedInfo.bankCard.branch, // 开户行（必填）/ 开户银行全称（含支行）（选填）
      };

      console.log('[handleSubmit] 提交数据: ', requestData);

      // 调用提交API
      const response = await createAndSubmitAccount(requestData);
      console.log('[handleSubmit] API响应: ', response);

      Toast.success({ content: '提交成功', duration: 2000 });
      setTimeout(() => {
        Taro.navigateBack();
      }, 2000);

    } catch (error) {
      console.error('[handleSubmit] 提交失败: ', error);
      Toast.error({ content: '提交失败，请重试', duration: 2000 });
    }
  }, [bankCardInfo]);

  // 是否显示账户类型选择
  const showAccountTypeSelect = merchantType === MerchantType.INDIVIDUAL;

  return (
    <View className="editSettlementPage">
      {platform !== "WX" &&<YkNavBar title="修改结算信息" />}
      <View className="content">
        <Cell.Group className="yk-cell-group" bordered={false}>
          {/* 账户类型 - 只有个体工商户可以选择 */}
          {showAccountTypeSelect && (
            <Cell 
              className="zhxx-cell-zhlx"
              label={<YkCellLabel label="账户类型" />}
            >
              <View className="zhxx-cell-zhlx-content">
                <Radio.Group
                  value={bankCardInfo.type}
                  onChange={(value: AccountType) => updateBankCardInfo('type', '', value)}
                >
                  <Radio value="grzh">个人账户</Radio>
                  <Radio value="dgzh">对公账户</Radio>
                </Radio.Group>
              </View>
            </Cell>
          )}

          {/* 持卡人/开户名称 */}
          <Cell
            className={`${bankCardInfo.type === 'grzh' ? 'zhxx-cell-ckr' : 'zhxx-cell-khmc'} ${formErrors.name ? 'has-error' : ''}`}
            label={<YkCellLabel label={bankCardInfo.type === 'grzh' ? '持卡人' : '开户名称'} />}
          >
            <Input
              className={bankCardInfo.type === 'grzh' ? 'zhxx-cell-ckr-content' : 'zhxx-cell-khmc-content'}
              placeholder={bankCardInfo.type === 'grzh' ? '请输入持卡人姓名' : '请输入开户名称'}
              value={bankCardInfo.bankCard.name}
              onChange={(e) => handleInputChange(e, 'name')}
              border="none"
              inputStyle={{ textAlign: 'right' }}
            />
          </Cell>

          {/* 银行卡号/银行账号 */}
          <Cell
            className={`zhxx-cell-yhkh ${formErrors.number ? 'has-error' : ''}`}
            label={<YkCellLabel label={bankCardInfo.type === 'grzh' ? '银行卡号' : '银行账号'} />}
          >
            <Input
              placeholder={bankCardInfo.type === 'grzh' ? '请输入银行卡号' : '请输入银行账号'}
              value={bankCardInfo.bankCard.number}
              onChange={(e) => handleInputChange(e, 'number')}
              type="text"
              border="none"
              inputStyle={{ textAlign: 'right' }}
            />
          </Cell>

          {/* 所属支行 */}
          <Cell
            className={`zhxx-cell-sszh ${formErrors.branch ? 'has-error' : ''}`}
            label={<YkCellLabel label="所属支行" />}
          >
            <Input
              placeholder="请输入开户支行名称"
              value={bankCardInfo.bankCard.branch}
              onChange={(e) => handleInputChange(e, 'branch')}
              border="none"
              inputStyle={{ textAlign: 'right' }}
            />
          </Cell>

          {/* 银行卡照片/开户证明 */}
          <Cell
            className="zhxx-cell-khzm"
            label={<YkCellLabel label={bankCardInfo.type === 'grzh' ? '银行卡照片' : '开户证明'} hint={bankCardInfo.type === 'grzh' ? "" : "(选填)"}/>}
            append={
              <View className="yk-imagebox-content">
                {bankCardInfo.type === 'dgzh' && (
                  <Text className="yk-imagebox-content-hint">可上传开户许可证，用于平台审核</Text>
                )}
                <View className="yk-imagebox-content-wrapper">
                  <View className="yk-imagebox-content-wrapper-left">
                    <YkImagePicker
                      type={bankCardInfo.type === 'grzh' ? 'bankCard' : 'accountOpening'}
                      value={bankCardInfo.image}
                      onChange={
                        img => {
                          updateBankCardInfo('image', '', img);
                          if (!img) {
                            setBankCardInfo(prev => ({ ...prev, bankCard: { ...prev.bankCard, branch: '', name: '', number: '' } }));
                          }
                        }
                      }
                      upload={bankCardInfo.type === 'grzh' ? handleImageUploadWechat : handleImageUpload}
                      onOCRResult={bankCardInfo.type === 'grzh' ? handleBankCardOCRResult : handleAccountOpeningOCRResult}
                      label={bankCardInfo.type === 'grzh' ? '银行卡照片（卡号面）' : '开户许可证'}
                    />
                  </View>
                  <View className="yk-imagebox-content-wrapper-right"></View>
                </View>

                {/* {bankCardInfo.type === 'grzh' && bankCardInfo.image && (
                  <View className="cell-noticebar">
                    <NoticeBar
                      className="cell-noticebar-content"
                      style={{ color: '#00B42A', backgroundColor: '#E8FFEA', marginTop: 12 }}
                      closeable={false}
                    >
                      请核对识别结果，如有错误请手动修正
                    </NoticeBar>
                  </View>
                )} */}
              </View>
            }
          />
        </Cell.Group>
      </View>

      <View className="holder"></View>

      <View className="footerbtn">
        <Button
          type="primary"
          className={`footerbtn-btn ${!isFormValid ? 'disabled' : ''}`}
          disabled={!isFormValid}
          onClick={handleSubmit}
        >
          <Text className="footerbtn-btn-text">提交申请</Text>
        </Button>
      </View>
    </View>
  );
}
