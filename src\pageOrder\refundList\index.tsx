import React, { useState, useEffect, useCallback  ,useRef   } from "react";
import { View, Image, Text } from "@tarojs/components";
import {
  Cell,
  PullRefresh,
  LoadMore,
  Loading,
} from "@arco-design/mobile-react";
import { IconPayment } from "@/components/YkIcons";
import { LoadMoreStatus } from "@arco-design/mobile-react/cjs/load-more";
import { getRefundList, getUserOrderDetails } from "@/utils/api/common/common_user";
import YkNavBar from "@/components/ykNavBar";
import Taro, { useReachBottom, useRouter, usePageScroll } from "@tarojs/taro";
import "./index.less";
import { toast } from "@/utils/yk-common";
// 退款项接口
interface RefundItem {
  id: number;
  userOrderId: number;
  reason: string;
  refundAmount: number;
  refundPostage: number;
  createTime: number;
  // 退款商品详情
  dynamics: Array<{
    dynamicsId: number;
    content: string;
    price: number;
    pictures: string;
    orderRefundDetails: Array<{
      orderDetailId: number;
      specName: string;
      colorName: string;
      refundQuantity: number;
    }>;
  }>;
}

export default function RefundList() {
  const router = useRouter();
  const { orderId, orderType } = router.params;

  const [refundList, setRefundList] = useState<RefundItem[]>([]);
  const [orderDetails, setOrderDetails] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [canPullRefresh, setCanPullRefresh] = useState(true);
  const [loadMoreStatus, setLoadMoreStatus] = useState<LoadMoreStatus>("default");
  const [pageNo, setPageNo] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const pageSize = 10;

  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);

  // 获取订单详情
  const fetchOrderDetails = useCallback(async () => {
    if (!orderId) return;

    try {
      const response = await getUserOrderDetails({ id: parseInt(orderId) });
      if (response && response.code === 0) {
        setOrderDetails(response.data);
      }
    } catch (error) {
      console.error("获取订单详情失败:", error);
    }
  }, [orderId]);

  // 加载退款列表
  const loadRefundList = useCallback(async (page: number = 1, isRefresh: boolean = false) => {
    if (loading) return;

    try {
      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
        if (page > 1) {
          setLoadMoreStatus("loading");
        }
      }

      const params: any = {
        pageNo: page,
        pageSize,
      };


      // 如果有指定订单ID，则添加到参数中
      if (router.params.orderId) {
        params.userOrderId = parseInt(router.params.orderId);
      }

      const response = await getRefundList(params);

      if (response && response.code === 0) {
        const newList = response.data?.list || [];

        if (isRefresh || page === 1) {
          setRefundList(newList);
        } else {
          setRefundList(prev => [...prev, ...newList]);
        }

        // 判断是否还有更多数据
        const total = response.data?.total || 0;
        const currentTotal = isRefresh || page === 1 ? newList.length : refundList.length + newList.length;
        setHasMore(currentTotal < total);

        if (page > 1) {
          setLoadMoreStatus(currentTotal < total ? "default" : "noMore");
        }
      } else {
        toast("error", {
          content: response.msg || "加载失败",
          duration: 2000,
        });
        if (page > 1) {
          setLoadMoreStatus("error");
        }
      }
    } catch (error) {
      console.error("加载退款列表失败:", error);
      toast("error", {
        content: "网络错误",
        duration: 2000,
      });
      if (page > 1) {
        setLoadMoreStatus("error");
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [loading, router.params.orderId, refundList.length]);

  // 下拉刷新
  const handleRefresh = useCallback(async () => {
    setPageNo(1);
    await loadRefundList(1, true);
  }, [loadRefundList]);

  // 上拉加载更多
  const handleLoadMore = useCallback(async () => {
    if (!hasMore || loading) return;
    const nextPage = pageNo + 1;
    setPageNo(nextPage);
    await loadRefundList(nextPage);
  }, [hasMore, loading, pageNo, loadRefundList]);

  // 监听页面滚动，控制下拉刷新
  usePageScroll(({ scrollTop }) => {
    // 当滚动位置为0时才允许下拉刷新
    setCanPullRefresh(scrollTop === 0);
  });

  // 触底加载更多
  useReachBottom(() => {
    handleLoadMore();
  });

  // 初始化加载
  useEffect(() => {
    fetchOrderDetails();
    loadRefundList(1);
  }, [fetchOrderDetails]);

  // 点击退款项跳转到详情
  const handleRefundItemClick = (refund: RefundItem) => {
    // 构造退款详情数据
    const refundDetailData = {
      refundId: refund.id,
      userOrderId: refund.userOrderId,
      reason: refund.reason,
      refundAmount: refund.refundAmount,
      refundPostage: refund.refundPostage,
      createTime: refund.createTime,
      dynamics: refund.dynamics,
      // 从订单详情中获取的信息
      shop: orderDetails?.shop || "",
      dynamicUserAvatar: orderDetails?.dynamicUserAvatar || "",
      express: orderDetails?.express || "",
      // 添加订单类型信息，用于判断跳转到买家还是卖家订单详情
      orderType: orderType || "buyer", // 默认为买家订单
    };

    Taro.navigateTo({
      url: `/pageOrder/refundDetail/index?refundData=${encodeURIComponent(JSON.stringify(refundDetailData))}`,
    });
  };

  return (
    <View className="refund-list-page">
     {platform !== "WX" && <YkNavBar title="退款详情" />}

      <PullRefresh
        disabled={loading || !canPullRefresh}
        onRefresh={handleRefresh}
        finishDelay={1000}
        loadingText={
          <View className="pull-refresh-loading">
            <Loading type="dot" radius={8} />
            <Text className="loading-text">正在刷新...</Text>
          </View>
        }
        finishText={<Text className="pull-refresh-success">刷新成功</Text>}
      >
        <View className="order-list-refund">
          {refundList.map((refund, idx) => (
            <View className="order-card" key={idx}>
              <Cell
                bordered={false}
                label={
                  <View className="shop-info">
                    <Image
                      className="shop-avatar"
                      src={orderDetails?.dynamicUserAvatar || ""}
                      mode="aspectFill"
                    />
                    <Text className="shop-name">{orderDetails?.shop || "商家"}</Text>
                    {/* <Image
                      className="verified-icon"
                      src={require("@/assets/images/common/wx_pay.png")}
                      mode="aspectFit"
                    /> */}
                    <IconPayment className="verified-icon" />
                  </View>
                }
                text={
                  (<Text className="order-status">已退款</Text>) as any
                }
                onClick={() => handleRefundItemClick(refund)}
                showArrow
              />

              <View className="goods-list"
                onClick={() => handleRefundItemClick(refund)}
              >
                {refund.dynamics?.map((dynamic, dynamicIdx) => (
                  <View className="goods-item" key={dynamicIdx}>
                    <Image
                      className="goods-img"
                      src={dynamic.pictures?.split(',')[0] || ""}
                      mode="aspectFill"
                    />
                    <View className="goods-info">
                      <View className="goods-title-row">
                        <Text className="goods-title">{dynamic.content || "商品"}</Text>
                        <View className="goods-price-section">
                          <Text className="goods-price">￥{dynamic.price || 0}</Text>
                          <Text className="goods-quantity">x{dynamic.orderRefundDetails?.reduce((sum, detail) => sum + detail.refundQuantity, 0) || 0}</Text>
                        </View>
                      </View>
                      {/* 显示SKU信息 */}
                      {dynamic.orderRefundDetails?.map((detail, detailIdx) => (
                        <View className="goods-sku" key={detailIdx}>
                          <Text className="sku-text">
                            {detail.colorName} {detail.specName}
                          </Text>
                          <View className="sku-count">
                            <Text className="refund-quantity-refund">{`x${detail.refundQuantity}`}</Text>
                          </View>
                        </View>
                      ))}
                    </View>
                  </View>
                ))}
              </View>

              {/* 退款原因信息 */}
              {refund.reason && (
                <View className="remark-section">
                  <View className="remark-row">
                    <Text className="remark-label">退款原因：</Text>
                    <View className="remark-label-block">
                      <View>
                        <Text className="remark-content">
                          {refund.reason}
                        </Text>
                      </View>
                    </View>
                  </View>
                </View>
              )}

              <View className="order-summary"
                onClick={() => handleRefundItemClick(refund)}
              >
                <Text className="express">{orderDetails?.delivery}</Text>
                <View className="order-summary-right">
                  <Text className="order-time"></Text>
                  <Text className="order-total">
                    <View className="time">{refund.createTime ? new Date(refund.createTime).toLocaleString() : ""} 退款</View>
                    <Text className="goods-count">退款{refund.dynamics?.reduce((sum, dynamic) => sum + (dynamic.orderRefundDetails?.reduce((detailSum, detail) => detailSum + detail.refundQuantity, 0) || 0), 0) || 0}件</Text>
                    <Text className="total-price">￥{((refund.refundAmount || 0) + (refund.refundPostage || 0)).toFixed(2)} </Text>
                    {refund.refundPostage>0&&(
                    <Text className="freight-text">
                     (含运费￥{refund.refundPostage})
                  </Text>
                )}
                </Text>
                  {/* <View className="refund-amount">
                    <Text className="refund-label">退款金额</Text>
                    <Text className="refund-value">￥{((refund.refundAmount || 0) + (refund.refundPostage || 0)).toFixed(2)}</Text>
                  </View> */}
                </View>
              </View>
            </View>
          ))}

          {refundList.length === 0 && <View className="empty-order"></View>}

          {pageNo > 1 && (
          <LoadMore
            style={{ paddingTop: 16, paddingBottom: 20 }}
            status={loadMoreStatus}
          />
          )}
        </View>
      </PullRefresh>
    </View>
  );
}