import React from 'react';
import { View, Text } from '@tarojs/components';
import { NoticeBar } from '@arco-design/mobile-react';
import {
  IconWarnCircle,
  IconSuccessCircle
} from '@arco-design/mobile-react/esm/icon';
import { IconSchedule } from '@/components/YkIcons';

import './index.less';

export type NoticeType = 'warning' | 'success' | 'schedule' | 'info';

interface YkNoticeBarProps {
  /** 提示类型，默认为 info */
  type?: NoticeType;
  /** 提示标题 */
  title?: string;
  /** 提示内容，支持字符串或 React 节点 */
  content?: string | React.ReactNode;
  /** 自定义左侧图标 */
  icon?: React.ReactNode;
  /** 是否显示图标，默认为 true */
  showIcon?: boolean;
  /** 是否可关闭，默认为 false */
  closeable?: boolean;
  /** 是否支持滚动，默认为 none */
  marquee?: 'none' | 'overflow' | 'always';
  /** 是否支持换行，默认为 true */
  wrapable?: boolean;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 自定义类名 */
  className?: string;
  /** 关闭回调 */
  onClose?: () => void;
  /** 点击回调 */
  onClick?: () => void;
}

export default function YkNoticeBar({
  type = 'info',
  title,
  content,
  icon,
  showIcon = true,
  closeable = false,
  marquee = 'none',
  wrapable = true,
  style,
  className = '',
  onClose,
  onClick
}: YkNoticeBarProps) {
  // 根据类型获取默认图标
  const getDefaultIcon = () => {
    if (!showIcon) return null;
    if (icon) return icon;

    switch (type) {
      case 'success':
        return <IconSuccessCircle />;
      case 'warning':
        return <IconWarnCircle />;
      case 'schedule':
        return <IconSchedule />;
      case 'info':
        return <IconWarnCircle />;
      default:
        return (
          <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
            <path d="M8 16A8 8 0 1 1 8 0a8 8 0 0 1 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z"/>
          </svg>
        );
    }
  };

  // 获取样式类名
  const getTypeClassName = () => {
    return `yk-notice-bar--${type}`;
  };

  // 渲染内容
  const renderContent = () => {
    if (title && content) {
      // 有标题和内容
      return (
        <View className="yk-notice-bar__content">
          <View className="yk-notice-bar__header">
            <Text className="yk-notice-bar__title">{title}</Text>
          </View>
          <View className="yk-notice-bar__body">
            {typeof content === 'string' ? (
              <Text className="yk-notice-bar__text">{content}</Text>
            ) : (
              content
            )}
          </View>
        </View>
      );
    } else if (title) {
      // 只有标题
      return (
        <Text className="yk-notice-bar__title">{title}</Text>
      );
    } else if (content) {
      // 只有内容
      return typeof content === 'string' ? (
        <Text className="yk-notice-bar__text">{content}</Text>
      ) : (
        content
      );
    }
    return null;
  };

  return (
    <View 
      className={`yk-notice-bar ${getTypeClassName()} ${className}`}
      onClick={onClick}
    >
      <NoticeBar
        className="yk-notice-bar__inner"
        leftContent={getDefaultIcon()}
        closeable={closeable}
        marquee={marquee}
        wrapable={wrapable}
        style={style}
        onClose={onClose}
      >
        {renderContent()}
      </NoticeBar>
    </View>
  );
}
