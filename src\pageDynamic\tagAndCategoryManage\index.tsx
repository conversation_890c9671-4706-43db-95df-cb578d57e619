import { View } from "@tarojs/components";
import YkNavBar from "@/components/ykNavBar/index";
import { Cell } from "@arco-design/mobile-react";
import Taro from '@tarojs/taro';
import { useEffect ,useRef , useState } from "react";
import "./index.less";
export default function TagManage() {
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  return (
    <View >
     {platform !== "WX" && <YkNavBar title="标签管理" />}
      <View className="tag-manage-list" >
        <Cell.Group bordered={false}>
          <Cell
            label="标签管理"
            className="tag-manage-cell"
            showArrow
            
            onClick={() => {
              // 跳转到标签管理页面
              Taro.navigateTo({
                url: '/pageDynamic/tagAndCategoryManage/tag/index'
              })
            }}
          />
          <Cell
            label="目录管理"
            className="tag-manage-cell"
            showArrow
            style={{ color: 'red' }}
            onClick={() => {
              // 跳转到目录管理页面
              Taro.navigateTo({
                url: '/pageDynamic/tagAndCategoryManage/catalog/index'
              })
            }}
          />
        </Cell.Group>
      </View>
    </View>
  );
}
