@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pageDynamic/album/batchForward/index"] {
  .dline {
    position: relative;
    width: 100%;
    padding: 15px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-bottom: 1px solid @dark-line-color;
  });

    &-left {
      margin-left: 15px;
      display: flex;
      align-items: center;
      justify-content: center;

      &-change {
        width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    &-right {
      margin-right: 15px;
      margin-left: 15px;
      flex: 1;
      height: 100%;
      display: block;

      &-top {
        padding: 5px;
        font-size: 13px;
        color: #333333;
        .use-dark-mode-query({
        color: var(--dark-font-color);
      });
        display: block;
        text-align: left;
        width: 100%;
        max-width: calc(100vw - 60px);

        .dynamic-title {
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          display: block;
          line-height: 1.4;
          text-align: left;
          width: 100%;
          max-width: 100%;
        }

        &.topBg {
          background-color: #f3f4f6;
          border-radius: 4px;
          padding: 10px;
        }
      }

      &-bottom {
        .rem(margin-top, 5);
        width: 100%;
        .rem(height, 55);
        display: grid;
        grid-template-columns: repeat(auto-fill, 55px);
        grid-gap: 0rpx 5px;

        .imageItem {
          position: relative;
          width: 55px;
          height: 55px;

          &-image {
            width: 55px;
            height: 55px;
            display: block;
          }

          &-mask {
            position: absolute;
            top: 0;
            left: 0;
            width: 55px;
            height: 55px;
            background-color: rgba(0, 0, 0, 0.4);
            font-size: 15px;
            color: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
          }
        }
      }
    }
  }

  .footerBtnBox {
    position: fixed;
    width: 100%;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50px;
    padding: 0 15px;
    background-color: #ffffff;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
    z-index: 3090;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-top: 1px solid @dark-line-color;
  });
    box-sizing: border-box;

    &-change {
      flex: 1;
      display: flex;
      align-items: center;

      &-image {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
      }

      &-c {
        display: flex;
        align-items: center;
        justify-content: center;
        &-img {
          width: 8px;
          height: 12px;
          display: block;
          margin-left: 10px;
        }

        &-text {
          font-size: 13px;
          color: #333333;
          .use-dark-mode-query({
          color: var(--dark-font-color);
        });
        }
      }
    }

    &-btn {
      width: 130px !important;
      height: 36px !important;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6px !important;
      background-color: var(--primary-color)  !important;
      opacity: 1;
      color: #ffffff !important;
      font-size: 13px !important;
      border: none !important;
      padding: 0 !important;
      margin: 0 !important;
      min-width: 130px !important;
      max-width: 130px !important;
      flex-shrink: 0;
      box-sizing: border-box;
    }

    &-notbtn {
      width: 130px !important;
      height: 36px !important;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6px !important;
      background-color: var(--primary-color) !important;
      opacity: 0.3;
      color: #ffffff !important;
      font-size: 13px !important;
      border: none !important;
      padding: 0 !important;
      margin: 0 !important;
      min-width: 130px !important;
      max-width: 130px !important;
      flex-shrink: 0;
      box-sizing: border-box;
    }
  }


  // 空状态样式
  .not_content {
    position: relative;
    width: 100%;
    height: 50vh;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    font-size: 15px;
    font-weight: normal;
    color: #999999;

    .not_content-image {
      width: 100px;
      height: 100px;
      display: block;
      margin-bottom: 15px;
    }
  }

  // 加载状态样式
  .notmorelist {
    position: relative;
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999999;
    font-size: 12px;
  }

  // 内容区域样式
  .boxContent {
    position: relative;
    width: 100%;
    overflow-y: auto;
    height: calc(100vh - 140px);
    -webkit-overflow-scrolling: touch;
  }

  // 底部占位
  .footer_content_z {
    position: relative;
    width: 100%;
    height: 64px;
  }

  /* 搜索栏样式 */
  .user-header-searchbar-wrap {
    display: flex;
    align-items: center;
    background-color: #fff;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
  }

  .user-header-searchbar {
    flex: 1;
    gap: 10px;
    padding: 10px 16px !important;
  }

  .user-header-filter {
    color: var(--primary-color);
    font-size: 15px;
    margin: 0px;
  }
}
