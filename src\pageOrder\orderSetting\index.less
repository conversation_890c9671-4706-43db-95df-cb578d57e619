@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageOrder/orderSetting/index"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });

  .orderSettingPage {
    position: relative;
    width: 100%;
    height: 100vh;

    .container {
      display: flex;
      flex-direction: column;
      padding: 0 10px;

      &-title {
        margin-top: 15px;
        padding: 8px 16px;
        font-size: 15px;
        color: #999999;
      }

      &-box {
        background-color: #ffffff;
        border-radius: 10px;
        overflow: hidden;
        margin-top: 10px;

        &-cell {
          margin-left: 16px;
        }
      }
    }
  }
}
