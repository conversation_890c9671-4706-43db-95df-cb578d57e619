@import "@arco-design/mobile-react/style/mixin.less";

.classify-modal {

  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;

  .classify-modal-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    z-index: 1;
  }

  .classify-modal-content {
    position: absolute;
    top: 20%;
    left: 0;
    right: 0;
    bottom: 0;
    // background: #fff;
      background: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
      background: @dark-background-color !important;
    });
    border-radius: 24px 24px 0 0;
    animation: slideUp 0.3s ease-out;
    z-index: 2;
    overflow: hidden;

    &.empty {
      top: 20%; // 修改为与有数据时一致的高度
      min-height: 300px;
      height: auto;
      bottom: 0;
    }
  }

  .classify-modal-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    font-size: 16px;
    color: #666;
  }

  .classify-modal-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 300px;
    padding: 40px;

    .empty-icon {
      font-size: 40px;
      margin-bottom: 20px;
      opacity: 0.3;
    }

    .empty-text {
      font-size: 16px;
      color: #999;
      margin-bottom: 40px;
    }

    .empty-actions {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .empty-btn {
        width: 88px;
        height: 44px;
        background: #007aff;
        color: #fff;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        font-weight: 500;
      }
    }
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
} 

