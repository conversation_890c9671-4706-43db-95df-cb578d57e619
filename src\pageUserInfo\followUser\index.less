  @import "@arco-design/mobile-react/style/mixin.less";
.follow-user {
  width: 100%;
  height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .nav-bar {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    background-color: #fff;
    border-bottom: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-bottom: 1px solid @dark-line-color;
  });
    
    .nav-title {
      font-size: 18px;
      font-weight: 500;
      color: #333;
    }
  }
  
  .follow-info-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    margin-top: 140px;
    gap: 10px;
    
    .avatar {
      width: 120px;
      height: 120px;
      border-radius: 60px;
      margin-bottom: 20px;
      border: 2px solid #eee;
    }
    
    .nickname {
      font-size: 17px;
      font-weight: bold;
      color: #333;
    }
    
    .hint-text {
      font-size: 14px;
      color: #999999;
      text-align: center;
      margin-top: 5px;
      }
    }

    // 🔧 右下角气泡消息样式
    .bubble-message-container {
      position: fixed;
      bottom: 60px;
      right: 20px;
      z-index: 100;
      cursor: pointer;
      
      .bubble-wrapper {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        
        .bubble-content {
          background: #05C160; // 从设计稿中提取的绿色
          border-radius: 8px;
          padding: 10px 15px;
          box-shadow: 0 2px 8px rgba(5, 193, 96, 0.3);
          
          .bubble-text {
            color: #FFFFFF;
            font-size: 15px;
            font-weight: 500;
            white-space: nowrap;
            line-height: 21px;
          }
        }
        
        // 箭头位于气泡底部，参考图2效果
        .bubble-arrow {
          &.down {
            position: absolute;
            bottom: -10px; // 箭头紧贴气泡底部
            right: 20px;  // 距离右边20px，实现中央偏右效果
            background: transparent; // 透明背景
            z-index: 1;
          }
        }
      }
      
      // 点击效果
      &:active {
        transform: scale(0.95);
        transition: transform 0.1s ease-in-out;
      }
      
      // 悬浮动画效果
      animation: bubbleBounce 2s infinite ease-in-out;
    }
  } 

  // 气泡消息悬浮动画
  @keyframes bubbleBounce {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-5px);
    }
  }
