import React, { useEffect, useCallback } from 'react'
import { View, Text } from '@tarojs/components'
import { Image } from '@arco-design/mobile-react'
import Taro, { useLoad, useReady } from "@tarojs/taro";
import YkNavBar from '@/components/ykNavBar'
import { toast } from "@/utils/yk-common";
import { getSmsCode, smsLogin, getUserInfo } from '@/utils/api/common/common_user'
import { YiCode } from './yi-code'; // Added import for YiCode
import './code.less'

export default function CodeLoginCode() {
  const [phone, setPhone] = React.useState('');
  //验证码倒计时
  const [countDown, setCountDown] = React.useState(60);
  // 是否开启倒计时
  const [autoStart, setAutoStart] = React.useState(false);
  // 是否正在登录中（防止重复提交）
  const [isLoggingIn, setIsLoggingIn] = React.useState(false);

  useLoad((e) => {
    setPhone(decodeURIComponent(e.phone))
  });

  useReady(() => {
    getCodeApi(phone)
  })

  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;

    if (autoStart && countDown > 0) {
      timer = setInterval(() => {
        setCountDown((prev) => prev - 1);
      }, 1000);
    } else if (countDown === 0) {
      setAutoStart(false);
      setCountDown(60); // 重置倒计时
    }

    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [autoStart, countDown]);

  // 获取验证码
  const getCodeApi = (phoneVal) => {
    setAutoStart(true);
    let data = {
      mobile: phoneVal,
      scene: 1, //1:手机号登陆
    };

    getSmsCode(data)
      .then((res: any) => {
        if (res && res.code >= 0) {
          //6的时候手机号已注册
          toast("success", {
            content: "验证码发送成功",
            duration: 2000,
          });
        } else {
          toast("error", {
            content: res.msg,
            duration: 2000,
          });
        }
      })
      .catch((err) => {
        toast("error", {
          content: err,
          duration: 2000,
        });
      });
  };

  const codeLoginApi = useCallback((inputCode: string) => {
    // 防止重复提交
    if (isLoggingIn) {
      return;
    }

    let data: any = {}
    data.mobile = phone;
    data.code = inputCode;

    setIsLoggingIn(true); // 开始登录

    smsLogin(data)
      .then((res: any) => {
        if (res && res.code == 0) {
          Taro.setStorageSync('userInfo', res.data)

          getUserInfo()
            .then((res2: any) => {
              if (res2 && res2.code == 0) {
                //合并用户信息
                toast("info", {
                  content: "登录成功",
                  duration: 2000
                })
                Taro.setStorageSync('userInfo', {
                  ...res2.data,
                  ...res.data
                })
                Taro.reLaunch({ url: '/pages/index/index' })
              }
            })
            .catch(() => {
              toast("error", {
                content: "获取用户信息失败",
                duration: 2000
              });
              setIsLoggingIn(false); // 重置登录状态
            });
        } else {
          toast("info", {
            content: res.msg,
            duration: 2000
          })
          setIsLoggingIn(false); // 重置登录状态
        }
      })
      .catch((err) => {
        toast("error", {
          content: err?.msg || "登录失败，请重试",
          duration: 2000
        });
        setIsLoggingIn(false); // 重置登录状态
      });
  }, [phone, isLoggingIn])

  const resendClick = () => {
    if (autoStart) {
      return
    }
    getCodeApi(phone)
  }

  return (
    <View className='new-login'>
      <YkNavBar title='' />
      <View className='logo-container'>
        <Image className='logo' src={require('../../assets/images/login/logo.png')} bottomOverlap={null} />
        <Text className='app-name'>验证码已发送至</Text>
        <Text className='phone-number'>{phone}</Text>
      </View>

      <View className='code-input-container'>
        <YiCode
          size={4}
          onConfirm={codeLoginApi}
          isFocus
        />
      </View>

      <View className='resend-code' onClick={resendClick}>
        <Text className='resend-text'>{!autoStart ? '重新获取验证码' : '重新获取(' + countDown + 's)'}</Text>
      </View>

    </View>
  )
}