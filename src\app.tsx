import { PropsWithChildren } from "react";
import { useLaunch } from "@tarojs/taro";
import setRootPixel from "@arco-design/mobile-react/tools/flexible";
import "@arco-design/mobile-react/esm/style";
import React from "react";
import { ContextProvider } from "@arco-design/mobile-react";
import VConsole from "vconsole";
setRootPixel(20);
import "./app.less";
import Taro from "@tarojs/taro";
import { RecoilRoot } from "recoil";
// 导入路由拦截器
import "./utils/router.js";
import { IosSlideBackProvider } from "@/utils/IosSlideBackProvider";
import navigationManager from "@/utils/navigationManager"; //不能删除
//new VConsole();
(window as any).vConsole = new VConsole();
(window as any).vConsole.setSwitchPosition(20, 120);

function App({ children }: PropsWithChildren<any>) {
  // 暗黑模式
  const [darkMode, setDarkMode] = React.useState(false);
  // 主题
  const [theme, setTheme] = React.useState<any>({});
    // 暗黑模式类型
    const [darkModeType, setDarkModeType] = React.useState<'system' | 'manual' | 'off'>('off');
  const [manualDarkMode, setManualDarkMode] = React.useState(false); // 手动选择的暗黑模式状态
  const [systemDarkMode, setSystemDarkMode] = React.useState(false); // 系统暗黑模式状态

  // 获取平台信息
  const getPlatformInfo = () => {
    let uaAll = window.navigator.userAgent;
    return {
      isAndroid: uaAll.indexOf(`android_${APP_NAME}`) > -1,
      isIos: uaAll.indexOf(`ios_${APP_NAME}`) > -1,
      isHM: uaAll.indexOf(`hm_${APP_NAME}`) > -1,
    };
  };

    // 初始化暗黑模式设置
    const initializeDarkModeSettings = () => {
      const savedDarkModeType = Taro.getStorageSync("darkModeType") || "off";
      const savedManualDarkMode = !!Taro.getStorageSync("manualDarkMode");
      setDarkModeType(savedDarkModeType);
      setManualDarkMode(savedManualDarkMode);
    };

  useLaunch(() => {
    let _window: any = window;
    if (typeof window !== "undefined") {
      (window as any).navigationManager = navigationManager;
      // console.log("[navigationManager] ：navigationManager 已暴露到全局，可在控制台使用");
    }


    _window.onSystemThemeResult = (systemIsDark) => {
      console.log("onSystemThemeResult", systemIsDark);
      Taro.setStorageSync("darkMode", systemIsDark);
        setDarkMode(systemIsDark);
    };

    console.log("useLaunch darkMode");
    let t_darkMode = !!Taro.getStorageSync("darkMode");
    const { isAndroid, isIos, isHM } = getPlatformInfo();

    if (isAndroid) {
      if (t_darkMode) {
        console.log("httpPlatform StatusBarDarkMode");
        _window.StatusBarLightMode.StatusBarLightMode();
      } else {
        console.log("httpPlatform StatusBarLightMode");
        _window.StatusBarDarkMode.StatusBarDarkMode();
      }
    } else if (isIos) {
      if (t_darkMode) {
        _window.webkit.messageHandlers.configStatusBarStyle.postMessage(
          "white"
        );
      } else {
        _window.webkit.messageHandlers.configStatusBarStyle.postMessage(
          "black"
        );
      }
    } else if (isHM) {
      if (t_darkMode) {
        _window.harmony.StatusBarLightMode();
      } else {
        _window.harmony.StatusBarDarkMode();
      }
    }
    getInfo();

      // 初始化暗黑模式设置
      initializeDarkModeSettings();
    // 检查是否需要跟随系统主题
    checkAndApplySystemTheme();
  });

  const getInfo = () => {
    setDarkMode(!!Taro.getStorageSync("darkMode"));
  };

  // 检测系统主题
  const detectSystemTheme = () => {
    console.log("detectSystemTheme");
    let _window: any = window;
    const { isAndroid, isIos, isHM } = getPlatformInfo();

    try {
      if (isAndroid) {
        if (_window.getSystemTheme) {
          return _window.getSystemTheme.getSystemTheme();
        }
      } else if (isIos) {
        // iOS 需要异步处理，这里先返回当前状态
        if (_window.webkit?.messageHandlers?.getSystemTheme) {
          _window.webkit.messageHandlers.getSystemTheme.postMessage();
        }
      } else if (isHM) {
        if (_window.harmony?.getSystemTheme) {
          _window.harmony.getSystemTheme();
        }
      }
    } catch (error) {
      console.log("检测系统主题失败:", error);
    }
    return false;
  };


  // 检查并应用系统主题
  const checkAndApplySystemTheme = React.useCallback(() => {
    const darkModeType = Taro.getStorageSync("darkModeType");
    if (darkModeType === "system") {
      console.log("检测到跟随系统设置，开始检测系统主题");
      const systemIsDark = detectSystemTheme();
      if (systemIsDark !== undefined) {
        console.log("系统主题检测结果:", systemIsDark);
        Taro.setStorageSync("darkMode", systemIsDark);
        setDarkMode(systemIsDark);
      }
    }
  }, []);

  React.useEffect(() => {
    setTheme(Taro.getStorageSync("theme"));
  }, [children]);

  React.useEffect(() => {
    const handleThemeChange = () => {
      console.log("changeTheme");
      setDarkMode(!!Taro.getStorageSync("darkMode"));
      // 保证 theme 是新的引用
      const newTheme = { ...Taro.getStorageSync("theme") };
      setTheme(newTheme);
    };

    Taro.eventCenter.on("changeTheme", handleThemeChange);

    // 初始化时检查是否需要跟随系统主题
    checkAndApplySystemTheme();

    return () => {
      Taro.eventCenter.off("changeTheme", handleThemeChange);
    };
  }, [checkAndApplySystemTheme]);

    // 获取当前生效的暗黑模式状态
    const getCurrentDarkMode = () => {
      if (darkModeType === "system") {
        return systemDarkMode;
      } else if (darkModeType === "manual") {
        return manualDarkMode;
      }
      return false;
    };

  return (
    <RecoilRoot>
      <ContextProvider
        theme={theme}
        isDarkMode={getCurrentDarkMode()}
        darkModeSelector="arco-theme-dark"
      >
        <IosSlideBackProvider defaultEnable={true}>
          {children}
        </IosSlideBackProvider>
      </ContextProvider>
    </RecoilRoot>
  );
}

export default App;
