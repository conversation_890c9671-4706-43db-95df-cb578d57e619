import React, { useState, useEffect, useRef, useMemo } from "react";
import { View, Text, Canvas } from "@tarojs/components";
import { Button, Image, ImagePreview, Avatar } from "@arco-design/mobile-react";
import YkNavBar from "@/components/ykNavBar";
import Taro from "@tarojs/taro";
import { QRCodeSVG } from "qrcode.react";
import html2canvas from "html2canvas";
import wx from "weixin-webview-jssdk";
import { URL_BASE } from "@/utils/api/urls";
import logoImg from "@/assets/images/common/logo.png";
import defaultHeadImg from "@/assets/images/common/default_head.png";
// import wechatIcon from '@/assets/images/common/wx_icon.png';
import { IconWechat } from "@/components/YkIcons";
import BottomPopup from "@/components/BottomPopup";
import { uploadFile } from "@/utils/api/common/common_user";
import { toast } from "@/utils/yk-common";
import { useGlobalCallbacks } from "@/utils/globalCallbackManager";
import { AuthTypes } from "@/utils/config/authTypes";
import PermissionPopup from "@/components/PermissionPopup";
import { usePermission } from "@/hooks/usePermission";
import "./index.less";

// 声明全局常量
declare const APP_NAME_CN: string;
declare const APP_NAME: string;

declare global {
  interface Window {
    harmony?: { downloadBase64Img: (base64Img: string) => void };
    downloadBase64Img?: { downloadBase64Img: (base64Img: string) => void };
    webDownloadSuc?: () => void;
    webDownloadFail?: () => void;
  }
}

const QrcodePage: React.FC = () => {
  const userInfo = Taro.getStorageSync("userInfo");
  const router = Taro.getCurrentInstance();
  const params: any = router?.router?.params || {};
  // 兼容两种传参方式：userinfo(JSON字符串) 优先，其次 uid/nickname/avatar
  let parsedUserInfo: {
    uid?: string | number;
    nickname?: string;
    avatar?: string;
  } = {};
  if (params.userinfo) {
    try {
      parsedUserInfo = JSON.parse(decodeURIComponent(params.userinfo));
    } catch (e) {
      parsedUserInfo = {};
    }
  }
  const paramUid = parsedUserInfo.uid || params.uid;
  const paramNickname =
    parsedUserInfo.nickname ||
    (params.nickname ? decodeURIComponent(params.nickname) : "");
  const paramAvatar =
    parsedUserInfo.avatar ||
    (params.avatar ? decodeURIComponent(params.avatar) : "");
  const targetUserId = paramUid || userInfo?.id;
  const displayNickname = paramUid
    ? paramNickname || APP_NAME_CN
    : userInfo?.nickname || APP_NAME_CN;
  const displayAvatar = paramUid
    ? paramAvatar || defaultHeadImg
    : userInfo?.avatar || defaultHeadImg;
  const canvasRef = useRef<any>(null);
  const [qrcodeImage, setQrcodeImage] = useState<string>("");
  const [isActionPopupVisible, setIsActionPopupVisible] =
    useState<boolean>(false);
  const operationTypeRef = useRef<string>(""); // 操作类型标识：'download' 或 'share'
  const [activeTab, setActiveTab] = useState<string>("qrcode"); // 'qrcode' 或 'miniprogram'

  const [platform, setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);

  // 自定义权限同意处理，处理二维码页面特有的逻辑
  const customWebPermissonConsent = () => {
    console.log("customWebPermissonConsent");
    console.log("operationType:", operationTypeRef.current);

    // 根据操作类型执行不同的后续处理
    if (operationTypeRef.current === "download") {
      // 下载操作
      handleSaveImageConfirm();
    } else if (operationTypeRef.current === "share") {
      // 分享操作
      handleShareToWechatConfirm();
    }

    // 清除操作类型标识
    operationTypeRef.current = "";

    return true;
  };

  // 使用全局权限管理
  const {
    initPermissions,
    hasPermission,
    requestPermission,
    webPermissonConsent,
    webPermissonDeny,
    permissionPopupProps,
    platformRef,
    authConfirmType,
  } = usePermission(customWebPermissonConsent);
  // 使用 useMemo 缓存二维码URL，只有当用户ID或baseUrl变化时才重新计算
  const qrcodeUrl = useMemo(() => {
    if (!targetUserId) return "";
    const baseUrl = Taro.getStorageSync("baseUrl") || URL_BASE;
    return `${baseUrl}/#/pageUserInfo/followUser/index?uid=${targetUserId}&redirect=wx`;
  }, [targetUserId]);

  // 当二维码URL变化时，清理缓存的图片
  useEffect(() => {
    // 初始化权限管理
    const permissionCleanup = initPermissions();

    // 使用全局回调管理器注册下载回调
    const callbackCleanup = useGlobalCallbacks("qrcode", {
      webDownloadSuc: webDownloadSuc,
      webDownloadFail: webDownloadFail,
    });

    if (qrcodeUrl && qrcodeImage) {
      setQrcodeImage(""); // 清理旧的图片缓存
    }
    return () => {
      permissionCleanup && permissionCleanup();
      callbackCleanup && callbackCleanup();
    };
  }, [qrcodeUrl]);

  const webDownloadSuc = () => {
    console.log("webDownloadSuc 被调用");
    // Taro.hideLoading();
    toast("success", {
      content: "已保存到系统相册",
      duration: 2000,
    });
  };

  const webDownloadFail = () => {
    // Taro.hideLoading();
    toast("error", {
      content: "保存失败",
      duration: 2000,
    });
  };

  // 处理微信小程序base64图片上传
  const handleWxBase64Upload = async (
    processedImageData: string,
    isShare = false
  ) => {
    try {
      // 将base64转换为blob URL
      const base64ToBlob = (base64: string) => {
        // 如果base64数据包含data:image前缀，需要提取实际的base64部分
        const base64WithoutPrefix = base64.includes(",")
          ? base64.split(",")[1]
          : base64;
        const mimeType = base64.includes("data:")
          ? base64.split(";")[0].split(":")[1]
          : "image/jpeg";

        const byteCharacters = atob(base64WithoutPrefix);
        const byteNumbers = new Array(byteCharacters.length);
        for (let j = 0; j < byteCharacters.length; j++) {
          byteNumbers[j] = byteCharacters.charCodeAt(j);
        }
        const byteArray = new Uint8Array(byteNumbers);
        return new Blob([byteArray], { type: mimeType });
      };

      const blob = base64ToBlob(processedImageData);
      const blobUrl = URL.createObjectURL(blob);

      // 使用现有的上传接口上传blob
      const uploadRes: any = await uploadFile(blobUrl);

      if (uploadRes && uploadRes.code === 0) {
        const networkUrl = uploadRes.data; // 服务器返回的网络URL
        console.log("图片上传成功，网络地址:", networkUrl);

        // 清理blob URL
        URL.revokeObjectURL(blobUrl);

        // Taro.hideLoading();

        // 根据isShare参数决定是分享还是下载
        if (isShare) {
          
        } else {
          // 调用微信小程序的下载页面，传递网络地址
          wx.miniProgram.navigateTo({
            url:
              "/pages/downloadImgs/index?imgs=" +
              encodeURIComponent(networkUrl),
          });
        }
      } else {
        // console.error('图片上传失败:', uploadRes);
        // Taro.hideLoading();
        if (isShare) {
          toast("error", {
            content: "图片分享失败，请重试",
            duration: 2000,
          });
        } else {
          toast("error", {
            content: "图片保存失败，请重试",
            duration: 2000,
          });
        }
      }
    } catch (error) {
      console.error("处理base64图片失败:", error);
      if (isShare) {
        toast("error", {
          content: "图片分享失败，请重试",
          duration: 2000,
        });
      } else {
        toast("error", {
          content: "图片保存失败，请重试",
          duration: 2000,
        });
      }
    }
  };

  // 使用 useMemo 优化图片转换逻辑，只有当二维码URL变化时才重新生成图片
  const generateQrcodeImage = useMemo(() => {
    return () => {
      if (!qrcodeUrl) return Promise.reject("二维码URL未生成");

      return new Promise<string>((resolve, reject) => {
        if (typeof window !== "undefined" && typeof document !== "undefined") {
          const element = document.getElementById("qrcode-page-content");
          if (!element) {
            reject("找不到二维码容器元素");
            return;
          }

          html2canvas(element, {
            useCORS: true,
            scale: 2,
            logging: false,
          })
            .then((canvas) => {
              // 生成 JPEG 格式的 Base64 数据，与 Android 端保持一致
              const base64Image = canvas.toDataURL("image/jpeg", 0.9); // 0.9 质量
              resolve(base64Image);
            })
            .catch((error) => {
              reject(error);
            });
        } else {
          reject("当前环境不支持此功能");
        }
      });
    };
  }, [qrcodeUrl]);

  // 将二维码元素转换为图片（不显示预览）
  const convertToImage = async () => {
    // 如果图片已经生成且二维码URL没有变化，直接返回
    if (qrcodeImage && qrcodeUrl) {
      return qrcodeImage;
    }

    // Taro.showLoading({ title: '生成图片中...' });

    try {
      const base64Image = await generateQrcodeImage();
      setQrcodeImage(base64Image);
      Taro.hideLoading();
      return base64Image;
    } catch (error) {
      console.error("转换图片失败", error);
      Taro.hideLoading();
      toast("error", {
        content: "生成图片失败",
        duration: 2000,
      });
      throw error;
    }
  };

  // 打开图片预览（单独的函数）
  const openImagePreview = (imageSrc: string) => {
    ImagePreview.open({
      images: [{ src: imageSrc }],
      openIndex: 0,
      // onImageLongTap: (index, image, e) => {
      //   // 长按图片时显示操作菜单
      //   e.preventDefault();
      //   if (Taro.getEnv() === Taro.ENV_TYPE.WEB) {
      //     // H5环境下长按图片时提示保存方法
      //     Taro.showActionSheet({
      //       itemList: ['保存图片'],
      //       success: function (res) {
      //         if (res.tapIndex === 0) {
      //           saveImageToAlbum();
      //         }
      //       }
      //     });
      //   } else {
      //     setIsActionPopupVisible(true);
      //   }
      // }
    });
  };

  // 点击二维码区域（已弃用）
  // const handleQrcodeClick = () => {
  //   convertToImage();
  // };

  // 返回上一页
  const handleBack = () => {
    Taro.navigateBack();
  };

  // 分享
  const handleShare = async () => {
    try {
      // 如果图片还没有生成，先生成图片
      if (!qrcodeImage) {
        await convertToImage();
        // 等待状态更新后再显示操作菜单
        setTimeout(() => {
          setIsActionPopupVisible(true);
        }, 100);
      } else {
        // 如果图片已经存在，直接显示操作菜单
        setIsActionPopupVisible(true);
      }
    } catch (error) {
      console.error("分享准备失败", error);
    }
  };

  // 处理底部弹出菜单操作
  const handleActionConfirm = async (index: number) => {
    switch (index) {
      case 0: // 转发到微信好友
        await handleShareToWechat();
        break;
      case 1: // 保存图片
        await handleSaveImage();
        break;
      default:
        break;
    }
  };

  // 处理分享到微信好友 - 添加权限检查
  const handleShareToWechat = async () => {
    if (platformRef.current === "HM" || platformRef.current === "WX") {
      // HM 平台直接执行分享
      handleShareToWechatConfirm();
    } else {
      // 检查存储权限（分享可能需要处理图片）
      if (!hasPermission(AuthTypes.STORAGE)) {
        console.log("没有权限");
        // 设置操作类型为分享
        operationTypeRef.current = "share";
        // 如果没有权限，请求权限
        requestPermission(AuthTypes.STORAGE);
        return;
      }
      // 有权限则执行分享
      handleShareToWechatConfirm();
    }
  };

  // 确认分享到微信好友（权限检查通过后执行）
  const handleShareToWechatConfirm = async () => {
    try {
      // WebView环境 - 使用原生分享
      // 确保图片已生成
      let imageData = qrcodeImage;
      if (!imageData) {
        Taro.showLoading({ title: "准备分享内容..." });
        try {
          imageData = await generateQrcodeImage();
          setQrcodeImage(imageData);
        } catch (error) {
          Taro.hideLoading();
          toast("error", {
            content: "生成分享图片失败",
            duration: 2000,
          });
          return;
        }
        Taro.hideLoading();
      }

      // 使用新的分享方法分享Base64图片到微信好友
      try {
        if (platformRef.current === "HM") {
          window.harmony?.shareBase64ImgWx(imageData);
        } else if (platformRef.current === "Android") {
          window.shareBase64ImgWx?.shareBase64ImgWx(imageData);
        } else if (platformRef.current === "IOS") {
          window.webkit.messageHandlers.shareBase64ImgToWechat.postMessage(
            imageData
          );
        } else if (platformRef.current === "WX") {
          await handleWxBase64Upload(imageData, true);
        }
        // toast("success", {
        //   content: '分享成功',
        //   duration: 1500
        // });
      } catch (error) {
        console.error("分享失败:", error);
        toast("error", {
          content: "分享失败，请重试",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("分享处理失败:", error);
      toast("error", {
        content: "分享失败，请重试",
        duration: 2000,
      });
    }
  };

  // 处理保存图片 - 添加权限检查
  const handleSaveImage = async () => {
    if (platformRef.current === "HM" || platformRef.current === "WX") {
      // HM 平台直接执行保存
      handleSaveImageConfirm();
    } else {
      // 检查存储权限
      if (!hasPermission(AuthTypes.STORAGE)) {
        console.log("没有权限");
        // 设置操作类型为下载
        operationTypeRef.current = "download";
        // 如果没有权限，请求权限
        requestPermission(AuthTypes.STORAGE);
        return;
      }
      // 有权限则执行下载
      handleSaveImageConfirm();
    }
  };

  // 确认保存图片（权限检查通过后执行）
  const handleSaveImageConfirm = async () => {
    try {
      // 确保图片已生成
      let imageData = qrcodeImage;
      if (!imageData) {
        Taro.showLoading({ title: "生成图片中..." });
        try {
          imageData = await generateQrcodeImage();
          setQrcodeImage(imageData);
        } catch (error) {
          Taro.hideLoading();
          toast("error", {
            content: "生成图片失败",
            duration: 2000,
          });
          return;
        }
        Taro.hideLoading();
      }

      // WebView环境 - 直接使用原生下载方法
      try {
        // 验证数据
        if (!imageData || imageData.length < 100) {
          throw new Error("图片数据无效");
        }

        // 格式转换：确保与 Android 端期望的格式一致
        let processedImageData = imageData;
        if (imageData.startsWith("data:image/png;base64,")) {
          // 将 PNG 格式转换为 JPEG 格式（Android 端期望的格式）
          processedImageData = imageData.replace(
            "data:image/png;base64,",
            "data:image/jpeg;base64,"
          );
          console.log("🔄 已将 PNG 格式转换为 JPEG 格式");
        }

        // Taro.showLoading({ title: '正在保存...' });

        if (platformRef.current === "HM") {
          window.harmony?.downloadBase64Img(processedImageData);
        } else if (platformRef.current === "Android") {
          window.downloadBase64Img?.downloadBase64Img(processedImageData);
        } else if (platformRef.current === "IOS") {
          window.webkit.messageHandlers.saveBase64Img.postMessage(
            processedImageData
          );
        } else if (platformRef.current === "WX") {
          // 上传base64图片到后台获取网络地址，然后调用下载
          await handleWxBase64Upload(processedImageData);
        } else {
          throw new Error("原生下载接口不可用");
        }
      } catch (error) {
        Taro.hideLoading();
        console.error("❌ 保存图片失败:", error);
        console.error("错误详情:", {
          message: error.message,
          stack: error.stack,
          imageDataLength: imageData?.length || 0,
          hasNativeInterface: !!window.downloadBase64Img,
        });

        // 降级处理：提示用户手动保存
        Taro.showModal({
          title: "保存失败",
          content:
            "图片保存失败，建议您截图保存。\n\n错误信息：" +
            (error.message || "未知错误"),
          confirmText: "知道了",
          showCancel: false,
        });
      }
    } catch (error) {
      console.error("保存图片失败:", error);
      toast("error", {
        content: "保存失败，请重试",
        duration: 2000,
      });
    }
  };

  // 显示备选分享方案
  const showFallbackShareOptions = () => {
    Taro.showModal({
      title: "分享选项",
      content: "您可以长按二维码图片保存到相册，然后通过微信发送给好友",
      confirmText: "打开微信",
      cancelText: "知道了",
      success: (res) => {
        if (res.confirm) {
          // 尝试打开微信（如果支持的话）
          window.location.href = "weixin://";
        }
      },
    });
  };

  // 保存图片到相册
  const saveImageToAlbum = () => {
    if (!qrcodeImage) {
      toast("error", {
        content: "图片未生成",
        duration: 2000,
      });
      return;
    }

    Taro.showLoading({ title: "正在保存..." });

    // 区分环境：H5环境和小程序环境
    if (Taro.getEnv() === Taro.ENV_TYPE.WEB) {
      // H5环境下使用a标签下载
      try {
        const link = document.createElement("a");
        link.download = `qrcode_${Date.now()}.png`;
        link.href = qrcodeImage;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        Taro.hideLoading();
        toast("info", {
          content: "图片已准备，请确认下载",
          duration: 2000,
        });
      } catch (error) {
        console.error("保存图片失败", error);
        Taro.hideLoading();
        toast("error", {
          content: "保存失败，请长按图片手动保存",
          duration: 2000,
        });
      }
    } else {
      // 小程序环境
      try {
        const base64Data = qrcodeImage.split(",")[1];
        const fsm = Taro.getFileSystemManager();
        const filePath = `${Taro.env.USER_DATA_PATH}/qrcode_${Date.now()}.png`;

        fsm.writeFile({
          filePath,
          data: base64Data,
          encoding: "base64",
          success: () => {
            Taro.saveImageToPhotosAlbum({
              filePath: filePath,
              success: () => {
                Taro.hideLoading();
                toast("success", {
                  content: "保存成功",
                  duration: 2000,
                });
              },
              fail: (err) => {
                console.error("保存到相册失败", err);
                Taro.hideLoading();
                if (err.errMsg.indexOf("auth deny") >= 0) {
                  Taro.showModal({
                    title: "提示",
                    content: "保存图片需要您授权相册权限",
                    confirmText: "去授权",
                    success: (res) => {
                      if (res.confirm) {
                        Taro.openSetting();
                      }
                    },
                  });
                } else {
                  toast("error", {
                    content: "保存失败",
                    duration: 2000,
                  });
                }
              },
            });
          },
          fail: (err) => {
            console.error("写入文件失败", err);
            Taro.hideLoading();
            toast("error", {
              content: "保存失败",
              duration: 2000,
            });
          },
        });
      } catch (error) {
        console.error("写入文件失败", error);
        Taro.hideLoading();
        toast("error", {
          content: "保存失败",
          duration: 2000,
        });
      }
    }
  };

  return (
    <View className="qrcode-page">
      {/* 导航栏 */}
      {platform !== "WX" && (
        <YkNavBar title="" backArrow onClickLeft={handleBack} />
      )}
      
      {/* 顶部切换按钮区域 - 匹配DSL组1862 */}
      <View className="qrcode-tab-buttons">
        <View 
          className={`qrcode-tab-button ${activeTab === 'qrcode' ? 'active' : ''}`}
          onClick={() => setActiveTab('qrcode')}
        >
          <View className="qrcode-icon">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M0 0H7V7H0V0ZM1.33333 1.33333V5.66667H5.66667V1.33333H1.33333ZM8.33333 0H12.6667V4.33333H8.33333V0ZM9.66667 1.33333V3H11.3333V1.33333H9.66667ZM2.33333 2.33333H4.66667V4.66667H2.33333V2.33333ZM10.6667 5.66667H12.6667V7H10.6667V5.66667ZM0 8.33333H4.33333V12.6667H0V8.33333ZM8.33333 5.66667H9.66667V7H8.33333V5.66667ZM1.33333 9.66667V11.3333H3V9.66667H1.33333ZM8.33333 8.33333H12.6667V12.6667H8.33333V8.33333ZM9.66667 9.66667V11.3333H11.3333V9.66667H9.66667ZM5.66667 9.66667V8.33333H7V9.66667H5.66667ZM5.66667 12.6667V10.6667H7V12.6667H5.66667Z" />
            </svg>
          </View>
          <Text className="qrcode-tab-text">二维码</Text>
        </View>
        <View 
          className={`qrcode-tab-button ${activeTab === 'miniprogram' ? 'active' : ''}`}
          onClick={() => setActiveTab('miniprogram')}
        >
          <View className="qrcode-icon">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M8 1.5C8.27614 1.5 8.5 1.72386 8.5 2V3.2C8.5 3.47614 8.27614 3.7 8 3.7C7.72386 3.7 7.5 3.47614 7.5 3.2V2C7.5 1.72386 7.72386 1.5 8 1.5ZM8 12.3C8.27614 12.3 8.5 12.5239 8.5 12.8V14C8.5 14.2761 8.27614 14.5 8 14.5C7.72386 14.5 7.5 14.2761 7.5 14V12.8C7.5 12.5239 7.72386 12.3 8 12.3ZM13.02 2.98C13.215 3.17521 13.215 3.49145 13.02 3.68667L12.14 4.56667C11.943 4.75023 11.636 4.74482 11.4456 4.55441C11.2552 4.364 11.2498 4.05701 11.4333 3.86L12.3133 2.98C12.5085 2.78503 12.8248 2.78503 13.02 2.98ZM4.56667 11.4333C4.76164 11.6285 4.76164 11.9448 4.56667 12.14L3.68667 13.02C3.4937 13.2271 3.16746 13.2329 2.9673 13.0327C2.76715 12.8325 2.77291 12.5063 2.98 12.3133L3.86 11.4333C4.05521 11.2384 4.37145 11.2384 4.56667 11.4333ZM14 8.5C14.2761 8.5 14.5 8.27614 14.5 8C14.5 7.72386 14.2761 7.5 14 7.5H12.8C12.5239 7.5 12.3 7.72386 12.3 8C12.3 8.27614 12.5239 8.5 12.8 8.5H14ZM3.2 8.5C3.47614 8.5 3.7 8.27614 3.7 8C3.7 7.72386 3.47614 7.5 3.2 7.5H2C1.72386 7.5 1.5 7.72386 1.5 8C1.5 8.27614 1.72386 8.5 2 8.5H3.2ZM4.58 4.57933C4.38464 4.77519 4.06736 4.77519 3.872 4.57933L2.98 3.68667C2.77291 3.4937 2.76715 3.16746 2.9673 2.9673C3.16746 2.76715 3.4937 2.77291 3.68667 2.98L4.58 3.872C4.77497 4.06721 4.77497 4.38345 4.58 4.57867V4.57933ZM12.2627 13.1933C12.7765 13.1933 13.193 12.7768 13.193 12.263C13.193 11.7492 12.7765 11.3327 12.2627 11.3327C11.749 11.3327 11.3327 11.749 11.3327 12.2627C11.3327 12.7763 11.749 13.1927 12.2627 13.1927V13.1933ZM9.33333 5.83333C8.87333 5.83333 8.5 6.20667 8.5 6.66667V9.33333C8.5 10.3459 7.67919 11.1667 6.66667 11.1667C5.65414 11.1667 4.83333 10.3459 4.83333 9.33333C4.83333 8.32081 5.65414 7.5 6.66667 7.5C6.94281 7.5 7.16667 7.27614 7.16667 7C7.16667 6.72386 6.94281 6.5 6.66667 6.5C5.20643 6.5 4 7.70643 4 9.16667C4 10.6269 5.20643 11.8333 6.66667 11.8333C8.1269 11.8333 9.33333 10.6269 9.33333 9.16667V6.66667C9.33333 6.20643 9.70643 5.83333 10.1667 5.83333C10.6269 5.83333 11 6.20643 11 6.66667C11 6.94281 11.2239 7.16667 11.5 7.16667C11.7761 7.16667 12 6.94281 12 6.66667C12 5.20643 10.7936 4 9.33333 4C7.8731 4 6.66667 5.20643 6.66667 6.66667V9.33333C6.66667 9.79357 7.03977 10.1667 7.5 10.1667C7.96024 10.1667 8.33333 9.79357 8.33333 9.33333C8.33333 8.82081 7.51252 7.5 6.5 7.5C6.22386 7.5 6 7.27614 6 7C6 6.72386 6.22386 6.5 6.5 6.5C7.96024 6.5 9.16667 7.70643 9.16667 9.16667C9.16667 10.6269 7.96024 11.8333 6.5 11.8333C5.03977 11.8333 3.83333 10.6269 3.83333 9.16667V6.66667C3.83333 5.15414 5.15414 3.83333 6.66667 3.83333C8.17919 3.83333 9.5 5.15414 9.5 6.66667C9.5 6.94281 9.72386 7.16667 10 7.16667C10.2761 7.16667 10.5 6.94281 10.5 6.66667C10.5 4.60181 8.73152 2.83333 6.66667 2.83333C4.60181 2.83333 2.83333 4.60181 2.83333 6.66667V9.16667C2.83333 11.1791 4.48708 12.8333 6.5 12.8333C8.51292 12.8333 10.1667 11.1791 10.1667 9.16667V6.66667C10.1667 5.65414 9.34586 4.83333 8.33333 4.83333C7.32081 4.83333 6.5 5.65414 6.5 6.66667C6.5 6.94281 6.72386 7.16667 7 7.16667C7.27614 7.16667 7.5 6.94281 7.5 6.66667C7.5 6.20643 7.8731 5.83333 8.33333 5.83333C8.79357 5.83333 9.16667 6.20643 9.16667 6.66667V9.16667C9.16667 9.62691 8.79357 10 8.33333 10C7.8731 10 7.5 9.62691 7.5 9.16667V6.66667C7.5 6.20643 7.8731 5.83333 8.33333 5.83333C8.79357 5.83333 9.16667 6.20643 9.16667 6.66667V9.16667C9.16667 9.62691 8.79357 10 8.33333 10Z" />
            </svg>
          </View>
          <Text className="qrcode-tab-text">小程序码</Text>
        </View>
      </View>

      {/* 动态列表区域 */}
      <View className="qrcode-page-content" id="qrcode-page-content">
        {/* 动态卡片外层容器 */}
        <View className="dynamic-card">
          {/* 用户头像和名称 */}
          <View className="dynamic-user">
            <Avatar className="dynamic-avatar" src={displayAvatar} shape="circle" />
          </View>
          
          {/* 动态内容区域 */}
          <View className="dynamic-content">
            {/* 商家名称 */}
            <View className="dynamic-header">
              <Text className="merchant-name">{displayNickname}</Text>
            </View>
            
            {/* 动态文案 */}
            <View className="dynamic-text">
              <Text className="dynamic-desc">
                亲们,关注我的海胆相册{'\n'}
                不仅第一时间查看上新动态{'\n'}
                还能轻松一键转图朋友圈
              </Text>
            </View>
            
            {/* 页面内容 - 带装饰背景 */}
            <View className="page-content-wrapper">
              {/* 装饰性圆形背景 */}
              <View className="decoration-circle circle-1" />
              <View className="decoration-circle circle-2" />
              <View className="decoration-circle circle-3" />
              <View className="decoration-circle circle-4" />
              <View className="decoration-circle circle-5" />
              <View className="decoration-circle circle-6" />
              <View className="decoration-circle circle-7" />
              <View className="decoration-circle circle-8" />
              <View className="decoration-circle circle-9" />
              <View className="decoration-circle circle-10" />
              
              <View className="qrcode-container">
                {/* 头部区域 - 匹配DSL容器12 */}
                <View className="qrcode-header">
                  {/* LOGO区域 - 匹配DSL LOGO样式 */}
                  <View className="qrcode-logo">
                    <Image
                      className="qrcode-logo-img"
                      src={logoImg}
                      bottomOverlap={null}
                      fit="cover"
                    />
                  </View>

                  {/* 标题容器 - 匹配DSL容器615 */}
                  <View className="qrcode-title-container">
                    <Text className="qrcode-title">我的相册共享二维码</Text>
                    <Text className="qrcode-subtitle">扫码可免费一键转图</Text>
                  </View>
                </View>

                {/* 二维码内容区域 - 匹配DSL容器16 */}
                <View className="qrcode-content">
                  {/* 用户信息区域 - 匹配DSL容器13 */}
                  <View className="user-info">
                    <Image className="user-avatar" src={displayAvatar} />
                    <Text className="user-name">{displayNickname}的相册</Text>
                  </View>

                  {/* 二维码框 - 匹配DSL二维码区域 */}
                  <View className="qrcode-box">
                    {qrcodeUrl ? (
                      <QRCodeSVG
                        value={qrcodeUrl}
                        size={156}
                        bgColor="#ffffff"
                        fgColor="#000000"
                        level="H"
                      />
                    ) : (
                      <Canvas
                        className="qrcode-canvas"
                        canvasId="qrcode-canvas"
                        ref={canvasRef}
                      />
                    )}
                  </View>

                  {/* 底部提示区域 - 匹配DSL容器14 */}
                  <View className="qrcode-tip">
                    <IconWechat className="wechat-icon" />
                    <Text className="tip-text">扫码共享我的相册</Text>
                  </View>
                </View>
              </View>
            </View>
            
            {/* 一键转发按钮 */}
            <View className="forward-button-wrapper">
              <Button className="forward-button" onClick={handleShare}>
                一键转发
              </Button>
            </View>
          </View>
        </View>
      </View>


      {/* 底部操作弹窗 */}
      <BottomPopup
        options={["转发到微信好友", "保存图片"]}
        btnCloseText="取消"
        onConfirm={handleActionConfirm}
        onClose={() => setIsActionPopupVisible(false)}
        visible={isActionPopupVisible}
      />

      {/* 权限弹窗 */}
      <PermissionPopup {...permissionPopupProps} />
    </View>
  );
};

export default QrcodePage;
