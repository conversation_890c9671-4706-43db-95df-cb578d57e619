import { View, Text } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import { Image } from "@arco-design/mobile-react";
import Taro from "@tarojs/taro";
const userInfo = Taro.getStorageSync('userInfo') || {};
// 组件
import YkNavBar from "@/components/ykNavBar/index";
import { useEffect,useRef, useState } from "react";
export default function aboutUs() {
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  return (
    <View className="PICCPageContent">
      {platform !== "WX" &&<YkNavBar title="" />}
      <View className="PICCPageContent-title">
        <View className="PICCPageContent-title-name">「头像」收集情况</View>
      </View>
      <View className="PICCModule">
        <View className="PICCModule-item">
          <View className="PICCModule-item-label">内容</View>
          <View className="PICCModule-item-content">
            <Image
              fit="cover"
              // showLoading={false}
              // showError={false}
              src={userInfo.avatar}
              className="userInfoImage"
            />
          </View>
        </View>
        <View className="PICCModule-item">
          <View className="PICCModule-item-label">目的</View>
          <View className="PICCModule-item-content">
            <Text className="PICCModule-item-content-text">
                用于完成{APP_NAME_CN}注册
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
}
