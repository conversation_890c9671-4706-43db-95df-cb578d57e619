@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pageSetting/aboutUs/index"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });

  .module {
    width: 100%;
    box-sizing: border-box;
    padding: 10px;

    &-content {
      width: 100%;
      box-sizing: border-box;
      border-radius: 10px;
      overflow: hidden;
    }
  }

  .aboutUsTop {
    width: 100%;
    box-sizing: border-box;
    padding-top: 35px;
    padding-bottom: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    &-logo {
      width: 70px;
      height: 70px;
      display: block;
    }

    &-name {
      font-size: 18px;
      margin-top: 10px;
      font-weight: bold;
      color: var(--font-color);
      .use-dark-mode-query({
        color: @dark-font-color !important;
      });
    }
  }

  .icp{
    position: absolute;
    bottom: 16px;
    left: 0;
    right: 0;
    font-size: 12px;
    color: #86909C;
    text-align: center;
    .use-dark-mode-query({
      color: @dark-font-color !important;
    });
  }
}
