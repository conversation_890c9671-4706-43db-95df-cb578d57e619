import React, { useEffect, useState ,useRef} from 'react';
import { View, Text } from '@tarojs/components';
import { Button } from '@arco-design/mobile-react';
import { IconCircleChecked } from '@arco-design/mobile-react/esm/icon';
import YkNavBar from '@/components/ykNavBar';
import Taro from '@tarojs/taro';
import './result.less';
const MoveMaterialsResult: React.FC = () => {
  const [movedCount, setMovedCount] = useState<number>(0);
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  useEffect(() => {
    const params = Taro.getCurrentInstance().router?.params;
    if (params?.count) {
      const countNum = parseInt(params.count);
      if (!Number.isNaN(countNum)) {
        setMovedCount(countNum);
      }
    }
  }, []);

  const handleGoAlbum = () => {
    Taro.navigateBack({
      delta: 3
    });

    setTimeout(() => {
      Taro.navigateTo({ url: '/pageDynamic/album/index' });
    }, 100);
    // Taro.switchTab({
    //   url: '/pages/my/index',
    //   success: () => {
    //     setTimeout(() => {
    //       Taro.navigateTo({ url: '/pageDynamic/album/index' });
    //     }, 100);
    //   },
    // });
  };

  const handleGoCollect = () => {
    Taro.navigateBack({
        delta: 2
      });
  };

  const handleKnow = () => {
    Taro.navigateBack({
        delta: 2
      });
  };



  return (
    <View className="move-result">
      {platform !== "WX" &&<YkNavBar title="" />}
      <View className="main-content">
        <View>
          <IconCircleChecked className="success-icon" />
        </View>
        <Text className="success-text">搬家成功</Text>
        <Text className="success-desc">本次搬家 <Text className="highlight">{movedCount}</Text> 个素材，可在“我的相册”中进行查看/管理。</Text>

        <View className="action-line">
          <Text className="action" onClick={handleGoAlbum}>查看相册</Text>
          <Text className="divider">|</Text>
          <Text className="action" onClick={handleGoCollect}>继续搬家</Text>
        </View>
      </View>

      {/* 暂时关闭 */}
      { false && 
      <View className="bottom-bar">
        <Button inline className="primary-btn" onClick={handleKnow}>我知道了</Button>
      </View>
      }
    </View>
  );
};

export default MoveMaterialsResult;


