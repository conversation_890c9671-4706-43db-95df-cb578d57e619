@import "@arco-design/mobile-react/style/mixin.less";
@import "@/utils/css/variables.less";
[id^="/pageOnlinePayment/onlinePayment/information/editContact"] {
  // 页面全局字体设置和基础样式
  .editContactPageContent {
    font-family: "PingFang SC", "Helvetica Neue", Helvetica, "Hiragino Sans GB",
      "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    .use-var(background-color, background-color);

    .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });

    // 防止页面滚动
    overflow: hidden;

    // 确保所有子元素继承字体 - 移除通配符选择器
    view, text, input, button {
      font-family: inherit;
      line-height: 140%;
    }

    .pageDescribe {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 25px 30px 60px 30px;
      box-sizing: border-box;
      background-color: transparent;
      flex-shrink: 0;

      &-title {
        font-family: PingFang SC;
        font-size: 18px;
        font-weight: 700;
        color: @--text-5;

        .use-dark-mode-query({
      color: @dark-font-color;
    });
        margin-bottom: 12px;
      }

      &-desc {
        font-size: 15px;
        font-weight: 500;
        color: @--text-3 !important;

        .use-dark-mode-query({
    color: @dark-sub-info-font-color;
  });
      }
    }

    .form-content {
      width: 100%;
      box-sizing: border-box;
      padding: 0 30px 120px 30px; // 底部预留按钮空间
      flex: 1;
      overflow-y: auto;

      // 表单项目
      .form-item {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        justify-content: flex-start;
        font-size: 15px;
        // padding: 16px 0 16px 0;
        width: 100%;
        gap: 16px;

        .form-item-input {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          text-align: left;
          gap: 16px;
        }
      }

      .verify-code-btn {
        flex-shrink: 0;
        font-size: 14px;
        color: var(--primary-color) !important;

        .use-dark-mode-query({
      color: @dark-primary-color !important;
      color: var(--dark-primary-color) !important;
    });

        &.disabled {
          .use-var(color, primary-disabled-color) !important;
          .use-dark-mode-query({
          color:var(--primary-disable-color) !important;
        });
          opacity: 0.6;
        }
      }
    }

    .footer-btn {
      width: 100%;
      box-sizing: border-box;
      padding: 16px;
      position: fixed;
      bottom: 0;
      left: 0;

      .use-var(background-color, background-color);

      .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });

      // 适配安全区域
      padding-bottom: calc(16px + env(safe-area-inset-bottom));
      padding-bottom: calc(
        16px + constant(safe-area-inset-bottom)
      ); // 兼容旧版 WebKit
    }
  }
}
