// 暂时注释掉mixin导入以避免编译错误
@import "@arco-design/mobile-react/style/mixin.less";
[id^="/pageOnlinePayment/wallet/withdrawal/history"] {
  .withdrawalHistoryPage {
    min-height: 100vh;
    background-color: var(--fill-1, #f7f8fa); /* 填充 Fill/填充fill-1 */

    .content {
      padding: 10px;

      .withdrawal-list {
        .withdrawal-item {
          background-color: var(
            --container-bg,
            #ffffff
          ); /* 填充 Fill/Container 容器背景色 */
          border-radius: 4px;
          margin-bottom: 10px;
          padding: 0;
          overflow: hidden;

          .withdrawal-header {
            padding: 15px 10px;
            border-bottom: 0.5px solid rgba(153, 153, 153, 0.2);

            .status-amount {
              display: flex;
              justify-content: space-between;
              align-items: center;

              .status-text {
                font-family: PingFangSC-Medium;
                font-size: 14px; /* 14/Medium */
                line-height: 1.4;
                color: var(
                  --text-5,
                  #1d2129
                ); /* 文字 Text/文字-5-基础  Grey 10 */

                &.success {
                  color: var(--text-5, #1d2129);
                }

                &.error {
                  color: var(--danger-6, #f53f3f); /* 危险 Danger/危险-6-基础 */
                }

                &.processing {
                  color: var(--text-5, #1d2129);
                }
              }

              .amount-text {
                font-family: PingFangSC-Medium;
                font-size: 14px; /* 14/Medium */
                line-height: 1.4;
                color: var(
                  --text-5,
                  #1d2129
                ); /* 文字 Text/文字-5-基础  Grey 10 */
              }
            }
          }

          .withdrawal-details {
            padding: 15px 10px;

            .detail-row {
              display: flex;
              align-items: flex-start;
              margin-bottom: 10px;
              gap: 15px;

              &:last-child {
                margin-bottom: 0;
              }

              .detail-label {
                font-family: PingFangSC-Medium;
                font-size: 14px; /* 14/Medium */
                line-height: 1.4;
                color: var(
                  --text-3,
                  #86909c
                ); /* 文字 Text/文字-3-附加信息-Grey 6 */
                width: 56px;
                flex-shrink: 0;
              }

              .detail-value {
                font-family: PingFangSC-Medium;
                font-size: 14px; /* 14/Medium */
                line-height: 1.4;
                color: var(
                  --text-5,
                  #1d2129
                ); /* 文字 Text/文字-5-基础  Grey 10 */
                flex: 1;
                word-break: break-all;
              }

              .account-info {
                flex: 1;

                .account-name {
                  display: block;
                  font-family: PingFangSC-Medium;
                  font-size: 14px; /* 14/Medium */
                  line-height: 1.4;
                  color: var(
                    --text-5,
                    #1d2129
                  ); /* 文字 Text/文字-5-基础  Grey 10 */
                  margin-bottom: 5px;
                }

                .account-number {
                  display: block;
                  font-family: PingFangSC-Medium;
                  font-size: 14px; /* 14/Medium */
                  line-height: 1.4;
                  color: var(
                    --text-5,
                    #1d2129
                  ); /* 文字 Text/文字-5-基础  Grey 10 */
                }
              }
            }
          }

          .failure-reason {
            background-color: var(
              --danger-1,
              #ffece8
            ); /* 危险 Danger/危险-1-更浅 */
            padding: 8px 10px;
            display: flex;
            align-items: flex-start;
            gap: 15px;

            .failure-label {
              font-family: PingFangSC-Medium;
              font-size: 12px; /* 12/Medium */
              line-height: 1.4;
              color: var(--danger-6, #f53f3f); /* 危险 Danger/危险-6-基础 */
              width: 48px;
              flex-shrink: 0;
            }

            .failure-text {
              font-family: PingFangSC-Medium;
              font-size: 12px; /* 12/Medium */
              line-height: 1.4;
              color: var(--danger-6, #f53f3f); /* 危险 Danger/危险-6-基础 */
              flex: 1;
            }
          }
        }
      }

      .empty-state {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
        color: var(--text-3, #86909c); /* 文字 Text/文字-3-附加信息-Grey 6 */
        font-size: 14px;
      }
    }
  }

  // 支持暗色模式
  .use-dark-mode-query({
  .withdrawalHistoryPage {
    background-color: var(--dark-fill-1, #1a1a1a);

    .content {
      .withdrawal-list {
        .withdrawal-item {
          background-color: var(--dark-container-bg, #2a2a2a);

          .withdrawal-header {
            border-bottom-color: rgba(255, 255, 255, 0.1);

            .status-amount {
              .status-text {
                color: var(--dark-text-5, #E5E6EB);

                &.error {
                  color: var(--dark-danger-6, #F76965);
                }
              }

              .amount-text {
                color: var(--dark-text-5, #E5E6EB);
              }
            }
          }

          .withdrawal-details {
            .detail-row {
              .detail-label {
                color: var(--dark-text-3, #6b7785);
              }

              .detail-value {
                color: var(--dark-text-5, #E5E6EB);
              }

              .account-info {
                .account-name,
                .account-number {
                  color: var(--dark-text-5, #E5E6EB);
                }
              }
            }
          }

          .failure-reason {
            background-color: var(--dark-danger-1, #2a1f1f);

            .failure-label,
            .failure-text {
              color: var(--dark-danger-6, #F76965);
            }
          }
        }
      }

      .empty-state {
        color: var(--dark-text-3, #6b7785);
      }
    }
  }
});
}
