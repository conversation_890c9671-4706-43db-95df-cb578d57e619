/**
 * 全局回调管理器
 * 解决多个页面注册同名回调函数导致相互覆盖的问题
 */

class GlobalCallbackManager {
  constructor() {
    // 存储每个回调的注册者列表
    this.callbacks = {
      webDownloadSuc: new Map(),
      webDownloadFail: new Map(),
      webPaySuc: new Map(),
      webPermissonConsent: new Map(),
      webPermissonDeny: new Map(),
      checkPermissionCallBack: new Map(),
      aliPayWithStr: new Map(),
      wxPayWithStr: new Map(),
    };

    // 初始化全局回调函数
    this.initGlobalCallbacks();
  }

  /**
   * 初始化全局回调函数
   */
  initGlobalCallbacks() {
    // 设置全局下载成功回调
    window.webDownloadSuc = () => {
      console.log("Global webDownloadSuc called");
      this.executeCallbacks("webDownloadSuc");
    };

    // 设置全局下载失败回调
    window.webDownloadFail = () => {
      console.log("Global webDownloadFail called");
      this.executeCallbacks("webDownloadFail");
    };

    // 设置全局支付成功回调
    window.webPaySuc = (status) => {
      console.log("Global webPaySuc called with status:", status);
      this.executeCallbacks("webPaySuc", status);
    };

    // 设置全局支付成功回调
    window.aliPayWithStr = (status) => {
      console.log("Global aliPayWithStr called with status:", status);
      this.executeCallbacks("aliPayWithStr", status);
    };

    // 设置全局支付成功回调
    window.wxPayWithStr = (status) => {
      console.log("Global wxPayWithStr called with status:", status);
      this.executeCallbacks("wxPayWithStr", status);
    };

    // 设置全局权限同意回调
    window.webPermissonConsent = () => {
      console.log("Global webPermissonConsent called");
      this.executeCallbacks("webPermissonConsent");
    };

    // 设置全局权限拒绝回调
    window.webPermissonDeny = () => {
      console.log("Global webPermissonDeny called");
      this.executeCallbacks("webPermissonDeny");
    };

    // 设置全局权限检查回调
    window.checkPermissionCallBack = (e) => {
      console.log("Global checkPermissionCallBack called with:", e);
      this.executeCallbacks("checkPermissionCallBack", e);
    };
  }

  /**
   * 执行指定类型的所有回调函数
   * @param {string} type 回调类型
   * @param {...any} args 传递给回调函数的参数
   */
  executeCallbacks(type, ...args) {
    const callbackMap = this.callbacks[type];
    if (!callbackMap) {
      console.warn(`Unknown callback type: ${type}`);
      return;
    }

    // 执行所有注册的回调函数
    for (const [id, callback] of callbackMap) {
      try {
        console.log(`Executing ${type} callback for ${id}`);
        callback(...args);
      } catch (error) {
        console.error(`Error executing ${type} callback for ${id}:`, error);
      }
    }
  }

  /**
   * 注册回调函数
   * @param {string} type 回调类型
   * @param {string} id 注册者ID（通常是页面名称）
   * @param {Function} callback 回调函数
   */
  registerCallback(type, id, callback) {
    if (!this.callbacks[type]) {
      console.warn(`Unknown callback type: ${type}`);
      return;
    }

    console.log(`Registering ${type} callback for ${id}`);
    this.callbacks[type].set(id, callback);
  }

  /**
   * 注销回调函数
   * @param {string} type 回调类型
   * @param {string} id 注册者ID
   */
  unregisterCallback(type, id) {
    if (!this.callbacks[type]) {
      console.warn(`Unknown callback type: ${type}`);
      return;
    }

    console.log(`Unregistering ${type} callback for ${id}`);
    this.callbacks[type].delete(id);
  }

  /**
   * 注销指定ID的所有回调函数
   * @param {string} id 注册者ID
   */
  unregisterAllCallbacks(id) {
    console.log(`Unregistering all callbacks for ${id}`);
    for (const type in this.callbacks) {
      this.callbacks[type].delete(id);
    }
  }

  /**
   * 获取指定类型的回调数量
   * @param {string} type 回调类型
   * @returns {number} 回调数量
   */
  getCallbackCount(type) {
    return this.callbacks[type]?.size || 0;
  }

  /**
   * 获取所有回调的状态信息
   * @returns {Object} 状态信息
   */
  getStatus() {
    const status = {};
    for (const type in this.callbacks) {
      status[type] = Array.from(this.callbacks[type].keys());
    }
    return status;
  }
}

// 创建全局实例
const globalCallbackManager = new GlobalCallbackManager();

export default globalCallbackManager;

/**
 * 便捷的Hook函数，用于在React组件中注册和注销回调
 * @param {string} pageId 页面ID
 * @param {Object} callbacks 回调函数对象
 * @returns {Function} 清理函数
 */
export const useGlobalCallbacks = (pageId, callbacks = {}) => {
  // 注册回调
  for (const [type, callback] of Object.entries(callbacks)) {
    if (callback && typeof callback === "function") {
      globalCallbackManager.registerCallback(type, pageId, callback);
    }
  }

  // 返回清理函数
  return () => {
    globalCallbackManager.unregisterAllCallbacks(pageId);
  };
};
