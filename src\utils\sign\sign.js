const { Encrypt, base64_encode }  = require('@/utils/sign/aes.js')

class Sign {
    token = {
        ['http-user-id']: '',
        ['http-random']: '',
        ['http-token']: '',
        ['http-platform']: 'H5',
        ['http-version']: '1.0.0',
        ['http-total']: ''
    }

    genTotal() {
        let str = '';
        let random = Math.random() ;
		let  randomArr = JSON.stringify(random).split(".")
		random = randomArr[1]
        this.setRandom(random);
        let obj = Object.assign({}, {
            ...this.token
        })
        delete obj['http-total'];
		delete obj['Content-Type'];
        let keys = Object.keys(obj);
        for (let i = 0; i < keys.length; i++) {
            str += obj[keys[i]]
        }
        const baseStr = base64_encode(str);
        return Encrypt(baseStr);
    }

    
    setUserId(id) {
        this.token['http-user-id'] = id;
    }

    setToken(token) {
        this.token['http-token'] = token
    }
	
	setPlatform(platform) {
	    this.token['http-platform'] = platform
	}

    setRandom(random) {
        this.token['http-random'] = random
    }

    setTotal(total) {
        this.token['http-total'] = total
    }

    getSign() {
        this.setTotal(this.genTotal())
        return this.token
    }

}

function getInstance() {
    let instance = null;
    return function () {
        if (!instance) {
            instance = new Sign()
        }
        return instance
    }
}

export default getInstance()()