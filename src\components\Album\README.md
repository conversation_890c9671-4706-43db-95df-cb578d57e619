# Album 组件库

基于 componentWrapper 设计的可复用相册组件库，支持"自己的相册"和"他人的相册"两种场景。

## 组件结构

```
Album/
├── index.tsx          # 主入口，使用 componentWrapper 组合所有子组件
├── AlbumCore.tsx      # 核心组件，包含主要逻辑和状态管理
├── AlbumActions.tsx   # 操作组件，包含不同场景的操作按钮
├── AlbumHeader.tsx    # 头部组件，用户信息展示
├── AlbumList.tsx      # 列表组件，商品列表展示
├── index.less         # 样式文件
└── README.md          # 使用文档
```

## 使用方法

### 1. 基本使用

```tsx
import Album from '@/components/Album';
import { getMyAlbumList, getUserHomeTopData, deleteDynamic, updateDynamic } from '@/utils/api/common/common_user';

// 自己的相册
const MyAlbum = () => {
  const localUserInfo = Taro.getStorageSync('userInfo') || {};
  
  return (
    <Album
      title="我的相册"
      userId={localUserInfo?.id}
      isOwnAlbum={true}
      onGetUserInfo={getUserHomeTopData}
      onGetAlbumList={getMyAlbumList}
      onDeleteDynamic={deleteDynamic}
      onUpdateDynamic={updateDynamic}
      onCreate={() => Taro.navigateTo({ url: '/pages/releaseDynamic/index' })}
      onEdit={(item) => {
        Taro.setStorageSync('releaseDynamicList', item);
        Taro.navigateTo({ url: `/pages/releaseDynamic/index?type=2` });
      }}
      onDetail={(item) => {
        Taro.navigateTo({
          url: `/pages/detail/index?dynamicId=${item.id}&dynamicUserId=${item.userId}`
        });
      }}
      onBatch={() => Taro.navigateTo({ url: `/pageDynamic/album/batchDelete/index` })}
      onClassify={() => {/* 商品分类逻辑 */}}
      onShare={() => {/* 分享逻辑 */}}
    />
  );
};

// 他人的相册
const OtherAlbum = () => {
  const { userId } = Taro.getCurrentInstance().router?.params || {};
  
  return (
    <Album
      title="Ta的相册"
      userId={userId}
      isOwnAlbum={false}
      onGetUserInfo={getUserHomeTopData}
      onGetAlbumList={getMyAlbumList}
      onDetail={(item) => {
        Taro.navigateTo({
          url: `/pages/detail/index?dynamicId=${item.id}&dynamicUserId=${item.userId}`
        });
      }}
      onContact={() => {/* 联系Ta逻辑 */}}
      onShare={() => {/* 分享逻辑 */}}
    />
  );
};
```

### 2. 使用子组件

```tsx
import Album, { AlbumCore, AlbumActions, AlbumHeader, AlbumList } from '@/components/Album';

// 使用主组件（推荐）
<Album {...props} />

// 或者使用子组件组合
<AlbumCore {...coreProps}>
  <AlbumHeader {...headerProps} />
  <AlbumList {...listProps} />
</AlbumCore>
```

### 3. 自定义渲染

```tsx
<Album
  {...props}
  renderHeader={(userInfo) => (
    <div className="custom-header">
      <h1>{userInfo.nickname}的相册</h1>
    </div>
  )}
  renderActions={(item) => (
    <div className="custom-actions">
      <button onClick={() => customAction(item)}>自定义操作</button>
    </div>
  )}
  renderEmpty={() => (
    <div className="custom-empty">
      <p>暂无商品</p>
    </div>
  )}
/>
```

## 组件特性

### 1. componentWrapper 组合

使用 arco-design-mobile 的 componentWrapper 进行组件组合：

```tsx
// 主组件组合了所有子组件
export default componentWrapper(AlbumCore, {
  Actions: AlbumActions,
  Header: AlbumHeader,
  List: AlbumList,
});
```

### 2. 场景适配

- **自己的相册**：显示编辑、删除、置顶等管理操作
- **他人的相册**：显示查看、下载、联系等浏览操作

### 3. 可配置性

- 支持显示/隐藏各个功能模块
- 支持自定义事件处理
- 支持自定义渲染函数

## Props 说明

### AlbumCoreProps

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| title | string | "相册" | 页面标题 |
| userId | string \| number | - | 用户ID |
| isOwnAlbum | boolean | false | 是否是自己的相册 |
| onGetUserInfo | function | - | 获取用户信息函数 |
| onGetAlbumList | function | - | 获取相册列表函数 |
| onDeleteDynamic | function | - | 删除动态函数 |
| onUpdateDynamic | function | - | 更新动态函数 |
| onCreate | function | - | 创建商品回调 |
| onEdit | function | - | 编辑商品回调 |
| onDetail | function | - | 查看详情回调 |
| onDownload | function | - | 下载回调 |
| onRefresh | function | - | 刷新回调 |
| onToggleTop | function | - | 置顶/取消置顶回调 |
| onBatch | function | - | 批量操作回调 |
| onContact | function | - | 联系Ta回调 |
| onShare | function | - | 分享回调 |
| onSort | function | - | 排序回调 |
| onClassify | function | - | 分类回调 |
| renderActions | function | - | 自定义操作按钮渲染 |
| renderHeader | function | - | 自定义头部渲染 |
| renderEmpty | function | - | 自定义空状态渲染 |
| showTabs | boolean | true | 是否显示Tab栏 |
| showSearch | boolean | true | 是否显示搜索栏 |
| showSort | boolean | true | 是否显示排序 |
| showCreate | boolean | true | 是否显示创建按钮 |
| showBottomTabs | boolean | true | 是否显示底部操作栏 |

## 样式定制

组件使用 Less 编写样式，可以通过以下方式定制：

1. 直接修改 `index.less` 文件
2. 使用 CSS 变量覆盖
3. 通过 className 传入自定义样式

## 注意事项

1. 确保传入正确的 API 函数
2. 根据 `isOwnAlbum` 属性配置不同的操作权限
3. 自定义渲染函数需要返回有效的 React 节点
4. 样式文件需要正确引入到项目中



