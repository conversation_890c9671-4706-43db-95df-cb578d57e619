import { View, Text, ScrollView } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import { Toast, Image, Dialog, ImagePreview, Carousel, Button, Avatar,ActionSheet, Input, Cell, Stepper, Textarea, Ellipsis, Tabs } from "@arco-design/mobile-react";
import { IconMore } from "@arco-design/mobile-react/esm/icon";
import Taro from "@tarojs/taro";
import { useState, useRef, useEffect } from "react";
import YkNavBar from "@/components/ykNavBar/index";
// import ImagePreviewList from './components/ImagePreviewList';
import {  addCart, unfollowUser  } from "@/utils/api/common/common_user";
import BottomPopup from "@/components/BottomPopup";
import { fetchCartCountUtil } from "@/utils/cartUtils";
import { toast } from "@/utils/yk-common";
import { IconPayment } from "@/components/YkIcons";
import CartModal from "@/components/CartModal";



import {
  dynamicDetails,
  deleteDynamic,
  dynamicRefresh,
  updateDynamic,
  favoritesDynamic,
  deleteFavoritesDynamic,
  getUserHomeTopData
} from '@/utils/api/common/common_user';
import defaultHeadImg from '@/assets/images/common/default_head.png';
// import GoodCar from '@/components/goodCar';
import { getCSSVariableValue } from "@/utils/utils";


export default function Detail() {
  // 状态定义
  const [showPage, setShowPage] = useState(false);
  const [dynamic, setDynamic] = useState<any>({});
  const [current, setCurrent] = useState(0);
  const [swiperList, setSwiperList] = useState([]);
  const [merchantUserInfo, setMerchantUserInfo] = useState({
    avatar: '',
    userId: '',
    nickname: '',
    newNumbers: 0,
    total: 0,
    hasOpenOnlinePayment: 0 // 是否开通在线支付：1-已开通，0-未开通
  });
  const [carListNum, setCarListNum] = useState(0);
  const [actionSheetVisible, setActionSheetVisible] = useState(false); // 1. 添加 state 控制显隐
  const [moreActionVisible, setMoreActionVisible] = useState(false); // 更多操作弹窗显隐
  
  const goodCarRef = useRef(null);
  const userInfo = Taro.getStorageSync('userInfo') || {};
  const dynamicId = Taro.getCurrentInstance().router?.params?.dynamicId;
  const userId = Taro.getCurrentInstance().router?.params?.dynamicUserId;

  // 获取购物车数量
  const fetchCartCount = async () => {
    const count = await fetchCartCountUtil();
    setCarListNum(count);
  };


  const [goodsCount, setGoodsCount] = useState(0);
  // 新增：用数组管理每个 Stepper 的数量 - 改为二维数组，第一维是颜色，第二维是规格
  const [stepperValues, setStepperValues] = useState<number[][]>([]);
  const [stepperNames, setStepperNames] = useState([]);
  // 添加备注状态
  const [remark, setRemark] = useState('');
  // 添加当前选中的颜色tab索引
  const [activeColorIndex, setActiveColorIndex] = useState(0);
  // 添加强制更新版本号
  const [forceUpdateKey, setForceUpdateKey] = useState(0);
  // 添加批量操作数量
  const [batchCount, setBatchCount] = useState(0);
  // 添加批量操作的上一个值，用于判断增减方向
  const [batchPrevValue, setBatchPrevValue] = useState(0);
  // 添加展开收起状态
  const [isContentExpanded, setIsContentExpanded] = useState(false);
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  //const [format, setFormat] = useState([]);
  // 初始化加载
  useLoad(() => {
    // 从接口获取购物车数量，而不是从本地存储
    fetchCartCount();
    getDynamicDetails();
    getShopHomeInfo();
    console.log('userInfo', userInfo);
    console.log('userId from params:', userId);
    console.log('dynamicId from params:', dynamicId);
  });


  useEffect(() => {
    // 监听刷新事件，与首页保持一致
    const handleRefreshDetail = async () => {
      // 重新获取详情数据
      await getDynamicDetails();
      await getShopHomeInfo();
    };

    // 监听购物车数量更新事件
    const handleUpdateCartCount = (count: number) => {
      setCarListNum(count);
    };

    Taro.eventCenter.on('refreshAlbumList', handleRefreshDetail);
    Taro.eventCenter.on('updateCartCount', handleUpdateCartCount);

    return () => {
      // 移除事件监听器
      Taro.eventCenter.off('refreshAlbumList', handleRefreshDetail);
      Taro.eventCenter.off('updateCartCount', handleUpdateCartCount);
    };
  }, []); // 组件挂载时执行一次

  // 初始化stepperValues数组
  const initStepperValues = () => {
    const colorNum = dynamic.productColorNames ? dynamic.productColorNames.split(',').length : 0;
    const specificationsNum = dynamic.productSpecificationsNames ? dynamic.productSpecificationsNames.split(',').length : 0;

    // 如果既没有颜色也没有规格，创建一个1x1的数组用于批量操作
    if (colorNum === 0 && specificationsNum === 0) {
      setStepperValues([[0]]);
      return;
    }

    // 如果只有规格没有颜色，创建1xN的数组
    if (colorNum === 0 && specificationsNum > 0) {
      setStepperValues([Array(specificationsNum).fill(0)]);
      return;
    }

    // 创建二维数组：颜色数量 x 规格数量
    const newStepperValues = Array(colorNum).fill(null).map(() => Array(specificationsNum).fill(0));
    setStepperValues(newStepperValues);
  };

  // 获取动态详情
  const getDynamicDetails = async () => {
    try {
      if (!dynamicId) return;
      const res: any = await dynamicDetails({ id: dynamicId, userId: Taro.getStorageSync("userInfo").id });
      console.log('res', res.data);
      if (res && res.code === 0) {
        setDynamic(res.data);
        if(res.data.pictures && res.data.pictures.length > 0) {
          setSwiperList(res.data.pictures.split(','));
        }
        // 在设置动态数据后初始化stepperValues
        setTimeout(() => {
          setShowPage(true);
          // 延迟初始化，确保dynamic状态已更新
          setTimeout(() => {
            initStepperValues();
          }, 100);
        }, 1);
        // console.log([ ...res.data.productSpecificationsNames.split(','), ...res.data.productColorNames.split(',')])
      } else {
        Toast.error({ content: res.msg });
      }
    } catch (err) {
      console.error(err);
      Toast.error({ content: '获取详情失败' });
    }
  };

  // 获取商家信息
  const getShopHomeInfo = async () => {
    try {

      if (!userId) return;
      const res: any = await getUserHomeTopData({ userId: userId, homePageCountType: 2 });
      console.log(res, '---------------');
      if (res && res.code === 0) {
        setMerchantUserInfo(res.data);

      }
    } catch (err) {
      console.error(err);
    }
  };

  const addCartData = async () => {
    const format: any[] = [];

    const colorNum = dynamic.productColorNames ? dynamic.productColorNames.split(',').length : 0;
    const specificationsNum = dynamic.productSpecificationsNames ? dynamic.productSpecificationsNames.split(',').length : 0;

    // 遍历每个颜色和规格的组合
    stepperValues.forEach((colorSpecs, colorIndex) => {
      colorSpecs.forEach((quantity, specIndex) => {
        if (quantity > 0) {
          const item: any = {
            quantity: quantity,
            price: dynamic.price,
          };

          // 如果有规格，添加规格ID
          if (specificationsNum > 0 && dynamic.productSpecificationsIds) {
            item.productSpecificationsId = dynamic.productSpecificationsIds.split(',')[specIndex] || '';
          }

          // 如果有颜色，添加颜色ID
          if (colorNum > 0 && dynamic.productColorIds) {
            item.productColorId = dynamic.productColorIds.split(',')[colorIndex] || '';
          }

          format.push(item);
        }
      });
    });

    // 检查是否有选择商品
    if (format.length === 0) {
      Toast.error({ content: '请选择商品数量' });
      return;
    }

    try {
      const data = {
        userId: Taro.getStorageSync("userInfo").id,
        sellerId: userId,
        dynamicsId: dynamicId,
        remark: remark, // 添加备注字段
        type: 1,
        details: format,
      };
      console.log(data, 'data');
      const res: any = await addCart(data);
      if (res && res.code === 0) {
        Toast.success({ content: '加入购物车成功' });
        // 重新获取购物车数量，确保数据准确
        await fetchCartCount();
        // 重置表单
        initStepperValues();
        setRemark('');
        setActionSheetVisible(false);
      } else {
        Toast.error({ content: res.msg });
      }
    } catch (err) {
      console.error(err);
    }
  };
  

  // 收藏/取消收藏
  const handleCollect = async () => {
    try {
      let res: any;
      if (dynamic.isCollect == 1) {
        res = await deleteFavoritesDynamic({ dynamicId: dynamic.id, userId: userInfo.userId });
      } else {
        res = await favoritesDynamic({ dynamicId: dynamic.id, userId: userInfo.userId });
      }
      if (res && res.code === 0) {
        const newDynamic = {...dynamic};
        newDynamic.isCollect = dynamic.isCollect === 1 ? 0 : 1;
        setDynamic(newDynamic);
        Toast.success({
          content: dynamic.isCollect === 1 ? '取消成功' : '收藏成功'
        });
      } else {
        Toast.error({ content: res.msg });
      }
    } catch (err) {
      console.error(err);
    }
  };

  // 置顶/取消置顶
  const handleTop = async () => {
    try {
      const res: any = await updateDynamic({
        ...dynamic,
        isTop: dynamic.isTop === 1 ? 0 : 1,
        id: dynamic.id
      });
      if (res && res.code === 0) {
        const newDynamic = {...dynamic};
        newDynamic.isTop = dynamic.isTop === 1 ? 0 : 1;
        setDynamic(newDynamic);
        Toast.success({
          content: dynamic.isTop === 1 ? '取消置顶成功' : '置顶成功'
        });
        // 通知首页刷新数据
        Taro.eventCenter.trigger('refreshAlbumList');
      } else {
        Toast.error({ content: res.msg });
      }
    } catch (err) {
      console.error(err);
    }
  };

  // 上架/下架商品
  const handleDown = () => {
    const isListed = dynamic.isListed === 2; // 2表示已下架
    const actionText = isListed ? "上架" : "下架";
    const confirmText = isListed ? "确定要上架商品吗？" : "确定要下架商品吗？";

    Dialog.confirm({
      title: "温馨提示",
      children: confirmText,
      okText: "确定",
      cancelText: "取消",
      platform: "ios",
      onOk: async () => {
        try {
          const res: any = await updateDynamic({
            ...dynamic,
            isListed: isListed ? 1 : 2, // 1上架，2下架
            id: dynamic.id
          });
          if (res && res.code === 0) {
            // 更新本地状态
            setDynamic(prev => ({ ...prev, isListed: isListed ? 1 : 2 }));
            Toast.success({ content: `${actionText}成功` });
            // 通知首页刷新数据
            Taro.eventCenter.trigger('refreshAlbumList');
          } else {
            Toast.error({ content: res.msg });
          }
        } catch (err) {
          console.error(err);
        }
      }
    });
  };

  // 刷新
  const handleRefresh = async () => {
    try {
      await dynamicRefresh({ dynamicsId: dynamic.id });
      Toast.success({ content: '刷新成功' });
      // 通知首页刷新数据
      Taro.eventCenter.trigger('refreshAlbumList');
    } catch (err) {
      console.error(err);
    }
  };

  // 删除
  const handleDelete = () => {
    console.log('handleDelete', dynamic);
    Dialog.confirm({
      title: "温馨提示",
      children: "删除图文后不可恢复，确定删除？",
      okText: "删除",
      cancelText: "取消",
      platform: "ios",
      onOk: async () => {
        try {
          const res: any = await deleteDynamic(dynamic?.id);
          if (res && res.code === 0) {
            Toast.success({ content: '删除成功' });
            // 通知首页刷新数据
            Taro.eventCenter.trigger('refreshAlbumList');
            setTimeout(() => Taro.navigateBack(), 1500);
          } else {
            Toast.error({ content: res.msg });
          }
        } catch (err) {
          console.error(err);
        }
      }
    });
  };

  // 编辑
  const handleEdit = () => {
    Taro.setStorageSync('releaseDynamicList', dynamic);
    Taro.navigateTo({
      url: `/pageDynamic/releaseDynamic/index?type=2`,

    });
  };

  const goToUserDetailPage = (userId) => {
    if (userId == userInfo.userId) {
      console.log("goToUserDetailPage userId", userId);
      Taro.navigateTo({
        url: "/pageDynamic/album/index",
      });
    } else {
      console.log("goToUserDetailPage userId", userId);
      Taro.navigateTo({
        url: `/pageUserInfo/userDetail/index?userId=${userId}`,
      });
    }
  };

  // 展开收起内容
  const handleContentToggle = () => {
    setIsContentExpanded(!isContentExpanded);
  };

  // 批量操作：对当前颜色的所有规格进行加减
  const handleBatchOperation = (operation: 'add' | 'subtract') => {
    const newStepperValues = [...stepperValues];
    const specificationsNum = dynamic.productSpecificationsNames ? dynamic.productSpecificationsNames.split(',').length : 0;

    // 确保当前颜色的数组存在
    if (!newStepperValues[activeColorIndex]) {
      newStepperValues[activeColorIndex] = Array(specificationsNum).fill(0);
    }

    // 直接对所有规格增加或减少1
    newStepperValues[activeColorIndex] = newStepperValues[activeColorIndex].map(value => {
      if (operation === 'add') {
        return Math.min(value + 1, 9999);
      } else {
        return Math.max(value - 1, 0);
      }
    });

    setStepperValues(newStepperValues);
    // 强制触发重新渲染，确保步进器组件同步
    setForceUpdateKey(prev => prev + 1);
  };

  // 计算总数量和总价格
  const getTotalQuantityAndPrice = () => {
    let totalQuantity = 0;
    stepperValues.forEach(colorSpecs => {
      colorSpecs.forEach(quantity => {
        totalQuantity += quantity;
      });
    });
    const totalPrice = totalQuantity * (dynamic.price || 0);
    return { totalQuantity, totalPrice };
  };

  // 构建选中商品数据，用于立即购买
  const buildSelectedItemsForBuy = () => {
    const details: any[] = [];

    // 遍历每个颜色和规格的组合
    stepperValues.forEach((colorSpecs, colorIndex) => {
      colorSpecs.forEach((quantity, specIndex) => {
        if (quantity > 0) {
          details.push({
            id: Date.now() + Math.random(), // 临时ID
            userId: userInfo.userId,
            shoppingCartId: Date.now() + Math.random(), // 临时购物车ID
            productColorId: dynamic.productColorIds ? dynamic.productColorIds.split(',')[colorIndex] : null,
            productSpecificationsId: dynamic.productSpecificationsIds ? dynamic.productSpecificationsIds.split(',')[specIndex] : null,
            productSpecificationsName: dynamic.productSpecificationsNames ? dynamic.productSpecificationsNames.split(',')[specIndex] : null,
            productColorName: dynamic.productColorNames ? dynamic.productColorNames.split(',')[colorIndex] : null,
            quantity: quantity,
            price: dynamic.price * quantity,
            checked: true
          });
        }
      });
    });

    // 构建按商家分组的数据结构
    const selectedItems = [{
      shoppingCartList: [{
        id: Date.now(),
        shoppingCartSellerId: userId, // 商家ID
        dynamicsId: dynamic.id,
        dynamicsContent: dynamic.content,
        dynamicsImage: swiperList[0] || '',
        remark: remark,
        type: 1,
        price: dynamic.price, // 单价
        checked: true,
        details: details,
        isCollect: dynamic.isCollect || 0
      }],
      shoppingCartSellerId: userId, // 商家ID
      shoppingCartSellerUserId: userId, // 商家用户ID
      dynamicsUserName: merchantUserInfo.nickname,
      dynamicsUserAvatar: merchantUserInfo.avatar,
      checked: true
    }];

    return selectedItems;
  };

  // 加入购物车
  const handleAddCar = () => {
    // 每次打开前重置
    const colorNum = dynamic.productColorNames ? dynamic.productColorNames.split(',').length : 0;
    const specificationsNum = dynamic.productSpecificationsNames ? dynamic.productSpecificationsNames.split(',').length : 0;

    // 确保创建全新的二维数组，避免引用问题
    const newStepperValues: number[][] = [];
    for (let i = 0; i < Math.max(colorNum, 1); i++) {
      newStepperValues.push(new Array(specificationsNum).fill(0));
    }
    setStepperValues(newStepperValues);

    setRemark(''); // 重置备注
    setActiveColorIndex(0); // 重置到第一个颜色tab
    setBatchCount(0); // 重置批量数量
    setActionSheetVisible(true); // 3. 点击按钮时，只更新 state
  };

  // 前往购物车页面
  // const goToCarPage = () => {
  //   if (!userInfo?.userId) {
  //     Toast.error({ content: '请先登录' });
  //     setTimeout(() => {

  //       Taro.navigateTo({ url: '/pages/login/index' });
  //     }, 1500);
  //     return;
  //   }
  //   navigateToVue("/pages/pagesA/car/index");
  // };

  // 处理更多操作点击
  const handleMoreAction = () => {
    setMoreActionVisible(true);
  };

  // 处理更多操作弹框关闭
  const handleMoreActionClose = () => {
    setMoreActionVisible(false);
  };

  // 处理更多操作确认
  const handleMoreActionConfirm = (index: number) => {
    console.log('更多操作选择:', index);

    if (index === 0) {
      // 拉黑商家
      handleBlockMerchant();
    } else if (index === 1) {
      // 举报商家
      handleReportMerchant();
    }

    handleMoreActionClose();
  };

  // 拉黑商家
  const handleBlockMerchant = () => {
    console.log("拉黑商家", merchantUserInfo);
    Dialog.confirm({
      title: "拉黑商家",
      children: (
        <View>
          <Text>是否拉黑 </Text>
          <Text style={{ color: getCSSVariableValue('--primary-color') }}>{merchantUserInfo.nickname}</Text>
        </View>
      ),
      okText: "确定",
      cancelText: "取消",
      onOk: () => {
        unfollowUser(merchantUserInfo.userId).then((res: any) => {
          if (res && res.code == 0) {
            toast("success", {
              content: "拉黑成功",
              duration: 2000,
            });
            // 可以选择返回上一页或刷新页面
            setTimeout(() => {
              Taro.navigateBack({
                delta: 1
              });
            }, 1000);
          } else {
            toast("error", {
              content: res.msg || "拉黑失败",
              duration: 2000,
            });
          }
        }).catch((error) => {
          console.error("拉黑失败:", error);
          toast("error", {
            content: "拉黑失败，请重试",
            duration: 2000,
          });
        });
      },
      platform: "ios",
    });
  };

  // 举报商家
  const handleReportMerchant = () => {
    console.log("举报商家", dynamic);
    Taro.navigateTo({
      url: `/pageDynamic/report/reportType/index?type=dynamic&id=${dynamic.id}`,
    });
  };

  // 先将字符串转数组
  const productColorArr = dynamic.productColorNames ? dynamic.productColorNames.split(',') : [];
  const productSpecificationsArr = dynamic.productSpecificationsNames ? dynamic.productSpecificationsNames.split(',') : [];

  return (
    <View className="detailBox" style={{ paddingBottom: dynamic.price !== undefined && dynamic.price !== null && dynamic.price !== '' && Number(dynamic.price) > 0 && merchantUserInfo.hasOpenOnlinePayment === 1 ? '80px' : '' }}>
      {platform !== "WX" &&<YkNavBar
        title={'详情'}
        rightContent={
          // 只有不是自己的动态才显示更多图标
          dynamic.userId !== userInfo.id ? (
            <IconMore
              style={{ fontSize: '20px', cursor: 'pointer' }}
              onClick={handleMoreAction}
            />
          ) : null
        }
      />}
      
      {showPage && (
        <>
          {/* 轮播图 */}
          {swiperList.length > 0 && (
            <View className="swiperList">
              <Carousel
                className='carousel-style'
                onChange={(index) => setCurrent(index)}
              >
                {swiperList.map((item, index) => (
                  <Image
                    key={index}
                    src={item}
                    fit="cover"
                    width="100%"
                    height="375px"
                    onClick={() => {
                      // 跳转到独立的图片预览页面
                      const params = {
                        images: encodeURIComponent(JSON.stringify(swiperList)),
                        initialIndex: index.toString(),
                        dynamic: encodeURIComponent(JSON.stringify(dynamic)),
                        showCar: 'true',
                        showBottom: 'true'
                      };
                      const queryString = Object.entries(params)
                        .map(([key, value]) => `${key}=${value}`)
                        .join('&');
                      Taro.navigateTo({
                        url: `/pageDynamic/imagePreview/index?${queryString}`
                      });
                    }}
                  />
                ))}
              </Carousel>
              <View className="imgnum">
                <Text>{`${current + 1}/${swiperList.length}`}</Text>
              </View>
            </View>
          )}

          {/* 操作按钮 */}
          {userId == userInfo.userId && (
            <View className="opera">
              <Text onClick={handleEdit} className="opera-text">编辑</Text>
              <Text onClick={handleTop} className="opera-text">
                {dynamic.isTop == '1' ? '取顶' : '置顶'}
              </Text>
              <Text onClick={handleDelete} className="opera-text">删除</Text>
              <Text onClick={handleDown} className="opera-text">{dynamic.isListed === 2 ? '上架' : '下架'}</Text>
              <Text onClick={handleRefresh} className="opera-text">刷新</Text>
            </View>
          )}

          {/* 商品信息 */}
          <View className="dynamicSign">
            {dynamic.price !== undefined && dynamic.price !== null && dynamic.price !== ''&& dynamic.price > 0 && (
              <View className="dynamicSign-price">
                <Text className="dynamicSign-price-title">售价</Text>
                <Text className="dynamicSign-price-text">
                  ￥{Number(dynamic.price)}
                </Text>
              </View>
            )}
            <View className="dynamicSign-desc">
              <View className="dynamicSign-desc-text selectable-text">
                <Ellipsis
                  text={dynamic.content?.replace(/(\r\n|\n|\r)/gm, '') || ''}
                  maxLine={3}
                  ellipsisNode={<span className="demo-link">...<span className='expand-link'>展开</span></span>}
                  collapseNode={<span className="demo-link expand-link">收起</span>}
                  ellipsis={!isContentExpanded}
                  onCollapseNodeClick={(e) => {
                    e.stopPropagation();
                    handleContentToggle();
                  }}
                  onEllipsisNodeClick={(e) => {
                    e.stopPropagation();
                    handleContentToggle();
                  }}
                />
              </View>
              <View className="dynamicSign-desc-collect" onClick={handleCollect}>
                <Image
                  className="dynamicSign-desc-collect-img"
                  bottomOverlap={null}
                  src={dynamic.isCollect === 1 ?
                    require('@/assets/images/common/trend_collect_p.png') :
                    require('@/assets/images/common/trend_collect_n.png')
                  }
                />
                <Text className="dynamicSign-desc-collect-text">
                  {dynamic.isCollect === 1 ? '已收藏' : '收藏'}
                </Text>
              </View>
            </View>
          </View>

          {/* 商家信息 */}
          <View className="myInfo">
            <View className="myInfo-head">
              <Avatar
                className="myInfo-head-image"
                src={merchantUserInfo.avatar || defaultHeadImg}
              />
              <View className="myInfo-head-name">
                <Text>{merchantUserInfo.nickname}</Text>
                {merchantUserInfo.hasOpenOnlinePayment === 1 && (
                  // <Image
                  //   className="payment-icon"
                  //   src={require('@/assets/images/common/wx_pay.png')}
                  // />
                  <IconPayment className="payment-icon" />
                )}
              </View>
              <View className="myInfo-head-entry" onClick={() => goToUserDetailPage(merchantUserInfo.userId)}>
                <Text>店铺主页</Text>
              </View>
            </View>
            <View className="myInfo-more">
              <View className="myInfo-more-item">
                <Text className="myInfo-more-item-text">上新</Text>
                <Text className="myInfo-more-item-num">
                  {merchantUserInfo.newNumbers}
                </Text>
              </View>
              <View className="myInfo-more-divider" />
              <View className="myInfo-more-item">
                <Text className="myInfo-more-item-text">总数</Text>
                <Text className="myInfo-more-item-num">
                  {merchantUserInfo.total}
                </Text>
              </View>
            </View>
          </View>

          {/* 图文详情 */}
          {(dynamic.pictures !== '') && (
            <>
              <View className="dlistImage_title">—— 图文详情 ——</View>
              <View className="dlistImage_desc">
                <Text selectable>{dynamic.dynamic_title}</Text>
              </View>
              {dynamic.pictures !== '' && swiperList.map((item, index) => (
                <View key={index}>
                  <Image 
                  onClick={() => {
                         // 跳转到独立的图片预览页面
                         const params = {
                          images: encodeURIComponent(JSON.stringify(swiperList)),
                          initialIndex: index.toString(),
                          dynamic: encodeURIComponent(JSON.stringify(dynamic)),
                          showCar: 'true',
                          showBottom: 'true'
                        };
                        const queryString = Object.entries(params)
                          .map(([key, value]) => `${key}=${value}`)
                          .join('&');
                        Taro.navigateTo({
                          url: `/pageDynamic/imagePreview/index?${queryString}`
                        });
                  }}
                    className="dlistImage"
                    src={item}
                    // mode="widthFix"
                  />
                </View>
              ))}
            </>
          )}

          {/* 底部操作栏 */}
          {dynamic.price !== undefined && dynamic.price !== null && dynamic.price !== '' && Number(dynamic.price) > 0 && merchantUserInfo.hasOpenOnlinePayment === 1 && (
              
            <View className="footer">
              {/* <Button 
                className="footer-share"
                onClick={() => {
                  if(userId == userInfo.userId) {
                    //分享

                  } else {
                    //转存
                    Taro.setStorageSync('releaseDynamicList', dynamic);
                    Taro.navigateTo({
                      url: `/pageDynamic/releaseDynamic/index?type=4`
                    });
                  }
                }}
              >
                {userId == userInfo.userId ? '一键分享' : '一键转存'}
              </Button>
              {dynamic.price !== undefined && dynamic.price !== null && dynamic.price !== '' && Number(dynamic.price) > 0 && merchantUserInfo.hasOpenOnlinePayment === 1 && (
                <View className="footer-car"
                  onClick={handleAddCar}
                >
                  <Image
                    className="footer-car-img"
                    bottomOverlap={null}
                    src={require('@/assets/images/common/good_car_icon.png')}
                  />
                </View>
              )} */}

<Button 
                className="footer-share"
                onClick={handleAddCar}
              >
                购买
              </Button>
            </View>
      )}


          {/* 购物车入口 */}
          <View className="car" onClick={() => {Taro.navigateTo({ url: '/pageOrder/cart/index' });}}>
            <Image
              className="car-img"
              bottomOverlap={null}
              src={require('@/assets/images/common/car_icon.png')}
            />
            {carListNum > 0 && (
              <Text className="car-text">{carListNum}</Text>
            )}
          </View>

          {/* 购物车组件 */}
          {/* <GoodCar ref={goodCarRef} /> */}
        </>
      )}
      {/* 购物车弹框组件 */}
      <CartModal
        visible={actionSheetVisible}
        onClose={() => setActionSheetVisible(false)}
        productData={{
          id: dynamic.id,
          pictures: dynamic.pictures,
          content: dynamic.content,
          dynamic_title: dynamic.dynamic_title,
          price: dynamic.price,
          colors: dynamic.colors,
          specifications: dynamic.specifications,
          userId: userId, // 商家ID
        }}
        merchantInfo={{
          nickname: merchantUserInfo.nickname,
          avatar: merchantUserInfo.avatar,
        }}
        onAddCart={async (cartData) => {
          try {
            const res: any = await addCart(cartData);
            if (res && res.code === 0) {
              Toast.success({ content: '加入购物车成功' });
              // 重新获取购物车数量，确保数据准确
              await fetchCartCount();
            } else {
              Toast.error({ content: res.msg });
            }
          } catch (err) {
            console.error(err);
            Toast.error({ content: '加入购物车失败' });
          }
        }}
        onBuyNow={(buyNowData) => {
          // 构建选中商品数据并存储
          Taro.setStorageSync('selectedItems', buyNowData);
          // 跳转到确认订单页面
          Taro.navigateTo({
            url: '/pageOrder/order/pay/index?from=detail'
          });
        }}
      />

      {/* 更多操作弹窗 */}
      <BottomPopup
        options={[
          {
            label: "拉黑商家",
            description: merchantUserInfo.nickname || ""
          },
          {
            label: "举报",
            description: "商品文案与图片不符，图文违规等"
          }
        ]}
        btnCloseText="取消"
        onConfirm={handleMoreActionConfirm}
        onClose={handleMoreActionClose}
        visible={moreActionVisible}
      />

    </View>
  );
}
