// fetch-post-sse-parser.ts
// A POST-capable SSE client that parses Server-Sent Events protocol
// Usage: const ps = new PostEventSource(url, { method: 'POST', body: JSON.stringify({foo:1}), headers: {...} });
// ps.addEventListener('message', e => {});
// ps.addEventListener('customEvent', e => {});
// ps.close();

export default class PostEventSource {
  url: string;
  options: any;
  listeners: Map<string, Function[]>;
  lastEventId: string | null;
  retry: number;
  autoReconnect: boolean;
  _closed: boolean;
  _connecting: boolean;
  _abortController: AbortController | null;

  constructor(url: string, options: any = {}) {
    this.url = url;
    this.options = options; // { method, headers, body, keepAlive }

    // listeners: { eventName: [fn, ...] }
    this.listeners = new Map();
    this.lastEventId = null;
    this.retry = 3000; // default retry ms
    this.autoReconnect = options?.autoReconnect !== undefined ? !!options.autoReconnect : true;
    this._closed = false;
    this._connecting = false;
    this._abortController = null;

    // 调试日志：构造器
    this._log('[PostSSE] init', {
      url: this.url,
      hasOptions: !!options,
      method: options?.method,
      headersType: options?.headers ? Object.prototype.toString.call(options.headers) : 'undefined',
      bodyType: options?.body ? Object.prototype.toString.call(options.body) : 'undefined'
    });

    // start connection automatically
    this.connect();
  }

  // 安全日志方法，避免第三方 console hook 抛错影响流程
  _log(...args: any[]) { try { console.log(...args); } catch (_) {} }
  _warn(...args: any[]) { try { console.warn(...args); } catch (_) {} }
  _error(...args: any[]) { try { console.error(...args); } catch (_) {} }

  addEventListener(event: string, fn: Function) {
    if (!this.listeners.has(event)) this.listeners.set(event, []);
    this.listeners.get(event)!.push(fn);
  }

  removeEventListener(event: string, fn: Function) {
    if (!this.listeners.has(event)) return;
    const arr = this.listeners.get(event)!.filter(x => x !== fn);
    if (arr.length) this.listeners.set(event, arr); else this.listeners.delete(event);
  }

  dispatchEventObject(obj: { event?: string; data?: any; id?: string }) {
    const eventName = obj.event || 'message';
    const listeners = this.listeners.get(eventName) || [];
    const messageEvent = {
      data: obj.data == null ? '' : obj.data,
      lastEventId: obj.id || this.lastEventId || '',
      origin: this.url,
      type: eventName,
      // convenience: raw object
      _raw: obj,
    } as any;

    // update lastEventId if provided
    if (obj.id) this.lastEventId = obj.id;

    // call specific listeners
    listeners.forEach(fn => {
      try { (fn as any).call(null, messageEvent); } catch (e) { this._error('listener error', e); }
    });

    // call 'message' listeners also for default
    if (eventName !== 'message') {
      const msgListeners = this.listeners.get('message') || [];
      msgListeners.forEach(fn => {
        try { (fn as any).call(null, messageEvent); } catch (e) { this._error('listener error', e); }
      });
    }
  }

  async connect() {
    if (this._closed) return;
    if (this._connecting) return;
    this._connecting = true;

    const headers: Record<string, string> = Object.assign({}, this.options.headers || {});
    // Accept text/event-stream and indicate we accept streamed data
    headers['Accept'] = (headers as any)['Accept'] || 'text/event-stream';
    if (this.lastEventId) (headers as any)['Last-Event-ID'] = this.lastEventId as any;

    this._abortController = new AbortController();

    const fetchOpts: RequestInit = {
      method: this.options.method || 'GET',
      headers,
      signal: this._abortController.signal,
    } as any;

    // allow body as function to re-generate on reconnect
    if (this.options.body !== undefined) {
      (fetchOpts as any).body = typeof this.options.body === 'function' ? this.options.body() : this.options.body;
    }

    try {
      this._log('[PostSSE] connect:start', {
        url: this.url,
        method: this.options.method || 'GET',
        hasBody: this.options.body !== undefined,
        headersKeys: Object.keys(headers || {})
      });
      const res = await fetch(this.url, fetchOpts);

      if (!res.ok) {
        const err = new Error('HTTP error ' + res.status);
        this._handleError(err);
        return;
      }

      const contentType = res.headers.get('content-type') || '';
      // Not strictly required, but warn if not SSE
      if (!contentType.includes('text/event-stream')) {
        this._warn('Response Content-Type is not text/event-stream:', contentType);
      }

      this._log('[PostSSE] connect:response', {
        status: res.status,
        ok: res.ok,
        contentType,
        hasBody: !!res.body,
        hasGetReader: !!(res.body && (res.body as any).getReader)
      });

      const bodyObj: any = res.body as any;
      const reader = bodyObj && bodyObj.getReader ? bodyObj.getReader() : null;
      if (!reader) {
        const err = new Error('ReadableStream.getReader is unavailable');
        this._handleError(err);
        return;
      }
      const decoder = new TextDecoder('utf-8');
      let buffer: string = '';

      // 通知外部：连接已打开
      try {
        this.dispatchEventObject({ event: 'open', data: '' });
      } catch (_) {}

      // parser state
      let fieldEvent: string | undefined = undefined;
      let fieldData: string = '';
      let fieldId: string | undefined = undefined;
      let fieldRetry: string | undefined = undefined;

      const flushEvent = () => {
        if (fieldData === '' && fieldEvent === undefined && fieldId === undefined && fieldRetry === undefined) return;
        // trim trailing newline from data
        let data = fieldData.replace(/\n$/, '');
        const obj = {
          event: fieldEvent,
          data,
          id: fieldId,
        };
        if (fieldRetry !== undefined) {
          const n = parseInt(fieldRetry, 10);
          if (!Number.isNaN(n)) this.retry = n;
        }

        // dispatch
        // this._log('[PostSSE] flushEvent', {
        //   event: obj.event || 'message',
        //   id: obj.id,
        //   dataLength: typeof obj.data === 'string' ? obj.data.length : -1,
        //   retry: this.retry
        // });
        this.dispatchEventObject(obj);

        // reset
        fieldEvent = undefined;
        fieldData = '';
        fieldId = undefined;
        fieldRetry = undefined;
      };

      let chunkIndex = 0;
      while (true) {
        const { value, done } = await reader.read();
        if (done || !value) {
          if (done) { break; }
          continue;
        }
        // 统一转换为 Uint8Array，避免对 value.length 的直接访问
        if (!value || (typeof value !== 'object' && !(value instanceof Uint8Array))) {
          continue; // 跳过无效数据
        }
        const isUint8Array = typeof Uint8Array !== 'undefined' && value instanceof Uint8Array;
        let chunk: Uint8Array;
        if (isUint8Array) {
          chunk = value as Uint8Array;
        } else if (value != null && (value as any).buffer instanceof ArrayBuffer) {
          try { chunk = new Uint8Array((value as any).buffer); } catch { chunk = new Uint8Array(0); }
        } else {
          try { chunk = new Uint8Array((value as any) || []); } catch { chunk = new Uint8Array(0); }
        }
        this._log('[PostSSE] chunk', {
          idx: ++chunkIndex,
          isUint8Array,
          typeofValue: typeof value,
          hasBuffer: !!(value && (value as any).buffer),
          byteLength: chunk ? chunk.byteLength : -1
        });
        if (!chunk || chunk.byteLength === 0) {
          continue;
        }
        try {
          buffer += decoder.decode(chunk, { stream: true });
        } catch (decodeErr) {
          this._error('[PostSSE] decode error', decodeErr);
          continue;
        }

        // split lines, keep trailing partial line in buffer
        let lines: string[] = [];
        try {
          lines = buffer.split(/\r?\n/);
          buffer = lines.pop() || '';
          // const linesCount = Array.isArray(lines) ? (lines as any).length : -1;
          // const bufferRemainderLen = typeof buffer === 'string' ? (buffer as any).length : -1;

          const linesCount = Array.isArray(lines) ? lines.length : 0;
          const bufferRemainderLen = (typeof buffer === 'string' && buffer != null) ? buffer.length : 0;
          this._log('[PostSSE] lines parsed', { linesCount, bufferRemainderLen });
        } catch (splitErr) {
          this._error('[PostSSE] split error', splitErr);
          lines = [] as any;
        }

        for (let line of lines) {
          if (typeof line !== 'string') {
            this._warn('[PostSSE] non-string line coerced', { type: typeof line });
            line = String(line ?? '');
          }
          if (line === '') {
            // dispatch event
            flushEvent();
            continue;
          }
          if (line.length > 0 && line[0] === ':') {
            // comment, ignore
            continue;
          }

          const idx = line.indexOf(':');
          let field: string, valuePart: string;
          if (idx === -1) {
            field = line;
            valuePart = '';
          } else {
            field = line.slice(0, idx);
            valuePart = line.slice(idx + 1);
            if (typeof valuePart !== 'string') valuePart = String(valuePart ?? '');
            if (valuePart.startsWith(' ')) valuePart = valuePart.slice(1);
          }

          switch (field) {
            case 'event':
              fieldEvent = valuePart || 'message';
              this._log('[PostSSE] field:event', fieldEvent);
              break;
            case 'data':
              fieldData += (typeof valuePart === 'string' ? valuePart : String(valuePart ?? '')) + '\n';
              {
                const addLen = typeof valuePart === 'string' ? valuePart.length : (valuePart == null ? 0 : String(valuePart).length);
                const totalLen = typeof fieldData === 'string' ? fieldData.length : -1;
                // this._log('[PostSSE] field:data(+)', { addLen, totalLen });
              }
              break;
            case 'id':
              fieldId = valuePart;
              this._log('[PostSSE] field:id', fieldId);
              break;
            case 'retry':
              fieldRetry = valuePart;
              this._log('[PostSSE] field:retry', fieldRetry);
              break;
            default:
              // unknown field, ignore or could store
              break;
          }
        }
      }

      // stream ended - flush any pending event
      if (buffer && typeof buffer === 'string' && buffer.length) {
        // process remaining buffer as if terminated by newline
        const remainingLines = buffer.split(/\r?\n/);
        for (let line of remainingLines) {
          if (typeof line !== 'string') line = String(line ?? '');
          if (line === '') { flushEvent(); continue; }
          if (line.length > 0 && line[0] === ':') continue;
          const idx = line.indexOf(':');
          let field, valuePart: any;
          if (idx === -1) { field = line; valuePart = ''; } else {
            field = line.slice(0, idx);
            valuePart = line.slice(idx + 1);
            if (typeof valuePart !== 'string') valuePart = String(valuePart ?? '');
            if (valuePart.startsWith(' ')) valuePart = valuePart.slice(1);
          }
          switch (field) {
            case 'event': fieldEvent = valuePart; break;
            case 'data': fieldData += valuePart + '\n'; break;
            case 'id': fieldId = valuePart; break;
            case 'retry': fieldRetry = valuePart; break;
          }
        }
        flushEvent();
      }

      // 派发 close 事件（自然结束或被动结束时）
      try {
        this.dispatchEventObject({ event: 'close', data: '' });
      } catch (_) {}

      // if ended naturally and not closed by user, reconnect only when enabled
      if (!this._closed && this.autoReconnect) {
        // small delay before reconnect
        await this._delay(this.retry);
        this._connecting = false;
        this.connect();
      }

    } catch (err) {
      this._error('[PostSSE] connect:error', err);
      if (this._closed) return;
      this._handleError(err as any);
    } finally {
      this._log('[PostSSE] connect:finally');
      this._connecting = false;
    }
  }

  _handleError(err: any) {
    this._error('PostEventSource error', err);
    const listeners = this.listeners.get('error') || [];
    listeners.forEach(fn => {
      try { (fn as any).call(null, err); } catch (e) { this._error(e); }
    });

    if (this._closed || !this.autoReconnect) return;
    // reconnect after retry
    setTimeout(() => {
      if (!this._closed && this.autoReconnect) this.connect();
    }, this.retry);
  }

  _delay(ms: number) { return new Promise(res => setTimeout(res, ms)); }

  close() {
    this._closed = true;
    try {
      if (this._abortController) this._abortController.abort();
    } catch (e) {}
    // 主动关闭时也派发 close 事件
    try { this.dispatchEventObject({ event: 'close', data: '' }); } catch (_) {}
  }
}

// --------- Example usage (not part of the class) ----------
/*
import PostEventSource from './fetch-post-sse-parser';

const ps = new PostEventSource('/stream', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: () => JSON.stringify({ userId: 123 }) // body can be function for reconnects
});

ps.addEventListener('message', e => {
  console.log('message', e.data);
});

ps.addEventListener('progress', e => {
  console.log('progress event', e.data);
});

ps.addEventListener('error', err => {
  console.error('stream error', err);
});

// to close
// ps.close();
*/


