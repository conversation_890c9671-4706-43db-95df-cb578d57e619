    @import "@arco-design/mobile-react/style/mixin.less";
.box{
    position: relative;
    // background-color: red;

    .hideInput{
        position: absolute;
        top: 20px;
        color: rgba(1, 1, 1, 0);
        background-color: transparent;
        z-index: 10;
        border: 0;
        height: 60px;
        width: 60px;
        font-size: 16px; // 保持正常字体大小以支持键盘弹出
        opacity: 0; // 完全透明
        outline: none; // 移除焦点边框
        caret-color: transparent; // 隐藏光标
    }

    .showInput{
        padding: 20px;
        display: flex;

        justify-content: space-between;
        .inputBox{
            position: relative;
            margin-right: 10px;
            background-color: #f7f8fa;
            border-radius: 8px;
            
            &:last-child {
                margin-right: 0;
            }
            
            .input{
                height: 60px;
                width: 60px;
                text-align: center;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 8px;
            }
            .active{
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 2px;
                height: 30px;
                background-color: var(--primary-color);
                animation: myfirst 1500ms infinite;
                @keyframes myfirst
                {
                    0%   {opacity: 0.3}
                    100% {opacity: 1}
                }
            }
        }

.pane {
    .input{
        border: 1px solid #e5e7eb;
        background-color: #ffffff;
    }
    .active{
        width: 2px;
        height: 30px;
        background-color: var(--primary-color);
        border: none;
    }
}

        .line{
            .input{
                border-bottom: 1px solid var(--line-color);
                .use-dark-mode-query({
                border-bottom: 1px solid @dark-line-color;
              });
            }
            .active{
                width: 2px;
                height: 30px;
                background-color: var(--primary-color);
                border: none;
            }
        }

    }

}
    
