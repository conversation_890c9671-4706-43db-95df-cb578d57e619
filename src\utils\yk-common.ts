import { Toast } from "@arco-design/mobile-react";
import { useRef, useCallback, useEffect } from "react";
export const toast = (func, options) => {
  let _window: any = window;
  if (!!_window.toastInstance) {
    _window.toastInstance.close();
  }
  _window.toastInstance = Toast[func](options);
};

export const useDebounce = (fn: Function, delay: number) => {
  const timerRef = useRef<NodeJS.Timeout>();
  const fnRef = useRef(fn); // 保存最新的函数引用

  useEffect(() => {
    fnRef.current = fn;
  }, [fn]);

  return useCallback((...args: any[]) => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
    timerRef.current = setTimeout(() => {
      fnRef.current(...args);
    }, delay);
  }, [delay]);
};
