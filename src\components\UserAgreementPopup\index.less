@import "@arco-design/mobile-react/style/mixin.less";

.user-agreement-popup-content {
  .rem(margin-bottom, 24);
  .rem(padding, 24);

  .popup-close {
    position: absolute;
    .rem(top, 15);
    .rem(right, 15);
    .rem(width, 10);
    .rem(height, 10);
  }

  .popup-title {
    .text-medium();
    .rem(font-size, 16);
    .rem(margin-bottom, 9);
    text-align: center;
    .use-var(color, font-color);
    .use-dark-mode-query({
      color: @dark-font-color;
    });
  }

  .popup-desc {
    .rem(font-size, 13);
    .use-var(color, sub-info-font-color);
    .use-dark-mode-query({
      color: @dark-sub-info-font-color;
    });
    .rem(margin-bottom, 24);
    text-align: left;

    .link {
      .use-var(color, primary-color);
    }
  }

  .popup-confirm {
    width: 100%;
    height: 44px;
    .rem(border-radius, 2);
    .rem(font-size, 15);
  }
}
