import { View, Text } from '@tarojs/components';
import { useEffect, useState, useRef } from 'react';
import Taro from '@tarojs/taro';
import YkNavBar from '@/components/ykNavBar';
import { Toast, Button, Avatar, Badge, Image } from '@arco-design/mobile-react';
import { getUserInfo } from '@/utils/api/common/common_user';
import { toast } from "@/utils/yk-common";


import {
  VipType,
  VipPackage,
  UserInfo
} from './types';
import type { ApplePurchaseResult, RestorePurchasesResult } from '@/utils/nativeBridge';
import { useVipPayment } from '@/utils/useVipPayment';
import {
  generateMockUserData,
  formatPrice,
  formatMemberExpireTime,
  isMemberValid,
} from './utils';
import BottomPopup from '@/components/BottomPopup';
import './index.less';

// 前端配置的会员套餐数据
const VIP_PACKAGES: VipPackage[] = [
  {
    type: VipType.HALF_YEAR,
    name: '半年会员',
    price: 16800, // 168元，以分为单位
    description: '享受6个月会员特权',
    recommended: false,
    appleProductId: 'hdHalfYearMember' // 苹果内购产品ID
  },
  {
    type: VipType.ANNUAL,
    name: '年度会员',
    price: 28800, // 288元，以分为单位
    description: '享受12个月会员特权，更优惠',
    recommended: true,
    appleProductId: 'hdYearMember' // 苹果内购产品ID
  }
];

const VipCenter = () => {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [paymentPopupVisible, setPaymentPopupVisible] = useState(false);
  const [selectedVipType, setSelectedVipType] = useState<VipType | null>(null);

  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);

  // 支付成功回调
  const handlePaymentSuccess = async (paymentData: any) => {
    console.log('[VIP Payment] 🎉 支付成功回调开始', {
      paymentData,
      timestamp: new Date().toISOString()
    });

    try {
      // 1. 设置刷新标记，用于从支付成功页面返回时刷新
      // console.log('[VIP Payment] 🏷️ 设置支付成功刷新标记');
      // Taro.setStorageSync('vip_payment_success_refresh', true);

      // 2. 先跳转到支付成功页面，让用户看到成功状态
      console.log('[VIP Payment] 🔄 立即跳转到支付成功页面');
      Taro.navigateTo({
        url: `/pageVip/vipCenter/paySuccess?orderId=${paymentData?.payOrderId || ''}&vipType=${paymentData?.vipType || ''}`
      });

      // 3. 显示成功提示
      Toast.success('支付成功');

      // 4. 后台延迟等待后端数据处理完成，然后刷新用户信息
      console.log('[VIP Payment] ⏰ 后台等待后端数据处理完成...');
      setTimeout(async () => {
        try {
          await new Promise(resolve => setTimeout(resolve, 3000)); // 等待3秒
          console.log('[VIP Payment] 📡 开始后台延迟刷新用户信息...');
          await forceRefreshUserInfo();
          console.log('[VIP Payment] ✅ 后台延迟用户信息刷新完成');
        } catch (error) {
          console.error('[VIP Payment] ❌ 后台延迟刷新失败', error);
        }
      }, 0);

      console.log('[VIP Payment] 🎉 支付成功处理完成');
    } catch (error) {
      console.error('[VIP Payment] ❌ 支付成功回调处理失败', error);
      // 即使失败也要设置刷新标记和跳转
      // Taro.setStorageSync('vip_payment_success_refresh', true);
      Taro.navigateTo({
        url: `/pageVip/vipCenter/paySuccess?orderId=${paymentData?.payOrderId || ''}&vipType=${paymentData?.vipType || ''}`
      });
      Toast.success('支付成功');
    }
  };

  // 支付失败回调
  const handlePaymentFailure = (errorMessage: string) => {
    Toast.error(errorMessage || '支付失败');
  };

  // 支付取消回调
  const handlePaymentCancel = () => {
    Toast.info('支付已取消');
  };

  // 使用支付Hook
  const {
    isPaying,
    error: paymentError,
    paymentStep,
    initiateVipPayment,
    resetPaymentState
  } = useVipPayment({
    onSuccess: handlePaymentSuccess,
    onFailure: handlePaymentFailure,
    onCancel: handlePaymentCancel
  });

  // 强制刷新远程用户信息并更新本地存储（用于支付成功后）
  const forceRefreshUserInfo = async (): Promise<void> => {
    console.log('[Force Refresh] 🔄 开始强制刷新用户信息');

    try {
      // 获取本地用户信息作为基础
      const localUserInfo = Taro.getStorageSync("userInfo");
      const res = await getUserInfo();

      if (res && res.code === 0) {
        // 合并本地信息和远程信息，远程信息优先
        const updatedUserInfo = {
          ...localUserInfo,
          ...res.data,
        };

        // 更新本地存储
        Taro.setStorageSync("userInfo", updatedUserInfo);

        // 更新组件状态
        setUserInfo(updatedUserInfo);
      } else {
        console.error('[Force Refresh] ❌ 远程用户信息获取失败:', res);
        throw new Error(`远程用户信息获取失败: ${res.msg || '未知错误'}`);
      }
    } catch (error) {
      console.error('[Force Refresh] ❌ 强制刷新用户信息失败:', error);
      throw error;
    }
  };

  // 判断用户是否为会员（使用工具函数）
  const isUserVip = () => {
    if (!userInfo) return false;

    // 检查会员状态和过期时间
    if (userInfo.isMember === 1 && userInfo.memberExpireTime) {
      const isValid = isMemberValid(userInfo.memberExpireTime);
      return isValid;
    }

    return false;
  };

  // 格式化会员到期时间（使用工具函数）
  const formatExpireTime = () => {
    if (!userInfo || !userInfo.memberExpireTime) return '';
    const formatted = formatMemberExpireTime(userInfo.memberExpireTime);
    return formatted;
  };

  // 处理套餐购买 - iOS环境下直接调用苹果内购，其他环境显示支付方式选择
  const handlePurchase = (vipType: VipType) => {
    // iOS环境下直接调用苹果内购
    if (platform === "IOS") {
      executePayment(vipType, 'apple_iap');
      return;
    }
    if (platform === "WX") {
      executePayment(vipType, 'wx_lite');
      return;
    }

    // 非iOS环境显示支付方式选择弹窗
    setSelectedVipType(vipType);
    setPaymentPopupVisible(true);
  };

  // 执行实际支付流程（使用useVipPayment Hook）
  const executePayment = (vipType: VipType, channelCode: string) => {
    // 获取套餐信息
    const selectedPackage = VIP_PACKAGES.find(pkg => pkg.type === vipType);
    if (!selectedPackage) {
      Toast.error('套餐信息不存在');
      return;
    }

    // 获取用户信息
    const localUserInfo = Taro.getStorageSync("userInfo");
    if (!localUserInfo || !localUserInfo.id) {
      Toast.error('请先登录');
      return;
    }

    // 如果是苹果内购，直接调用苹果内购流程
    if (channelCode === 'apple_iap') {
      handleAppleIAPPayment(selectedPackage);
      return;
    }

    initiateVipPayment(vipType, selectedPackage.price, channelCode);
  };

  // 处理苹果内购支付
  const handleAppleIAPPayment = async (selectedPackage: VipPackage) => {
    if (!selectedPackage.appleProductId) {
      Toast.error('苹果内购产品ID未配置');
      return;
    }

    try {
      // 动态导入NativeBridge
      const { NativeBridge } = await import('@/utils/nativeBridge');
      
      
      // 调用苹果内购
      const purchaseResult = await NativeBridge.purchaseProduct(selectedPackage.appleProductId);
      
      
      // 处理苹果内购成功
      await handleAppleIAPSuccess(purchaseResult, selectedPackage);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      Toast.error(errorMessage || '苹果内购失败');
    }
  };

  // 处理苹果内购成功
  const handleAppleIAPSuccess = async (purchaseResult: ApplePurchaseResult, selectedPackage: VipPackage) => {
    try {
      // 获取用户信息
      const localUserInfo = Taro.getStorageSync("userInfo");
      if (!localUserInfo || !localUserInfo.id) {
        Toast.error('用户信息获取失败，请重新登录');
        return;
      }

      console.log('[Apple IAP] 🔄 开始后台校验JWS...');
      // 动态导入API函数
      const { appleCreateOrder } = await import('@/utils/api/common/common_user');
      
      // 调用后台校验接口
      const verifyResult = await appleCreateOrder({
        jws: purchaseResult.jws,
        userId: localUserInfo.id
      });

      console.log('[Apple IAP] 📡 后台校验响应:', verifyResult);

      // if (verifyResult) {
        // 校验成功，完成交易
        console.log('[Apple IAP] ✅ JWS校验成功，开始完成交易...');
        
        try {
          // 调用完成交易
          const { NativeBridge } = await import('@/utils/nativeBridge');
          await NativeBridge.finishTransaction(purchaseResult.transactionId);
          console.log('[Apple IAP] ✅ 交易已完成');
        } catch (finishError) {
          console.warn('[Apple IAP] ⚠️ 交易完成失败，但不影响支付结果:', finishError);
        }
        
        const paymentData = {
          vipType: selectedPackage.type,
          price: selectedPackage.price,
          payOrderId: 'apple_iap_' + Date.now(),
          channelCode: 'apple_iap',
          jws: purchaseResult.jws,
          transactionId: purchaseResult.transactionId,
          // verifyResult: verifyResult.data
        };
        
        handlePaymentSuccess(paymentData);
    } catch (error) {
      console.error('[Apple IAP] ❌ 后台校验异常:', error);
      
      // 即使出现异常也要尝试完成交易
      try {
        const { NativeBridge } = await import('@/utils/nativeBridge');
        await NativeBridge.finishTransaction(purchaseResult.transactionId);
        console.log('[Apple IAP] ✅ 交易已完成（虽然发生异常）');
      } catch (finishError) {
        console.warn('[Apple IAP] ⚠️ 交易完成失败:', finishError);
      }
      
      const errorMessage = error instanceof Error ? error.message : String(error);
      Toast.error(`校验失败: ${errorMessage}`);
    }
  };


  // 获取支付方式列表（根据环境动态生成）
  const getPaymentMethods = () => {
    const methods = [
      { name: '微信支付', channelCode: 'wx_app' },
      { name: '支付宝', channelCode: 'alipay_app' }
    ];
    return methods;
  };

  // 处理支付方式选择
  const handlePaymentMethodSelect = (index: number) => {
    if (!selectedVipType) {
      Toast.error('请先选择套餐');
      return;
    }

    const paymentMethods = getPaymentMethods();
    const selectedMethod = paymentMethods[index];
    if (!selectedMethod) {
      Toast.error('支付方式选择错误');
      return;
    }

    // 关闭弹窗并执行支付
    setPaymentPopupVisible(false);
    executePayment(selectedVipType, selectedMethod.channelCode);
  };

  // 关闭支付方式选择弹窗
  const handlePaymentPopupClose = () => {
    setPaymentPopupVisible(false);
    setSelectedVipType(null);
  };

  // 跳转到会员权益页面
  const goToBenefits = () => {
    Taro.navigateTo({ url: '/pageVip/benefits/index' });
  };

  // 已购服务
  const goToPurchasedServices = () => {
    Taro.navigateTo({ url: '/pageVip/vipCenter/payOrder' });
  };

  // 处理恢复购买成功
  const handleRestorePurchaseSuccess = async (restoreResult: RestorePurchasesResult) => {
    try {
      const { restoredTransactions } = restoreResult;
      
      if (!restoredTransactions || restoredTransactions.length === 0) {
        Toast.info('没有找到可恢复的购买记录');
        return;
      }

      console.log('[Restore Purchase] 📋 找到恢复购买记录:', restoredTransactions.length);
      
      // 获取用户信息
      const localUserInfo = Taro.getStorageSync("userInfo");
      if (!localUserInfo || !localUserInfo.id) {
        Toast.error('用户信息获取失败，请重新登录');
        return;
      }

      // 处理每个恢复的交易
      let successCount = 0;
      for (const transaction of restoredTransactions) {
        try {
          console.log('[Restore Purchase] 🔄 处理恢复交易:', transaction);
          
          // 动态导入API函数
          const { appleCreateOrder } = await import('@/utils/api/common/common_user');
          
          // 调用后台校验接口
          const verifyResult = await appleCreateOrder({
            jws: transaction.jws,
            userId: localUserInfo.id
          });

          console.log('[Restore Purchase] 📡 恢复交易校验结果:', verifyResult);

          // 完成交易
          try {
            const { NativeBridge } = await import('@/utils/nativeBridge');
            await NativeBridge.finishTransaction(transaction.transactionId);
            console.log('[Restore Purchase] ✅ 恢复交易完成:', transaction.transactionId);
            successCount++;
          } catch (finishError) {
            console.warn('[Restore Purchase] ⚠️ 恢复交易完成失败:', finishError);
          }
        } catch (error) {
          console.error('[Restore Purchase] ❌ 处理恢复交易失败:', error);
        }
      }

      if (successCount > 0) {
        Toast.success(`成功恢复 ${successCount} 个购买记录`);
        
        // 刷新用户信息
        setTimeout(async () => {
          try {
            await forceRefreshUserInfo();
            console.log('[Restore Purchase] ✅ 恢复购买后用户信息刷新完成');
          } catch (error) {
            console.error('[Restore Purchase] ❌ 恢复购买后用户信息刷新失败', error);
          }
        }, 2000);
      } else {
        Toast.info('恢复购买完成，但没有成功处理的交易');
      }
    } catch (error) {
      console.error('[Restore Purchase] ❌ 处理恢复购买结果失败:', error);
      Toast.error('处理恢复购买结果失败');
    }
  };

  // 恢复购买
  const restorePurchase = async () => {
    if (platform !== "IOS") {
      Toast.info('恢复购买功能仅支持iOS设备');
      return;
    }

    
    // 显示加载提示
    const loadingToast = Toast.loading('正在恢复购买...');

    try {
      // 动态导入NativeBridge
      const { NativeBridge } = await import('@/utils/nativeBridge');
      
      console.log('[Restore Purchase] 🔄 调用iOS恢复购买...');
      
      // 调用iOS恢复购买
      const restoreResult = await NativeBridge.restorePurchases();
      
      console.log('[Restore Purchase] ✅ 恢复购买成功:', restoreResult);
      
      // 关闭加载提示
      loadingToast.close();
      
      // 处理恢复购买结果
      await handleRestorePurchaseSuccess(restoreResult);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('[Restore Purchase] ❌ 恢复购买失败:', errorMessage);
      
      // 关闭加载提示
      loadingToast.close();
      Toast.error(errorMessage || '恢复购买失败');
    }
  };

  // 使用 Taro 页面生命周期监听页面显示
  Taro.useDidShow(() => {
    forceRefreshUserInfo();
  });

  return (
    <View className="vip-center">
      {/* 导航栏 */}
      {platform!== "WX" && <YkNavBar title="会员中心" />}
      {/* 用户信息区域 */}
      <View className="user-info-section">
        {/* <View className="user-avatar"> */}
          {/* <Image
            src={userInfo?.avatar || ""}
            className="avatar-image"
            radius="50%"
          /> */}
          <Avatar
            src={userInfo?.avatar || ""}
            className="avatar-image"
            size="large"
          />
        {/* </View> */}
        <Text className="user-name">
          {userInfo?.nickname}
        </Text>
        {isUserVip() && (
          <View className="member-expire-info">
            <Text className="member-expire-msg">
              会员到期日：
            </Text>
            <Text className="member-expire-time">
              {formatExpireTime()}
            </Text>
          </View>
        )}
      </View>

      {/* 套餐选择区域 */}
      <View className="packages-section">
        {VIP_PACKAGES.map((pkg) => (
          <View key={pkg.type} className="package-card">
            <View className="package-content">
              <View className="package-left">
                <View className="vip-box">
                  <View className="vip-badge">
                    <Text className="vip-text">VIP</Text>
                  </View>
                </View>
                <View className="package-info">
                  <View className="package-name-container">
                    <Text className="package-name">{pkg.name}</Text>
                    {pkg.recommended && (
                      <Badge className="recommended-text" text="超值推荐" absolute style={{ top: '-20px', left: '0', marginLeft: '0' }}/>
                    )}
                    
                  </View>
                  <Text className="package-price">
                    {formatPrice(pkg.price)}
                  </Text>
                </View>
              </View>
              <Button
                inline
                size="small"
                // className={cls("purchase-btn", {
                //   primary: index === 1,
                //   outline: index !== 1,
                // })}
                type={pkg.recommended ? "primary" : "ghost"}
                shape="round"
                onClick={() => handlePurchase(pkg.type)}
              >
                {isUserVip() ? '续费' : '开通'}
              </Button>
            </View>
          </View>
        ))}
      </View>

      {/* 功能区域 */}
      <View className="functions-section">
        <View className={`function-links ${platform !== "IOS" ? 'single-link' : ''}`}>
          <Text className="function-link" onClick={goToPurchasedServices}>
            已购服务
          </Text>
          {platform === "IOS" && (
            <>
              <View className="divider" />
              <Text className="function-link" onClick={restorePurchase}>
                恢复购买
              </Text>
            </>
          )}
        </View>
        <Button inline size="mini" className="benefits-btn" onClick={goToBenefits}>
          了解会员权益
        </Button>
      </View>

      {/* 底部协议 */}
      <View className="agreement-section">
        <Text className="agreement-text-msg">
          会员付费即代表你同意
        </Text>
        <Text className="agreement-text-link" onClick={() => { Taro.navigateTo({ url: `/pageSetting/webView/index?url=${USER_AGREEMENT}&name=会员服务协议` }) }}>
          《会员服务协议》
        </Text>
      </View>

      {/* 支付方式选择弹窗 */}
      <BottomPopup
        visible={paymentPopupVisible}
        onClose={handlePaymentPopupClose}
        onConfirm={handlePaymentMethodSelect}
        options={getPaymentMethods().map(method => method.name) as string[]}
        // title="选择支付方式"
        btnCloseText="取消"
      />
    </View>
  );
};

export default VipCenter;
