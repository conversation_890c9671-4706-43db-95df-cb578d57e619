import { View, Text, ScrollView,Image } from "@tarojs/components";
import React, { useState, useEffect, useRef  } from "react";
import Taro from "@tarojs/taro";
import { Checkbox, SearchBar, Dialog, Popup, DatePicker, Button } from "@arco-design/mobile-react";
import YkNavBar from "@/components/ykNavBar/index";
import { getCollectDynamicList, getLinkId } from "@/utils/api/common/common_user";
import { toast } from "@/utils/yk-common";
import "./index.less";
import { IconTriDown, IconClose } from "@arco-design/mobile-react/esm/icon";
import { createPortal } from "react-dom";
import { isMemberValid } from '@/pages/my/utils'
import wx from "weixin-webview-jssdk";
// 定义 dynamic 类型
interface DynamicItem {
  id: number;
  timeStamp: number;
  title: string;
  imgsSrc: string[];
  optimaPrice: string;
  attrs: string;
  createTime: string;
  skus?: string[];  // 添加可选的 skus 属性
  color?: string[]; // 添加可选的 color 属性
  digitalWatermark?: string | number; // 将 digitalWatermark 的类型更改为 string | number
}

interface DynamicData {
  merchantAvatar: string;
  merchantName: string;
  newCount: number;
  totalCount: number;
  time: string;
  items: DynamicItem[];
}

// 定义 tab 类型
type TabType = 'all' | 'new' | 'video' | 'image';

export default function SelectMaterials() {
  const scrollViewRef = useRef(null);
  const [selectedMap, setSelectedMap] = useState<Map<number | string, DynamicItem>>(new Map());
  const [search, setSearch] = useState("");
  const [activate, setActivate] = useState("all");
  const [timeStamp, setTimeStamp] = useState("");
  const [linkId, setlinkId] = useState("");
  const [expandedId, setExpandedId] = useState<string | number | null>(null);
  const [currentTab, setCurrentTab] = useState<TabType>('all');  // 添加当前 tab 状态
  const [isTabFixed, setIsTabFixed] = useState(false); // 添加tab固定状态
  const paramsId = useRef("");
  const [dynamic, setDynamic] = useState<DynamicData>({
    merchantAvatar: '',
    merchantName: '',
    newCount: 0,
    totalCount: 0,
    time: '',
    items: []
  });
  //const [value, setValue] = React.useState([2, '22328490198975000']);
  const selectNum = 100    //最多能选择的商品数量
  const [newFilteredData, setNewFilteredData]= useState<any[]>([]);
  const [ids, setIds]= useState<any[]>([]);
  // 获取所有商品对象
  const allItems = dynamic?.items || [];
  const selectedIds = Array.from(selectedMap.keys());

  // 筛选弹框相关状态
  const [showFilterPopup, setShowFilterPopup] = useState(false);
  const [startTime, setStartTime] = useState('开始时间');
  const [endTime, setEndTime] = useState('结束时间');
  const [selectedTimeRange, setSelectedTimeRange] = useState('');
  const [filterStartDate, setFilterStartDate] = useState<number | null>(null); // 筛选用的开始时间戳
  const [filterEndDate, setFilterEndDate] = useState<number | null>(null); // 筛选用的结束时间戳

  // 日期选择器相关状态
  const [datePickerVisible, setDatePickerVisible] = useState(false);
  const [currentDateType, setCurrentDateType] = useState<1 | 2>(1); // 1: 开始时间, 2: 结束时间

  // 计算可用项目数量（排除已被排除的项目）
  const availableItems = allItems.filter(item => !ids.includes(item.digitalWatermark));
  const availableItemsCount = availableItems.length;
  const maxSelectableCount = Math.min(availableItemsCount, selectNum);

  // 修正全选状态判断：当选中数量等于最大可选数量时就是全选状态
  const allSelected = selectedIds.length === maxSelectableCount && availableItemsCount > 0;
  
  const [platform,setPlatform] = useState<string>("H5");

  const openVip = ()=>{
    Dialog.confirm({
      title: "提示",
      children: "请先开通会员",
      okText: "去开通",
      cancelText: "取消",
      platform: 'ios',
      onOk: () => {
        if (platform === "WX") {
          wx.miniProgram.navigateTo({ url: "/pages/web?path=/pageVip/vipCenter/index" });
        } else {
          Taro.navigateTo({ url: "/pageVip/vipCenter/index" });
        }
      }
    })
  }

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  const getLinkIdInfo = async () => {
    const params = Taro.getCurrentInstance().router?.params;
    if(!paramsId.current&&!params?.id){
      return;
    }
    paramsId.current = params?.id || paramsId.current;
    const data = {
      id:  paramsId.current,
    }
    const res: any = await getLinkId(data);
    if (res && res.code === 0 && res.data) {
      if (res.data && res.data) {
        // 确保所有ID都是字符串类型
        // const selectedIds = res.data.split(',').map(id => String(id));
        // const initialMap = new Map<number | string, DynamicItem>();
        // selectedIds.forEach(id => {
        //   initialMap.set(id, {} as DynamicItem);
        // });
        // setSelectedMap(initialMap);
        setIds(res.data.split(','))
      }
    }
  }

  const getDynamicListData = async (tab?: TabType, isFilter: boolean = false, customStartDate?: number | null, customEndDate?: number | null) => {
    const params = Taro.getCurrentInstance().router?.params;
    if (!params?.id) return;
    
    Taro.showLoading({
      title: '加载中...',
      mask: true
    });

    // 使用传入的日期参数或者状态中的日期
    const startDate = customStartDate !== undefined ? customStartDate : filterStartDate;
    const endDate = customEndDate !== undefined ? customEndDate : filterEndDate;

    const data = {
      id: params.id,
      timeStamp: isFilter ? '' : timeStamp, // 筛选时重置 timeStamp
      type: (tab || currentTab) === 'all' ? 1 : 2, // 全部=1, 上新=2
      // url: '',
      ...(startDate && { startDate }), // 添加开始时间戳
      ...(endDate && { endDate }) // 添加结束时间戳
    } as any;
    
    // 调试信息
    console.log('调用接口参数:', {
      id: params.id,
      timeStamp: isFilter ? '' : timeStamp,
      type: (tab || currentTab) === 'all' ? 1 : 2,
      startDate: startDate || '无',
      endDate: endDate || '无',
      isFilter
    });

    try {
      const res: any = await getCollectDynamicList(data);
      let time;
      Taro.hideLoading();
      if (res && res.code === 0 && res.data) {
        setlinkId(params.id);
        const itemsWithSkus = res.data.items?.map(item => {
          time = item.timeStamp;
          return ({
            ...item,
            color: [],
            digitalWatermark: String(item.digitalWatermark) // 确保 digitalWatermark 是字符串
          });
        }) || [];

        // 如果是筛选，清空之前的数据；否则追加数据
        const newItems = isFilter ? itemsWithSkus : [...dynamic.items, ...itemsWithSkus];
        setDynamic({
          ...res.data,
          items: newItems
        });
        
        // 只有当有新数据时才更新 timeStamp，避免设置为 undefined
        if (time !== undefined) {
          setTimeStamp(time);
        }

        // 更新选中状态
        const currentSelectedIds = Array.from(selectedMap.keys());
        console.log('currentSelectedIds before update:', currentSelectedIds);
        if (currentSelectedIds.length > 0) {
          const newSelectedMap = new Map<number | string, DynamicItem>();
          currentSelectedIds.forEach(id => {
            const item = newItems.find(item => String(item.digitalWatermark) === String(id));
            if (item) {
              newSelectedMap.set(String(id), item);
              console.log('Found and set item for id:', id, item);
            } else {
              console.log('Item not found for id:', id);
            }
          });
          console.log('newSelectedMap size:', newSelectedMap.size);
          setSelectedMap(newSelectedMap);
        }

        // 处理分组数据
        const grouped = {};
        newItems.forEach(item => {
          const time = item.time;
          if (!grouped[time]) {
            grouped[time] = [];
          }
          grouped[time].push(item);
        });

        const newFilteredData_s = Object.keys(grouped).map(time => ({
          //group: `${time.replace('/', '月')}日`,
          group: time,
          items: grouped[time].map(item => ({
            ...item,
            digitalWatermark: item.digitalWatermark,
          }))
        }));    
        setNewFilteredData(newFilteredData_s);
      }
    } catch (error) {
      Taro.hideLoading();
      toast("info", {
        content: "网络异常，请重试",
        duration: 2000
      });
      console.error('获取动态列表失败:', error);
    }
  }

  const rollOver = (event) => {
    const scrollTop = event.detail.scrollTop;

    // 当滚动超过头部信息区域时，固定tab栏
    // 头部信息区域包括：头像(64px) + 昵称(~30px) + 统计信息(~60px) + 间距(~50px) ≈ 200px
    const fixedThreshold = 209;

    if (scrollTop > fixedThreshold && !isTabFixed) {
      setIsTabFixed(true);
    } else if (scrollTop <= fixedThreshold && isTabFixed) {
      setIsTabFixed(false);
    }
  }

  // useDidShow(() => {

  // 直接瞬间跳转到顶部（无动画）
  
    //setSelectedMap(new Map());  //清空全选
    //setIds([]);           //清空已ID  
  // });

  useEffect(() => {
    getLinkIdInfo();
    getDynamicListData();

    
    Taro.eventCenter.on("refreshAlbumList", getLinkIdInfo);

    // 页面隐藏时移除监听器
    return () => {
      Taro.eventCenter.off("refreshAlbumList", getLinkIdInfo);
    };
  }, []);

  // 使用 useDidShow 确保页面重新显示时能接收事件
  // useDidShow(() => {
  //   console.log('采集页面显示，注册事件监听器');

  //   // 注册事件监听器
  //   const handleRefreshAlbumList = async () => {
  //     console.log('采集页面收到refreshAlbumList事件，开始调用接口');
  //     try {
  //       await getLinkIdInfo();
  //       console.log('getLinkIdInfo 调用完成');
  //       await getDynamicListData();
  //       console.log('getDynamicListData 调用完成');
  //     } catch (error) {
  //       console.error('刷新数据时出错:', error);
  //     }
  //   };

  // });


  // 修改 handleChange 处理函数
  const handleChange = (selectid: (number | string)[]) => {
    
    const filteredArrayIds = selectid.filter(item => !ids.includes(item));
    // 如果当前选中的数量已经达到10条，且用户尝试选择新的项
    if (selectedIds.length >= selectNum && filteredArrayIds.length > selectedIds.length) {
      toast("info", {
        content: `最多只能选择${selectNum}条`,
        duration: 2000
      });
      return;
    }
    console.log('selectedIds before update:', filteredArrayIds);
    const newMap = new Map<number | string, DynamicItem>();
    filteredArrayIds.forEach(id => {
      const item = allItems.find(item => String(item.digitalWatermark) === String(id));
      if (item) {
        newMap.set(id, item);
      }
    });
    setSelectedMap(newMap);
  };

  // 获取选中的商品对象数组
  const getSelectedItems = () => Array.from(selectedMap.values());

  // 根据 tab 类型过滤数据
  const getFilteredDataByTab = (items: DynamicItem[]) => {
    switch (currentTab) {
      case 'new':
        // 上新 tab 显示所有商品
        return items;
      case 'video':
        // 假设视频类型的商品标题包含"视频"或"小视频"
        return items.filter(item => 
          item.title.includes('视频') || item.title.includes('小视频')
        );
      case 'image':
        // 假设图集类型的商品有多张图片
        return items.filter(item => item.imgsSrc.length > 1);
      default:
        return items;
    }
  };

  // 组合搜索和 tab 过滤
  const filteredData = getFilteredDataByTab(dynamic?.items || [])
    .filter(item => item.title.toLowerCase().includes(search.toLowerCase()));

  // 处理 tab 切换
  const handleTabChange = (tab: TabType) => {
    setCurrentTab(tab);
    // 切换 tab 时重置吸顶状态
    setIsTabFixed(false);
    // 切换 tab 时清空筛选条件
    setStartTime('开始时间');
    setEndTime('结束时间');
    setSelectedTimeRange('');
    setFilterStartDate(null);
    setFilterEndDate(null);
    setTimeStamp('');
    setDynamic(prevState => ({ ...prevState, items: [] }));
    // 切换 tab 时重新获取数据（不带筛选条件）
    getDynamicListData(tab, false, null, null);
    // 切换 tab 时清空选中状态
    //setSelectedMap(new Map());
    
    // 滚动到顶部 - 使用 Taro 的 API
    Taro.pageScrollTo({
      scrollTop: 0,
      duration: 0 // 立即滚动，没有动画
    });
  };

  const loadingData = () => {
    // 上拉加载更多时，保持当前的筛选条件
    getDynamicListData(currentTab, false);
  };



  // 商品属性弹窗选中逻辑
  const [attrSelected, setAttrSelected] = useState({});

  // 清除采集记录的处理函数
  const handleClearRecords = () => {
    Dialog.confirm({
      title: "清除采集记录",
      children: "清除后不会永久删除采集记录,下次进入素材搬家页面依然可以展示过往的采集记录。",
      okText: "清除",
      cancelText: "取消",
      onOk: () => {
        setSelectedMap(new Map());
        setIds([]);
        toast("info", {
          content: "清除成功",
          duration: 2000
        });
      },
      platform: "ios",
    });
  };

  // 筛选弹框处理函数
  const handleTimeRangeSelect = (type: string) => {
    const today = new Date();
    let start = '';
    let end = '';

    switch(type) {
      case '今天':
        start = end = formatDate(today);
        break;
      case '昨天':
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        start = end = formatDate(yesterday);
        break;
      case '本月':
        start = formatDate(new Date(today.getFullYear(), today.getMonth(), 1));
        end = formatDate(today);
        break;
      case '上月':
        const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
        start = formatDate(lastMonth);
        end = formatDate(lastMonthEnd);
        break;
      case '本年':
        start = formatDate(new Date(today.getFullYear(), 0, 1));
        end = formatDate(today);
        break;
      case '去年':
        start = formatDate(new Date(today.getFullYear() - 1, 0, 1));
        end = formatDate(new Date(today.getFullYear() - 1, 11, 31));
        break;
    }

    setStartTime(start);
    setEndTime(end);
    setSelectedTimeRange(type); // 设置选中的时间范围类型
  };

  const formatDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const handleFilterReset = () => {
    setStartTime('开始时间');
    setEndTime('结束时间');
    setSelectedTimeRange(''); // 清空选中状态
    setFilterStartDate(null);
    setFilterEndDate(null);
    // 重置筛选，重新加载数据（清除日期筛选条件）
    setTimeStamp('');
    setDynamic(prevState => ({ ...prevState, items: [] }));
    getDynamicListData(currentTab, false, null, null);
  };

  const handleFilterConfirm = () => {
    // 检查是否同时选择了开始时间和结束时间
    if (startTime === '开始时间' || endTime === '结束时间') {
      Taro.showToast({
        title: '请选择开始时间和结束时间',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    
    // 将日期字符串转换为时间戳
    let startTimestamp: number | null = null;
    let endTimestamp: number | null = null;
    
    if (startTime !== '开始时间') {
      startTimestamp = new Date(startTime + ' 00:00:00').getTime();
    }
    
    if (endTime !== '结束时间') {
      // 结束时间设置为当天的 23:59:59
      endTimestamp = new Date(endTime + ' 23:59:59').getTime();
    }
    
    // 时间验证
    if (startTimestamp && endTimestamp) {
      // 获取今天的日期字符串（只比较日期部分）
      const today = new Date();
      const todayDateStr = formatDate(today);
      
      // 检查结束时间是否大于今天（比较日期字符串）
      if (endTime > todayDateStr) {
        Taro.showToast({
          title: '结束时间不能大于今天',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      
      // 检查开始时间是否大于结束时间
      if (startTimestamp > endTimestamp) {
        Taro.showToast({
          title: '开始时间不能大于结束时间',
          icon: 'none',
          duration: 2000
        });
        return;
      }
    }
    
    // 设置筛选时间戳
    setFilterStartDate(startTimestamp);
    setFilterEndDate(endTimestamp);
    
    // 清空现有数据并重新加载
    setTimeStamp('');
    setDynamic(prevState => ({ ...prevState, items: [] }));
    
    // 立即调用接口，传入日期参数
    getDynamicListData(currentTab, true, startTimestamp, endTimestamp);
    
    setShowFilterPopup(false);
  };

  // 日期选择器处理函数
  const handleDatePickerOpen = (type: 1 | 2) => {
    setCurrentDateType(type);
    setDatePickerVisible(true);
  };

  const handleDatePickerClose = () => {
    setDatePickerVisible(false);
  };

  const handleDateConfirm = (formattedDate: string) => {
    if (currentDateType === 1) {
      setStartTime(formattedDate);
    } else {
      setEndTime(formattedDate);
    }
    setSelectedTimeRange(''); // 清空快捷选择状态
    setDatePickerVisible(false);
  };

  // 渲染上新编辑页面 - 使用和商品列表一样的界面
  const renderNewEditPage = () => {
    // 直接使用 dynamic.items，不进行过滤
    const newItems = dynamic?.items || [];
    
    return (
      <View className="move-list">
        <Checkbox.Group 
          value={[...ids, ...selectedIds]} 
          onChange={handleChange}
          disabled={true}
        >
          {newFilteredData.map(group => (
            <View key={group.group} style={{marginTop: '10px'}}>
              <Text className="move-group-title">{group.group}</Text>
              {group.items.map(item => {
                const itemId = String(item.digitalWatermark);
                console.log('Rendering item with id:', itemId, 'selected:', selectedMap.has(itemId));
                return (
                  <View key={itemId}>
                    <View className="move-item-m">
                      {item.digitalWatermark !== undefined && (
                        <Checkbox 
                          value={itemId}
                          className="move-checkbox" 
                          shape="circle"
                          disabled={ids.includes(String(item.digitalWatermark))}
                        >
                          <View></View>
                        </Checkbox>
                      )}
                      <Image className="move-img" src={item.imgsSrc[0]} />
                      <View className="move-info">
                        <Text className="move-title-text">{item.title}</Text>
                        {item.optimaPrice && <Text className="move-price">{item.optimaPrice}</Text>}
                      </View>
                    </View>
                    <View className="move-attrs-wrap">
                      {((item.skus && item.skus.length > 0) || (item.color && item.color.length > 0)) && (
                        <>
                          <Text
                            className="move-attrs"
                            onClick={() => 
                              setExpandedId(item.digitalWatermark === undefined 
                                ? null 
                                : (expandedId === item.digitalWatermark ? null : item.digitalWatermark)
                              )
                            }
                          >
                            商品属性
                            {expandedId === item.digitalWatermark ? 
                              // <Text className="move-attrs-arrow">▼</Text> 
                              <IconTriDown className="move-attrs-arrow" style={{ transform: "rotate(0deg)" }} />
                       : 
                              // <Text className="move-attrs-arrow">▶</Text>
                              <IconTriDown className="move-attrs-arrow" style={{ transform: "rotate(-90deg)" }} />
                        
                            }
                          </Text>
                          {expandedId === item.digitalWatermark && (
                            <View className="attr-content card-attr-content">
                              {item.skus && item.skus.length ?
                              (<View className="attr-row" key="规格">
                                <Text className="attr-label">规格：</Text>
                                <View className="attr-values-s">
                                  {item.skus.map(val => (
                                    <Text
                                      className={`attr-value${attrSelected['规格'] === val ? ' ' : ''}`}
                                      key={val}
                                    >
                                      {val}
                                    </Text>
                                  ))}
                                </View>
                              </View>):(<></>)
                              }

                              {item.color && item.color.length ?
                                (<View className="attr-row" key="颜色">
                                  <Text className="attr-label">颜色：</Text>
                                  <View className="attr-values-s">
                                    {item.color.map(val => (
                                      <Text
                                        className={`attr-value${attrSelected['颜色'] === val ? ' ' : ''}`}
                                        key={val}
                                      >
                                        {val}
                                      </Text>
                                    ))}
                                  </View>
                                </View>):(<></>)
                              }  
                            </View>
                          )}
                        </>
                      )}
                    </View>
                  </View>
                );
              })}
            </View>
          ))}
        </Checkbox.Group>
      </View>
    );
  };

  // 渲染商品列表
  const renderProductList = () => {
    return (
      <View className="move-list-m" >
        <Checkbox.Group 
          value={[...ids, ...selectedIds]} 
          onChange={handleChange}
        >
 
          {filteredData.map(item => (
            <View key={item.digitalWatermark}>
              <View className="move-item-m">
                <Checkbox 
                  disabled={ids.includes(String(item.digitalWatermark))}
                  value={String(item.digitalWatermark)} 
                  className="move-checkbox-home" 
                  shape="circle"
                >
                  <View></View>
                </Checkbox>
                <Image className="move-img" src={item.imgsSrc[0]} />
                <View className="move-info-home">
                  <Text className="move-title-text-home">{item.title}</Text>
                  {item.optimaPrice && <Text className="move-price">{item.optimaPrice}</Text>}
                </View>
              </View>
              <View className="move-attrs-wrap-m">
                {((item.skus && item.skus.length > 0) || (item.color && item.color.length > 0)) && (
                  <>
                    <Text
                      className="move-attrs-m"
                      onClick={() => 
                        setExpandedId(item.digitalWatermark === undefined 
                          ? null 
                          : (expandedId === item.digitalWatermark ? null : item.digitalWatermark)
                        )
                      }
                    >
                      商品属性
                      {expandedId === item.digitalWatermark ? 
                              // <Text className="move-attrs-arrow">▼</Text> 
                              <IconTriDown className="move-attrs-arrow" style={{ transform: "rotate(0deg)" }} />
                       : 
                              // <Text className="move-attrs-arrow">▶</Text>
                              <IconTriDown className="move-attrs-arrow" style={{ transform: "rotate(-90deg)" }} />
                        
                            }
                    </Text>
                    {expandedId === item.digitalWatermark && (
                      <View className="attr-content card-attr-content">
                        {item.skus && item.skus.length ?
                        (<View className="attr-row" key="规格">
                          <Text className="attr-label">规格：</Text>
                          <View className="attr-values-s">
                            {item.skus.map(val => (
                              <Text
                                className={`attr-value${attrSelected['规格'] === val ? ' ' : ''}`}
                                key={val}
                              >
                                {val}
                              </Text>
                            ))}
                          </View>
                        </View>):(<></>)
                        }

                        {item.color && item.color.length ?
                          (<View className="attr-row" key="颜色">
                            <Text className="attr-label">颜色：</Text>
                            <View className="attr-values-s">
                              {item.color.map(val => (
                                <Text
                                  className={`attr-value${attrSelected['颜色'] === val ? ' ' : ''}`}
                                  key={val}
                                >
                                  {val}
                                </Text>
                              ))}
                            </View>
                          </View>):(<></>)
                        }  

                      </View>
                    )}
                  </>
                )}
              </View>
            </View>
          ))}
        </Checkbox.Group>
      </View>
    );
  };

  return (
    <View >
      {platform !== "WX" &&<YkNavBar title="采集内容"
      rightContent={<Text onClick={handleClearRecords} className="yk-navbar-back">清除采集记录</Text>}
      />}
      {/* 固定的tab栏 */}
      <View className={`fixed-tabs-container ${isTabFixed ? 'show' : ''}`}>
        <View className="user-header-tabs fixed-tabs">
          <View
            className={`user-header-tab ${currentTab === 'all' ? 'active' : ''}`}
            onClick={() => handleTabChange('all')}
          >
            全部
          </View>
          <View
            className={`user-header-tab ${currentTab === 'new' ? 'active' : ''}`}
            onClick={() => handleTabChange('new')}
          >
            上新
          </View>
          {/* <View
            className={`user-header-tab ${currentTab === 'video' ? 'active' : ''}`}
            onClick={() => handleTabChange('video')}
          >
            小视频
          </View>
          <View
            className={`user-header-tab ${currentTab === 'image' ? 'active' : ''}`}
            onClick={() => handleTabChange('image')}
          >
            图集
          </View> */}
        </View>
        {/* 固定搜索栏 */}
        {currentTab !== 'new' && (
          <View className="fixed-search-bar">
            <SearchBar
              actionButton={<span className="user-header-filter" onClick={() => setShowFilterPopup(true)}>搜索</span>}
              placeholder="搜索"
              onChange={(e) => setSearch(e.target.value)}
              className="demo-input-btn-input"
              clearable
              onClear={() => setSearch('')}
            />
          </View>
        )}
      </View>

      {dynamic.items.length > 0 ? (
      <ScrollView
          ref={scrollViewRef}
          onScroll={rollOver}
          className="main-scroll-view"
          style={{
            height: 'calc(100vh - 44px)' // 全屏高度减去导航栏
          }}
          scrollY
          onScrollToLower={loadingData}
      >
        <View className="user-header">
          {/* 头像昵称简介 */}
          <View className="user-header-info">

            <View className="user-header-avatar-wrap">
              <Image className="user-header-avatar" src={dynamic.merchantAvatar} />
              <View className="user-header-qrcode" />
            </View>
            <Text className="user-header-nick">{dynamic.merchantName}</Text>
            <Text className="user-header-desc">
              {/* {dynamic.merchantName} - 优质商品，品质保证... */}
            </Text>
            <View className="user-header-stats">
              <View className="user-header-stat">
                <Text className="user-header-stat-num">{dynamic.newCount}</Text>
                <Text className="user-header-stat-label">上新</Text>
              </View>
              <View className="user-header-stat">
                <Text className="user-header-stat-num">{dynamic.totalCount}</Text>
                <Text className="user-header-stat-label">总数</Text>
              </View>
            </View>
          </View>
          {/* tab栏 - 使用CSS动画控制显示隐藏 */}
          {/* <View className={`user-header-tabs ${isTabFixed ? 'hide' : ''}`}> */}
          <View className={`user-header-tabs ${isTabFixed ? '' : ''}`}>
            <View
              className={`user-header-tab ${currentTab === 'all' ? 'active' : ''}`}
              onClick={() => handleTabChange('all')}
            >
              全部
            </View>
            <View
              className={`user-header-tab ${currentTab === 'new' ? 'active' : ''}`}
              onClick={() => handleTabChange('new')}
            >
              上新
            </View>
            {/* <View
              className={`user-header-tab ${currentTab === 'video' ? 'active' : ''}`}
              onClick={() => handleTabChange('video')}
            >
              小视频
            </View>
            <View
              className={`user-header-tab ${currentTab === 'image' ? 'active' : ''}`}
              onClick={() => handleTabChange('image')}
            >
              图集
            </View> */}
          </View>
          {/* 搜索栏 - 当固定tab栏显示时隐藏 */}
          {/* {currentTab !== 'new' && !isTabFixed && ( */}
          {currentTab !== 'new'  && (
            <View>
              <SearchBar
                actionButton={<span className="user-header-filter" onClick={() => setShowFilterPopup(true)}>搜索</span>}
                placeholder="搜索"
                onChange={(e) => setSearch(e.target.value)}
                className="demo-input-btn-input"
                clearable
                onClear={() => setSearch('')}
              />
            </View>
          )}
        </View>

        {/* 商品列表内容 */}
        <View className="move-list">
          {currentTab === 'new' ? renderNewEditPage() : renderProductList()}
        </View>

        {/* 底部操作栏 - 只在非上新页面显示 */}
        {currentTab !== 'new1' && (
          <View className="move-footer">
            <Checkbox
              value="all"
              checked={allSelected}
              //disabled={selectedIds.length > 0}  // 如果有选中的项，则禁用全选
              onChange={() => {
                console.log('全选点击 - 调试信息:', {
                  allSelected,
                  availableItemsCount,
                  selectNum,
                  idsLength: ids.length,
                  allItemsLength: allItems.length,
                  selectedIdsLength: selectedIds.length
                });

                if (allSelected) {
                  console.log('执行清空选中状态');
                  setSelectedMap(new Map());  // 清空选中状态
                } else {
                  console.log('执行全选逻辑');
                  // 选择所有可用项目，但不超过最大限制
                  const selectableCount = Math.min(availableItemsCount, selectNum);
                  console.log('计算结果:', { selectableCount, availableItemsCount, selectNum });

                  // 只有当可用项目数量超过限制时才提示
                  if (availableItemsCount > selectNum) {
                    console.log('显示限制提示');
                    toast("info", {
                      content: `最多只能选择${selectNum}条`,
                      duration: 2000
                    });
                  }

                  const newMap = new Map<number | string, DynamicItem>();
                  console.log('准备选中的项目数量:', availableItems.length);

                  // 使用 Set 来跟踪已添加的 digitalWatermark，避免重复
                  const addedWatermarks = new Set<number | string>();
                  let addedCount = 0;

                  // 遍历所有可用项目，直到选中足够的数量或遍历完所有项目
                  for (let i = 0; i < availableItems.length && addedCount < selectNum; i++) {
                    const item = availableItems[i];
                    if (item.digitalWatermark !== undefined && !addedWatermarks.has(item.digitalWatermark)) {
                      newMap.set(item.digitalWatermark, item);
                      addedWatermarks.add(item.digitalWatermark);
                      addedCount++;
                      console.log(`添加项目 ${addedCount}:`, item.digitalWatermark);
                    } else if (item.digitalWatermark !== undefined) {
                      console.log(`跳过重复项目:`, item.digitalWatermark);
                    }
                  }

                  console.log('最终选中的项目数量:', newMap.size);
                  setSelectedMap(newMap);
                }
              }}
              className="move-footer-checkbox"
              shape="circle"
            >
              全选
            </Checkbox>
            <View className="move-footer-setting-btn-wrap">
            <Button 
              className="move-footer-setting-btn"
              onClick={() => {
                Taro.navigateTo({
                  url: `/pageDynamic/album/editPrice/index?type=moveSetting`,
                });
              }}
            >
              搬家设置
            </Button>
            <Button 
              className={`move-footer-btn ${selectedIds.length === 0 ? 'disabled' : ''}`}
              // disabled={selectedIds.length === 0}
              onClick={() => {
                const selectedItems = getSelectedItems();
                Taro.setStorageSync("selectedDynamics", selectedItems);
                console.log(selectedItems, '-----------selected items');
                if(selectedIds.length>0){
                  if(!isMemberValid(Taro.getStorageSync("userInfo"))){
                    openVip();
                    return;
                  }
                  // Taro.navigateTo({
                  //   url: '/pageDynamic/moveMaterials/selectMaterials/edit?linkId='+linkId
                  // });

                  Taro.navigateTo({
                    url: `/pageDynamic/album/editPrice/index?type=move&linkId=${linkId}`,
                  });
                }else{
                  toast("info", {
        content: "先选择商品再进行搬家",
        duration: 2000
      }); 
                }
                
              }}
            >
              开始搬家({selectedIds.length})
            </Button>
            </View>
          </View>
        )}
      </ScrollView>
      ):(
        <View className="empty-state">
          <Image 
            className="empty-image" 
            src="" 
          />
          <Text className="empty-text">暂无数据</Text>
        </View>
      )}

      {/* 筛选弹框 */}
      <Popup
        visible={showFilterPopup}
        close={() => setShowFilterPopup(false)}
        direction="bottom"
        className="filter-popup"
      >
        <View className="filter-popup-content">
          {/* 标题 */}
          <View className="filter-popup-header">
            <View className="filter-close-icon" onClick={() => setShowFilterPopup(false)}>
              <IconClose />
            </View>
            <Text className="filter-popup-title">筛选</Text>
          </View>

          {/* 时间区间 */}
          <View className="filter-time-section">
            <Text className="filter-section-title">时间区间</Text>
            <View className="filter-time-range">
              <View className="filter-time-item" onClick={() => handleDatePickerOpen(1)}>
                <Text className="filter-time-label">{startTime}</Text>
              </View>
              <Text className="filter-time-separator">至</Text>
              <View className="filter-time-item" onClick={() => handleDatePickerOpen(2)}>
                <Text className="filter-time-label">{endTime}</Text>
              </View>
            </View>
          </View>

          {/* 快捷时间选择 */}
          <View className="filter-quick-time">
            <View
              className={`filter-quick-btn ${selectedTimeRange === '今天' ? 'active' : ''}`}
              onClick={() => handleTimeRangeSelect('今天')}
            >
              <Text className="filter-quick-text">今天</Text>
            </View>
            <View
              className={`filter-quick-btn ${selectedTimeRange === '昨天' ? 'active' : ''}`}
              onClick={() => handleTimeRangeSelect('昨天')}
            >
              <Text className="filter-quick-text">昨天</Text>
            </View>
            <View
              className={`filter-quick-btn ${selectedTimeRange === '本月' ? 'active' : ''}`}
              onClick={() => handleTimeRangeSelect('本月')}
            >
              <Text className="filter-quick-text">本月</Text>
            </View>
          </View>

          <View className="filter-quick-time">
            <View
              className={`filter-quick-btn ${selectedTimeRange === '上月' ? 'active' : ''}`}
              onClick={() => handleTimeRangeSelect('上月')}
            >
              <Text className="filter-quick-text">上月</Text>
            </View>
            <View
              className={`filter-quick-btn ${selectedTimeRange === '本年' ? 'active' : ''}`}
              onClick={() => handleTimeRangeSelect('本年')}
            >
              <Text className="filter-quick-text">本年</Text>
            </View>
            <View
              className={`filter-quick-btn ${selectedTimeRange === '去年' ? 'active' : ''}`}
              onClick={() => handleTimeRangeSelect('去年')}
            >
              <Text className="filter-quick-text">去年</Text>
            </View>
          </View>

          {/* 底部按钮 */}
          <View className="filter-popup-footer">
            <Button
              className="filter-reset-btn"
              onClick={handleFilterReset}
              style={{ marginTop: 0 }}
            >
              重置
            </Button>
            <Button
              className="filter-confirm-btn"
              onClick={handleFilterConfirm}
              style={{ marginTop: 0 }}
            >
              确定
            </Button>
          </View>
        </View>
      </Popup>

      {/* 日期选择器 - 使用Portal渲染到body顶层 */}
      {datePickerVisible && typeof document !== 'undefined' && createPortal(
        <View style={{ zIndex: 10000, position: 'relative' }}>
          <DatePicker
            visible={datePickerVisible}
            title={currentDateType === 1 ? '选择开始时间' : '选择结束时间'}
            maskClosable
            disabled={false}
            currentTs={currentDateType === 1 ?
              (startTime !== '开始时间' ? new Date(startTime).getTime() : Date.now()) :
              (endTime !== '结束时间' ? new Date(endTime).getTime() : Date.now())
            }
            mode="date"
            onHide={handleDatePickerClose}
            onOk={(timestamp) => {
              const ts = Array.isArray(timestamp) ? timestamp[0] : timestamp;
              const date = new Date(ts);
              const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
              handleDateConfirm(formattedDate);
            }}
            formatter={(value, type) => {
              if (type === 'year') {
                return `${value}年`;
              } else if (type === 'month') {
                return `${value}月`;
              } else if (type === 'date') {
                return `${value}日`;
              }
              return `${value}`;
            }}
          />
        </View>,
        document.body
      )}

    </View>
  );
}

