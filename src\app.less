@import "@arco-design/mobile-react/style/mixin.less";
@import "./utils/css/variables.less";

@arcoblue-1: #e8f3ff; //浅色/白底悬浮
@arcoblue-2: #bedaff; //文字禁用
@arcoblue-3: #94bfff; //一般禁用
@arcoblue-4: #6aa1ff; //特殊场景
@arcoblue-5: #4080ff; //悬浮（hover）
@arcoblue-6: #165dff; //常规
@arcoblue-7: #0e42d2; //点击（click）
@arcoblue-8: #072ca6;
@arcoblue-9: #031a79;
@arcoblue-10: #000d4d;

@dark-arcoblue-1: #000d4d;
@dark-arcoblue-2: #041b79;
@dark-arcoblue-3: #0e32a6;
@dark-arcoblue-4: #1d4dd2;
@dark-arcoblue-5: #306fff;
@dark-arcoblue-6: #3c7eff;
@dark-arcoblue-7: #689fff;
@dark-arcoblue-8: #93beff;
@dark-arcoblue-9: #bedaff;
@dark-arcoblue-10: #eaf4ff;

.taro_page {
  .use-var(background-color, background-color);
  .use-dark-mode-query({
        background-color: @dark-background-color !important;
      });
}

:root {
  --page-primary-background-color: #FAFAFA; // 设置页-主题背景颜色
  // --primary-color: @arcoblue-6;
  // --primary-disabled-color: @arcoblue-3;
  // --lighter-primary-color: @arcoblue-1;
  // --button-primary-clicked-background: @arcoblue-7;

  // --dark-primary-color: @dark-arcoblue-6;
  // --dark-primary-disabled-color: @dark-arcoblue-3;
  // --dark-lighter-primary-color: @dark-arcoblue-1;
  // --dark-button-primary-clicked-background: @dark-arcoblue-7;
}
body{
  position: fixed;
}

body, html {
  // margin: 0;
  // padding: 0;
  height: 100vh !important;
  width: 100vw;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;

  // 适配安全区域
  padding-bottom: env(safe-area-inset-bottom);
  padding-bottom: constant(safe-area-inset-bottom); // 兼容旧版 WebKit

}

// .taro_page_shade {
//   display: block !important;
// }

// .taro_page_shade
//   > .taro_page.taro_page_show.taro_page_stationed:not(.taro_page_shade):not(.taro_tabbar_page):not(:last-child) {
//   display: block !important;
// }

div.taro_page_shade,
div.taro_router
  > .taro_page.taro_page_show.taro_page_stationed:not(.taro_page_shade):not(
    .taro_tabbar_page
  ):not(:last-child) {
  display: initial;
}

div.taro_router > .taro_page {
  transition-timing-function: cubic-bezier(0.26, 1, 0.48, 1);

  // 修复 h5 首页中使用 useLoad 导致出现路由动画的问题
  &:first-child {
    transition: none;
  }
}