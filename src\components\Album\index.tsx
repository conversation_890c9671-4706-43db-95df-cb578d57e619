import { componentWrapper } from '@arco-design/mobile-utils';
import AlbumCore from './AlbumCore';
import AlbumActions from './AlbumActions';
import AlbumHeader from './AlbumHeader';
import AlbumList from './AlbumList';

// 使用 componentWrapper 组合所有子组件
const Album = componentWrapper(AlbumCore, {
  Actions: AlbumActions,
  Header: AlbumHeader,
  List: AlbumList,
});

// 为了支持 externalRefreshKey，需要创建一个包装组件
const AlbumWrapper = (props: any) => {
  return <Album {...props} />;
};

export default AlbumWrapper;

// 导出各个子组件供单独使用
export { AlbumCore, AlbumActions, AlbumHeader, AlbumList };



