@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageSetting/settings/index"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
  --button-radius: 8px;

  .setPageContent {
    position: relative;
    width: 100%;
  }



  .module {
    width: 100%;
    box-sizing: border-box;
    padding: 10px;

    &-title {
      width: 100%;
      height: 32px;
      display: flex;
      align-items: center;

      &-text {
        font-size: 13px;
        color: #999999;
      }
    }

    &-content {
      width: 100%;
      box-sizing: border-box;
      border-radius: 10px;
      overflow: hidden;
    }
  }

  .module-bottom{
    padding-bottom: 60px;
  }

  .moduleBtn {
    z-index: 100;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: calc(100% - 20px);
    margin: 0 10px;
    box-sizing: border-box;
    border-radius: var(--button-radius) !important;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 44px !important;
    margin-bottom: 15px;

    &:active {
      opacity: 0.8;
    }

    &-text {
      font-size: 16px;
    }

    // &-primary{
    //   background-color: var(--primary-color);
    // }

    // &-ghost{
    //   background-color: var(--cell-background-color);
    // }
  }

  // .arco-theme-dark{
  //   .moduleBtn{
  //     // background-color: var(--dark-cell-background-color);
  //   }
  // }
}
