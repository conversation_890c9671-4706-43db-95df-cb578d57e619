import React, { useState, useRef, useImperativeHandle, forwardRef } from 'react';
import { View, Text, Image } from '@tarojs/components';
import { PopupSwiper, Button } from '@arco-design/mobile-react';
import './index.less';
import { IconRight } from "@arco-iconbox/react-yk-arco";
interface FilterDrawerProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (data: FilterData) => void;
  onReset: () => void;
  onTimeClick: (type: 1 | 2) => void;
  tempBeginDate?: string;
  tempEndDate?: string;
}

interface FilterData {
  beginDate: string;
  endDate: string;
}

export interface FilterDrawerRef {
  reset: () => void;
  setDateRange: (beginDate: string, endDate: string) => void;
}

const FilterDrawer = forwardRef<FilterDrawerRef, FilterDrawerProps>(({
  visible,
  onClose,
  onConfirm,
  onReset,
  onTimeClick,
  tempBeginDate = '请选择',
  tempEndDate = '请选择'
}, ref) => {
  const [beginDate, setBeginDate] = useState('请选择');
  const [endDate, setEndDate] = useState('请选择');

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    reset: () => {
      setBeginDate('请选择');
      setEndDate('请选择');
    },
    setDateRange: (begin: string, end: string) => {
      setBeginDate(begin);
      setEndDate(end);
    }
  }));

  // 点击时间选择
  const handleTimeClick = (type: 1 | 2) => {
    onTimeClick(type);
  };

  // 重置
  const handleReset = () => {
    setBeginDate('请选择');
    setEndDate('请选择');
    onReset();
  };

  // 确定
  const handleConfirm = () => {
    const data: FilterData = {
      beginDate,
      endDate
    };
    onConfirm(data);
    // 不自动关闭抽屉，由父组件决定是否关闭
  };

  return (
    <>
      <PopupSwiper
        visible={visible}
        close={onClose}
        direction="right"
        className="filter-drawer-popup"
      >
      <View className="rightBox">
        <View className="rightContentBox">
          <View className="rightContentBox-timeTitle">
            <Text className="rightContentBox-timeTitle-text">时间区间</Text>
          </View>
          
          <View 
            className="rightContentBox-timeStart clickOpacity" 
            onClick={() => handleTimeClick(1)}
          >
            <Text className="rightContentBox-timeStart-title">起始时间</Text>
            <View className="rightContentBox-timeStart-right">
              <Text className="rightContentBox-timeStart-right-text">{tempBeginDate}</Text>
              {/* <Image 
                className="rightContentBox-timeStart-right-img" 
                src={require("@/assets/images/common/right_arrow.png")}
                mode="aspectFit"
              /> */}
              <IconRight className="rightContentBox-timeStart-right-img" />
            </View>
          </View>
          
          <View 
            className="rightContentBox-timeEnd clickOpacity" 
            onClick={() => handleTimeClick(2)}
          >
            <Text className="rightContentBox-timeEnd-title">结束时间</Text>
            <View className="rightContentBox-timeEnd-right">
              <Text className="rightContentBox-timeEnd-right-text">{tempEndDate}</Text>
              {/* <Image 
                className="rightContentBox-timeEnd-right-img" 
                src={require("@/assets/images/common/right_arrow.png")}
                mode="aspectFit"
              /> */}
              <IconRight className="rightContentBox-timeEnd-right-img" />

            </View>
          </View>

          <View className="foot_holder"></View>

          <View className="rightContentBox-foot">
            <View 
              className="rightContentBox-foot-reset clickOpacity" 
              onClick={handleReset}
            >
              <Text className="rightContentBox-foot-reset-text">重置</Text>
            </View>
            <View 
              className="rightContentBox-foot-comfirm clickOpacity" 
              onClick={handleConfirm}
            >
              <Text className="rightContentBox-foot-comfirm-text">确定</Text>
            </View>
          </View>
        </View>
      </View>
      </PopupSwiper>

    </>
  );
});

FilterDrawer.displayName = 'FilterDrawer';

export default FilterDrawer;
