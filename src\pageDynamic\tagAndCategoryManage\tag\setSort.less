@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageDynamic/tagAndCategoryManage/tag/setSort"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;  
  });

  .set-sort-page {
    min-height: 100vh;
    background: #f7f8fa;
    .use-dark-mode-query({
      background-color: @dark-background-color;     //黑色背景
    });
  }

  .set-sort-navbar {
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
  }

  .set-sort-back {
    position: absolute;
    left: 16px;
    font-size: 22px;
    color: #222;
    top: 0;
    height: 48px;
    line-height: 48px;
    cursor: pointer;
  }

  .set-sort-title {
    font-size: 17px;
    font-weight: 500;
    color: #222;
  }

  .set-sort-list {
    margin-top: 8px;
    background: #fff;
  }

  .set-sort-item {
    display: flex;
    align-items: center;
    padding: 0 16px;
    height: 52px;
    position: relative;
    background: #fff;
    .use-dark-mode-query({
      background-color: var(--dark-container-background-color);   //灰色背景
    });
  }

  .set-sort-radio {
    width: 22px;
    height: 22px;
    border-radius: 50%;
    border: 2px solid #d1d5db;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    transition: border-color 0.2s;
  }
  .set-sort-radio.checked {
    border-color: #165dff;
  }
  .set-sort-radio-inner {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #165dff;
  }
  .set-sort-label {
    font-size: 16px;
    color: #222;
    .use-dark-mode-query({
      color: var(--dark-font-color);    //白色字体
    });
  }
  .set-sort-divider {
    position: absolute;
    left: 16px;
    right: 0;
    bottom: 0;
    height: 1px;
    background: #f0f0f0;
  }

  .sort-confirm-mask {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .sort-confirm-modal {
    background: #fff;
    border-radius: 12px;
    width: 80vw;
    max-width: 320px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
    padding: 24px 0 0 0;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .sort-confirm-title {
    font-size: 18px;
    font-weight: bold;
    color: #222;
    text-align: center;
    margin-bottom: 12px;
  }
  .sort-confirm-content {
    font-size: 15px;
    color: #8c8c8c;
    text-align: center;
    margin-bottom: 8px;
    line-height: 1.5;
  }
  .sort-confirm-actions {
    display: flex;
    width: 100%;
    border-top: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-top: 1px solid @dark-line-color;
  });
    margin-top: 18px;
  }
  .sort-confirm-cancel {
    flex: 1;
    text-align: center;
    padding: 14px 0;
    color: #222;
    font-size: 16px;
    border-right: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-right: 1px solid @dark-line-color;
  });
    cursor: pointer;
  }
  .sort-confirm-ok {
    flex: 1;
    text-align: center;
    padding: 14px 0;
    color: #165dff;
    font-size: 16px;
    cursor: pointer;
  }
}
