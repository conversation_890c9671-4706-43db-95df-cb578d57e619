@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageDynamic/tagAndCategoryManage/tag/addContent"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;  
  });

  .add-content-page {
    width: 100vw;
    height: 100vh;
    background: #fff;
    display: flex;
    flex-direction: column;
    .use-dark-mode-query({
    background-color: @dark-background-color;     //黑色背景
  });
  }

  .nav-bar-row {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    //height: 44px;
    background: #fff;
  }

  .demo-input-btn-input {
    padding: 0 15px !important;
    margin: 0 !important;
  }

  .nav-bar-filter {
    position: absolute;
    right: 16px;
    top: 0;
    height: 44px;
    line-height: 44px;
    font-size: 15px;
    color: #165dff;
    z-index: 10;
  }

  .search-bar-wrap {
    padding: 0 16px;
    background: #fff;
    border-bottom: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-bottom: 1px solid @dark-line-color;
  });
    .use-dark-mode-query({
    background-color: @dark-background-color;     //黑色背景
  });
  }

  .search-bar {
    width: 100%;
    height: 32px;
    background: #f7f8fa;
    border-radius: 16px;
    border: none;
    font-size: 14px;
    color: #222;
    // margin: 8px 0;
    padding: 0 12px;
    box-sizing: border-box;
  }

  .content-scroll {
    flex: 1;
    background: #fff;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
  }

  .content-list {
    background: #fff;
    .use-dark-mode-query({
    background-color: @dark-background-color;     //黑色背景
  });
  }

  .loading-more,
  .no-more {
    text-align: center;
    padding: 20px;
    color: #999;
    font-size: 14px;
  }

  // 列表项样式 - 参考批量编辑的dline
  .dline {
    position: relative;
    width: 100%;
    padding: 15px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });

  border-bottom: 1px solid var(--line-color);
  .use-dark-mode-query({
  border-bottom: 1px solid @dark-line-color;
});

    &-left {
      margin-left: 15px;
      display: flex;
      align-items: center;
      justify-content: center;

      &-change {
        width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;

        // 隐藏所有可能的文本元素
        span:not(.arco-checkbox-icon) {
          display: none !important;
        }
      }
    }

    &-right {
      flex: 1;
      margin-right: 15px;
      margin-left: 10px;
      display: flex;
      flex-direction: column;

      &-top {
        margin-bottom: 8px;

        &.topBg {
          background-color: #f3f4f6;
          padding: 8px;
          border-radius: 4px;
          .use-dark-mode-query({
          background-color: #2a2a2a;
        });
        }

        .dynamic-title {
          font-size: 14px;
          color: #222;
          line-height: 1.4;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
          .use-dark-mode-query({
          color: var(--dark-font-color);
        });
        }
      }

      &-bottom {
        display: flex;
        flex-wrap: wrap;
        gap: 4px;

        .imageItem {
          position: relative;
          width: 60px;
          height: 60px;
          border-radius: 4px;
          overflow: hidden;

          &-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            background: #f7f8fa;
          }

          &-mask {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 12px;
          }
        }
      }
    }
  }

  .add-content-footer {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px 16px 16px;
    background: #fff;
    height: 56px;
    border-top: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-top: 1px solid @dark-line-color;
  });
    .use-dark-mode-query({
    background-color: @dark-background-color;     //黑色背景
  });
  }
  .footer-left {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: left;
    font-size: 15px;
    color: #222;
  }

  .footer-all {
    margin-left: 8px;
  }
  .footer-count {
    color: #222;
    margin-left: -3px;
    .use-dark-mode-query({
    color: var(--dark-font-color);    //白色字体
  });
  }
  .footer-btn {
    background: #165dff;
    color: #fff;
    border-radius: 6px;
    font-size: 16px;
    height: 36px;
    line-height: 36px;
    padding: 0 32px;
    border: none;
    margin-left: 12px;
  }

  .arco-input-prefix {
    width: 20px;
    margin: 0;
    padding: 0;
  }
  .arco-input-label {
    min-width: 40px;
  }
}
