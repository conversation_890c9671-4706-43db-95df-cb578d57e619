import { useState, useRef, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { View, Text, Image } from '@tarojs/components';
import Taro, { useLoad, useReachBottom, usePageScroll } from '@tarojs/taro';
import {
  LoadMore,
  PullRefresh,
  Toast,
  Dialog,
  SearchBar,
  DatePicker,
  Checkbox
} from '@arco-design/mobile-react';
import { LoadMoreStatus } from '@arco-design/mobile-react/cjs/load-more';
import { IconTriDown } from '@arco-design/mobile-react/esm/icon';
import YkNavBar from '@/components/ykNavBar/index';
import FilterDrawer, { FilterDrawerRef } from '@/components/FilterDrawer';
import { getMyCollectList,deleteFavoritesDynamicBatch } from '@/utils/api/common/common_user';
import './index.less';
import { IconLoadEmpty } from "@/components/YkIcons";
import SelectQuantityManager from '@/components/SelectQuantityPopup/SelectQuantityManager';
  import { IconClose } from "@arco-iconbox/react-yk-arco";
  import React, { useEffect } from "react";
  import { formatDate } from "@/utils/utils";
// 类型定义
interface CollectItem {
  id: number;
  userId: number;
  content: string;
  pictures: string;
  price: number;
  isTop: number;
  createTime: number;
  time: string;
  labelAndCatalogueIds: string | null;
  productSpecificationsNames: string;
  productColorNames: string;
  updateTime: number;
}

export default function MyCollect() {
  const [showPage, setShowPage] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState(''); // 搜索关键词
  const searchKeywordRef = useRef(''); // 实时保存搜索内容
  const [dynamicList, setDynamicList] = useState<CollectItem[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [isManager, setIsManager] = useState(false);
  const [delIdlist, setDelIdlist] = useState<number[]>([]);
  const [allchange, setAllchange] = useState(false);

  // 分页和加载状态
  const [canPullRefresh, setCanPullRefresh] = useState(true);
  const [isLoadEnd, setIsLoadEnd] = useState(false);
  const [loadStatus, setLoadStatus] = useState<LoadMoreStatus>("prepare");
  const pageRef = useRef(1);
  const pageSizeRef = useRef(20);
  const isFetchingRef = useRef(false);

  // 筛选抽屉相关状态
  const [isFilterDrawerVisible, setFilterDrawerVisible] = useState(false);
  const filterDrawerRef = useRef<FilterDrawerRef>(null);
  const [tempBeginDate, setTempBeginDate] = useState('请选择');
  const [tempEndDate, setTempEndDate] = useState('请选择');
  const [confirmedBeginDate, setConfirmedBeginDate] = useState('请选择');
  const [confirmedEndDate, setConfirmedEndDate] = useState('请选择');
  const [currentFilterTime, setCurrentFilterTime] = useState<{createTime?: string; endTime?: string}>({});

  // 日期选择器相关状态
  const [datePickerVisible, setDatePickerVisible] = useState(false);
  const [currentDateType, setCurrentDateType] = useState<1 | 2>(1); // 1: 起始时间, 2: 结束时间

  // 数量选择弹框相关状态
  const [popupVisible, setPopupVisible] = useState(false);
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);


  useLoad(() => {
    getCollectListData();
  });

  // 监听页面滚动，控制下拉刷新
  usePageScroll(({ scrollTop }) => {
    // 当滚动位置为0时才允许下拉刷新
    setCanPullRefresh(scrollTop === 0);
  });

  // 上拉加载更多 - 完全参考首页逻辑
  useReachBottom(() => {
    // 简化检查逻辑
    if (!loading && !isLoadEnd && !isFetchingRef.current) {
      getCollectListData();
    }
  });

  // 获取收藏列表 - 完全参考首页逻辑
  const getCollectListData = useCallback(async () => {
    // 防止并发请求
    if (isFetchingRef.current) {
      return;
    }

    try {
      // 首页加载显示loading，分页加载显示LoadMore的loading
      if (pageRef.current === 1 && !loading) {
        setLoading(true);
      } else if (pageRef.current > 1) {
        setLoadStatus("loading");
      }

      isFetchingRef.current = true;

      const userInfo = Taro.getStorageSync("userInfo");
      if (!userInfo?.id) {
        Toast.error('请先登录');
        return;
      }

      // 构建请求参数
      const requestData = {
        pageNo: pageRef.current,
        pageSize: pageSizeRef.current,
        userId: userInfo.id
      } as any;

      // 添加搜索关键词
      const keyword = searchKeywordRef.current;
      if (keyword && keyword.length > 0) {
        requestData.content = keyword;
      }

      // 添加时间筛选参数
      if (currentFilterTime.createTime) {
        requestData.createTime = currentFilterTime.createTime;
      }
      if (currentFilterTime.endTime) {
        requestData.endTime = currentFilterTime.endTime;
      }

      const res = await getMyCollectList(requestData);

      if (res && res.code === 0) {
        const dataList = res.data.list || [];
        setTotalCount(res.data.total || 0);

        if (pageRef.current === 1) {
          setDynamicList(dataList);
          setTimeout(() => {
            setShowPage(true);
          }, 500);
        } else {
          setDynamicList(prev => [...prev, ...dataList]);
        }

        // 检查是否还有更多数据 - 完全按照首页代码逻辑
        if (dataList.length < pageSizeRef.current) {
          setIsLoadEnd(true);
          setLoadStatus("nomore");
        } else {
          // 直接递增页面，和首页代码一致
          pageRef.current += 1;
          setLoadStatus("prepare");
          setIsLoadEnd(false);
        }
      } else {
        setLoadStatus("retry");
        console.error("API error:", res.message);
      }
    } catch (error) {
      console.error("Request failed:", error);
      setLoadStatus("retry");
    } finally {
      setLoading(false);
      isFetchingRef.current = false;
    }
  }, [currentFilterTime, loading]);

  // 下拉刷新处理函数
  const handleRefresh = useCallback(async () => {
    // 重置分页和状态
    pageRef.current = 1;
    setIsLoadEnd(false);
    setLoadStatus("prepare");
    setDelIdlist([]);

    // 统一调用getCollectListData，内部会自动判断是否有搜索内容
    await getCollectListData();
  }, [getCollectListData]);

  // 跳转到详情页
  const goToTrendDetailPage = (item: CollectItem) => {
    Taro.navigateTo({
      url: `/pageDynamic/detail/index?dynamicId=${item.id}&dynamicUserId=${item.userId}`,
    });
  };

  // 管理模式切换
  const changeManager = () => {
    setIsManager(!isManager);
  };

  // 选择删除项
  const getDelIdlist = (item: CollectItem) => {
    const newDelIdlist = [...delIdlist];
    const index = newDelIdlist.indexOf(item.id);

    if (index === -1) {
      newDelIdlist.push(item.id);
    } else {
      newDelIdlist.splice(index, 1);
    }

    setDelIdlist(newDelIdlist);
    setAllchange(newDelIdlist.length === dynamicList.length);
  };

  // 全选/取消全选
  const getAllChange = () => {
    if (delIdlist.length < dynamicList.length) {
      const allIds = dynamicList.map(item => item.id);
      setDelIdlist(allIds);
      setAllchange(true);
    } else {
      setDelIdlist([]);
      setAllchange(false);
    }
  };

  // 取消收藏确认
  const openDelPopup = () => {
    Dialog.confirm({
      title: '温馨提示',
      children: `确定要取消收藏${delIdlist.length}件商品？`,
      okText: '确定',
      cancelText: '取消',
      onOk: getCommonDel,
      platform: 'ios'
    });
  };

  // 执行取消收藏
  const getCommonDel = async () => {
    try {
      const userInfo = Taro.getStorageSync("userInfo");
      if (!userInfo?.id) {
        Toast.error('请先登录');
        return;
      }

      // 调用批量取消收藏的API
      const res = await deleteFavoritesDynamicBatch({ dynamicsIds: delIdlist, userId: userInfo.id });

      if (res && res.code === 0) {
        // 从列表中移除已取消收藏的项目
        setDynamicList(prev => prev.filter(item => !delIdlist.includes(item.id)));
        setDelIdlist([]);
        setAllchange(false);
        setIsManager(false);

        Toast.success('成功取消收藏');
      } else {
        Toast.error(res.message || '取消收藏失败');
      }
    } catch (error) {
      console.error('取消收藏失败:', error);
      Toast.error('取消收藏失败');
    }
  };

  // 自定义数量选择函数
  const selectNum = (num: number) => {
    const allItems = dynamicList || [];
    let newIds: number[] = [];

    const selectCount = Math.min(num, allItems.length);
    for (let i = 0; i < selectCount; i++) {
      newIds.push(allItems[i].id);
    }

    setDelIdlist(newIds);
    setAllchange(newIds.length === allItems.length);
  };



  // 处理搜索
  const handleSearch = useCallback(
    (keyword: string) => {
      setSearchKeyword(keyword);
      searchKeywordRef.current = keyword;

      // 重置分页状态
      pageRef.current = 1;
      setIsLoadEnd(false);
      setLoadStatus("prepare");
      setDelIdlist([]);

      // 调用搜索接口，同时传递当前的筛选条件
      getCollectListData();
    },
    [getCollectListData]
  );

  // 搜索框onChange处理
  const handleSearchChange = (e: any) => {
    const value = e.target.value;
    setSearchKeyword(value); // 立即更新输入框的值
    searchKeywordRef.current = value; // 同时更新ref

    // 如果输入为空，立即清除搜索结果，同时传递当前的筛选条件
    if (value.length === 0) {
      pageRef.current = 1;
      setIsLoadEnd(false);
      setLoadStatus("prepare");
      setDelIdlist([]);
      getCollectListData();
    }
  };

  // 处理键盘按键事件
  const handleKeyDown = (e: any) => {
    console.log("Key pressed:", e.key);
    if (e.key === 'Enter') {
      console.log("Enter key pressed, searching...");
      handleSearchSubmit();
    }
  };

  // 处理键盘搜索按钮点击
  const handleSearchSubmit = (value?: string) => {
    console.log("handleSearchSubmit called with:", value);
    const searchValue = value || searchKeyword;
    if (searchValue.trim().length > 0) {
      handleSearch(searchValue.trim());
    }
  };

  // 搜索框清除处理
  const handleSearchClear = () => {
    setSearchKeyword("");
    searchKeywordRef.current = ""; // 同时清除ref
    pageRef.current = 1;
    setIsLoadEnd(false);
    setLoadStatus("prepare");
    setDelIdlist([]);
    // 清除搜索时，保持当前的筛选条件
    getCollectListData();
  };

  // 处理筛选按钮点击
  const handleFilter = useCallback(() => {
    console.log("点击筛选按钮");
    setFilterDrawerVisible(true);
  }, []);

  // 处理筛选抽屉关闭
  const handleFilterDrawerClose = useCallback(() => {
    setFilterDrawerVisible(false);
  }, []);

  // 处理筛选确认
  const handleFilterConfirm = useCallback((data: any) => {
    console.log("筛选确认", data);

    // 验证时间选择的有效性
    if (tempBeginDate !== '请选择' && tempEndDate !== '请选择') {
      const beginTime = new Date(tempBeginDate).getTime();
      const endTime = new Date(tempEndDate).getTime();

      if (endTime < beginTime) {
        Toast.error('结束时间必须大于起始时间');
        return;
      }
    }

    // 将日期字符串转换为指定格式
    let createTime: string | undefined;
    let endTime: string | undefined;

    if (tempBeginDate && tempBeginDate !== '请选择') {
      createTime = tempBeginDate + ' 00:00:00';
    }

    if (tempEndDate && tempEndDate !== '请选择') {
      endTime = tempEndDate + ' 23:59:59';
    }

    // 确认临时选择的时间
    setConfirmedBeginDate(tempBeginDate);
    setConfirmedEndDate(tempEndDate);

    // 保存当前筛选条件
    const filterTime = { createTime, endTime };
    setCurrentFilterTime(filterTime);

    // 重置分页状态
    pageRef.current = 1;
    setIsLoadEnd(false);
    setLoadStatus("prepare");
    setDelIdlist([]);

    // 调用获取收藏列表接口，传递时间参数
    getCollectListData();

    // 验证成功，关闭抽屉
    setFilterDrawerVisible(false);
  }, [tempBeginDate, tempEndDate, getCollectListData]);

  // 处理筛选重置
  const handleFilterReset = useCallback(() => {
    console.log("筛选重置");
    filterDrawerRef.current?.reset();
    setTempBeginDate('请选择');
    setTempEndDate('请选择');
    setConfirmedBeginDate('请选择');
    setConfirmedEndDate('请选择');

    // 清空筛选条件
    setCurrentFilterTime({});

    // 重置分页状态
    pageRef.current = 1;
    setIsLoadEnd(false);
    setLoadStatus("prepare");
    setDelIdlist([]);

    // 重新获取数据，不传递时间参数
    getCollectListData();
  }, [getCollectListData]);

  // 处理时间选择点击
  const handleTimeClick = useCallback((type: 1 | 2) => {
    setCurrentDateType(type);
    setDatePickerVisible(true);
  }, []);

  // 日期选择确认
  const handleDateConfirm = useCallback((value: string) => {
    if (currentDateType === 1) {
      setTempBeginDate(value);
      filterDrawerRef.current?.setDateRange(value, tempEndDate);
    } else {
      setTempEndDate(value);
      filterDrawerRef.current?.setDateRange(tempBeginDate, value);
    }
    setDatePickerVisible(false);
  }, [currentDateType, tempBeginDate, tempEndDate]);

  // 日期选择关闭
  const handleDatePickerClose = useCallback(() => {
    setDatePickerVisible(false);
  }, []);

  // 获取筛选条件显示文本
  const getFilterText = useCallback(() => {
    const parts: string[] = [];
    if (confirmedBeginDate && confirmedBeginDate !== '请选择') {
      parts.push(`${confirmedBeginDate}`);
    }
    if (confirmedEndDate && confirmedEndDate !== '请选择') {
      parts.push(`${confirmedEndDate}`);
    }
    return parts.join('，');
  }, [confirmedBeginDate, confirmedEndDate]);

  // 处理清除筛选条件
  const handleClearFilter = useCallback(() => {
    handleFilterReset();
  }, [handleFilterReset]);

  // 判断是否显示日期
  const getDataShow = (item: CollectItem, index: number) => {
    if (index === 0) {
      return true;
    } else {
      const prevItem = dynamicList[index - 1];
      if (item.time === prevItem.time) {
        if (item.isTop === 1) {
          return false;
        } else {
          return prevItem.isTop === 1;
        }
      } else {
        if (item.isTop === 1) {
          return false;
        } else {
          return prevItem.isTop === 1 || true;
        }
      }
    }
  };

  // 渲染图片
  const renderImages = (item: CollectItem) => {
    if (!item.pictures) return null;

    const images = item.pictures.split(',');

    if (images.length >= 4) {
      return (
        <View className="list-item-rightC-right-img">
          <View className="list-item-rightC-right-img-item">
            <Image
              className="list-item-rightC-right-img-item-img4"
              src={images[0]}
              mode="aspectFill"
            />
            <Image
              className="list-item-rightC-right-img-item-img4"
              src={images[1]}
              mode="aspectFill"
            />
          </View>
          <View className="list-item-rightC-right-img-item">
            <Image
              className="list-item-rightC-right-img-item-img4"
              src={images[2]}
              mode="aspectFill"
            />
            <Image
              className="list-item-rightC-right-img-item-img4"
              src={images[3]}
              mode="aspectFill"
            />
          </View>
        </View>
      );
    } else if (images.length === 3) {
      return (
        <View className="list-item-rightC-right-img">
          <Image
            className="list-item-rightC-right-img-img2"
            src={images[0]}
            mode="aspectFill"
          />
          <View className="list-item-rightC-right-img-item">
            <Image
              className="list-item-rightC-right-img-item-img4"
              src={images[1]}
              mode="aspectFill"
            />
            <Image
              className="list-item-rightC-right-img-item-img4"
              src={images[2]}
              mode="aspectFill"
            />
          </View>
        </View>
      );
    } else if (images.length === 2) {
      return (
        <View className="list-item-rightC-right-img">
          <Image
            className="list-item-rightC-right-img-img2"
            src={images[0]}
            mode="aspectFill"
          />
          <Image
            className="list-item-rightC-right-img-img2"
            src={images[1]}
            mode="aspectFill"
          />
        </View>
      );
    } else {
      return (
        <Image
          className="list-item-rightC-right-img"
          src={images[0]}
          mode="aspectFill"
        />
      );
    }
  };

  return (
    <View className={`my-collect ${showPage ? '' : 'hiddenStyle'}`}>
      {platform !== "WX" &&<YkNavBar title="我的收藏" />}
      
      {showPage && (
        <>
          {/* 搜索栏 */}
          <View className="searchLine-collect"
          style={platform !== "WX" ? { top: '80px' } : { top: '0' }}
          >
            <SearchBar
              placeholder="搜索收藏内容"
              clearable
              value={searchKeyword}
              onChange={handleSearchChange}
              onClear={handleSearchClear}
              onKeyDown={handleKeyDown}
              // actionButton={
              //   <View className="demo-search-btn" onClick={handleFilter}>
              //     筛选
              //   </View>
              // }
            />

            {/* 筛选条件显示 */}
            {(confirmedBeginDate !== '请选择' || confirmedEndDate !== '请选择') && (
              <View className="filter">
                <View className="filter-text">
                  <Text>{getFilterText()}</Text>
                </View>
                {/* <Image
                  className="filter-img clickOpacity"
                  src={require("@/assets/images/common/clear_filter_icon.png")}
                  mode="aspectFit"
                  onClick={handleClearFilter}
                /> */}
                <IconClose className="filter-img" onClick={handleClearFilter} />
              </View>
            )}
          </View>

          {/* 内容区域 */}
          <PullRefresh
            style={{ marginTop: confirmedEndDate !== '请选择'||confirmedBeginDate !== '请选择' ? '102px' : '53px' }}
            className="boxContent"
            disabled={loading || !canPullRefresh}
            onRefresh={handleRefresh}
            finishDelay={1000}
          >
              {dynamicList.length === 0 && !loading ? (
                <View className="not_content">
                  {/* <Image
                    className="not_content-image"
                    src={require("@/assets/images/common/not_content_trend.png")}
                    mode="scaleToFill"
                  /> */}
                  <IconLoadEmpty className="not_content-image" />
                  <Text>暂无收藏内容</Text>
                </View>
              ) : (
                <View>
                  <View className="totalNum">共{totalCount}件商品</View>
                  {dynamicList.map((item, index) => (
                    <View key={item.id} className="list">
                      <View className="list-item">
                        {getDataShow(item, index) && (
                          <View className="list-item-left">
                            <View className="list-item-left-date">
                              <Text>{formatDate(item.createTime)}</Text>
                              {/* <Image
                                className="list-item-left-date-icon"
                                src={require("@/assets/images/common/album_left.png")}
                                mode="scaleToFill"
                              /> */}
                              <IconTriDown className="list-item-left-date-icon" style={{ transform: "rotate(-90deg)" }} />
                            </View>
                          </View>
                        )}
                        <View className="list-item-rightC">
                          <View
                            className={`list-item-rightC-left ${isManager ? '' : 'hiddenStyle'}`}
                          >
                            <Checkbox
                              className="list-item-rightC-left-checkbox"
                              checked={delIdlist.includes(item.id)}
                              onChange={() => getDelIdlist(item)}
                              value=""
                            />
                          </View>
                          <View
                            className="list-item-rightC-right"
                            onClick={() => goToTrendDetailPage(item)}
                          >
                            {item.pictures ? (
                              <>
                                {renderImages(item)}
                                <View className="list-item-rightC-right-right">
                                  <View className="list-item-rightC-right-right-p1">
                                    <Text className="list-item-rightC-right-right-p1-text">
                                      {item.content.replace(/(\r\n|\n|\r)/gm, '')}
                                    </Text>
                                  </View>
                                  {item.price > 0 && (
                                    <View className="list-item-rightC-right-right-price">
                                      <Text>￥{(item.price/100).toFixed(2)}</Text>
                                    </View>
                                  )}
                                </View>
                              </>
                            ) : (
                              <View className="list-item-rightC-right-right2">
                                <View className="list-item-rightC-right-right2-p1 p1Bg">
                                  <Text className="list-item-rightC-right-right2-p1-text">
                                    {item.content.replace(/(\r\n|\n|\r)/gm, '')}
                                  </Text>
                                </View>
                                {item.price > 0 && (
                                  <View className="list-item-rightC-right-right2-price">
                                    <Text>￥{item.price}</Text>
                                  </View>
                                )}
                              </View>
                            )}
                          </View>
                        </View>
                      </View>
                    </View>
                  ))}

                  {/* LoadMore 组件 */}
                  {pageRef.current > 1 && (
                  <LoadMore
                    style={{ paddingTop: 16, paddingBottom: 20 }}
                    status={loadStatus}
                  />
                  )}
                </View>
              )}

              {/* 占位 */}
              <View className="footer_content_z"></View>
          </PullRefresh>

          {/* 底部操作栏 */}
          <View className="footerBtnBox-collect">
            {isManager ? (
              <>
                <View className="footerBtnBox-collect-change">
                  <Checkbox
                    className="footerBtnBox-collect-change-checkbox"
                    checked={allchange}
                    onChange={getAllChange}
                    value=""
                  />
                  <View className="footerBtnBox-collect-change-c" onClick={() => setPopupVisible(true)}>
                    <Text className="footerBtnBox-collect-change-c-text">选中{delIdlist.length}条</Text>
                    <Image
                      src={require('@/assets/images/common/check_all_icon.png')}
                      className="footerBtnBox-collect-change-c-img"
                    />
                  </View>
                </View>
                {delIdlist.length > 0 ? (
                  <View className="footerBtnBox-collect-btn" onClick={openDelPopup}>
                    <Text>取消收藏</Text>
                  </View>
                ) : (
                  <View className="footerBtnBox-collect-notbtn">
                    <Text>取消收藏</Text>
                  </View>
                )}
              </>
            ) : (
              <View className="footerBtnBox-collect-managerbtn" onClick={changeManager}>
                <Text>管理</Text>
              </View>
            )}
          </View>

          {/* 筛选抽屉 */}
          <FilterDrawer
            ref={filterDrawerRef}
            visible={isFilterDrawerVisible}
            onClose={handleFilterDrawerClose}
            onConfirm={handleFilterConfirm}
            onReset={handleFilterReset}
            onTimeClick={handleTimeClick}
            tempBeginDate={tempBeginDate}
            tempEndDate={tempEndDate}
          />

          {/* 日期选择器 - 使用Portal渲染到body顶层 */}
          {datePickerVisible && typeof document !== 'undefined' && createPortal(
            <DatePicker
              visible={datePickerVisible}
              title={currentDateType === 1 ? '选择起始时间' : '选择结束时间'}
              maskClosable
              disabled={false}
              currentTs={currentDateType === 1 ?
                (tempBeginDate !== '请选择' ? new Date(tempBeginDate).getTime() : Date.now()) :
                (tempEndDate !== '请选择' ? new Date(tempEndDate).getTime() : Date.now())
              }
              mode="date"
              onHide={handleDatePickerClose}
              onOk={(timestamp) => {
                const date = new Date(timestamp);
                const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
                handleDateConfirm(formattedDate);
              }}
              formatter={(value, type) => {
                if (type === 'year') {
                  return `${value}年`;
                } else if (type === 'month') {
                  return `${value}月`;
                } else if (type === 'date') {
                  return `${value}日`;
                }
                return `${value}`;
              }}
            />,
            document.body
          )}

          <SelectQuantityManager
            visible={popupVisible}
            onClose={() => setPopupVisible(false)}
            onSelectAll={getAllChange}
            onSelectCustom={selectNum}
            totalCount={dynamicList?.length || 0}
          />
        </>
      )}
    </View>
  );
}
