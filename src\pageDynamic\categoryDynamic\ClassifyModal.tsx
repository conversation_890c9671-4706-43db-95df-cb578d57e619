import { useState, useEffect, useCallback } from "react";
import { View } from "@tarojs/components";
import { getTreeCatalogApi } from "@/utils/api/common/common_user";
import { toast } from "@/utils/yk-common";
import SelectClassify from "./selectClassify";
import "./ClassifyModal.less";
import { Image } from "@arco-design/mobile-react";
import Taro from "@tarojs/taro";

interface Tag {
  id: number;
  userId: number | null;
  parentId: number | null;
  dynamicsId: string | null;
  name: string;
  coverImage: string;
  type: number;
  sort: number | null;
  isTop: number | null;
  sortType: number | null;
  children: Tag[];
}

interface ClassifyModalProps {
  visible: boolean;
  userId: string;
  onClose: () => void;
  onConfirm: (selectedTags: Tag[]) => void;
  selectedIds?: number[];
  isOwnAlbum?: boolean; // 是否是自己的相册
}

export default function ClassifyModal({
  visible,
  userId,
  onClose,
  onConfirm,
  selectedIds = [],
  isOwnAlbum = false,
}: ClassifyModalProps) {
  const [data, setData] = useState<Tag[]>([]);
  const [loading, setLoading] = useState(false);

  const loadData = useCallback(async () => {
    setLoading(true);
    try {
      const res: any = await getTreeCatalogApi({ userId });
      if (res && res.code === 0 && res.data) {
        setData(res.data);
      } else {
        toast("error", {
          content: res.msg || "获取分类数据失败",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("获取分类数据失败:", error);
      toast("error", {
        content: "获取分类数据失败",
        duration: 2000,
      });
    } finally {
      setLoading(false);
    }
  }, [userId]);

  useEffect(() => {
    if (visible && userId) {
      loadData();
    }
  }, [visible, userId, loadData]);

  const handleConfirm = (selectedTags: Tag[]) => {
    onConfirm(selectedTags);
    onClose();
  };

    const handleAddTag = () => {
      Taro.navigateTo({ url: "/pageDynamic/tagAndCategoryManage/index" });
  };

  if (!visible) {
    return null;
  }

  const isEmpty = !loading && data.length === 0;
  const contentClassName = `classify-modal-content${isEmpty ? " empty" : ""}`;

  return (
    <View className="classify-modal">
      <View className="classify-modal-mask" onClick={onClose} />
      <View className={contentClassName}>
        {
        // loading ? (
        //   <View className="classify-modal-loading">加载中...</View>
        // ) : 
        data.length === 0 ? (
          <View className="classify-modal-empty">
            <Image
              className="empty-icon"
              src={require("@/assets/images/common/empty_tags.png")}
            />
            <View className="empty-text">暂无分类</View>
            {isOwnAlbum && (
              <View className="empty-actions">
                <View className="empty-btn" onClick={handleAddTag}>
                  添加分类
                </View>
              </View>
            )}
          </View>
        ) : (
          <SelectClassify
            data={data}
            onConfirm={handleConfirm}
            onCancel={onClose}
            selectedIds={selectedIds}
          />
        )}
      </View>
    </View>
  );
}
