import { View, Text } from "@tarojs/components";
import { useDidShow, useLoad } from "@tarojs/taro";
import wx from "weixin-webview-jssdk";
import React, { useEffect } from "react";
import "./index.less";
import {
  Cell,
  Switch,
  ContextProvider,
  Picker,
} from "@arco-design/mobile-react";
import Taro from "@tarojs/taro";
// 引入色板
import arcoPalette from "@/utils/arco-palette.json";
import { switchTestAccount } from "@/utils/api/common/common_user";

// 组件
import YkNavBar from "@/components/ykNavBar/index";

export default function colorSettings() {
  const [platform, setPlatform] = React.useState("H5");
  // 明亮模式色板列表
  const lightPaletteList = arcoPalette.light;
  // 暗黑模式色板列表
  const darkPaletteList = arcoPalette.dark;
  // 用户资料（包含会员信息）
  const [userInfo, setUserInfo] = React.useState<UserInfo | null>(null);

  // 暗黑模式
  const [darkMode, setDarkMode] = React.useState(false);
  // 暗黑模式类型
  const [darkModeType, setDarkModeType] = React.useState<
    "system" | "manual" | "off"
  >("off");
  const [manualDarkMode, setManualDarkMode] = React.useState(false); // 手动选择的暗黑模式状态
  const [systemDarkMode, setSystemDarkMode] = React.useState(false); // 系统暗黑模式状态

  // 初始化暗黑模式设置
  const initializeDarkModeSettings = () => {
    const savedDarkModeType = Taro.getStorageSync("darkModeType") || "off";
    const savedManualDarkMode = !!Taro.getStorageSync("manualDarkMode");

    setDarkModeType(savedDarkModeType);
    setManualDarkMode(savedManualDarkMode);
  };

  // 获取平台信息
  const getPlatformInfo = React.useCallback(() => {
    let uaAll = window.navigator.userAgent;
    return {
      isAndroid: uaAll.indexOf(`android_${APP_NAME}`) > -1,
      isIos: uaAll.indexOf(`ios_${APP_NAME}`) > -1,
      isHM: uaAll.indexOf(`hm_${APP_NAME}`) > -1,
    };
  }, []);

  // 检测系统主题
  const detectSystemTheme = React.useCallback(() => {
    let _window: any = window;
    const { isAndroid, isIos, isHM } = getPlatformInfo();

    try {
      if (isAndroid) {
        if (_window.getSystemTheme) {
          return _window.getSystemTheme.getSystemTheme();
        }
      } else if (isIos) {
        // iOS 需要异步处理，这里先返回当前状态
        if (_window.webkit?.messageHandlers?.getSystemTheme) {
          _window.webkit.messageHandlers.getSystemTheme.postMessage();
        }
      } else if (isHM) {
        if (_window.harmony?.getSystemTheme) {
          _window.harmony.getSystemTheme();
        }
      }
    } catch (error) {
      console.log("检测系统主题失败:", error);
    }
    return false;
  }, []);

  // 检查并应用系统主题
  const checkAndApplySystemTheme = React.useCallback(() => {
    const darkModeType = Taro.getStorageSync("darkModeType");
    if (darkModeType === "system") {
      console.log("检测到跟随系统设置，开始检测系统主题");
      const systemIsDark = detectSystemTheme();
      if (systemIsDark !== undefined) {
        console.log("系统主题检测结果:", systemIsDark);
        Taro.setStorageSync("darkMode", systemIsDark);
        setDarkMode(systemIsDark);
      }
    }
  }, []);

  // 获取当前生效的暗黑模式状态
  const getCurrentDarkMode = () => {
    if (darkModeType === "system") {
      return systemDarkMode;
    } else if (darkModeType === "manual") {
      return manualDarkMode;
    }
    return false;
  };

  // 切换测试账号
  const handleMemberStatus = (value) => {
    switchTestAccount({ status: value ? 1 : 0, userId: userInfo?.id }).then(
      (res) => {
        if (res && res.code == 0) {
          // 创建新对象以触发状态更新
          const updatedUserInfo = {
            ...userInfo,
            isMember: value ? 1 : 0,
          };
          console.log(updatedUserInfo);
          setUserInfo(updatedUserInfo);
          // 同时更新本地存储
          Taro.setStorageSync("userInfo", updatedUserInfo);
        }
      }
    );
  };

  // 切换暗黑模式
  const handleSetDarkMode = (value) => {
    Taro.setStorageSync("darkMode", value);
    setDarkMode(value);
    wx.miniProgram.postMessage({
      data: {
        action: "changeTheme",
        value: value,
      },
    });
  };

  // 主题
  const [themeType, setThemeType] = React.useState("");
  const pageBgColor = ["#FFFFFF", "#F7F8FA", "#FAFAFA", "#F7F7F7"];
  const [pageBgColorType, setPageBgColorType] = React.useState("#F7F7F7");

  const theme = React.useMemo(() => {
    let pageArr = {
      "page-primary-background-color": pageBgColorType,
    };
    if (themeType != "" && themeType) {
      Taro.setStorageSync("themeType", themeType);
      let arr = {
        "primary-color": lightPaletteList[themeType][6 - 1], //6
        "primary-disabled-color": lightPaletteList[themeType][3 - 1], //3
        "lighter-primary-color": lightPaletteList[themeType][1 - 1], //1
        "button-primary-clicked-background": lightPaletteList[themeType][7 - 1], //7

        "dark-primary-color": darkPaletteList[themeType][6 - 1],
        "dark-primary-disabled-color": darkPaletteList[themeType][3 - 1],
        "dark-lighter-primary-color": darkPaletteList[themeType][1 - 1],
        "dark-button-primary-clicked-background":
          darkPaletteList[themeType][7 - 1],
        ...pageArr,
      };
      Taro.setStorageSync("theme", arr);
      return arr;
    } else {
      return pageArr;
    }
  }, [themeType, pageBgColorType]);

  const getColorPickerData = (originalColors: Record<string, any>) => {
    return Object.entries(originalColors).map(([colorName, colorArray]) => {
      const background = colorArray[5];
      return {
        label: (
          <span className="demo-picker-color">
            <i style={{ background: background }} />
            <span>{colorName}</span>
          </span>
        ),
        value: colorName,
      };
    });
  };

  // 页面背景颜色
  const getPageBgColorPickerData = () => {
    return pageBgColor.map((colorName) => {
      const background = colorName;
      return {
        label: (
          <span className="demo-picker-color">
            <i style={{ background: background }} />
            <span>{colorName}</span>
          </span>
        ),
        value: colorName,
      };
    });
  };

  const httpPlatformMode = (darkMode) => {
    let _window: any = window;
    if (platform == "Android") {
      if (darkMode) {
        console.log("platform StatusBarDarkMode");
        _window.StatusBarLightMode.StatusBarLightMode();
      } else {
        console.log("platform StatusBarLightMode");
        _window.StatusBarDarkMode.StatusBarDarkMode();
      }
    } else if (platform == "IOS") {
      console.log("IOS darkMode", darkMode);
      if (darkMode) {
        _window.webkit.messageHandlers.configStatusBarStyle.postMessage(
          "white"
        );
      } else {
        _window.webkit.messageHandlers.configStatusBarStyle.postMessage(
          "black"
        );
      }
    } else if (platform == "HM") {
      console.log("HM darkMode", darkMode);
      if (darkMode) {
        _window.harmony.StatusBarLightMode();
      } else {
        _window.harmony.StatusBarDarkMode();
      }
    }
  };

  const themeSingle = React.useMemo(() => {
    // StatusBarDarkMode
    // StatusBarLightMode
    // configStatusBarStyle('black')
    // configStatusBarStyle('white')
    httpPlatformMode(darkMode);
    if (darkMode) {
      return getColorPickerData(darkPaletteList);
    } else {
      return getColorPickerData(lightPaletteList);
    }
  }, [darkMode]);

  // 页面背景颜色
  const pageBgColorSingle = React.useMemo(() => {
    return getPageBgColorPickerData();
  }, []);

  // 获取暗黑模式状态文字
  const getDarkModeStatusText = () => {
    if (darkModeType === "system") {
      return "跟随系统";
    } else if (darkModeType === "manual") {
      const manualDarkMode = !!Taro.getStorageSync("manualDarkMode");
      return manualDarkMode ? "深色模式" : "普通模式";
    } else {
      return "已关闭";
    }
  };

  // 跳转到暗黑模式设置页面
  const navigateToDarkModeSettings = () => {
    Taro.navigateTo({
      url: "/pageSetting/darkModeSettings/index",
    });
  };

  useDidShow(() => {
    // 初始化暗黑模式
    const savedDarkModeType = Taro.getStorageSync("darkModeType") || "off";
    setDarkModeType(savedDarkModeType);
    setDarkMode(!!Taro.getStorageSync("darkMode"));

    setThemeType(
      Taro.getStorageSync("themeType")
        ? Taro.getStorageSync("themeType")
        : "arcoblue"
    );
    setPageBgColorType(
      Taro.getStorageSync("pageBgColorType")
        ? Taro.getStorageSync("pageBgColorType")
        : "#F7F7F7"
    );

    setUserInfo(Taro.getStorageSync("userInfo"));
    const { isAndroid, isIos, isHM } = getPlatformInfo();

    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }

    (window as any).onSystemThemeResult = (systemIsDark: boolean) => {
      Taro.setStorageSync("darkMode", systemIsDark);
      setDarkMode(systemIsDark);
    };
    // 初始化暗黑模式设置
    initializeDarkModeSettings();
    // 检查是否需要跟随系统主题
    checkAndApplySystemTheme();
  }); // 添加空依赖数组，只在组件挂载时执行一次

  return (
    <View className="setPageContent">
      {platform !== "WX" && <YkNavBar title="设置" />}

      <View className="module">
        <View className="module-title">
          <Text className="module-title-text">系统设置</Text>
        </View>
        <View className="module-content">
          <ContextProvider
            isDarkMode={getCurrentDarkMode()}
            darkModeSelector="arco-theme-dark"
            theme={theme}
          >
            <Cell.Group bordered={false}>
              {/* 主色设置 */}
              {userInfo?.isTestUser == 1 && (
                <Picker
                  cascade={false}
                  data={themeSingle}
                  maskClosable={true}
                  value={[themeType]}
                  onChange={(val: any) => setThemeType(val[0])}
                  mountOnEnter={false}
                  unmountOnExit={false}
                  renderLinkedContainer={(_, data) => (
                    <Cell
                      label="主色设置"
                      className="module-content-cell"
                      showArrow
                    >
                      {data[0]?.label}
                    </Cell>
                  )}
                />
              )}
              {/* 页面背景颜色设置 */}
              {userInfo?.isTestUser == 1 && (
                <Picker
                  cascade={false}
                  data={pageBgColorSingle}
                  maskClosable={true}
                  value={[pageBgColorType]}
                  onChange={(val: any) => setPageBgColorType(val[0])}
                  mountOnEnter={false}
                  unmountOnExit={false}
                  renderLinkedContainer={(_, data) => (
                    <Cell
                      label="页面背景颜色设置"
                      className="module-content-cell"
                      showArrow
                    >
                      {data[0]?.label}
                    </Cell>
                  )}
                />
              )}

              {userInfo?.isTestUser == 1 && (
                <Cell className="module-content-cell" label="会员" text="">
                  <Switch
                    checked={userInfo?.isMember == 1}
                    platform={platform == "IOS" ? "ios" : "android"}
                    onChange={(value) => handleMemberStatus(value)}
                  />
                </Cell>
              )}

              {/* 暗黑模式 */}
              {platform === "WX" && (
                <Cell className="module-content-cell" label="深色模式" text="">
                  <Switch
                    checked={darkMode}
                    platform={platform == "IOS" ? "ios" : "android"}
                    onChange={(value) => handleSetDarkMode(value)}
                  />
                </Cell>
              )}

              {(platform !== "WX")&&(
              <Cell
                className="module-content-cell"
                label="深色模式"
                text={getDarkModeStatusText()}
                showArrow
                onClick={navigateToDarkModeSettings}
              />
              )}
            </Cell.Group>
          </ContextProvider>
        </View>
      </View>
    </View>
  );
}
