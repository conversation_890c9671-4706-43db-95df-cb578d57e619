import { View, Text, Input, Image } from "@tarojs/components";
import React, { useState, useEffect, useRef } from "react";
import YkNavBar from "@/components/ykNavBar/index";
import { Cell, Button, SwipeAction } from "@arco-design/mobile-react";
import Taro, { useDidShow, useRouter } from '@tarojs/taro';
import { createTag, editTag, uploadFile, getTagDetail } from "@/utils/api/common/common_user";
import { IconAdd, IconDelete } from "@arco-design/mobile-react/esm/icon";
import { toast } from "@/utils/yk-common";
import BottomPopup from "@/components/BottomPopup";
import PermissionPopup from "@/components/PermissionPopup";
import { AuthTypes } from "@/utils/config/authTypes";
import { usePermission } from "@/hooks/usePermission";
import "./index.less";

interface FormParams {
  type: 'tag' | 'catalog';
  mode: 'create' | 'edit';
  id?: string;
}

export default function UnifiedForm() {
  const router = useRouter();
  const userInfo = Taro.getStorageSync("userInfo");
  
  // 解析URL参数
  const { type, mode, id } = router.params as FormParams;
  
  // 通用状态
  const [name, setName] = useState("");
  const [loading, setLoading] = useState(false);
  
  // 标签特有状态
  const [coverImg, setCoverImg] = useState("");
  const [articles, setArticles] = useState<any[]>([]);

  // 目录特有状态
  const [tags, setTags] = useState<any[]>([]);

  // 弹框相关状态
  const [isPopupVisible, setPopupVisible] = useState(false);

  
  // 选择图片的方法
  const chooseImage = (sourceType: "album" | "camera") => {
    if(platformRef.current === "Android"){
      window.setPhotoNum?.setPhotoNum(1);
    }
    if(platformRef.current === "HM"){
      window.harmony.setPhotoNum(1);
    }
    Taro.chooseImage({
      count: 1,
      sourceType: [sourceType], // 'album' 为从相册选择，'camera' 为拍照
      success: async (res) => {
        const imagePath = res.tempFilePaths[0];
        console.log("图片路径: ", imagePath);

        try {
          const uploadRes: any = await uploadFile(imagePath);
          if (uploadRes && uploadRes.code === 0) {
            setCoverImg(uploadRes.data);
            console.log("上传成功", uploadRes.data);
          } else {
            console.error("上传失败", uploadRes.message);
            toast("error", {
              content: "上传失败",
              duration: 2000
            });
          }
        } catch (error) {
          console.error("上传错误", error);
          toast("error", {
            content: "上传失败",
            duration: 2000
          });
        }
      },
      fail: (err) => {
        console.error("选择图片失败", err);
      },
    });
  };
  
  // 自定义权限同意处理
  const customWebPermissonConsent = () => {
    console.log("form customWebPermissonConsent");
    console.log("authType:", authConfirmType.current);

    // 根据权限类型执行不同的后续处理
    if (authConfirmType.current === AuthTypes.CAMERA) {
      // 拍照
      chooseImage("camera");
    } else if (authConfirmType.current === AuthTypes.GALLERY_PHOTO) {
      // 从相册选择
      chooseImage("album");
    }

    return true;
  };

  // 使用全局权限管理
  const {
    initPermissions,
    hasPermission,
    requestPermission,
    webPermissonConsent,
    webPermissonDeny,
    permissionPopupProps,
    platformRef,
    authConfirmType,
  } = usePermission(customWebPermissonConsent);

  // 获取页面标题
  const getPageTitle = () => {
    if (type === 'tag') {
      return mode === 'create' ? '新建标签' : '编辑标签';
    } else {
      return mode === 'create' ? '新建目录' : '编辑目录';
    }
  };

  // 获取名称标签
  const getNameLabel = () => {
    return type === 'tag' ? '标签名称' : '目录名称';
  };

  // 获取名称占位符
  const getNamePlaceholder = () => {
    return type === 'tag' ? '未设置标签名称' : '请输入目录名称';
  };

  // 打开选择封面图弹框（仅标签）
  const handleChooseCover = () => {
    if (type !== 'tag') return;
    if (platformRef.current === "WX") {
      chooseImage("album");
      return;
    }
    setPopupVisible(true);
  };

  // 关闭弹框
  const handleClose = () => {
    setPopupVisible(false);
  };

  // 弹框确认选择
  const handleConfirm = (index: number) => {
    if (index === 0) {
      if(platformRef.current === "HM"){
        chooseImage("camera");
      }else{
      // 请求相机权限
      requestPermission(AuthTypes.CAMERA);
      }
    } else if (index === 1) {
      // 请求相册权限
      if(platformRef.current === "HM"){
        chooseImage("album");
      }else{
        requestPermission(AuthTypes.GALLERY_PHOTO);
      }
    }
  };


  // 删除标签（仅目录）
  const handleDeleteTag = (tagId: number) => {
    const updatedTags = tags.filter(tag => tag.id !== tagId);
    setTags(updatedTags);
    // 同时更新缓存
    Taro.setStorageSync('selectedTag', updatedTags);
  };

  // 删除图文（仅标签）
  const handleDeleteArticle = (articleId: number) => {
    const updatedArticles = articles.filter(article => article.id !== articleId);
    setArticles(updatedArticles);
    // 同时更新缓存
    Taro.setStorageSync('selectedArticles', updatedArticles);
  };

  // 加载编辑数据
  const loadEditData = async () => {
    if (mode !== 'edit' || !id) return;

    try {
      setLoading(true);
      const res: any = await getTagDetail({ id: parseInt(id) });
      if (res && res.code === 0) {
        const data = res.data;
        setName(data.name);

        if (type === 'tag') {
          setCoverImg(data.coverImage || "");
          // 加载关联的文章 - 从API返回的dynamics中获取
          if (data.dynamics && data.dynamics.length > 0) {
            const processedDynamics = data.dynamics.map(item => {
              if (item.pictures) {
                // 去除首尾逗号，按逗号分割，并过滤空值
                const trimmedPictures = item.pictures.replace(/^,+|,+$/g, '');
                const picturesArray = trimmedPictures ? trimmedPictures.split(',').filter(pic => pic.trim()) : [];
                return {
                  ...item,
                  pictures: picturesArray
                };
              }
              return item;
            });
            setArticles(processedDynamics);
            // 同时更新缓存，以便添加图文页面能正确回显
            Taro.setStorageSync('selectedArticles', processedDynamics);
          } else {
            setArticles([]);
            Taro.setStorageSync('selectedArticles', []);
          }
        } else {
          // 加载关联的标签 - 从API返回的labelList中获取
          if (data.labelList && data.labelList.length > 0) {
            setTags(data.labelList);
            // 同时更新缓存，以便添加标签页面能正确回显
            Taro.setStorageSync('selectedTag', data.labelList);
          } else {
            setTags([]);
            Taro.setStorageSync('selectedTag', []);
          }
        }
      }
    } catch (error) {
      toast("error", {
        content: "加载数据失败",
        duration: 2000
      });
    } finally {
      setLoading(false);
    }
  };

  // 提交表单
  const handleSubmit = async () => {
    if (!name.trim()) {
      toast("info", {
        content: `请输入${type === 'tag' ? '标签' : '目录'}名称`,
        duration: 2000
      });
      return;
    }

    try {
      setLoading(true);
      
      let data: any = {
        name: name.trim(),
        userId: userInfo.id,
        type: type === 'tag' ? 1 : 2,
      };

      if (mode === 'edit' && id) {
        data.id = parseInt(id);
      }

      if (type === 'tag') {
        // 标签相关数据
        const articleIds = articles.map(item => item.id);
        data.dynamicIds = articleIds;
        data.coverImage = coverImg;
      } else {
        // 目录相关数据
        const tagIds = tags.map(item => item.id);
        data.labelIds = tagIds;
        data.parentId = 0;
      }

      const apiCall = mode === 'create' ? createTag : editTag;
      const res: any = await apiCall(data);
      
      if (res && res.code === 0) {
        // 清理缓存
        if (type === 'tag') {
          Taro.setStorageSync('selectedArticles', []);
        } else {
          Taro.setStorageSync('selectedTag', []);
        }
        
        toast("success", {
          content: `${mode === 'create' ? '创建' : '编辑'}成功`,
          duration: 2000
        });

        // 发送事件通知列表刷新
        const eventName = type === 'tag' ? 'refreshTagList' : 'refreshCatalogList';
        Taro.eventCenter.trigger(eventName);
        
        setTimeout(() => {
          Taro.navigateBack({ delta: 1 });
        }, 1000);
        
      } else {
        toast("info", {
          content: res.msg,
          duration: 2000
        });
      }
    } catch (error) {
      toast("error", {
        content: `${mode === 'create' ? '创建' : '编辑'}失败`,
        duration: 2000
      });
    } finally {
      setLoading(false);
    }
  };

  useDidShow(() => {
    if (type === 'tag') {
      const selectedArticles = Taro.getStorageSync('selectedArticles') || [];
      setArticles(selectedArticles);
    } else {
      const selectedTags = Taro.getStorageSync('selectedTag') || [];
      setTags(selectedTags);
    }
  });

  useEffect(() => {
    // 初始化权限管理
    const cleanup = initPermissions();

    loadEditData();

    return () => {
      cleanup && cleanup();
    };
  }, []);

  if (loading && mode === 'edit') {
    return (
      <View className="unified-form-page">
        {platformRef.current !== "WX" &&<YkNavBar title={getPageTitle()} />}
        <View className="loading-content">
          <Text>加载中...</Text>
        </View>
      </View>
    );
  }

  return (
    <View className="unified-form-page">
      {platformRef.current !== "WX" &&<YkNavBar title={getPageTitle()} />}
      <View className="form-content">
        {/* 名称输入 */}
        <View className="form-group">
          <Text className="form-label">{getNameLabel()}</Text>
          <Input 
            maxlength={10}
            className="form-input" 
            placeholder={getNamePlaceholder()} 
            value={name} 
            onInput={e => setName(e.detail.value)} 
          />
        </View>

        {/* 标签特有功能 */}
        {type === 'tag' && (
          <>
          
            {/* 封面图 */}
            <View className="cover-content">
            <Text className="group-title">封面图</Text>
            <Cell.Group bordered={false}>
              <Cell
                label="封面图"
                className="cover-cell"
                showArrow
                onClick={handleChooseCover}
              >
                {coverImg ? (
                  <Image className="cover-preview-thumb" src={coverImg} mode="aspectFill" />
                ) : (
                  <Text className="cover-placeholder">未设置</Text>
                )}
              </Cell>
            </Cell.Group>

            </View>

            {/* 已选择的图文 */}
            <Text className="group-title2">已选择的图文（{articles.length}）</Text>
            <View className="selected-articles">
              {articles.map((article, index) => (
                <SwipeAction
                  key={index}
                  rightActions={[
                    {
                      icon: <IconDelete />,
                      text: '删除',
                      style: {
                        backgroundColor: '#F53F3F',
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                      },
                      onClick: () => handleDeleteArticle(article.id),
                    }
                  ]}
                >
                  <View className="article-item">
                    <View className="article-main">
                      <View className="article-title">{article.content}</View>
                      {article.pictures && article.pictures.length > 0 && (
                        <View className="article-images">
                          {article.pictures.slice(0, 7).map((img, imgIndex) => (
                            <View key={imgIndex} className="article-img-container">
                              <Image className="article-img" src={img} mode="aspectFill" />
                              {imgIndex === 6 && article.pictures.length > 7 && (
                                <View className="article-img-mask">
                                  <Text>+{article.pictures.length - 7}</Text>
                                </View>
                              )}
                            </View>
                          ))}
                        </View>
                      )}
                    </View>
                  </View>
                </SwipeAction>
              ))}
            </View>

            {/* 添加图文按钮 */}
            <View className="add-article-row">
              <IconAdd className="add-article-icon" />
              <View className="add-article-btn" 
                onClick={() => {
                  Taro.navigateTo({
                    url: '/pageDynamic/tagAndCategoryManage/tag/addContent'
                  })
                }}
              >
                添加图文
              </View>
            </View>
          </>
        )}

        {/* 目录特有功能 */}
        {type === 'catalog' && (
          <>
            <View className="catalog-label">
              标签（{tags.length}）
            </View>
            <View className="catalog-tag-list">
              <View className="catalog-tag-add"
                onClick={() => {
                  Taro.navigateTo({
                    url: '/pageDynamic/tagAndCategoryManage/catalog/addTag'
                  })
                }}
              >
                <Text className="catalog-tag-add-icon">＋</Text>
                <Text className="catalog-tag-add-text">添加标签</Text>
              </View>
              {tags.map((tag, index) => (
                <SwipeAction
                  style={{ marginRight: '-10px' }}
                  key={index}
                  rightActions={[
                    {
                      icon: <IconDelete />,
                      text: '删除',
                      style: {
                        backgroundColor: '#F53F3F',
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                      },
                      onClick: () => handleDeleteTag(tag.id),
                    }
                  ]}
                >
                  <View className="catalog-tag-item">
                    <Text className="catalog-tag-name">{tag.name}</Text>
                  </View>
                </SwipeAction>
              ))}
            </View>
          </>
        )}
      </View>

      <Button
        className="submit-btn"
        onClick={handleSubmit}
        loading={loading}
      >
        完成
      </Button>

      {/* 底部弹出对话框 */}
      <BottomPopup
        options={["拍照", "从相册选择"]}
        btnCloseText="取消"
        onConfirm={handleConfirm}
        onClose={handleClose}
        visible={isPopupVisible}
      />

      <PermissionPopup {...permissionPopupProps} />
    </View>
  );
}
