import React, { useState } from 'react';
import SelectQuantityPopup from './index';
import CustomQuantityDialog from './CustomQuantityDialog';

interface SelectQuantityManagerProps {
  /** 控制选择弹窗显示/隐藏 */
  visible: boolean;
  /** 关闭弹窗的回调 */
  onClose: () => void;
  /** 选择全部的回调 */
  onSelectAll: () => void;
  /** 选择自定义数量的回调 */
  onSelectCustom: (num: number) => void;
  /** 总数量，用于显示提示信息和限制最大值 */
  totalCount?: number;
}

const SelectQuantityManager: React.FC<SelectQuantityManagerProps> = ({
  visible,
  onClose,
  onSelectAll,
  onSelectCustom,
  totalCount
}) => {
  const [numPopupVisible, setNumPopupVisible] = useState(false);
  const [customNum, setCustomNum] = useState('');

  const handleSelectCustom = () => {
    setNumPopupVisible(true);
  };

  const handleNumConfirm = () => {
    const num = parseInt(customNum, 10);
    if (!Number.isNaN(num) && num > 0) {
      onSelectCustom(num);
    }
    setNumPopupVisible(false);
    setCustomNum('');
  };

  const handleNumCancel = () => {
    setNumPopupVisible(false);
    setCustomNum('');
  };

  return (
    <>
      <SelectQuantityPopup
        visible={visible}
        onClose={onClose}
        onSelectCustom={handleSelectCustom}
        onSelectAll={onSelectAll}
        totalCount={totalCount}
      />
      
      <CustomQuantityDialog
        visible={numPopupVisible}
        value={customNum}
        onValueChange={setCustomNum}
        onConfirm={handleNumConfirm}
        onCancel={handleNumCancel}
      />
    </>
  );
};

export default SelectQuantityManager;
