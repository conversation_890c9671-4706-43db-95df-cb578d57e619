import { View, Text } from '@tarojs/components';
import Taro from '@tarojs/taro';
import YkNavBar from '@/components/ykNavBar';
import { Button } from '@arco-design/mobile-react';
import { useRef, useEffect, useState } from 'react';
import { IconCircleChecked } from '@arco-design/mobile-react/esm/icon';
import './paySuccess.less';

const PaySuccess = () => {
  // 获取URL参数
  const router = Taro.useRouter();
  const orderId = router.params.orderId || '';
  // 我知道了 - 返回会员中心
  const handleConfirm = () => {
    // 返回会员中心页面
    Taro.navigateBack({
      delta: 1 // 返回到会员中心
    });
  };

  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);

  return (
    <View className="pay-success">
      {platform !== "WX" && <YkNavBar title="支付成功" />}
      {/* 主要内容区域 */}
      <View className="main-content">
        {/* 成功图标 */}
        <View>
          <IconCircleChecked className="success-icon"/>
        </View>
        
        {/* 支付成功文字 */}
        <Text className="success-text">支付成功</Text>
      </View>

      {/* 底部按钮 */}
      <View className="bottom-button">
        <Button 
          className="confirm-btn" 
          type="primary"
          onClick={handleConfirm}
        >
          我知道了
        </Button>
      </View>
    </View>
  );
};

export default PaySuccess;
