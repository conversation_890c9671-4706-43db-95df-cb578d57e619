import { useState, useEffect, useRef } from 'react';
import { View, Text, Image } from '@tarojs/components';
import { Loading, Button } from '@arco-design/mobile-react';
import Taro from '@tarojs/taro';
import { AuthTypes } from "@/utils/config/authTypes";
import PermissionPopup from "@/components/PermissionPopup";
import { usePermission } from "@/hooks/usePermission";
import BottomPopup from "@/components/BottomPopup";

import './index.less';
import {
  baiduIdCardOcr,
  baiduBusinessLicenseOcr,
  baiduBankCardOcr,
  baiduBankAccountLicenseOcr
} from '@/utils/api/common/common_wechat';
  import type { ImagePickItem } from '@arco-design/mobile-react/cjs/image-picker/type';
// // 自定义图片项接口
// interface CustomImageItem {
//   url: string;
//   file?: any;
// }

interface YkImagePickerProps {
  type: 'idCardFront' | 'idCardBack' | 'businessLicense' | 'bankCard' | 'accountOpening' | 'others';
  value: ImagePickItem | null;
  onChange?: (img: ImagePickItem | null) => void;
  onOCRResult?: (result: any) => void;
  upload?: (img: ImagePickItem) => Promise<any> | void;
  label?: string;
  disabled?: boolean;
  /** 图片URL，用于触发OCR识别 */
  imageUrl?: string;
}

export default function YkImagePicker({
  type,
  value,
  onChange,
  onOCRResult,
  upload,
  label,
  disabled,
  imageUrl
}: YkImagePickerProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isChoosePopupVisible, setIsChoosePopupVisible] = useState(false);
  const operationTypeRef = useRef<string>(''); // 操作类型标识：'camera' 或 'album'

  // 自定义权限同意处理
  const customWebPermissonConsent = () => {
    console.log("YkImagePicker customWebPermissonConsent");
    console.log("operationType:", operationTypeRef.current);

    // 根据操作类型执行不同的后续处理
    if (operationTypeRef.current === "camera") {
      // 拍照操作
      chooseImageWithType("camera");
    } else if (operationTypeRef.current === "album") {
      // 相册操作
      chooseImageWithType("album");
    }

    // 清除操作类型标识
    operationTypeRef.current = "";

    return true;
  };

  // 使用全局权限管理
  const {
    initPermissions,
    hasPermission,
    requestPermission,
    webPermissonConsent,
    webPermissonDeny,
    permissionPopupProps,
    platformRef,
    authConfirmType,
  } = usePermission(customWebPermissonConsent);

  // 初始化权限管理
  // useEffect(() => {
  //     console.log('权限初始化')
  //     const cleanup = initPermissions();
  //     return () => {
  //       console.log('权限销毁')
  //       cleanup && cleanup();
  //       Taro.setStorageSync('isInit', false);
  //     };
  // }, []);

  // 将blob URL转换为base64
  const blobToBase64 = async (blobUrl: string): Promise<string> => {
    try {
      // 首先通过fetch获取blob数据
      const response = await fetch(blobUrl);
      const blob = await response.blob();

      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          const base64String = reader.result as string;
          // 移除data:image/jpeg;base64,前缀，只保留base64数据
          const base64Data = base64String.split(',')[1];
          resolve(base64Data);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error('转换base64失败:', error);
      throw error;
    }
  };

  // 选择图片的方法
  const chooseImageWithType = (sourceType: "album" | "camera") => {
    if (platformRef.current === "Android") {
      window.setPhotoNum?.setPhotoNum(1);
    }
    if (platformRef.current === "HM") {
      window.harmony?.setPhotoNum(1);
    }

    Taro.chooseImage({
      count: 1,
      sourceType: [sourceType], // 'album' 为从相册选择，'camera' 为拍照
      success: async (res) => {
        const imagePath = res.tempFilePaths[0];
        // 转为base64
        const base64Data = await blobToBase64(imagePath);

        // 将 Taro 临时文件转换为标准 File 对象
        let file: File | null = null;
        try {
          const response = await fetch(imagePath);
          const blob = await response.blob();
          file = new File([blob], 'image.jpg', { type: 'image/jpeg' });
        } catch (fileError) {
          console.error('转换文件失败:', fileError);
        }

        // 创建自定义图片对象
        const imageItem: ImagePickItem = {
          url: imagePath,
          file: file
        };

        // 调用 onChange 回调
        if (onChange) {
          onChange(imageItem);
        }

        // 如果有上传函数，调用实际的上传
        if (upload) {
          try {
            await handleActualUpload(imageItem);
          } catch (uploadError) {
            console.error('上传失败:', uploadError);
            setError('上传失败，请重试');
          }
        }

        // // 如果有 imageUrl 或者需要 OCR 识别，调用 OCR
        // if (imageUrl || (type !== 'others' && imageItem.url)) {
        //   try {
        //     await handleOCR(imageUrl || imageItem.url);
        //   } catch (ocrError) {
        //     console.error('OCR识别失败:', ocrError);
        //     // OCR 失败不影响图片选择，只是不显示识别结果
        //   }
        // }
      },
      fail: (err) => {
        console.error("选择图片失败:", err);
        // setError('选择图片失败，请重试');
      },
    });
  };



  // 处理选择方式确认
  const handleChooseConfirm = (index: number) => {
    initPermissions();
    setIsChoosePopupVisible(false);

    if (index === 0) {
      // 拍照
      if (platformRef.current === "HM") {
        chooseImageWithType("camera");
      } else {
        operationTypeRef.current = "camera";
          // 检查相机权限
          console.log("检查相机权限:", hasPermission(AuthTypes.CAMERA));
          if (!hasPermission(AuthTypes.CAMERA)) {
            console.log("请求相机权限");
            requestPermission(AuthTypes.CAMERA);
            return;
          }
          chooseImageWithType("camera");
      }
    } else if (index === 1) {
      // 相册
      if (platformRef.current === "HM") {
        chooseImageWithType("album");
      } else {
        operationTypeRef.current = "album";
          // 检查相册权限
          console.log("检查相册权限:", hasPermission(AuthTypes.GALLERY_PHOTO));
          if (!hasPermission(AuthTypes.GALLERY_PHOTO)) {
            console.log("请求相册权限");
            requestPermission(AuthTypes.GALLERY_PHOTO);
            return;
          }
          chooseImageWithType("album");
      }
    }
  };

  // 当imageUrl变化时触发OCR
  useEffect(() => {
    if (imageUrl && onOCRResult && type !== 'others') {
      handleOCR(imageUrl);
    }
  }, [imageUrl, onOCRResult, type]);

  // OCR识别 - 使用后端接口
  const handleOCR = async (ocrImageUrl: string) => {
    console.log("YkImagePicker: handleOCR with backend API...", ocrImageUrl);
    setLoading(true);
    setError(null);
    try {
      if (!ocrImageUrl) {
        setError('图片URL不存在');
        setLoading(false);
        return;
      }

      let res: any;
      const ocrData = { url: ocrImageUrl };

      if (type === 'idCardFront' || type === 'idCardBack') {
        res = await baiduIdCardOcr(ocrData);
      } else if (type === 'businessLicense') {
        res = await baiduBusinessLicenseOcr(ocrData);
      } else if (type === 'bankCard') {
        res = await baiduBankCardOcr(ocrData);
      } else if (type === 'accountOpening') {
        res = await baiduBankAccountLicenseOcr(ocrData);
      }

      console.log('[YkImagePicker] OCR API返回结果:', {
        type,
        res,
        dataType: typeof res?.data,
        dataIsString: typeof res?.data === 'string',
        hasWordsResult: !!res?.data?.words_result,
        hasErrorCode: !!res?.data?.error_code,
        resultKeys: res?.data ? (typeof res?.data === 'object' ? Object.keys(res?.data) : 'isString') : []
      });

      // 检查是否有识别结果并转换格式
      if (res && res.code === 0 && res?.data) {
        let standardResult = res.data;
        
        // 如果 data 是字符串（JSON字符串），需要先解析
        if (typeof standardResult === 'string') {
          console.log('[YkImagePicker] 检测到 data 是 JSON 字符串，正在解析...');
          try {
            standardResult = JSON.parse(standardResult);
            console.log('[YkImagePicker] JSON 解析后的 standardResult:', standardResult);
          } catch (parseError) {
            console.error('[YkImagePicker] JSON 解析失败:', parseError);
            setError('数据格式错误，请重试');
            setLoading(false);
            return;
          }
        }
        
        console.log('[YkImagePicker] standardResult: ', standardResult);
        console.log('[YkImagePicker] 检查 words_result:', !!standardResult.words_result, standardResult.words_result);
        console.log('[YkImagePicker] 检查 data:', !!standardResult.data, standardResult.data);
        console.log('[YkImagePicker] 检查 result:', !!standardResult.result, standardResult.result);

        // 如果后端返回的是新格式（有result字段），需要转换为前端期望的格式
        if (standardResult.result && !standardResult.words_result) {
          console.log('[YkImagePicker] 检测到新格式OCR结果，正在转换...');

          if (type === 'bankCard') {
            // 银行卡格式转换
            standardResult = {
              ...standardResult,
              words_result: {
                bank_card_number: { words: standardResult.result.bank_card_number || '' },
                bank_name: { words: standardResult.result.bank_name || '' },
                bank_card_type: { words: standardResult.result.bank_card_type?.toString() || '' },
                holder_name: { words: standardResult.result.holder_name || '' }
              }
            };
          } else if (type === 'accountOpening') {
            // 开户证明格式转换（根据实际字段调整）
            standardResult = {
              ...standardResult,
              words_result: {
                公司名称: { word: standardResult.result.company_name || standardResult.result.公司名称 || '' },
                账号: { word: standardResult.result.account_number || standardResult.result.账号 || '' },
                开户银行: { word: standardResult.result.bank_name || standardResult.result.开户银行 || '' }
              }
            };
          } else {
            // 其他类型的转换
            standardResult = {
              ...standardResult,
              words_result: standardResult.result
            };
          }

          console.log('[YkImagePicker] 格式转换完成:', standardResult);
        }

        // 检查转换后的结果
        console.log('[YkImagePicker] 最终检查 - words_result:', !!standardResult.words_result);
        console.log('[YkImagePicker] 最终检查 - data:', !!standardResult.data);
        console.log('[YkImagePicker] 最终检查 - result:', !!standardResult.result);
        const hasValidResult = standardResult.words_result || standardResult.data || standardResult.result;
        console.log('[YkImagePicker] hasValidResult:', hasValidResult);

        if (hasValidResult) {
          if (onOCRResult) {
            console.log('[YkImagePicker] 调用 onOCRResult');
            onOCRResult(standardResult);
          }
        } else {
          console.warn('[YkImagePicker] OCR结果格式不符合预期:', {
            standardResult,
            hasWordsResult: !!standardResult.words_result,
            hasData: !!standardResult.data,
            hasResult: !!standardResult.result,
            type
          });
          setError('未识别到有效内容，请重试');
          setLoading(false);
          return;
        }
      } else {
        console.warn('[YkImagePicker] OCR返回空结果');
        setError('未识别到有效内容，请重试');
        setLoading(false);
        return;
      }
    } catch (e: any) {
      console.error('[YkImagePicker] OCR识别失败:', e);
      setError(e.message || '识别失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理删除图片
  const handleDelete = (e: any) => {
    e.stopPropagation(); // 阻止事件冒泡
    if (onChange) {
      onChange(null);
    }
    setError(null);
  };

  // 处理点击添加图片
  const handleAddImage = () => {
    if (disabled) return;
    if (platformRef.current === "WX") {
      chooseImageWithType("album");
      return;
    }
    setIsChoosePopupVisible(true);
  };

  // 实际的上传处理函数
  const handleActualUpload = async (img: ImagePickItem): Promise<ImagePickItem | null> => {
    if (upload) {
      try {
        // 调用外部的 upload 方法
        const uploadResult = await Promise.resolve(upload(img));

        // 如果有OCR回调且上传成功，尝试获取图片URL并进行OCR
        if (onOCRResult && uploadResult) {
          // 从上传结果中获取图片URL
          let uploadedImageUrl = '';
          if (typeof uploadResult === 'string') {
            uploadedImageUrl = uploadResult;
          } else if (uploadResult && typeof uploadResult === 'object') {
            // 尝试从不同可能的字段获取URL
            uploadedImageUrl = (uploadResult as any).localUrl || (uploadResult as any).url || (uploadResult as any).data || '';
          }

          console.log('[YkImagePicker] 准备进行OCR识别:', {
            type,
            uploadResult,
            imageUrl: uploadedImageUrl,
            hasOnOCRResult: !!onOCRResult
          });

          if (uploadedImageUrl) {
            // 延迟一下再调用OCR，确保上传完全完成
            setTimeout(() => {
              handleOCR(uploadedImageUrl);
            }, 500);
          } else {
            console.warn('[YkImagePicker] 无法从上传结果中获取图片URL:', uploadResult);
          }
        }

        return uploadResult as ImagePickItem | null;
      } catch (error) {
        console.error('[YkImagePicker] 上传失败:', error);
        setError('上传失败，请重试');
        return null;
      }
    }
    // 如果没有 upload 方法，直接返回 null
    return null;
  };

//   // 处理重拍
//   const handleReupload = () => {
//     onChange && onChange(null);
//     setError(null);
//     return null;
//   };

  return (
    <View className="yk-imagepicker">
      <View className="yk-imagepicker-container">
        {value ? (
          // 显示已选择的图片
          <View className="yk-imagepicker-image-container">
            <Image
              className="yk-imagepicker-image"
              src={value.url}
              mode="aspectFill"
            />
            <View className="yk-imagepicker-delete" onClick={handleDelete}>
              <Text className="yk-imagepicker-delete-icon">×</Text>
            </View>
          </View>
        ) : (
          // 显示添加按钮
          <View
            className={`yk-imagepicker-add ${disabled ? 'disabled' : ''}`}
            onClick={handleAddImage}
          >
            <Text className="yk-imagepicker-add-icon">+</Text>
            <Text className="yk-imagepicker-add-text">添加图片</Text>
          </View>
        )}
      </View>

      {label && (
        <View className="yk-imagepicker-label">
          <Text className="yk-imagepicker-label-text">{label}</Text>
        </View>
      )}

      {loading && (
        <View className="yk-imagepicker-ocr-status">
          <Loading type="circle" />
          <Text className="yk-imagepicker-ocr-text">正在识别...</Text>
        </View>
      )}

      {error && (
        <View className="yk-imagepicker-error-status">
          <Text className="yk-imagepicker-error-text">{error}</Text>
        </View>
      )}

      {/* 选择方式弹窗 */}
      <BottomPopup
        options={["拍照", "从相册选择"]}
        btnCloseText="取消"
        onConfirm={handleChooseConfirm}
        onClose={() => setIsChoosePopupVisible(false)}
        visible={isChoosePopupVisible}
      />

      {/* 权限弹窗 */}
      <PermissionPopup {...permissionPopupProps} />
    </View>
  );
}