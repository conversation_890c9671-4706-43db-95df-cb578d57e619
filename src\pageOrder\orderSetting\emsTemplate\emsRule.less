@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageOrder/orderSetting/emsTemplate/emsRule"] {
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });

  .ems-rule {
    min-height: 100vh;

    .rule-container {
      margin: 12px;
      border-radius: 8px;
      background-color: #fff;
      background-color: var(--page-primary-background-color) !important;
      .use-dark-mode-query({
      background-color: @dark-background-color !important;
    });
      overflow: hidden;
      padding: 10px 0px;

      .rule-group {
        border-radius: 4px;
        overflow: hidden;

        .rule-tab-cell {
          padding: 12px 16px;
        }
      }
    }

    .area-group {
      margin-bottom: 16px;
      border-radius: 4px;
      overflow: hidden;
    }

    // 标签页样式
    .tab-container {
      display: flex;
      width: 100%;
      height: 40px;
      border-radius: 4px;
      overflow: hidden;
      // border: 1px solid #1961f1;
      border: 1px solid var(--primary-color);

      .tab-item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary-color);
        font-size: 14px;

        &.tab-active {
          color: #fff;
          .use-var(background-color, primary-color);
          .use-dark-mode-query({
          background-color: var(--dark-primary-color) !important;
        });
        }
      }
    }

    // 表单样式
    .form-container {
      padding: 0;
    }

    .form-row {
      display: flex;
      align-items: center;
      padding: 10px 0px 10px 10px;

      &.conditional-row {
        // margin-top: 12px;
        // padding-left: 10px;
      }
    }

    .form-cell-label {
      font-size: 16px;
      .use-var(color, font-color);
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    }

    .form-label {
      flex-shrink: 0;
      width: 70px;
      font-size: 13px;
      color: #333;
      // .use-var(color, font-color);
      .use-var(color, sub-info-font-color);
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    }

    .note {
      margin-left: 8px;
      font-size: 12px;
      // .use-var(color, font-color);
      .use-var(color, sub-info-font-color);
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    }

    .input-container {
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 0 8px;
      height: 36px;
      width: 110px;
      .use-var(background-color, background-color);
      .use-dark-mode-query({
      background-color: @dark-background-color !important;
    });

      .price-input {
        margin: 0;
        padding: 0;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        justify-self: center;
        text-align: center;
        font-size: 15px;
      }

      .unit {
        margin-left: 4px;
        color: #666;
        font-size: 14px;
      }
    }

    .divider {
      height: 1px;
      background-color: #f0f0f0;
      margin: 16px 0;
    }

    // 底部固定区域
    .holder {
      height: 60px;
    }

    // .footer {
    //   position: fixed;
    //   left: 0;
    //   right: 0;
    //   bottom: 0;
    //   padding: 10px 12px;
    //   background-color: #fff;
    //   background-color: var(--page-primary-background-color) !important;
    //   .use-dark-mode-query({
    //     background-color: @dark-background-color !important;
    //   });

    //   .save-btn {
    //     width: 100%;
    //     height: 40px;
    //     border-radius: 0;
    //     font-size: 16px;
    //     background-color: #4080FF;
    //   }
    // }

    .footer {
      background-color: #ffffff;
      .use-var(background-color, background-color);
      .use-dark-mode-query({
      background-color: @dark-cell-background-color !important;
    });
      position: fixed;
      bottom: 0;
      width: 100%;
      height: 64px;
      display: flex;
      justify-content: center;
      align-items: center;

      &-save-btn {
        width: 90%;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ffffff;
        font-size: 14px;
      }
    }

    // // 条件包邮满额输入框
    // .conditional-row {
    //   padding: 10px 32px;
    //   .input-container {
    //     width: 60px;
    //   }
    //   .form-label {
    //     font-size: 13px;
    //   }
    //   .note {
    //     font-size: 12px;
    //     white-space: nowrap;
    //   }
    // }
  }
}
