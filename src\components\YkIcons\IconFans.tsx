import { IconProps } from './types';

const IconFans: React.FC<IconProps> = ({
  color = 'var(--primary-color)',
  size = 20,
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    fill="none"
    version="1.1"
    width={size}
    height={size}
    viewBox="0 0 20 20"
    className={className}
  >
    <g>
      <g>
        <path
          d="M5.416666626930237,6.25C5.416666626930237,5.0994084,6.349416326930236,4.1666666,7.4999996269302365,4.1666666C8.650583426930236,4.1666666,9.583333126930237,5.0994084,9.583333126930237,6.25C9.583333126930237,7.4005833,8.650583426930236,8.333333,7.4999996269302365,8.333333C6.349416326930236,8.333333,5.416666626930237,7.4005833,5.416666626930237,6.25C5.416666626930237,6.25,5.416666626930237,6.25,5.416666626930237,6.25ZM7.4999996269302365,2.5C5.428933226930237,2.5,3.749999926930237,4.1789333,3.749999926930237,6.25C3.749999926930237,8.3210831,5.428933226930237,10,7.4999996269302365,10C9.571083226930238,10,11.249999626930236,8.3210831,11.249999626930236,6.25C11.249999626930236,4.1789333,9.571083226930238,2.5,7.4999996269302365,2.5C7.4999996269302365,2.5,7.4999996269302365,2.5,7.4999996269302365,2.5ZM13.333332626930236,7.9166665C13.333332626930236,7.4564166,13.706415626930237,7.083333,14.166666626930237,7.083333C14.626916626930237,7.083333,14.999999626930236,7.4564166,14.999999626930236,7.9166665C14.999999626930236,8.376916399999999,14.626916626930237,8.75,14.166666626930237,8.75C13.706415626930237,8.75,13.333332626930236,8.376916399999999,13.333332626930236,7.9166665C13.333332626930236,7.9166665,13.333332626930236,7.9166665,13.333332626930236,7.9166665ZM14.166666626930237,5.4166665C12.785957626930237,5.4166665,11.666666626930237,6.535954,11.666666626930237,7.9166665C11.666666626930237,9.297374699999999,12.785957626930237,10.4166665,14.166666626930237,10.4166665C15.547375626930236,10.4166665,16.666666626930237,9.297374699999999,16.666666626930237,7.9166665C16.666666626930237,6.535954,15.547375626930236,5.4166665,14.166666626930237,5.4166665C14.166666626930237,5.4166665,14.166666626930237,5.4166665,14.166666626930237,5.4166665ZM1.6666666269302368,14.166666C1.6666666269302368,12.3257074,3.159050026930237,10.833333,4.999999926930236,10.833333C4.999999926930236,10.833333,9.999999626930236,10.833333,9.999999626930236,10.833333C11.840958626930236,10.833333,13.333332626930236,12.3257074,13.333332626930236,14.166666C13.333332626930236,14.166666,13.333332626930236,17.5,13.333332626930236,17.5C13.333332626930236,17.5,1.6666666269302368,17.5,1.6666666269302368,17.5C1.6666666269302368,17.5,1.6666666269302368,14.166666,1.6666666269302368,14.166666C1.6666666269302368,14.166666,1.6666666269302368,14.166666,1.6666666269302368,14.166666ZM4.999999926930236,12.5C4.079524826930237,12.5,3.333333226930237,13.246208,3.333333226930237,14.166666C3.333333226930237,14.166666,3.333333226930237,15.833333,3.333333226930237,15.833333C3.333333226930237,15.833333,11.666666626930237,15.833333,11.666666626930237,15.833333C11.666666626930237,15.833333,11.666666626930237,14.166666,11.666666626930237,14.166666C11.666666626930237,13.246208,10.920458426930237,12.5,9.999999626930236,12.5C9.999999626930236,12.5,4.999999926930236,12.5,4.999999926930236,12.5C4.999999926930236,12.5,4.999999926930236,12.5,4.999999926930236,12.5ZM18.333332626930236,14.166666C18.333332626930236,12.785958,17.214042626930237,11.666666,15.833332626930236,11.666666C15.833332626930236,11.666666,14.166666626930237,11.666666,14.166666626930237,11.666666C14.166666626930237,11.666666,14.166666626930237,13.333333,14.166666626930237,13.333333C14.166666626930237,13.333333,15.833332626930236,13.333333,15.833332626930236,13.333333C16.293582626930238,13.333333,16.666666626930237,13.706416,16.666666626930237,14.166666C16.666666626930237,14.166666,16.666666626930237,15,16.666666626930237,15C16.666666626930237,15,14.166666626930237,15,14.166666626930237,15C14.166666626930237,15,14.166666626930237,16.666666,14.166666626930237,16.666666C14.166666626930237,16.666666,18.333332626930236,16.666666,18.333332626930236,16.666666C18.333332626930236,16.666666,18.333332626930236,14.166666,18.333332626930236,14.166666C18.333332626930236,14.166666,18.333332626930236,14.166666,18.333332626930236,14.166666Z"
          fillRule="evenodd"
          fill={color}
          fillOpacity="1"
        />
      </g>
    </g>
  </svg>
);

export default IconFans;
