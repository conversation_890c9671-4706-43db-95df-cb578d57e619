// 会员中心相关类型定义

/** 通用 API 响应结构 */
export interface CommonResult<T = any> {
  /** 错误码，0 表示成功 */
  code: number;
  /** 返回数据 */
  data: T;
  /** 错误提示信息 */
  msg: string;
}

/** 分页结果结构 */
export interface PageResult<T = any> {
  /** 数据列表 */
  list: T[];
  /** 总数量 */
  total: number;
}

/** 会员类型枚举 */
export enum VipType {
  /** 半年会员 */
  HALF_YEAR = 1,
  /** 年度会员 */
  ANNUAL = 2
}

/** 支付状态枚举 */
export enum PayStatus {
  /** 未支付 */
  UNPAID = 0,
  /** 已支付 */
  PAIDED = 1
}

/** 支付类型枚举 */
export enum PayType {
  /** 支付宝 */
  ALIPAY = 1,
  /** 微信支付 */
  WECHAT = 2
}

/** 会员状态枚举 */
export enum VipStatus {
  /** 非会员 */
  NON_VIP = 0,
  /** 会员 */
  VIP = 1
}

/** 用户信息接口 */
export interface UserInfo {
  id?: string;
  nickname?: string;
  avatar?: string;
  isMember?: number;
  memberExpireTime?: string; // 格式: YYYY-MM-DD HH:mm:ss
  /** 扩展 */
  [key: string]: any;
}

/** 会员套餐信息接口 */
export interface VipPackage {
  /** 套餐类型 */
  type: VipType;
  /** 套餐名称 */
  name: string;
  /** 套餐价格（分） */
  price: number;
  /** 套餐描述 */
  description?: string;
  /** 是否推荐 */
  recommended?: boolean;
  /** 苹果内购产品ID */
  appleProductId?: string;
}

/** 会员权益信息接口 */
export interface VipBenefit {
  /** 权益ID */
  id: string;
  /** 权益名称 */
  name: string;
  /** 权益描述 */
  description: string;
  /** 权益图标 */
  icon?: string;
}

/** 会员订单记录接口 - 根据实际API返回结构定义 */
export interface VipOrderRecord {
  /** 编号 */
  id: number;
  /** 会员类型(1半年 2一年) */
  menberType: VipType;
  /** 用户编号 */
  userId: number;
  /** 价格，单位：分 */
  price: number;
  /** 是否已支付：[0:未支付 1:已经支付过] */
  payStatus: PayStatus;
  /** 支付订单编号 */
  payOrderId: number;
  /** 支付成功的支付渠道 */
  payChannelCode: string;
  /** 订单支付时间 */
  payTime: string;
  /** 退款订单编号 */
  payRefundId: number;
  /** 退款金额，单位：分 */
  refundPrice: number;
  /** 退款时间 */
  refundTime: string;
  /** 渠道 package 信息 */
  transferChannelPackageInfo: string;
  /** 创建时间 */
  createTime: string;
}

/** 分页信息接口 */
export interface PaginationInfo {
  /** 当前页码 */
  page: number;
  /** 每页条数 */
  limit: number;
  /** 是否还有更多数据 */
  hasMore: boolean;
  /** 是否正在加载 */
  isLoading: boolean;
}

/** 获取会员订单列表请求接口 */
export interface GetVipOrderListRequest {
  /** 页码 */
  pageNo?: number;
  /** 每页条数 */
  pageSize?: number;
  /** 用户ID */
  userId?: number;
  /** 支付状态 */
  payStatus?: PayStatus;
}

/** getVipOrderList API 响应类型 */
export type GetVipOrderListResponse = CommonResult<PageResult<VipOrderRecord>>;

/** 支付订单信息接口 */
export interface PayOrderInfo {
  /** 应用ID */
  appId: number;
  /** 渠道ID */
  channelId: number;
  /** 渠道编码 */
  channelCode: string;
  /** 商户订单ID */
  merchantOrderId: number;
  /** 订单主题 */
  subject: string;
  /** 订单内容 */
  body: string;
  /** 通知URL */
  notifyUrl: string;
  /** 价格（分） */
  price: number;
  /** 渠道费率 */
  channelFeeRate: number;
  /** 渠道费用 */
  channelFeePrice: number;
  /** 订单状态 */
  status: number;
  /** 用户IP */
  userIp: string;
  /** 过期时间 */
  expireTime: string;
  /** 成功时间 */
  successTime: string;
  /** 扩展ID */
  extensionId: number;
  /** 订单号 */
  no: number;
  /** 退款价格 */
  refundPrice: number;
  /** 渠道用户ID */
  channelUserId: number;
  /** 渠道订单号 */
  channelOrderNo: number;
  /** 订单ID */
  id: number;
  /** 创建时间 */
  createTime: string;
}

/** 支付提交结果接口 */
export interface PaySubmitResult {
  /** 支付状态 */
  status: number;
  /** 展示模式 */
  displayMode: string;
  /** 展示内容 */
  displayContent: string;
}

/** 会员订单状态枚举 - 用于UI显示 */
export enum VipOrderStatus {
  /** 生效中 */
  ACTIVE = 'active',
  /** 已过期 */
  EXPIRED = 'expired',
  /** 已取消 */
  CANCELLED = 'cancelled'
}

/** 扩展的订单记录接口 - 用于UI渲染 */
export interface VipOrderRecordForUI extends VipOrderRecord {
  /** UI显示状态 - 根据支付状态和时间计算 */
  status: VipOrderStatus;
  /** 套餐名称 - 根据会员类型生成 */
  package_name: string;
}
