// vipCenter调试器工具函数 - 使用通用调试器
import {
  VipType,
  PayType,
  VipOrderRecord,
  VipOrderRecordForUI,
  VipOrderStatus,
  PayStatus,
  VipPackage
} from './types';

// VipCenter特定的调试场景
export enum VipCenterDebugScenario {
  NON_VIP_USER = 'non_vip',          // 非会员用户
  VIP_USER = 'vip_user',             // 会员用户
  EXPIRED_VIP = 'expired_vip',       // 过期会员
  PAYMENT_SUCCESS = 'pay_success',    // 支付成功
  PAYMENT_FAILED = 'pay_failed',     // 支付失败
  EMPTY_ORDERS = 'empty_orders',     // 空订单列表
  FULL_ORDERS = 'full_orders'        // 完整订单列表
  // ... 其他场景
}

/**
 * 获取会员类型文本
 */
export const getVipTypeText = (type: VipType): string => {
  switch (type) {
    case VipType.HALF_YEAR:
      return '半年会员';
    case VipType.ANNUAL:
      return '年度会员';
    default:
      return '未知类型';
  }
};

/**
 * 获取支付类型文本
 */
export const getPayTypeText = (type: PayType): string => {
  switch (type) {
    case PayType.ALIPAY:
      return '支付宝';
    case PayType.WECHAT:
      return '微信支付';
    default:
      return '未知支付方式';
  }
};

/**
 * 格式化价格显示
 */
export const formatPrice = (price: number): string => {
  return `¥${(price / 100).toFixed(2)}`;
};

/**
 * 格式化会员到期时间
 * 专门优化处理毫秒级时间戳，同时兼容其他格式
 * @param expireTime 到期时间（主要为毫秒级时间戳，也支持秒级时间戳或字符串）
 * @param format 输出格式，默认为 'YYYY-MM-DD'
 * @returns 格式化后的时间字符串
 */
export const formatMemberExpireTime = (
  expireTime: number | string | null | undefined,
  format: 'YYYY-MM-DD' | 'YYYY-MM-DD HH:mm:ss' = 'YYYY-MM-DD'
): string => {
  if (!expireTime) return '';

  try {
    let date: Date;
    let timestamp: number;

    // 判断数据类型并处理
    if (typeof expireTime === 'number') {
      // 数字类型时间戳处理
      timestamp = expireTime;

      // 优化时间戳识别逻辑
      if (timestamp > 1000000000000) {
        // 13位数字，毫秒级时间戳（主要场景）
        date = new Date(timestamp);
        console.log(`[时间戳] 毫秒级时间戳: ${timestamp} -> ${date.toISOString()}`);
      } else if (timestamp > 1000000000) {
        // 10位数字，秒级时间戳，转换为毫秒
        timestamp = timestamp * 1000;
        date = new Date(timestamp);
        console.log(`[时间戳] 秒级时间戳: ${expireTime} -> 毫秒: ${timestamp} -> ${date.toISOString()}`);
      } else {
        // 小于10位，可能是无效的时间戳
        console.warn('可能无效的时间戳:', expireTime);
        return '';
      }
    } else if (typeof expireTime === 'string') {
      // 字符串格式处理
      if (expireTime.includes(' ') || expireTime.includes('-') || expireTime.includes('/')) {
        // 日期时间字符串格式
        date = new Date(expireTime);
        console.log(`[时间戳] 日期字符串: ${expireTime} -> ${date.toISOString()}`);
      } else {
        // 纯数字字符串，当作时间戳处理
        timestamp = parseInt(expireTime);
        if (isNaN(timestamp)) {
          console.warn('无效的数字字符串:', expireTime);
          return '';
        }

        if (timestamp > 1000000000000) {
          // 毫秒级时间戳字符串
          date = new Date(timestamp);
          console.log(`[时间戳] 毫秒级时间戳字符串: ${expireTime} -> ${date.toISOString()}`);
        } else if (timestamp > 1000000000) {
          // 秒级时间戳字符串
          timestamp = timestamp * 1000;
          date = new Date(timestamp);
          console.log(`[时间戳] 秒级时间戳字符串: ${expireTime} -> 毫秒: ${timestamp} -> ${date.toISOString()}`);
        } else {
          console.warn('可能无效的时间戳字符串:', expireTime);
          return '';
        }
      }
    } else {
      console.warn('未知的时间格式:', expireTime, typeof expireTime);
      return '';
    }

    // 验证日期是否有效
    if (isNaN(date.getTime())) {
      console.warn('无效的日期对象:', expireTime, date);
      return '';
    }

    // 检查日期是否在合理范围内（1970-2100年）
    const year = date.getFullYear();
    if (year < 1970 || year > 2100) {
      console.warn('日期超出合理范围:', expireTime, date, `年份: ${year}`);
      return '';
    }

    // 格式化输出
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    if (format === 'YYYY-MM-DD') {
      const formatted = `${year}-${month}-${day}`;
      console.log(`[时间戳] 格式化完成: ${expireTime} -> ${formatted}`);
      return formatted;
    } else {
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      const formatted = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      console.log(`[时间戳] 格式化完成: ${expireTime} -> ${formatted}`);
      return formatted;
    }
  } catch (error) {
    console.error('formatMemberExpireTime 处理错误:', error, '输入值:', expireTime);
    return '';
  }
};

/**
 * 检查会员是否有效（未过期）
 * 专门优化处理毫秒级时间戳
 * @param expireTime 到期时间（主要为毫秒级时间戳）
 * @returns 是否有效
 */
export const isMemberValid = (expireTime: number | string | null | undefined): boolean => {
  if (!expireTime) return false;

  try {
    let timestamp: number;
    const currentTime = Date.now(); // 当前时间的毫秒时间戳

    if (typeof expireTime === 'number') {
      // 数字类型时间戳处理
      if (expireTime > 1000000000000) {
        // 13位数字，毫秒级时间戳（主要场景）
        timestamp = expireTime;
        console.log(`[会员检查] 毫秒级时间戳: ${timestamp}, 当前时间: ${currentTime}, 有效: ${timestamp > currentTime}`);
      } else if (expireTime > 1000000000) {
        // 10位数字，秒级时间戳，转换为毫秒
        timestamp = expireTime * 1000;
        console.log(`[会员检查] 秒级时间戳: ${expireTime} -> 毫秒: ${timestamp}, 当前时间: ${currentTime}, 有效: ${timestamp > currentTime}`);
      } else {
        // 小于10位，可能是无效的时间戳
        console.warn('[会员检查] 可能无效的时间戳:', expireTime);
        return false;
      }
    } else if (typeof expireTime === 'string') {
      // 字符串格式处理
      if (expireTime.includes(' ') || expireTime.includes('-') || expireTime.includes('/')) {
        // 日期时间字符串格式
        const date = new Date(expireTime);
        if (isNaN(date.getTime())) return false;
        timestamp = date.getTime();
        console.log(`[会员检查] 日期字符串: ${expireTime} -> 毫秒: ${timestamp}, 当前时间: ${currentTime}, 有效: ${timestamp > currentTime}`);
      } else {
        // 纯数字字符串，当作时间戳处理
        const parsedTimestamp = parseInt(expireTime);
        if (isNaN(parsedTimestamp)) return false;

        if (parsedTimestamp > 1000000000000) {
          // 毫秒级时间戳字符串
          timestamp = parsedTimestamp;
          console.log(`[会员检查] 毫秒级时间戳字符串: ${expireTime} -> ${timestamp}, 当前时间: ${currentTime}, 有效: ${timestamp > currentTime}`);
        } else if (parsedTimestamp > 1000000000) {
          // 秒级时间戳字符串
          timestamp = parsedTimestamp * 1000;
          console.log(`[会员检查] 秒级时间戳字符串: ${expireTime} -> 毫秒: ${timestamp}, 当前时间: ${currentTime}, 有效: ${timestamp > currentTime}`);
        } else {
          console.warn('[会员检查] 可能无效的时间戳字符串:', expireTime);
          return false;
        }
      }
    } else {
      console.warn('[会员检查] 未知的时间格式:', expireTime, typeof expireTime);
      return false;
    }

    // 检查时间戳是否在合理范围内
    const year = new Date(timestamp).getFullYear();
    if (year < 1970 || year > 2100) {
      console.warn('[会员检查] 时间戳超出合理范围:', expireTime, new Date(timestamp), `年份: ${year}`);
      return false;
    }

    // 检查是否未过期（到期时间 > 当前时间）
    const isValid = timestamp > currentTime;

    // 输出详细的比较信息
    const expireDate = new Date(timestamp).toLocaleString('zh-CN');
    const currentDate = new Date(currentTime).toLocaleString('zh-CN');
    console.log(`[会员检查] 到期时间: ${expireDate}, 当前时间: ${currentDate}, 会员有效: ${isValid}`);

    return isValid;
  } catch (error) {
    console.error('[会员检查] 处理错误:', error, '输入值:', expireTime);
    return false;
  }
};

/**
 * 格式化时间显示
 */
export const formatTime = (timeStr: string): string => {
  if (!timeStr) return '';

  try {
    const date = new Date(timeStr);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  } catch (error) {
    return timeStr;
  }
};

/**
 * 计算会员到期时间
 */
export const calculateEndTime = (createTime: string, memberType: VipType): string => {
  if (!createTime) return '';

  try {
    const startDate = new Date(createTime);
    const endDate = new Date(startDate);

    if (memberType === VipType.HALF_YEAR) {
      endDate.setMonth(endDate.getMonth() + 6);
    } else if (memberType === VipType.ANNUAL) {
      endDate.setFullYear(endDate.getFullYear() + 1);
    }

    return endDate.toISOString().slice(0, 19).replace('T', ' ');
  } catch (error) {
    return '';
  }
};

/**
 * 根据订单状态计算UI显示状态
 */
export const calculateOrderStatus = (order: VipOrderRecord): VipOrderStatus => {
  // 如果未支付
  if (order.payStatus === PayStatus.UNPAID) {
    return VipOrderStatus.CANCELLED;
  }

  // 如果有退款
  if (order.refundPrice > 0) {
    return VipOrderStatus.CANCELLED;
  }

  // 如果已支付，检查是否过期
  const endTime = calculateEndTime(order.createTime, order.menberType);
  if (endTime && new Date(endTime) < new Date()) {
    return VipOrderStatus.EXPIRED;
  }

  return VipOrderStatus.ACTIVE;
};

/**
 * 获取套餐名称
 */
export const getPackageName = (memberType: VipType): string => {
  switch (memberType) {
    case VipType.HALF_YEAR:
      return '半年会员';
    case VipType.ANNUAL:
      return '年度会员';
    default:
      return '会员';
  }
};

/**
 * 将API返回的订单数据转换为UI需要的格式
 */
export const transformOrderForUI = (order: VipOrderRecord): VipOrderRecordForUI => {
  return {
    ...order,
    status: calculateOrderStatus(order),
    package_name: getPackageName(order.menberType)
  };
};

/**
 * 批量转换订单数据
 */
export const transformOrdersForUI = (orders: VipOrderRecord[]): VipOrderRecordForUI[] => {
  return orders.map(transformOrderForUI);
};
