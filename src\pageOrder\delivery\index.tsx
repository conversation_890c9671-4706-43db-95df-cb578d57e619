import { View, Text, Input } from "@tarojs/components";
import { useLoad, useDidShow } from "@tarojs/taro";
import "./index.less";
import {
  Image,
  Loading,
  Toast,
  Dialog,
  Checkbox,
  Stepper,
} from "@arco-design/mobile-react";
import { useState, useEffect, useRef } from "react";
import Taro from "@tarojs/taro";
import {
  getUserOrderDetails,
  deliveryOrder,
  uploadFile,
  updateOrderRemark
} from "@/utils/api/common/common_user";
import YkNavBar from "@/components/ykNavBar/index";
import { toast } from "@/utils/yk-common";
import BottomPopup from "@/components/BottomPopup";
import PermissionPopup from "@/components/PermissionPopup";
import { AuthTypes } from "@/utils/config/authTypes";
import { usePermission } from "@/hooks/usePermission";
import { IconRight } from "@arco-iconbox/react-yk-arco";
import { IconPayment } from "@/components/YkIcons";
import areaDataType from '@/constants/area.json'; // 引入公有目录下的完整地区数据

// 类型定义
interface DeliveryCompany {
  id: number;
  name: string;
}

interface OrderGoods {
  id: number;
  dynamicId: number;
  dynamicName: string;
  price: number;
  dynamicPictures: string;
  totalPrice: number;
  count: number;
  orderDetails: Array<{
    id: number;
    orderId: number;
    cartDetailId: number;
    quantity: number;
    originalQuantity?: number; // 保存原始数量作为最大限制
    isDelivery: number;
    properties: string | null;
    trackingNumber: string | null;
    expressCompany: string | null;
    quantityShipped: number | null;
    isSelected?: boolean;
  }>;
  isCheck?: boolean;
}

interface OrderDetails {
  id: number;
  receiverName: string;
  receiverMobile: string;
  receiverAreaId: string;
  receiverDetailAddress: string;
  deliveryName: string;
  orderDynamics: OrderGoods[];
  totalPrice: number;
  isAllCheck?: boolean;
  userName: string;
  userAvatar: string;
  deliveryPrice?: number;
  remark?: string;
  pictureRemark?: string;
  sellerRemark?: string;
  finishTime?: string;
  sellerPictureRemark?: string;
}

export default function DeliveryPage() {
  const [orderDetails, setOrderDetails] = useState<OrderDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [orderId, setOrderId] = useState("");
  const [deliveryType, setDeliveryType] = useState(""); // 1: 部分发货, 2: 全部发货

  // 快递信息
  const [expressNumber, setExpressNumber] = useState("");
  const [selectedCompany, setSelectedCompany] = useState<DeliveryCompany | null>(null);

  // 选中的商品ID列表
  const [selectedGoodsIds, setSelectedGoodsIds] = useState<number[]>([]);

  // 每个SKU当前选择的发货数量
  const [skuQuantities, setSkuQuantities] = useState<{[key: string]: number}>({});

  // 弹窗状态
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");

  // 上传的图片列表
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  const [areaName, setAreaName] = useState<string>("");


  // 图片选择弹窗状态
  const [isPopupVisible, setPopupVisible] = useState(false);
  const chooseImageRef = useRef<"album" | "camera">("album");

      // 根据地区 ID 查找对应的名称
const findAreaNameById = (areaId: string): string => {
  if(areaName){
    return areaName;
  }
  for (const province of areaDataType) {
    if (province.id === areaId) {
      return province.name;
    }
    for (const city of province.children || []) {
      if (city.id === areaId) {
        return city.name;
      }

      for (const area of city.children || []) {
        if (area.id === areaId) {
          setAreaName(`${province.name} ${city.name} ${area.name}`);
          return `${province.name} ${city.name} ${area.name}`;
        }
      }
    }
  }
  return '';
};

   // 保存我的备注到服务器
   const saveSellerRemark = async (customImages?: string[]) => {
    const imagesToSave = customImages || uploadedImages;
    if (!orderDetails?.id) return;

    try {
      const response = await updateOrderRemark({
        id: orderDetails.id,
        sellerRemark: orderDetails.sellerRemark || '',
        sellerPictureRemark: imagesToSave.join(',') || '',
      });

      if (response && (response as any).code === 0) {
        toast("success", {
          content: "备注保存成功",
          duration: 2000,
        });
      } else {
        toast("error", {
          content: "备注保存失败",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error('保存备注失败:', error);
      toast("error", {
        content: "备注保存失败",
        duration: 2000,
      });
    }
  };

      // 选择图片的方法
      const chooseImage = (sourceType: "album" | "camera") => {
        const count = 9 - uploadedImages.length;
  
        Taro.chooseImage({
          count,
          sizeType: ["original", "compressed"],
          sourceType: [sourceType],
          success: async (res) => {
            console.log("选择的图片:", res.tempFilePaths);
    
            // 显示上传进度
            Taro.showLoading({
              title: "上传中...",
            });
    
            try {
              // 上传所有选择的图片
              const uploadPromises = res.tempFilePaths.map(async (imagePath) => {
                const uploadRes = await uploadFile([imagePath]);
                console.log("上传结果:", uploadRes);
    
                // 根据实际返回结构提取URL
                let url = "";
                if (uploadRes && uploadRes.code === 0) {
                  if (typeof uploadRes.data === "string") {
                    url = uploadRes.data;
                  } else if (uploadRes.data && uploadRes.data.url) {
                    url = uploadRes.data.url;
                  } else if (Array.isArray(uploadRes.data) && uploadRes.data[0]) {
                    url = uploadRes.data[0];
                  }
                }
    
                if (!url) {
                  throw new Error("上传失败，未获取到图片URL");
                }
    
                return url;
              });
    
              const uploadedUrls = await Promise.all(uploadPromises);
              console.log("所有图片上传完成:", uploadedUrls);
    
              // 更新上传的图片列表
              setUploadedImages((prev) => {
                const newImages = [...prev, ...uploadedUrls];
                // 图片上传完成后自动保存备注，传递新的图片数组
                setTimeout(() => {
                  saveSellerRemark(newImages);
                }, 100);
                return newImages;
              });
    
              Taro.hideLoading();
              toast("success", {
                content: "上传成功",
                duration: 2000,
              });
            } catch (error) {
              console.error("上传图片失败:", error);
              Taro.hideLoading();
              toast("error", {
                content: "上传失败，请重试",
                duration: 2000,
              });
            }
          },
          fail: (error) => {
            console.error("选择图片失败:", error);
            toast("error", {
              content: "选择图片失败",
              duration: 2000,
            });
          },
        });
  
        // 关闭弹窗
        setPopupVisible(false);
      };

  // 权限获得后的回调
  const customWebPermissonConsent = () => {
    if (chooseImageRef.current === "camera") {
      chooseImage("camera");
    } else {
      chooseImage("album");
    }
  };

  // 使用全局权限管理
  const { initPermissions, hasPermission, requestPermission, permissionPopupProps ,platformRef} =
    usePermission(customWebPermissonConsent);
  // 辅助函数：计算选中SKU的总数量
  const getSelectedSkuCount = () => {
    console.log('orderDetails:', orderDetails);
    const result = orderDetails?.orderDynamics.reduce((total, item) => {
      console.log(`商品 ${item.id} isCheck:`, item.isCheck);
      // 不管商品是否选中，只要SKU选中就计算数量
      const skuCount = item.orderDetails.reduce((skuTotal, detail, detailIdx) => {
        const key = `${item.id}-${detailIdx}`;
        const selectedQuantity = skuQuantities[key] || (detail.quantity - (detail.quantityShipped || 0));
        console.log(`  SKU isSelected: ${detail.isSelected}, selectedQuantity: ${selectedQuantity}`);
        return detail.isSelected ? skuTotal + selectedQuantity : skuTotal;
      }, 0);
      console.log(`  商品 ${item.id} SKU总数量:`, skuCount);
      return total + skuCount;
    }, 0) || 0;
    console.log('最终计算结果:', result);
    return result;
  };

  // 辅助函数：更新选择状态并检查联动
  const updateSelectionState = (updatedGoods: any[]) => {
    // 更新选中的商品ID列表
    const newSelectedGoodsIds = updatedGoods
      .filter(item => item.isCheck)
      .map(item => item.id);

    setSelectedGoodsIds(newSelectedGoodsIds);

    // 检查是否所有商品都被选中
    const allGoodsSelected = updatedGoods.every(item => item.isCheck);

    setOrderDetails(prev => prev ? {
      ...prev,
      orderDynamics: updatedGoods,
      isAllCheck: allGoodsSelected,
    } : null);
  };


  // 选择留言图片
    const handleChooseRemarkImage = () => {
      const count = 9 - uploadedImages.length;
      if (platformRef.current === "Android") {
        window.setPhotoNum?.setPhotoNum(count);
      }
      if(platformRef.current === "HM"){
        window.harmony.setPhotoNum(count);
      }
      if (count <= 0) {
        toast("error", {
          content: "最多只能上传9张图片",
          duration: 2000,
        });
        return;
      }

      // 显示选择图片来源的弹窗
      setPopupVisible(true);
    };

    // 关闭选择图片方式弹窗
    const handleClose = () => {
      setPopupVisible(false);
    };

    // 处理用户选择（拍照/相册）
    const handleConfirm = (index: number) => {
      if (index === 0) {
        // 设置图片选择类型为拍照
        chooseImageRef.current = "camera";
        // 在 H5 环境下直接执行，在原生环境下检查权限
        if (platformRef.current === "HM") {
          chooseImage("camera");
        } else {
          // 请求相机权限
          if (!hasPermission(AuthTypes.CAMERA)) {
            // 如果没有权限，请求权限
            requestPermission(AuthTypes.CAMERA);
            return;
          }
          chooseImage("camera");
        }
      } else if (index === 1) {
        // 设置图片选择类型为相册
        chooseImageRef.current = "album";
        // 在 H5 环境下直接执行，在原生环境下检查权限
        if (platformRef.current === "HM") {
          chooseImage("album");
        } else {
          // 请求相册权限
          if (!hasPermission(AuthTypes.GALLERY_PHOTO)) {
            // 如果没有权限，请求权限
            requestPermission(AuthTypes.GALLERY_PHOTO);
            return;
          }
          chooseImage("album");
        }
      }
    };


  
    // 删除留言图片
    const handleDeleteRemarkImage = (index: number) => {
      const newImages = [...uploadedImages];
      newImages.splice(index, 1);
      setUploadedImages(newImages);

      // 删除图片后自动保存备注，传递新的图片数组
      setTimeout(() => {
        saveSellerRemark(newImages);
      }, 100);
    };
  
    // 处理留言编辑
    const handleRemarkChange = (value: string) => {
      setOrderDetails({ ...orderDetails, sellerRemark: value });
    };
  
   

  useLoad((options) => {
    console.log("页面参数:", options);
    if (options.orderId) {
      setOrderId(options.orderId);
    }
    if (options.type) {
      setDeliveryType(options.type);
    }
  });

  useEffect(() => {
    // 初始化权限管理
    const cleanup = initPermissions();

    return () => {
      cleanup && cleanup();
    };
  }, []);

  useEffect(() => {
    if (orderId) {
      fetchOrderDetails();
    }
  }, [orderId]);

  // 监听页面显示事件，检查是否有选择的快递公司
  useDidShow(() => {
    const savedCompany = Taro.getStorageSync('selectedDeliveryCompany');
    if (savedCompany) {
      console.log('从 localStorage 读取到快递公司:', savedCompany);
      setSelectedCompany(savedCompany);
      // 读取后清除 localStorage，避免影响其他页面
      Taro.removeStorageSync('selectedDeliveryCompany');
    }
  });

  // 获取订单详情
  const fetchOrderDetails = async () => {
    try {
      setLoading(true);
      const response = await getUserOrderDetails({ id: orderId });
      
      if (response && response.code === 0) {
        const orderData = response.data;
        
        // 初始化商品选择状态，并保存原始数量
        const goodsWithSelection = orderData.orderDynamics.map((item: OrderGoods) => {
          const isGoodsSelected = deliveryType === "2"; // 全部发货时默认全选
          return {
            ...item,
            isCheck: isGoodsSelected,
            orderDetails: item.orderDetails.map(detail => ({
              ...detail,
              isSelected: isGoodsSelected, // SKU选择状态与商品选择状态保持一致
            })),
          };
        });
        
        setOrderDetails({
          ...orderData,
          orderDynamics: goodsWithSelection,
          isAllCheck: deliveryType === "2",
        });

        // 初始化SKU数量状态
        const initialSkuQuantities: {[key: string]: number} = {};
        goodsWithSelection.forEach(item => {
          item.orderDetails.forEach((detail, detailIdx) => {
            const key = `${item.id}-${detailIdx}`;
            const availableQuantity = detail.quantity - (detail.quantityShipped || 0);
            if (availableQuantity > 0) {
              initialSkuQuantities[key] = availableQuantity;
            }
          });
        });
        setSkuQuantities(initialSkuQuantities);

        // 初始化已上传的图片列表
        if (orderData.sellerPictureRemark) {
          const existingImages = orderData.sellerPictureRemark
            .split(',')
            .filter(img => img.trim() !== ''); // 过滤空字符串
          setUploadedImages(existingImages);
        }

        // 如果是全部发货，默认选中所有商品
        if (deliveryType === "2") {
          setSelectedGoodsIds(goodsWithSelection.map((item: OrderGoods) => item.id));
        }
      } else {
        showToastMessage(response.msg || "获取订单详情失败");
      }
    } catch (error) {
      console.error("获取订单详情失败:", error);
      showToastMessage("获取订单详情失败");
    } finally {
      setLoading(false);
    }
  };

  // 显示提示消息
  const showToastMessage = (message: string) => {
    setToastMessage(message);
    setShowToast(true);
    setTimeout(() => setShowToast(false), 2000);
  };

  // 处理快递单号输入
  const handleExpressNumberChange = (value: string) => {
    // 只允许字母和数字
    const filteredValue = value.replace(/[^a-zA-Z0-9]/g, "");
    setExpressNumber(filteredValue);
  };

  // 选择快递公司
  const handleSelectCompany = () => {
    Taro.navigateTo({
      url: "/pageOrder/deliveryCompany/index",
    });
  };



  // 处理商品选择
  const handleGoodsSelect = (goodsId: number) => {
    if (!orderDetails) return;

    const newSelectedGoods = selectedGoodsIds.includes(goodsId)
      ? selectedGoodsIds.filter(id => id !== goodsId)
      : [...selectedGoodsIds, goodsId];

    // 更新商品选择状态，智能处理SKU选中状态
    const updatedGoods = orderDetails.orderDynamics.map(item => {
      if (item.id === goodsId) {
        // 只处理当前操作的商品
        const isSelected = newSelectedGoods.includes(item.id);
        return {
          ...item,
          isCheck: isSelected,
          // 选中商品时：选中所有SKU；取消选中商品时：取消选中所有SKU
          orderDetails: item.orderDetails.map(detail => ({
            ...detail,
            isSelected: isSelected,
          })),
        };
      } else {
        // 其他商品保持原有状态，不改变SKU选择状态
        return item;
      }
    });

    // 使用辅助函数更新状态
    updateSelectionState(updatedGoods);
  };

  // 全选/取消全选
  const handleSelectAll = () => {
    if (!orderDetails) return;

    const newIsAllSelected = !orderDetails.isAllCheck;

    // 更新商品选择状态，同时更新所有SKU状态
    const updatedGoods = orderDetails.orderDynamics.map(item => ({
      ...item,
      isCheck: newIsAllSelected,
      // 全选/取消全选时，同时更新所有SKU的选中状态
      orderDetails: item.orderDetails.map(detail => ({
        ...detail,
        isSelected: newIsAllSelected,
      })),
    }));

    // 使用辅助函数更新状态
    updateSelectionState(updatedGoods);
  };

  // 处理SKU选择
  const handleSkuSelect = (goodsId: number, detailIdx: number) => {
    if (!orderDetails) return;

    const updatedGoods = orderDetails.orderDynamics.map(item => {
      if (item.id === goodsId) {
        const updatedDetails = [...item.orderDetails];
        updatedDetails[detailIdx] = {
          ...updatedDetails[detailIdx],
          isSelected: !updatedDetails[detailIdx].isSelected,
        };

        // 检查该商品的所有SKU是否都被选中
        const allSkuSelected = updatedDetails.every(detail => detail.isSelected);

        return {
          ...item,
          orderDetails: updatedDetails,
          isCheck: allSkuSelected, // 如果所有SKU都选中，商品也选中
        };
      }
      return item;
    });

    // 使用辅助函数更新状态
    updateSelectionState(updatedGoods);
  };

  // 处理SKU数量变更
  const handleQuantityChange = (goodsId: number, detailIdx: number, quantity: number) => {
    if (!orderDetails) return;

    // 获取商品信息
    const goods = orderDetails.orderDynamics.find(item => item.id === goodsId);
    if (!goods || !goods.orderDetails[detailIdx]) return;

    const detail = goods.orderDetails[detailIdx];
    const key = `${goodsId}-${detailIdx}`;

    // 获取该SKU的最大可发货数量
    const shippedQuantity = detail.quantityShipped || 0;
    const maxQuantity = detail.quantity - shippedQuantity;

    // 限制数量范围：最少1件，最多不超过该SKU的可发货数量
    const newQuantity = Math.max(1, Math.min(quantity, maxQuantity));

    // 如果超过该SKU的可发货数量，显示提示
    if (quantity > maxQuantity) {
      showToastMessage(`该规格最多只能选择${maxQuantity}件`);
      return;
    }

    // 更新SKU数量状态
    setSkuQuantities(prev => ({
      ...prev,
      [key]: newQuantity
    }));
  };

    // 格式化时间
    const formatTime = (timestamp: number) => {
      if (!timestamp) return "--";
      const date = new Date(timestamp);
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    };

  // 选择发货类型
  const handleSelectDeliveryType = (type: string) => {
    setDeliveryType(type);
    setShowDeliveryOptions(false);

    // 根据类型跳转到对应页面
    if (type === "1") {
      // 部分发货 - 当前页面
      return;
    } else if (type === "2") {
      // 全部发货 - 跳转到发货页面
      Taro.navigateTo({
        url: `/pageOrder/delivery/index?orderId=${orderId}&type=2`
      });
    }
  };

  // 发货确认
  const handleDeliveryConfirm = () => {
    if (!expressNumber.trim()) {
      showToastMessage("请填写快递单号");
      return;
    }

    if (!selectedCompany) {
      showToastMessage("请选择快递公司");
      return;
    }

    // 计算选中SKU的总数量
    const selectedSkuCount = getSelectedSkuCount();
console.log('selectedSkuCount', selectedSkuCount);
    if (selectedSkuCount === 0) {
      showToastMessage("请选择要发货的商品");
      return;
    }

    // 显示确认对话框
    Dialog.confirm({
      title: "确认发货",
      children: `确认发货${selectedSkuCount}件商品吗？`,
      okText: "确定",
      cancelText: "取消",
      platform: "ios",
      onOk: executeDelivery,
    });
  };

  // 执行发货
  const executeDelivery = async () => {
    try {
      setLoading(true);
      console.log('selectedGoodsIds', selectedGoodsIds);

      const response = await deliveryOrder({
        orderId: orderId, // 订单ID
        trackingNumber: expressNumber,
        expressCompany: selectedCompany!.name,
        //选中的sku列表，过滤掉数量为0的商品
      //  orderDetailQuantityMap 格式为{"30": "45"}
        // orderDetailQuantityMap: orderDetails?.orderDynamics.flatMap(item =>
        //   item.orderDetails
        //     .map((detail, originalDetailIdx) => ({ detail, originalDetailIdx }))
        //     .filter(({ detail }) => detail.isSelected)
        //     .map(({ detail, originalDetailIdx }) => ({
        //       id: detail.id,
        //       quantityShipped: skuQuantities[`${item.id}-${originalDetailIdx}`] || (detail.quantity - (detail.quantityShipped || 0)),
        //     }))
        //     .filter(item => item.quantityShipped > 0) // 过滤掉数量为0的商品
        // ) || [],

        orderDetailQuantityMap:
  orderDetails?.orderDynamics
    .flatMap(item =>
      item.orderDetails
        .map((detail, originalDetailIdx) => ({ detail, originalDetailIdx }))
        .filter(({ detail }) => detail.isSelected)
        .map(({ detail, originalDetailIdx }) => ({
          id: detail.id,
          quantityShipped:
            skuQuantities[`${item.id}-${originalDetailIdx}`] ||
            (detail.quantity - (detail.quantityShipped || 0)),
        }))
        .filter(item => item.quantityShipped > 0)
    )
    .reduce((acc, { id, quantityShipped }) => {
      acc[id] = quantityShipped;
      return acc;
    }, {}) || {},

        // id: orderId,
      });
      
      if (response && response.code === 0) {
        showToastMessage("发货成功");
        setTimeout(() => {
          //刷新订单列表
          Taro.eventCenter.trigger('refreshOrderList');
          //刷新订单详情的物流数据
          Taro.eventCenter.trigger('refreshLogisticsData');
          Taro.navigateBack();
        }, 1500);
      } else {
        showToastMessage(response.msg || "发货失败");
      }
    } catch (error) {
      console.error("发货失败:", error);
      showToastMessage("发货失败");
    } finally {
      setLoading(false);
    }
  };

  // if (loading) {
  //   return (
  //     <View className="out">
  //       {platformRef.current !== "WX" &&<YkNavBar title="发货" />}
  //       <Loading />
  //     </View>
  //   );
  // }

  if (!orderDetails) {
    return (
      <View className="out">
        {platformRef.current !== "WX" &&<YkNavBar title="发货" />}
        <View className="error-message">
          {/* <Text>订单信息加载失败</Text> */}
        </View>
      </View>
    );
  }

  return (
    <View className="out">
      {platformRef.current !== "WX" &&<YkNavBar title="发货" />}
      
      {/* 收货地址信息 */}
      {orderDetails.delivery !== "自提" && orderDetails.delivery !== "其他" && orderDetails.delivery !== "跑腿" && (
          <View className="order-info-card-delivery">
          <View className="receive">
            <View className="receive-container">
            <Image bottomOverlap={null} className="receive-img" src={require("@/assets/images/common/location_icon.png")} />
            <Text className="receive-name">{orderDetails.receiverName}</Text>
            <Text className="receive-phone">{orderDetails.receiverMobile}</Text>
            </View>
            <Text className="receive-address">{findAreaNameById(orderDetails.receiverAreaId) + orderDetails.receiverDetailAddress}</Text>
          </View>
          
          <View className="line" />
          
          <View className="emsNum">
            <Text className="emsNum-title">快递单号</Text>
            <Input
              className="emsNum-input"
              placeholder="请填写快递单号"
              value={expressNumber}
              onInput={(e) => handleExpressNumberChange(e.detail.value)}
            />
          </View>
          
          <View className="line" />
          
          <View className="delivery">
            <Text className="delivery-title">快递公司</Text>
            <View className="delivery-company" onClick={handleSelectCompany}>
              <Text className={selectedCompany ? "delivery-company-input" : "delivery-company-inputHint"}>
                {selectedCompany ? selectedCompany.name : "请选择快递公司"}
              </Text>
              <IconRight className="delivery-company-img" />
              {/* <Image bottomOverlap={null} className="delivery-company-img" src={require("@/assets/images/common/arrow_right.png")} /> */}
            </View>
          </View>
          
          {orderDetails.deliveryPrice && parseFloat(orderDetails.deliveryPrice.toString()) > 0 && (
            <>
              <View className="line-del" />
              <View className="freight-del">
                <Text className="freight-del-title">运费</Text>
                <Text className="freight-del-text">￥{(orderDetails.deliveryPrice/100 || 0).toFixed(2)}</Text>
              </View>
            </>
          )}
        </View>
      )}

     

      {/* 商品列表 - 仅在部分发货时显示 */}
      {deliveryType === "1" && (
        <>
          {/* 商品列表 - 参考购物车样式 */}
          <View className="delivery-list">
            <View className="delivery-shop">
              {/* 商家头部 - 参考购物车样式，右侧添加快递方式和收款时间 */}
              <View className="delivery-shop-header">
                <Checkbox
                  checked={orderDetails.isAllCheck}
                  onChange={handleSelectAll}
                  className="delivery-checkbox"
                  value=""
                />
                <View className="delivery-shop-info">
                  <View className="delivery-shop-avatar-wrapper">
                    <Image
                      className="delivery-shop-avatar"
                      src={orderDetails.userAvatar || require("@/assets/images/common/default_head.png")}
                    />
                  </View>
                  <View className="delivery-shop-name-group">
                    <Text className="delivery-shop-name">{orderDetails.userName || "商家"}</Text>
                    <IconPayment className="delivery-shop-wx-icon" />
                    {/* <Image bottomOverlap={null} className="delivery-shop-wx-icon" src={require('@/assets/images/common/wx_pay.png')} /> */}
                  </View>
                </View>
                <View className="delivery-shop-right">
                  <Text className="delivery-express-info">{orderDetails.deliveryName}</Text>
                  <Text className="delivery-time-info">{`${formatTime(orderDetails.finishTime)} 收款`}</Text>
                </View>
              </View>

              {/* 商品列表 */}
              <View className="delivery-shop-products">
                {orderDetails.orderDynamics.map((item) => {
                  // 检查是否有可发货的SKU
                  const hasAvailableSku = item.orderDetails.some(detail =>
                    (detail.quantity - (detail.quantityShipped || 0)) > 0
                  );

                  // 如果没有可发货的SKU，不显示这个商品
                  if (!hasAvailableSku) return null;

                  return (
                    <View className="delivery-product-card" key={item.id}>
                      <View className="delivery-product-main">
                        <Checkbox
                          checked={item.isCheck}
                          onChange={() => handleGoodsSelect(item.id)}
                          className="delivery-checkbox"
                          value=""
                        />
                        <View className="delivery-product-img-wrapper">
                          <Image className="delivery-product-img" src={item.dynamicPictures} />
                        </View>
                        <View className="delivery-product-info">
                          <Text className="delivery-product-title">{item.dynamicName}</Text>
                          <View className="delivery-product-meta">
                            <Text className="delivery-product-price">￥{(item.price/100 || 0).toFixed(2)}</Text>
                          </View>
                        </View>
                      </View>

                      {/* SKU列表 */}
                      {item.orderDetails && item.orderDetails.map((detail, detailIdx) => {
                        const availableQuantity = detail.quantity - (detail.quantityShipped || 0);
                        if (availableQuantity <= 0) return null;

                        return (
                          <View key={detailIdx} className="delivery-product-sku">
                            <View className="delivery-product-sku-main">
                              <Checkbox
                                checked={detail.isSelected}
                                onChange={() => handleSkuSelect(item.id, detailIdx)}
                                className="delivery-checkbox"
                                value=""
                              />
                              <View className="delivery-product-sku-info">
                                <Text className="delivery-product-sku-text">
                                  {(() => {
                                    try {
                                      const props = JSON.parse(detail.properties || "{}");
                                      return `${props.colors || ""} ${props.specifications || ""}`.trim();
                                    } catch (e) {
                                      return detail.properties; // 如果解析失败，就原样显示
                                    }
                                  })()}
                                </Text>
                              </View>
                              <View className="delivery-product-sku-controls">
                                <Stepper
                                  defaultValue={skuQuantities[`${item.id}-${detailIdx}`] || availableQuantity}
                                  min={1}
                                  max={availableQuantity}
                                  step={1}
                                  onChange={(val) => handleQuantityChange(item.id, detailIdx, val || 1)}
                                />
                              </View>
                            </View>
                          </View>
                        );
                      })}
                    </View>
                  );
                })}
              </View>
            </View>
          </View>
        </>
      )}


       {/* 买家留言 - 参考 sellOrder details 样式 */}
       {(orderDetails.pictureRemark || orderDetails.remark) && (
        <View className="order-remark">
          <View className="contact-info-row">
            <Text className="contact-label">买家留言</Text>
            <View className="contact-info">
              <Text className="contact-value">
                {orderDetails.remark || "--"}
              </Text>
            </View>
          </View>

          {/* 买家留言图片 - 横向滚动 */}
          {orderDetails.pictureRemark && (
            <View className="remark-images-container">
              <View className="remark-images-scroll">
                {orderDetails.pictureRemark
                  .split(",")
                  .map((img: string, idx: number) => (
                    <Image
                      key={idx}
                      src={img.trim()}
                      className="remark-image"
                    />
                  ))}
              </View>
            </View>
          )}
        </View>
      )}

      {/* 我的备注 - 参考 sellOrder details 样式 */}
        <View className="contact-info-remark-row-del">
                  <View className="contact-info-remark-row1">
                    <Text className="contact-label">我的备注</Text>
                    <View className="order-remark-header-del">
                      <View className="order-remark-content">
                        {/* 显示所有商品的留言信息 */}
                        <Input
                          className="order-remark-input-sell"
                          placeholder="请输入留言信息"
                          value={orderDetails.sellerRemark}
                          onInput={(e) => handleRemarkChange(e.detail.value)}
                          onBlur={() => saveSellerRemark()}
                        />
                      </View>
                      <View
                        className="order-remark-camera"
                        onClick={handleChooseRemarkImage}
                      >
                        <Image
                          className="camera-icon"
                          src={require("@/assets/images/common/add_picture.png")}
                        />
                      </View>
                    </View>
                  </View>
        
                  {/* 图片上传区域 */}
                  {uploadedImages.length > 0 && (
                    <View className="uploaded-images-detail">
                      {uploadedImages.map((image, index) => (
                        <View key={index} className="uploaded-image-item">
                          <Image src={image} className="uploaded-image" />
                          <View
                            className="delete-image-btn"
                            onClick={() => handleDeleteRemarkImage(index)}
                          >
                            ×
                          </View>
                        </View>
                      ))}
                    </View>
                  )}
                </View>

      {/* 底部操作栏 */}
      <View className="footerbtn">
        {deliveryType === "1" && (
          <View className="footerbtn-left" onClick={handleSelectAll}>
            <Checkbox
              checked={orderDetails.isAllCheck}
              // onChange={handleSelectAll}
              className="footerbtn-left-img"
              value=""
            />
            <View className="footerbtn-left-all">
              <Text className="footerbtn-left-all-text">全选</Text>
            </View>
          </View>
        )}

        <View className="footerbtn-right">
          <View
            className={
              getSelectedSkuCount() > 0 && selectedCompany && expressNumber.trim()
                ? "footerbtn-right-send"
                : "footerbtn-right-sendN"
            }
            onClick={handleDeliveryConfirm}
          >
            <Text>发货</Text>
          </View>
        </View>
      </View>

      <View className="foot_holder" />

      <Toast
        visible={showToast}
        content={toastMessage}
        onClose={() => setShowToast(false)}
      />

      {/* 底部弹出对话框 */}
      <BottomPopup
        options={["拍照", "从相册选择"]}
        btnCloseText="取消"
        onConfirm={handleConfirm}
        onClose={handleClose}
        visible={isPopupVisible}
      />

      <PermissionPopup {...permissionPopupProps} />
    </View>
  );
}
