# Msgx 组件

一个通用的消息提示组件，支持 12 个方向的箭头定位，参照 Popover 组件的实现。

## 特性

- 支持 12 个方向：`topLeft`、`topCenter`、`topRight`、`bottomLeft`、`bottomCenter`、`bottomRight`、`leftTop`、`leftCenter`、`leftBottom`、`rightTop`、`rightCenter`、`rightBottom`
- 自动箭头定位
- 支持自定义样式
- 支持暗色模式
- 响应式适配

## 基本用法

```tsx
import Msgx from '@/components/Msgx';
import { View } from '@tarojs/components';

// 基本使用
<Msgx direction="topCenter" content="这是一个提示消息">
  <View>触发元素</View>
</Msgx>

// 或者直接包裹内容
<Msgx direction="bottomRight">
  <View>这是消息内容</View>
</Msgx>
```

## 所有方向示例

```tsx
// 上方位置
<Msgx direction="topLeft" content="左上角">
  <View>按钮</View>
</Msgx>

<Msgx direction="topCenter" content="正上方">
  <View>按钮</View>
</Msgx>

<Msgx direction="topRight" content="右上角">
  <View>按钮</View>
</Msgx>

// 下方位置
<Msgx direction="bottomLeft" content="左下角">
  <View>按钮</View>
</Msgx>

<Msgx direction="bottomCenter" content="正下方">
  <View>按钮</View>
</Msgx>

<Msgx direction="bottomRight" content="右下角">
  <View>按钮</View>
</Msgx>

// 左侧位置
<Msgx direction="leftTop" content="左上">
  <View>按钮</View>
</Msgx>

<Msgx direction="leftCenter" content="左中">
  <View>按钮</View>
</Msgx>

<Msgx direction="leftBottom" content="左下">
  <View>按钮</View>
</Msgx>

// 右侧位置
<Msgx direction="rightTop" content="右上">
  <View>按钮</View>
</Msgx>

<Msgx direction="rightCenter" content="右中">
  <View>按钮</View>
</Msgx>

<Msgx direction="rightBottom" content="右下">
  <View>按钮</View>
</Msgx>
```

## 自定义样式

```tsx
<Msgx 
  direction="topCenter"
  content="自定义样式"
  backgroundColor="#007AFF"
  textColor="#fff"
  borderRadius={8}
  padding="12px 16px"
  arrowSize={10}
  verticalOffset={15}
  horizontalOffset={15}
>
  <View>触发元素</View>
</Msgx>
```

## 控制显示/隐藏

```tsx
const [visible, setVisible] = useState(false);

<Msgx 
  direction="topCenter"
  content="可控制显示"
  visible={visible}
>
  <View onClick={() => setVisible(!visible)}>
    点击切换显示
  </View>
</Msgx>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| direction | 消息框展示的位置 | `MsgxDirection` | `'topCenter'` |
| children | 触发元素或消息内容 | `ReactNode` | - |
| content | 消息框内容，如果提供则显示在气泡中 | `ReactNode` | - |
| visible | 是否显示消息框 | `boolean` | `true` |
| verticalOffset | 垂直方向偏移量(px) | `number` | `10` |
| horizontalOffset | 水平方向偏移量(px) | `number` | `10` |
| arrowSize | 箭头大小(px) | `number` | `8` |
| backgroundColor | 背景色 | `string` | `'#000'` |
| textColor | 文字颜色 | `string` | `'#fff'` |
| borderRadius | 圆角大小 | `number` | `4` |
| padding | 内边距 | `string` | `'8px 12px'` |
| minWidth | 最小宽度 | `string` | `'auto'` |
| maxWidth | 最大宽度 | `string` | `'90vw'` |
| boxShadow | 阴影 | `string` | `'0 2px 8px rgba(0, 0, 0, 0.15)'` |
| zIndex | 层级 | `number` | `1000` |
| className | 自定义类名 | `string` | `''` |
| style | 自定义样式 | `CSSProperties` | `{}` |

### MsgxDirection

```typescript
type MsgxDirection =
  | 'topLeft'
  | 'topCenter' 
  | 'topRight'
  | 'bottomLeft'
  | 'bottomCenter'
  | 'bottomRight'
  | 'leftTop'
  | 'leftCenter'
  | 'leftBottom'
  | 'rightTop'
  | 'rightCenter'
  | 'rightBottom';
```

### Ref 方法

| 方法 | 说明 | 类型 |
|------|------|------|
| updatePosition | 手动更新位置 | `() => void` |

## 注意事项

1. 组件使用 CSS 变量实现动态定位，确保浏览器支持
2. 在 Taro 环境中，位置计算可能需要根据实际情况调整
3. 支持暗色模式，会自动适配主题色
4. 响应式设计，在小屏幕设备上会自动调整大小

## 样式定制

组件支持通过 CSS 变量进行样式定制：

```less
.msgx-container {
  --msgx-vertical-offset: 15px;
  --msgx-horizontal-offset: 15px;
  --msgx-arrow-size: 10px;
  --msgx-bg-color: #007AFF;
}
```
