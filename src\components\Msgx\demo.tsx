import React, { useState } from 'react';
import { View, Text } from '@tarojs/components';
import { Button } from '@arco-design/mobile-react';
import Msgx, { MsgxDirection } from './index';
import './demo.less';

const MsgxDemo: React.FC = () => {
  const [visibleStates, setVisibleStates] = useState<Record<string, boolean>>({});

  const directions: MsgxDirection[] = [
    'topLeft', 'topCenter', 'topRight',
    'bottomLeft', 'bottomCenter', 'bottomRight',
    'leftTop', 'leftCenter', 'leftBottom',
    'rightTop', 'rightCenter', 'rightBottom'
  ];

  const toggleVisible = (direction: string) => {
    setVisibleStates(prev => ({
      ...prev,
      [direction]: !prev[direction]
    }));
  };

  const directionGroups = [
    {
      title: '上方位置',
      directions: ['topLeft', 'topCenter', 'topRight'] as MsgxDirection[]
    },
    {
      title: '下方位置', 
      directions: ['bottomLeft', 'bottomCenter', 'bottomRight'] as MsgxDirection[]
    },
    {
      title: '左侧位置',
      directions: ['leftTop', 'leftCenter', 'leftBottom'] as MsgxDirection[]
    },
    {
      title: '右侧位置',
      directions: ['rightTop', 'rightCenter', 'rightBottom'] as MsgxDirection[]
    }
  ];

  return (
    <View className="msgx-demo">
      <View className="demo-header">
        <Text className="demo-title">Msgx 组件演示</Text>
        <Text className="demo-subtitle">支持 12 个方向的消息提示组件</Text>
      </View>

      {directionGroups.map((group, groupIndex) => (
        <View key={groupIndex} className="demo-section">
          <Text className="section-title">{group.title}</Text>
          <View className="direction-grid">
            {group.directions.map((direction) => (
              <View key={direction} className="demo-item">
                <Msgx
                  direction={direction}
                  content={`${direction} 提示消息`}
                  visible={visibleStates[direction] || false}
                  backgroundColor="#007AFF"
                  textColor="#fff"
                  borderRadius={6}
                  padding="10px 14px"
                >
                  <Button
                    type="ghost"
                    size="small"
                    onClick={() => toggleVisible(direction)}
                    className="demo-button"
                  >
                    {direction}
                  </Button>
                </Msgx>
              </View>
            ))}
          </View>
        </View>
      ))}

      <View className="demo-section">
        <Text className="section-title">自定义样式示例</Text>
        <View className="custom-examples">
          <View className="demo-item">
            <Msgx
              direction="topCenter"
              content="成功提示"
              visible={visibleStates.success || false}
              backgroundColor="#00B42A"
              textColor="#fff"
              borderRadius={8}
              padding="12px 16px"
              arrowSize={10}
            >
              <Button
                type="primary"
                size="small"
                onClick={() => toggleVisible('success')}
                style={{ backgroundColor: '#00B42A' }}
              >
                成功样式
              </Button>
            </Msgx>
          </View>

          <View className="demo-item">
            <Msgx
              direction="bottomCenter"
              content="警告提示"
              visible={visibleStates.warning || false}
              backgroundColor="#FF7D00"
              textColor="#fff"
              borderRadius={8}
              padding="12px 16px"
              arrowSize={10}
            >
              <Button
                type="primary"
                size="small"
                onClick={() => toggleVisible('warning')}
                style={{ backgroundColor: '#FF7D00' }}
              >
                警告样式
              </Button>
            </Msgx>
          </View>

          <View className="demo-item">
            <Msgx
              direction="rightCenter"
              content="错误提示"
              visible={visibleStates.error || false}
              backgroundColor="#F53F3F"
              textColor="#fff"
              borderRadius={8}
              padding="12px 16px"
              arrowSize={10}
            >
              <Button
                type="primary"
                size="small"
                onClick={() => toggleVisible('error')}
                style={{ backgroundColor: '#F53F3F' }}
              >
                错误样式
              </Button>
            </Msgx>
          </View>
        </View>
      </View>

      <View className="demo-section">
        <Text className="section-title">长文本示例</Text>
        <View className="demo-item">
          <Msgx
            direction="topCenter"
            content="这是一个比较长的提示消息，用来测试组件在长文本情况下的表现"
            visible={visibleStates.longText || false}
            maxWidth="200px"
            backgroundColor="#165DFF"
            textColor="#fff"
            borderRadius={6}
            padding="10px 14px"
          >
            <Button
              type="primary"
              size="small"
              onClick={() => toggleVisible('longText')}
            >
              长文本
            </Button>
          </Msgx>
        </View>
      </View>

      <View className="demo-footer">
        <Text className="footer-text">点击按钮查看不同方向的消息提示效果</Text>
      </View>
    </View>
  );
};

export default MsgxDemo;
