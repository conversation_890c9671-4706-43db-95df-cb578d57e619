@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageDynamic/moveMaterials/selectMaterials/index"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });

  // 固定tab栏样式
  .fixed-tabs-container {
    position: fixed;
    top: 84px; // 导航栏高度 + 一些间距，确保在导航栏下方
    left: 0;
    right: 0;
    z-index: 100;
    // 渐变透明度动画
    opacity: 0;
    transition: opacity 0.2s ease;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });

    // 显示状态
    &.show {
      opacity: 1;
    }

    .fixed-tabs {
      z-index: 100;
      background: #f7f8fa;
      .use-dark-mode-query({
      background-color: @dark-background-color;
    });
      // 继承原始tab栏的样式
      display: flex;
      justify-content: space-around;
      align-items: center;
      margin-top: 0;
      margin-bottom: 0;
      padding: 16px 0 4px 0;
      // border-bottom: 1px solid #f0f0f0;
      // .use-dark-mode-query({
      //   border-bottom: 1px solid #333;
      // });

      .user-header-tab {
        font-size: 16px;
        color: #222;
        padding: 8px 0;
        position: relative;
        cursor: pointer;
        transition: color 0.2s ease;
        .use-dark-mode-query({
        color: var(--dark-font-color);
      });

        &.active {
          color: var(--primary-color);
          font-weight: 500;
          &::after {
            content: "";
            display: block;
            height: 2px;
            background: var(--primary-color);
            border-radius: 2px;
            position: absolute;
            transition: all 0.2s ease;
            left: 0;
            right: 0;
            bottom: -2px;
          }
        }
      }
    }

    // 固定搜索栏样式
    .fixed-search-bar {
      // padding: 8px 16px;
      background: #fff;
      .use-dark-mode-query({
      background-color: @dark-background-color;
    });
    }
  }

  // 主滚动视图样式
  .main-scroll-view {
    background: #f7f8fa;
    padding-bottom: 100px; // 为固定底部操作栏留出更多空间
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
  }

  .user-header {
    .use-dark-mode-query({
        background-color: @dark-background-color;     //黑色背景
    });

    // .user-header-bar {
    //   display: flex;
    //   align-items: center;
    //   justify-content: space-between;
    //   height: 48px;
    //   padding: 0 16px;
    //   position: relative;
    //   .user-header-back {
    //     font-size: 22px;
    //     color: #222;
    //     cursor: pointer;
    //     .use-dark-mode-query({
    //         color: var(--dark-font-color);    //白色字体
    //       });
    //   }
    //   .user-header-action {
    //     color: #8c8c8c;
    //     font-size: 15px;
    //     .iconfont {
    //       font-size: 18px;
    //       margin-right: 2px;
    //     }
    //   }
    // }
    .user-header-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 8px;
      .user-header-avatar-wrap {
        position: relative;
        .user-header-avatar {
          width: 64px;
          height: 64px;
          border-radius: 50%;
          object-fit: cover;
          background: #f7f8fa;
        }
        .user-header-qrcode {
          position: absolute;
          right: -10px;
          bottom: 0;
          width: 20px;
          height: 20px;
          //   background: url('二维码icon地址') no-repeat center/cover;
        }
      }
      .user-header-nick {
        font-size: 15px;
        font-weight: 500;
        color: #222;
        margin-top: 8px;
        max-width: 200px;
        text-align: center;
        // white-space: nowrap;
        // overflow: hidden;
        // text-overflow: ellipsis;
        .use-dark-mode-query({
            color: var(--dark-font-color);    //白色字体
          });
      }
      .user-header-desc {
        font-size: 14px;
        color: #8c8c8c;
        margin-top: 4px;
        max-width: 320px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .user-header-stats {
        display: flex;
        justify-content: center;
        margin-top: 12px;
        .user-header-stat {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin: 0 32px;
          .user-header-stat-num {
            font-size: 20px;
            color: #222;
            font-weight: 500;
            .use-dark-mode-query({
                color: var(--dark-font-color);    //白色字体
              });
          }
          .user-header-stat-label {
            font-size: 13px;
            color: #8c8c8c;
            margin-top: 2px;
          }
        }
      }
    }
    .user-header-tabs {
      display: flex;
      justify-content: space-around;
      align-items: center;
      margin-top: 16px;
      margin-bottom: 3px;
      // 渐变透明度动画
      opacity: 1;
      transition: opacity 0.2s ease;

      // 隐藏状态
      &.hide {
        opacity: 0;
      }

      //   border-bottom: 2px solid #f0f0f0;
      .user-header-tab {
        font-size: 16px;
        color: #222;
        padding: 8px 0;
        position: relative;
        cursor: pointer;
        transition: color 0.2s ease;
        .use-dark-mode-query({
            color: var(--dark-font-color);    //白色字体
        });
        &.active {
          color: var(--primary-color);
          font-weight: 500;
          &::after {
            content: "";
            display: block;
            height: 2px;
            background: var(--primary-color);
            border-radius: 2px;
            position: absolute;
            transition: all 0.2s ease;
            left: 0;
            right: 0;
            bottom: -2px;
          }
        }
      }
    }
    .user-header-searchbar {
      display: flex;
      align-items: center;
      padding: 8px 16px;
      background: #fff;
      //   border-bottom: 1px solid #f0f0f0;
      .use-dark-mode-query({
        background-color: @dark-background-color;     //黑色背景
      });
      //   .user-header-search {
      //     display: flex;
      //     align-items: center;
      //     flex: 1;
      //     height: 32px;
      //     line-height: 32px;
      //     background: #f7f8fa;
      //     border-radius: 8px;
      //     border: none;
      //     font-size: 14px;
      //     color: #222;
      //     padding: 0 12px;
      //     box-sizing: border-box;
      //     .use-dark-mode-query({
      //         background-color: var(--dark-container-background-color);   //灰色背景
      //     });
      //     .use-dark-mode-query({
      //         color: var(--dark-font-color);    //白色字体
      //     });

      //   }
    }
  }

  .user-header-filter {
    color: var(--primary-color);
    font-size: 15px;
    margin-left: 8px;
    cursor: pointer;
  }

  /* 商品列表样式 */
  .move-list {
    flex: 1;
    background: #fff;
    padding: 0 10px;
    .use-dark-mode-query({
    background-color: @dark-background-color;
  });
  }
  .move-list-m {
    //height: 550px;
    flex: 1;
    background: #fff;
    .use-dark-mode-query({
        background-color: @dark-background-color;    //黑色背景
    });
  }
  .move-group-title {
    font-size: 16px;
    color: #222;
    font-weight: bold;
    margin: 16px 0 8px 16px;
    .use-dark-mode-query({
        color: var(--dark-font-color);    //白色字体
    });
  }
  .move-item-m {
    display: flex;
    align-items: flex-start;
    padding: 12px 16px 0 16px;
    // border-bottom: 1px solid #f0f0f0;
    background: #fff;
    .use-dark-mode-query({
        background-color: @dark-background-color;   //黑色背景
    });
  }
  .move-checkbox-home {
    margin-right: 12px;
    margin-top: 6px;
  }
  .move-img {
    width: 60px;
    height: 60px;
    border-radius: 6px;
    object-fit: cover;
    background: #f7f8fa;
    margin-right: 12px;
  }

  .move-info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
  }

  /* 商品属性样式 */
  .move-attrs-wrap {
    margin-top: 5px;
  }

  .move-attrs {
    font-size: 12px;
    font-weight: 500;
    color: #4e5969;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
  }

  .move-title-text {
    padding-bottom: 3px;
    font-size: 12px;
    line-height: 1.4;
    color: #1d2129;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
    .use-dark-mode-query({
      color: var(--dark-font-color);
    });
  }

  .move-info-home {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
  }
  .move-title-text-home {
    width: 200px;
    font-size: 15px;
    color: #222;
    margin-bottom: 6px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    .use-dark-mode-query({
        color: var(--dark-font-color);    //白色字体
    });
  }
  .move-price {
    color: #f53f3f;
    font-size: 15px;
    margin-bottom: 4px;
  }
  .move-attrs-m {
    font-size: 13px;
    color: #8c8c8c;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    .move-attrs-arrow {
      font-size: 12px;
      margin-left: 2px;
      color: #c0c0c0;
    }
  }
  .move-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 200;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    padding: 12px 12px;
    background: #fff;
    height: 72px;
    border-top: 1px solid var(--line-color);
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.08);
    box-sizing: border-box;
    .use-dark-mode-query({
      border-top: 1px solid @dark-line-color;
      box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.2);
    });
    .use-dark-mode-query({
      background-color: @dark-background-color;     //黑色背景
    });
  }
  .move-footer-checkbox {
    font-size: 15px;
    color: #4e5969;
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
    .use-dark-mode-query({
      color: var(--dark-font-color);
    });
  }

  .move-footer-setting-btn-wrap {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
  }
  
  // 搬家设置按钮样式
  .move-footer-setting-btn {
    // flex: 1;
    max-width: 120px;
    height: 44px;
    line-height: 44px;
    text-align: center;
    background: #f7f8fa;
    color: #4e5969;
    font-size: 15px;
    border: none;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    flex-shrink: 0;
    white-space: nowrap;
    
    &:active {
      background: #e5e6eb;
    }
    
    .use-dark-mode-query({
      background-color: @dark-container-background-color;
      color: var(--dark-font-color);
      
      &:active {
        background: lighten(@dark-container-background-color, 10%);
      }
    });
  }

  // 开始搬家按钮样式
  .move-footer-btn {
    // flex: 1;
    max-width: 120px;
    height: 44px;
    line-height: 44px;
    text-align: center;
    background: var(--primary-color);
    color: #fff;
    font-size: 16px;
    border: none;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    white-space: nowrap;
    
    &.disabled {
      background: #e5e6eb;
      color: #86909c;
      cursor: not-allowed;
      
      .use-dark-mode-query({
        background: #4b5563;
        color: #9ca3af;
      });
    }
    
    &:not(.disabled):active {
      background: darken(#165dff, 10%);
    }
  }

  .attr-mask {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.45);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .attr-modal {
    background: #fff;
    border-radius: 18px;
    min-width: 320px;
    max-width: 90vw;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
    padding: 20px 20px 18px 20px;
    .use-dark-mode-query({
    background: @dark-container-background-color;
  });
  }
  .attr-title-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
  }
  .attr-title {
    font-size: 17px;
    font-weight: 500;
    color: #222;
    .use-dark-mode-query({ color: var(--dark-font-color); });
  }
  .attr-close {
    font-size: 22px;
    color: #b0b0b0;
    cursor: pointer;
    font-weight: 400;
  }
  .attr-content {
    display: flex;
    flex-direction: column;
    gap: 18px;
  }
  .attr-row {
    display: flex;
    align-items: flex-start;
  }
  .attr-label {
    font-size: 15px;
    color: #8c8c8c;
    min-width: 48px;
    // margin-top: 4px;
  }
  .attr-values-s {
    width: 215px;
  }
  .attr-value {
    background: #f7f8fa;
    border-radius: 8px;
    padding: 4px 16px;
    font-size: 12px;
    color: #222;
    //margin-bottom: 6px;
    cursor: pointer;
    transition: all 0.2s;
    .use-dark-mode-query({
    background: @dark-background-color;
    color: var(--dark-font-color);
  });
  }
  .attr-value.selected {
    background: #165dff;
    color: #fff;
    .use-dark-mode-query({
    background: #165dff;
    color: #fff;
  });
  }

  .card-attr-content {
    background: #f7f8fa;
    border-radius: 14px;
    //margin: 12px 0 0 0;
    padding: 16px 0px 10px 18px;
    margin-right: 5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    .use-dark-mode-query({
    background: @dark-container-background-color;
    box-shadow: 0 2px 8px rgba(0,0,0,0.18);
  });
  }
  .card-attr-content .attr-row + .attr-row {
    padding-top: 12px;
    border-top: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-top: 1px solid @dark-line-color;
  });
  }
  .card-attr-content .attr-label {
    font-size: 15px;
    color: #222;
    font-weight: 600;
    min-width: 48px;
    // margin-top: 4px;
    .use-dark-mode-query({ color: var(--dark-font-color); });
  }
  .card-attr-content .attr-values-s {
    display: flex;
    flex-wrap: wrap;
    gap: 0;
  }
  .card-attr-content .attr-value {
    font-size: 12px;
    color: #444;
    background: none;
    border: none;
    padding: 0 18px 0 0;
    margin-bottom: 6px;
    position: relative;
    cursor: pointer;
    transition: none;
    .use-dark-mode-query({ color: var(--dark-font-color); });
  }
  .card-attr-content .attr-value:not(:last-child)::after {
    content: "";
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 18px;
    background: #e5e6eb;
    .use-dark-mode-query({ background: #333; });
  }
  .card-attr-content .attr-value.selected {
    background: #165dff;
    color: #fff;
    border: 1px solid #165dff;
    .use-dark-mode-query({
    background: #165dff;
    color: #fff;
    border: 1px solid #165dff;
  });
  }

  .move-attrs-wrap-m {
    margin-top: -5px;
    margin-left: 70px;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px 0;
    background: #fff;
    .use-dark-mode-query({
    background: @dark-background-color;
  });
  }

  .empty-image {
    width: 83px;
    height: 82px;
    margin-bottom: 16px;
  }

  .empty-text {
    font-size: 14px;
    color: #86909c;
  }

  .content-scroll-view {
    // height: 570px
  }

  .yk-navbar-back {
    cursor: pointer;
  }


}

  // 筛选弹框样式
  .filter-popup {
    .filter-popup-content {
      background: #fff;
      border-radius: 15px 15px 0 0;
      padding: 0 16px 20px;
      .use-dark-mode-query({
        background-color: @dark-card-background-color;
      });

      .filter-popup-header {
        padding: 16px 0;
        text-align: center;
        border-bottom: 1px solid #f0f0f0;
        position: relative;
        .use-dark-mode-query({
          border-bottom-color: @dark-line-color;
        });

        .filter-close-icon {
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          color: #86909c;
          font-size: 20px;

          &:hover {
            color: #4e5969;
          }

          .use-dark-mode-query({
            color: var(--dark-font-color);
          });
        }

        .filter-popup-title {
          font-size: 16px;
          font-weight: 500;
          color: #222;
          .use-dark-mode-query({
            color: var(--dark-font-color);
          });
        }
      }

      .filter-time-section {
        margin-top: 20px;

        .filter-section-title {
          font-size: 14px;
          color: #86909c;
          margin-bottom: 12px;
          display: block;
        }

        .filter-time-range {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 8px;

          .filter-time-item {
            flex: 1;
            height: 40px;
            background: #f7f8fa;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            .use-dark-mode-query({
              background-color: @dark-background-color;
            });

            &:active {
              opacity: 0.7;
            }

            .filter-time-label {
              font-size: 14px;
              color: #4e5969;
              .use-dark-mode-query({
                color: var(--dark-font-color);
              });
            }
          }

          .filter-time-separator {
            font-size: 14px;
            color: #86909c;
            padding: 0 4px;
          }
        }
      }

      .filter-quick-time {
        display: flex;
        gap: 12px;
        margin-top: 12px;

        .filter-quick-btn {
          flex: 1;
          height: 36px;
          background: #f7f8fa;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s;
          .use-dark-mode-query({
            background-color: @dark-background-color;
          });

          &.active {
            background: var(--primary-color);

            .filter-quick-text {
              color: #fff;
            }
          }

          .filter-quick-text {
            font-size: 14px;
            color: #4e5969;
            .use-dark-mode-query({
              color: var(--dark-font-color);
            });
          }
        }
      }

      .filter-popup-footer {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-top: 24px;

        // 覆盖Taro Button组件的默认margin-top
        :global {
          button + button {
            margin-top: 0 !important;
          }

          .arco-button + .arco-button {
            margin-top: 0 !important;
          }
        }

        .filter-reset-btn,
        .filter-confirm-btn {
          flex: 1;
          height: 44px;
          border-radius: 8px;
          font-size: 16px;
          border: none;
        }

        .filter-reset-btn {
          background: #f7f8fa;
          color: #4e5969;
          .use-dark-mode-query({
            background-color: @dark-background-color;
            color: var(--dark-font-color);
          });
        }

        .filter-confirm-btn {
          background: var(--primary-color);
          color: #fff;
        }
      }
    }
  }

