import { View } from "@tarojs/components";
import { Popup, PickerView } from "@arco-design/mobile-react";
import React, { useMemo, useEffect, useState } from "react";
import industryData from "./industry.json";
import "./index.less";

interface IndustryPickerProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (value: { parentName: string; childName: string }) => void;
  title?: string;
  currentValue?: string;
}


const YkIndustryPicker: React.FC<IndustryPickerProps> = ({
  visible,
  onClose,
  onConfirm,
  title = "",
  currentValue,
}) => {
  const [selectedValue, setSelectedValue] = useState<(string | number)[]>([]);

  useEffect(() => {
    if (currentValue) {
      setSelectedValue(currentValue.split("-"));
    }
  }, []);

  const handleConfirm = () => {
    if (selectedValue.length === 2) {
      const parentItem = (industryData as any).data.find(
        (item) => item.value === selectedValue[0]
      );
      const childItem = parentItem?.children.find(
        (item) => item.value === selectedValue[1]
      );

      if (parentItem && childItem) {
        onConfirm({
          parentName: parentItem.label,
          childName: childItem.label,
        });
      }
    }
    onClose();
  };

  return (
    <Popup
      visible={visible}
      close={onClose}
      direction="bottom"
      contentStyle={{ borderRadius: "4px 4px 0 0" }}
      className="industry-picker"
    >
      <View className="picker-content">
        <View className="picker-header">
          <View className="cancel-btn" onClick={onClose}>
            取消
          </View>
          <View className="title">{title}</View>
          <View className="confirm-btn" onClick={handleConfirm}>
            确定
          </View>
        </View>
        <PickerView
          className="picker-view"
          data={(industryData as any).data}
          value={selectedValue}
          onPickerChange={(value) => {
            setSelectedValue(value);
          }}
          cascade={true}
          cols={2}
        />
      </View>
    </Popup>
  );
};

export default YkIndustryPicker; 