@import '@arco-design/mobile-react/style/mixin.less';

.industry-picker {
  z-index: 9999;
  position: fixed;
  bottom: 0;
  width: 100%;
  .use-var(background-color, background-color);
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });

  .picker-content {
    margin: 0;
    padding: 0;
    width: 100%;

    .picker-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .rem(padding, 13);
      border-bottom-style: solid;
      .rem(border-bottom-width, 1);

      .cancel-btn, .confirm-btn {
        .rem(font-size, 14);
        .use-var(color, primary-color);
        .use-dark-mode-query({
          color: var(--dark-primary-color);
        });
      } 

      .title {
        .rem(font-size, 15);
        font-weight: 500;
        .use-var(color, font-color);
        .use-dark-mode-query({
          color: @dark-font-color;
        });
      }

      .picker-view {
        .rem(font-size, 16);
      }
    }
  }
} 