@import "@arco-design/mobile-react/style/mixin.less";
@import "@/utils/css/variables.less";

[id^="/pages/friends/index"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });

  .friendsBox {
    min-height: 100vh;
    .use-var(background-color, background-color);
    .use-dark-mode-query({
    background: @dark-background-color;
  });
  }

  // 搜索栏容器
  .search-container {
    background: #ffffff;
    position: sticky;
    top: 0;
    z-index: 100;
    .use-dark-mode-query({
      background: @dark-background-color;
    });
    padding: 10px 16px;

    .arco-search-bar-container {
      padding: 0 !important;
    }
  }

  .fansContainer {
    padding-bottom: 15px;
    background: #f7f8fa;
    .use-dark-mode-query({
    background: @dark-background-color;
  });
  }

  // 粉丝入口样式
  .fansEntry {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    .use-var(background-color, background-color);
    .use-dark-mode-query({
    background: @dark-background-color;
  });
    margin-bottom: 0;

    .use-var(color,font-color);
    .use-dark-mode-query({
    color: @dark-font-color;
  });

    .fansAvatar {
      background-color: @--warning-6;
      overflow: hidden;
    }

    .fansIcon {
      width: 50px;
      height: 50px;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .fansText {
      font-size: 16px;
      font-weight: 500;
      .use-var(color, font-color);
      .use-dark-mode-query({
      color: @dark-font-color;
    });
    }
  }

  .listBox {
    .use-var(background-color, background-color);
    .use-dark-mode-query({
    background: @dark-background-color;
  });
    padding: 0;

    // 列表头部
    .list-header {
      padding: 16px 16px 12px 16px;
      background: #ffffff;
      .use-dark-mode-query({
      background: @dark-background-color;
    });
    }

    .listTitle {
      display: block;
      .use-var(color, sub-font-color);
      .use-dark-mode-query({
      color: @dark-sub-font-color;
    });
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
    }

    .list-container {
      // 容器背景 - 填充 Fill/card 卡片-背景色: #F7F8FA
      // background-color: #F7F8FA !important;
      // .use-dark-mode-query({
      //   background-color: @dark-background-color !important;
      // });
      .cell-group-body {
        .use-var(background-color, background-color) !important;
        .use-dark-mode-query({
        background-color: @dark-background-color !important;
      });
      }
    }

    // 好友列表项样式
    .friend-item {
      padding: 12px 0;
      // 继承原有的 listItem 样式
      .friend-item-inner {
        display: flex;
        align-items: center;
        padding: 12px 0;

        .friend-avatar {
          flex-shrink: 0;
          margin-right: 8px;
        }

        .friend-content {
          flex: 1;
          margin-left: 0;

          .nameRow {
            display: flex;
            align-items: center;
            margin-bottom: 6px;

            .nickname {
              font-size: 16px;
              font-weight: 500;
              .use-var(color, font-color);
              .use-dark-mode-query({
              color: @dark-font-color;
            });
              margin-right: 10px;
            }

            .jinjianIcon {
              width: 16px;
              height: 16px;
              margin-right: 10px;
            }

            .starredIcon {
              width: 16px;
              height: 16px;
            }
          }

          .countRow {
            .count {
              .use-var(color, sub-font-color);
              .use-dark-mode-query({
              color: @dark-sub-font-color;
            });
              font-size: 12px;
              margin-right: 16px;
            }
          }
        }
      }
    }

    // 保持原有的 listItem 样式以兼容
    .listItem {
      display: flex;
      align-items: center;
      padding: 10px 0;

      .itemContent {
        flex: 1;
        margin-left: 12px;

        .nameRow {
          display: flex;
          align-items: center;

          .nickname {
            font-size: 14px;
            .use-var(color, font-color);
            .use-dark-mode-query({
            color: @dark-font-color;
          });
          }

          .jinjianIcon {
            width: 16px;
            height: 16px;
            margin-left: 6px;
          }
        }

        .countRow {
          margin-top: 6px;

          .count {
            // color: #8f8f8f;
            .use-var(color, sub-font-color);
            .use-dark-mode-query({
            color: @dark-sub-font-color;
          });
            font-size: 11px;
            margin-right: 16px;
          }
        }
      }
    }
  }

  // 错误状态样式
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;

    .error-message {
      margin-bottom: 20px;
      text-align: center;

      .error-text {
        .use-var(color, sub-font-color);
        .use-dark-mode-query({
        color: @dark-sub-font-color;
      });
        font-size: 14px;
        line-height: 1.5;
      }
    }

    .retry-button {
      width: 100%;
      max-width: 200px;
    }
  }

  .emptyBox {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 500px; /* 根据设计稿容器高度 */
    background: #ffffff; /* 填充 Fill/Container 容器背景色 */
    flex: 1;
    .use-dark-mode-query({
    background: @dark-background-color;
  });

    .empty-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 19px; /* 根据设计稿间距 */
    }

    .emptyIcon {
      width: 100px;
      height: 100px;
    }

    .empty-text-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 5px; /* 根据设计稿间距 */
      // width: 133px; /* 根据设计稿文本容器宽度 */
      // height: 48px; /* 根据设计稿文本容器高度 */
    }

    .emptyText {
      /* 主标题样式 - 16/Bold */
      font-family: "PingFang SC";
      font-size: 16px;
      font-weight: bold;
      line-height: 140%;
      // color: #1D2129; /* 文字 Text/文字-5-基础  Grey 10 */
      .use-var(color, font-color);
      text-align: center;
      .use-dark-mode-query({
      color: @dark-font-color;
    });
    }

    .subText {
      /* 副标题样式 - 14/Medium */
      font-family: "PingFang SC";
      font-size: 14px;
      font-weight: 500;
      line-height: 140%;
      // color: #86909C; /* 文字 Text/文字-3-附加信息-Grey 6 */
      .use-var(color, sub-font-color);
      text-align: center;
      .use-dark-mode-query({
      color: @dark-sub-font-color;
    });
    }
  }

  .noMore {
    display: block;
    text-align: center;
    .use-var(color, sub-font-color);
    .use-dark-mode-query({
    color: @dark-sub-font-color;
  });
    font-size: 12px;
    padding: 12px 0;
  }

  // 未登录状态样式
  .wait-login-container {
    position: relative;
    width: 100%;
    min-height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 80px 54.5px 0;
    box-sizing: border-box;
    
    &-content {
      width: 266px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 20px;
    }
    
    &-text-area {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 5px;
      
      &-title {
        font-size: 16px;
        font-weight: bold;
        color: #1D2129;
        line-height: 1.4;
        text-align: center;
        .use-dark-mode-query({
          color: var(--dark-font-color);
        });
      }
      
      &-subtitle {
        font-size: 14px;
        font-weight: 500;
        color: #86909C;
        line-height: 1.4;
        text-align: center;
        .use-dark-mode-query({
          color: var(--dark-sub-info-font-color);
        });
      }
    }
    
    &-image-wrapper {
      width: 144px;
      height: 179px;
      position: relative;
      overflow: hidden;
      border-radius: 4px;
      border: 0.5px solid #E5E6EB;
      background: linear-gradient(180deg, rgba(215, 255, 255, 0.16) 46%, rgba(216, 216, 216, 0) 92%);
      
      .use-dark-mode-query({
        border-color: #333;
        background: linear-gradient(180deg, rgba(215, 255, 255, 0.08) 46%, rgba(216, 216, 216, 0) 92%);
      });
      
      &-bg {
        width: 100%;
        height: 100%;
        display: block;
      }
    }
    
    &-login-btn {
      position: fixed;
      bottom: calc(80px + env(safe-area-inset-bottom));
      left: 50%;
      transform: translateX(-50%);
      width: 88px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #165DFF;
      color: #FFFFFF;
      font-size: 14px;
      font-weight: normal;
      border-radius: 4px;
      border: 1px solid #165DFF;
      cursor: pointer;
      transition: opacity 0.2s;
      z-index: 100;
      
      &:active {
        opacity: 0.8;
      }
      
      .use-dark-mode-query({
        background: var(--dark-primary-color);
        border-color: var(--dark-primary-color);
      });
    }
  }
}
