import { View, Text } from "@tarojs/components";
import "./index.less";
import {
  Loading,
  Divider,
} from "@arco-design/mobile-react";
import React, { useState } from "react";
import Taro, { useLoad } from "@tarojs/taro";
import {
  getDeliveryCompanyList,
} from "@/utils/api/common/common_user";
import YkNavBar from "@/components/ykNavBar/index";
import { toast } from "@/utils/yk-common";
import  { useEffect,useRef } from "react";
// 类型定义
interface DeliveryCompany {
  id: number;
  name: string;
}

export default function DeliveryCompanyPage() {
  const [loading, setLoading] = useState(true);
  const [companyList, setCompanyList] = useState<DeliveryCompany[]>([]);
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  useLoad(() => {
    fetchDeliveryCompanies();
  });

  // 获取快递公司列表
  const fetchDeliveryCompanies = async () => {
    try {
      setLoading(true);
      const response = await getDeliveryCompanyList();

      if (response && response.code === 0) {
        setCompanyList(response.data || []);
      } else {
        console.error("获取快递公司列表失败:", response.msg);
        toast("error", {
          content: response.msg || "获取快递公司列表失败",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("获取快递公司列表失败:", error);
      toast("error", {
        content: "获取快递公司列表失败",
        duration: 2000,
      });
    } finally {
      setLoading(false);
    }
  };

  // 选择快递公司
  const handleSelectCompany = (company: DeliveryCompany) => {
    console.log("选择快递公司:", company);
    // 将选中的快递公司保存到 localStorage
    Taro.setStorageSync('selectedDeliveryCompany', company);
    console.log("已保存到 localStorage:", Taro.getStorageSync('selectedDeliveryCompany'));
    // 返回上一页
    Taro.navigateBack();
  };

  // if (loading) {
  //   return (
  //     <View className="delivery-company-page">
  //      {platform !== "WX" && <YkNavBar title="选择快递公司" />}
  //       <Loading />
  //     </View>
  //   );
  // }

  return (
    <View className="delivery-company-page">
     {platform !== "WX" && <YkNavBar title="选择快递公司" />}

      <View className="company-list">
        {companyList.map((company, index) => (
            <View
              className="company-item"
              onClick={() => handleSelectCompany(company)}
            >
              <Text className="company-name">{company.name}</Text>
            </View>
        ))}

        {companyList.length === 0 && (
          <View className="empty-message">
            <Text>暂无快递公司</Text>
          </View>
        )}
      </View>
    </View>
  );
}