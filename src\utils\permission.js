import { AuthTypes } from "@/utils/config/authTypes";

// 权限映射配置
export const permissionMapping = {
  [AuthTypes.CAMERA]: {
    hint: "在下方弹窗中选择允许后，你可以使用「拍照」用于发布动态、添加头像、添加微信二维码、添加订单留言等。你还可以在其他场景中访问摄像头进行拍摄照片。",
  },
  [AuthTypes.GALLERY_PHOTO]: {
    hint: "用于选择图片、保存照片。拒绝该权限，可能会影响照片上传、商品图片保存等功能。",
  },
  [AuthTypes.GALLERY_VIDEO]: {
    hint: "在下方弹窗中选择允许后，你可以从手机相册中选择视频发送给朋友。",
  },
  [AuthTypes.GALLERY_AUDIO]: {
    hint: "在下方弹窗中选择允许后，你可以从手机相册中选择音频发送给朋友。",
  },
  [AuthTypes.STORAGE]: {
    hint: "用于选择图片、保存照片。拒绝该权限，可能会影响照片上传、商品图片保存等功能。",
  },
  [AuthTypes.AUDIO]: {
    hint: "录音权限说明",
  },
  [AuthTypes.NOTICE]: {
    hint: "通知权限说明",
  },
  [AuthTypes.LOCATION]: {
    hint: "定位权限说明",
  },
  [AuthTypes.CONTACT]: {
    hint: "通讯录权限说明",
  },
  [AuthTypes.FLOATWIN]: {},
  [AuthTypes.ACCESS]: {},
  [AuthTypes.AUTOSTART]: {},
};

// 全局权限状态
let globalPermissions = [];
let globalSetPermission = null;
let globalSetPermissionsState = null;

// 初始化全局权限管理
export const initPermissionManager = (setPermission, setPermissionsState, initialPermissions) => {
  globalSetPermission = setPermission;
  globalSetPermissionsState = setPermissionsState;

  // 如果提供了初始权限数据，则设置到 globalPermissions
  if (initialPermissions && Array.isArray(initialPermissions)) {
    globalPermissions = initialPermissions;
    console.log("Initialized globalPermissions with:", globalPermissions);
  }
};

// 检查是否有权限
export const hasPermission = (authType) => {
  // 确保 globalPermissions 是数组
  if (!Array.isArray(globalPermissions)) {
    console.log("globalPermissions is not an array:", globalPermissions);
    return false;
  }

  const permission = globalPermissions.find(
    (perm) => perm.authType === authType
  );
  console.log("hasPermission check:", authType, permission);
  // 修复：检查 state 字段而不是 currentStatus，因为 state 是处理后的布尔值
  return permission ? permission.state : false;
};

// 权限回调处理
export const checkPermissionCallBack = async (e, authConfirmType, isRequestPermission, permissionHint, setPermissionPopupVisible, webPermissonConsent, webPermissonDeny, platformRef) => {
  console.log(JSON.stringify(e));
  // 更新权限状态
  if (globalSetPermission) {
    globalPermissions = await globalSetPermission(e); // 确保更新后赋值
    console.log("Updated globalPermissions:", globalPermissions);
  } else {
    console.warn("globalSetPermission is not available, using raw data");
    globalPermissions = e;
  }

  if (globalSetPermissionsState) {
    globalSetPermissionsState([...globalPermissions]); // 更新状态
  }
  
  const currentAuth = authConfirmType.current;
  const permissionConfig = permissionMapping[currentAuth];
  
  if (isRequestPermission.current) {
    isRequestPermission.current = false;
    if (
      !permissionConfig &&
      currentAuth !== AuthTypes.FLOATWIN &&
      currentAuth !== AuthTypes.ACCESS &&
      currentAuth !== AuthTypes.AUTOSTART
    ) {
      console.error("Invalid authConfirmType:", currentAuth);
      return;
    }

    if (!hasPermission(currentAuth)) {
      // 设置权限提示信息（如果存在）
      if (permissionConfig && permissionConfig.hint) {
        permissionHint.current = permissionConfig.hint;
        // 显示权限弹窗
        setPermissionPopupVisible(true);
      }
    }

    if (platformRef.current === "Android") {
      window.requestPermission.requestPermission(currentAuth + "");
    } else {
      if (hasPermission(currentAuth)) {
        webPermissonConsent();
      } else {
        webPermissonDeny();
      }
    }
  }
};

// 请求权限
export const requestPermissionWeb = (type, authConfirmType, isRequestPermission, platformRef) => {
  isRequestPermission.current = true;
  authConfirmType.current = type;
  if (platformRef.current === "Android") {
    window.checkPermission.checkPermission();
  } else {
    window.webkit.messageHandlers.requestPermission.postMessage(`${type}`);
  }
};

// 生成权限设置弹框文本
export const generatePermissionText = (authType, platformRef) => {
  let title = "";
  let text = "";
  
  if (platformRef === "Android") {
    title = "权限申请";
    if (authType === AuthTypes.STORAGE) {
      text = `在设置＞应用＞${APP_NAME_CN}＞权限中开启存储权限，以正常使用拍照、录制视频等功能`;
    } else if (authType === AuthTypes.CAMERA) {
      text = `在「设置＞应用＞${APP_NAME_CN}＞权限」中开启相机权限，以正常使用拍照、录制视频等功能`;
    } else if (authType === AuthTypes.GALLERY_PHOTO) {
      text = `在「设置＞应用＞${APP_NAME_CN}＞权限」中开启相册权限，以使用查看或选择图片视频、文件等功能。`;
    }
  } else {
    if (authType === AuthTypes.STORAGE) {
      title = "无法保存";
      text = `请在iphone的「设置＞隐私＞照片」选项中，允许${APP_NAME_CN}访问你的照片。`;
    } else if (authType === AuthTypes.CAMERA) {
      title = "相机权限未开启";
      text = `无法录制视频，前往「设置＞${APP_NAME_CN}」中打开相机权限`;
    } else if (authType === AuthTypes.GALLERY_PHOTO) {
      title = "相册权限未开启";
      text = `无法选择图片，前往「设置＞${APP_NAME_CN}」中打开相册权限`;
    }
  }
  
  return { title, text };
};

// 打开系统设置
export const openSetting = (platformRef) => {
  if (platformRef === "Android") {
    window.openSetting.openSetting();
  } else {
    window.webkit.messageHandlers.openSetting.postMessage("");
  }
};
