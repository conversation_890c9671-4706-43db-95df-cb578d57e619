import { View, Text } from "@tarojs/components";
import { useLoad} from "@tarojs/taro";
import "./index.less";
import {
  Image,
  Loading,
} from "@arco-design/mobile-react";
import React, { useState, useEffect, useRef } from "react";
import Taro from "@tarojs/taro";
import {
  getDeliveryByTrackingNumber,
} from "@/utils/api/common/common_user";
import YkNavBar from "@/components/ykNavBar/index";
import { toast } from "@/utils/yk-common";
export default function OrderDetails() {
  const [orderDetails, setOrderDetails] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [trackingNumber, setTrackingNumber] = useState<string>('');
  const [expressCompany, setExpressCompany] = useState<string>('');
  const [receivingAddress, setReceivingAddress] = useState<string>('');

  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);

  // 获取路由参数
  const getRouterParams = () => {
    const router = Taro.getCurrentInstance().router;
    console.log("router",router);
    return {
      id: router?.params?.id,
      trackingNumber: router?.params?.trackingNumber,
      expressCompany: router?.params?.expressCompany,
      receivingAddress: router?.params?.receivingAddress
    };
  };

  // 获取物流详情
  const fetchOrderDetails = async () => {
    try {
      setLoading(true);
      const params = getRouterParams();

      // 设置路由参数到状态
      console.log('路由参数:', params);
      if (params.trackingNumber) {
        const decodedTrackingNumber = decodeURIComponent(params.trackingNumber);
        console.log('快递单号:', decodedTrackingNumber);
        setTrackingNumber(decodedTrackingNumber);
      }
      if (params.expressCompany) {
        const decodedExpressCompany = decodeURIComponent(params.expressCompany);
        console.log('快递公司:', decodedExpressCompany);
        setExpressCompany(decodedExpressCompany);
      }
      if (params.receivingAddress) {
        const decodedReceivingAddress = decodeURIComponent(params.receivingAddress);
        console.log('收件地址:', decodedReceivingAddress);
        setReceivingAddress(decodedReceivingAddress);
      }

      const emsNumber = params.trackingNumber || params.id;
      if (!emsNumber) {
        toast("info", {
        content: "物流单号不存在",
        duration: 2000
      });
        return;
      }

      const response = await getDeliveryByTrackingNumber({ trackingNumber: emsNumber });

      if (response && response.code === 0) {
        setOrderDetails(response.data);
      } else {
        toast("info", {
          content: response.msg || "获取物流详情失败",
          duration: 2000
        });
      }
    } catch (error) {
      console.error("获取物流详情失败:", error);
      toast("info", {
        content: "获取物流详情失败",
        duration: 2000
      });
    } finally {
      setLoading(false);
    }
  };

  useLoad(() => {
    fetchOrderDetails();
  });

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <View className="ems-details-box">
       {platform !== "WX" && <YkNavBar title="查看物流" />}
        <View style={{ padding: "50px", textAlign: "center" }}>
          <Loading />
          <Text style={{ marginTop: "10px", display: "block" }}>加载中...</Text>
        </View>
      </View>
    );
  }

  // 如果没有订单数据，显示错误状态
  if (!orderDetails) {
    return (
      <View className="ems-details-box">
       {platform !== "WX" && <YkNavBar title="查看物流" />}
        <View style={{ padding: "50px", textAlign: "center" }}>
          <Text>物流详情不存在</Text>
        </View>
      </View>
    );
  }
  // 格式化时间
  const formatTime = (timestamp: number) => {
    if (!timestamp) return "--";
    const date = new Date(timestamp);
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // 复制快递单号
  const handleCopyTrackingNumber = (orderNo: string) => {
    Taro.setClipboardData({
      data: orderNo,
      success: () => {
        Taro.hideToast();
        toast("success", {
          content: "快递单号已复制",
          duration: 2000
        });
      },
      fail: () => {
        Taro.hideToast();
        toast("error", {
          content: "复制失败",
          duration: 2000
        });
      },
    });
  };

  return (
    <View className="ems-details-box">
     {platform !== "WX" && <YkNavBar title="查看物流" />}

      {/* 数据来源提示 */}
      <View className="data-source">
        <Text className="data-source-text">
          本数据由<Text className="company-name">{expressCompany || '快递公司'}</Text>提供
        </Text>
      </View>

      {/* 快递单号信息 */}
      <View className="tracking-info">
        <View className="tracking-row">
          <Text className="tracking-label">快递单号：</Text>
          <Text className="tracking-number">{trackingNumber || orderDetails?.nu || '--'}</Text>
          <Text
            className="copy-btn"
            onClick={() => handleCopyTrackingNumber(trackingNumber || orderDetails?.nu || '')}
          >
            复制
          </Text>
        </View>

        {/* 收件地址 */}
        <View className="address-row">
          <Text className="address-label">收件地址：</Text>
          <Text className="address-text">{receivingAddress || '--'}</Text>
        </View>
      </View>

      {/* 物流轨迹 */}
      <View className="logistics-timeline">
        {orderDetails&& orderDetails.length > 0 ? (
          orderDetails.map((item: any, index: number) => (
            <View key={index} className="timeline-item">
              <View className="timeline-dot-wrapper">
                <View className={`timeline-dot ${index === 0 ? 'active' : ''}`}></View>
                {index < orderDetails.length - 1 && <View className="timeline-line"></View>}
              </View>
              <View className="timeline-content">
                <View className="timeline-time">{item.ftime}</View>
                <View className="timeline-context">{item.context}</View>
              </View>
            </View>
          ))
        ) : (
          <View className="no-data">
            <Text>暂无物流信息</Text>
          </View>
        )}
      </View>
    </View>
  );
}
