import { useState, useEffect, useCallback } from "react";
import {
  handlePaymentResult,
  registerPaymentCallbacks,
  PAYMENT_PROVIDERS,
} from "@/utils/paymentService";
import {
  createVipOrder,
  getPayOrder,
  submitPayOrder,
} from "@/utils/api/common/common_user";
import Taro from "@tarojs/taro";

/**
 * @description 用于处理VIP会员支付流程的React Hook
 * 支持完整的支付流程：创建订单 -> 获取支付信息 -> 提交支付 -> 调起支付 -> 处理回调
 * @param {{onSuccess?: Function, onFailure?: Function, onCancel?: Function, debug?: boolean}} options
 */
export function useVipPayment({
  onSuccess = () => {},
  onFailure = () => {},
  onCancel = () => {},
} = {}) {
  const [isPaying, setIsPaying] = useState(false);
  const [error, setError] = useState(null);
  const [paymentStep, setPaymentStep] = useState(""); // 当前支付步骤
  const [paymentData, setPaymentData] = useState(null); // 支付相关数据

  // 注册和清理全局回调
  useEffect(() => {
    const cleanup = registerPaymentCallbacks({
      onSuccess: () => {
        setIsPaying(false);
        setPaymentStep("success");
        setError(null);
        onSuccess(paymentData);
      },
      onFailure: (errorMessage) => {
        setIsPaying(false);
        setPaymentStep("failed");
        setError(errorMessage);
        onFailure(errorMessage);
      },
      onCancel: () => {
        setIsPaying(false);
        setPaymentStep("cancelled");
        setError(null);
        onCancel();
      },
    });

    return cleanup; // 在组件卸载时执行清理
  }, [onSuccess, onFailure, onCancel, paymentData]);

  // 创建Promise化的API请求函数
  const createOrderPromise = useCallback(
    (vipType, price, userId) => {
      return new Promise((resolve, reject) => {
        setPaymentStep("creating_order");

        createVipOrder({
          memberType: vipType,
          userId: userId,
          price: price,
          payStatus: 0,
        })
          .then((response) => {
            if (response && response.code !== 0) {
              reject(new Error(response.msg || "创建订单失败"));
              return;
            }

            const payOrderId = response.data;
            if (!payOrderId) {
              reject(new Error("获取支付单ID失败"));
              return;
            }

            resolve(payOrderId);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    []
  );

  const getPayOrderPromise = useCallback(
    (payOrderId) => {
      return new Promise((resolve, reject) => {
        setPaymentStep("getting_pay_info");

        getPayOrder({ id: payOrderId })
          .then((response) => {
            if (response && response.code !== 0) {
              reject(new Error(response.msg || "获取支付信息失败"));
              return;
            }

            const payOrderInfo = response.data;
            if (!payOrderInfo) {
              reject(new Error("支付订单信息为空"));
              return;
            }

            resolve(payOrderInfo);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    []
  );

  const submitPayOrderPromise = useCallback(
    (payOrderInfo, channelCode = PAYMENT_PROVIDERS.WECHAT) => {
      return new Promise((resolve, reject) => {
        setPaymentStep("submitting_payment");

        submitPayOrder({
          id: payOrderInfo.id,
          channelCode: channelCode,
          displayMode: "app",
          returnUrl: payOrderInfo.notifyUrl
        })
          .then((response) => {
            if (response && response.code !== 0) {
              reject(new Error(response.msg || "提交支付失败"));
              return;
            }

            const payResult = response.data;
            resolve(payResult);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    []
  );

  const invokePaymentPromise = useCallback(
    (payResult, channelCode) => {
      return new Promise((resolve, reject) => {
        setPaymentStep("invoking_payment");

        handlePaymentResult(payResult, channelCode)
          .then((result) => {
            setPaymentStep("waiting_result");
            resolve(result);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    []
  );

  // 发起VIP支付的完整流程（Promise链式版本）
  const initiateVipPayment = useCallback(
    (vipType, price, channelCode = PAYMENT_PROVIDERS.WECHAT) => {
      setIsPaying(true);
      setError(null);
      setPaymentStep("starting");


      // 获取用户信息
      const localUserInfo = Taro.getStorageSync("userInfo");
      if (!localUserInfo || !localUserInfo.id) {
        const error = new Error("请先登录");
        setError(error.message);
        setIsPaying(false);
        setPaymentStep("failed");
        onFailure(error.message);
        return Promise.reject(error);
      }

      // Promise链式调用
      return createOrderPromise(vipType, price, localUserInfo.id)
        .then((payOrderId) => {
          return getPayOrderPromise(payOrderId).then((payOrderInfo) => ({
            payOrderId,
            payOrderInfo,
          }));
        })
        .then(({ payOrderId, payOrderInfo }) => {
          return submitPayOrderPromise(payOrderInfo, channelCode).then(
            (payResult) => ({ payOrderId, payOrderInfo, payResult })
          );
        })
        .then(({ payOrderId, payOrderInfo, payResult }) => {
          // 保存支付数据供回调使用
          const paymentData = {
            vipType,
            price,
            payOrderId,
            payOrderInfo,
            payResult,
            channelCode,
          };
          setPaymentData(paymentData);

          return invokePaymentPromise(payResult, channelCode).then((invokeResult) => ({
            ...paymentData,
            invokeResult,
          }));
        })
        .catch((error) => {
          const errorMessage =
            error instanceof Error ? error.message : String(error);
          setError(errorMessage);
          setIsPaying(false);
          setPaymentStep("failed");
          onFailure(errorMessage);
          throw error; // 重新抛出错误供外部处理
        });
    },
    [
      createOrderPromise,
      getPayOrderPromise,
      submitPayOrderPromise,
      invokePaymentPromise,
      onFailure,
      paymentStep,
    ]
  );


  // 重置支付状态
  const resetPaymentState = useCallback(() => {
    setIsPaying(false);
    setError(null);
    setPaymentStep("");
    setPaymentData(null);
  }, []);

  return {
    // 状态
    isPaying,
    error,
    paymentStep,
    paymentData,

    // 主要方法
    initiateVipPayment,
    resetPaymentState,
  };
}
