import { useState, useEffect    , useRef } from 'react';
import { Input, Button, Textarea } from '@arco-design/mobile-react';
import './index.less';
import { View, Text } from '@tarojs/components';
import Taro, { useRouter } from '@tarojs/taro';
import { editUser } from '@/utils/api/common/common_user';
import YkNavBar from "@/components/ykNavBar/index";
import { toast } from "@/utils/yk-common";

export default function EditItem() {
    const router = useRouter();
    const [content, setContent] = useState<string>('');
    const [hint, setHint] = useState<string>('');
    const [placeHolder, setPlaceHolder] = useState<string>('');
    const [maxLength, setMaxLength] = useState<number>(32);
    const [type, setType] = useState<number | null>(null);
    const [isFocused, setIsFocused] = useState<boolean>(false); // 焦点状态
    const [platform,setPlatform] = useState<string>("H5");            
    useEffect(() => {
        let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
        if (router?.params?.type) {
            setType(Number(router.params.type));
        }
        if (router?.params?.content) {
            const decodedContent = decodeURIComponent(router.params.content);
            // 如果内容是 "null" 字符串或者实际的null，则设置为空字符串
            setContent(decodedContent === "null" || decodedContent === null ? "" : decodedContent);
        }
    }, [router]);

    // 动态设置标题等
    useEffect(() => {
        switch (type) {
            case 1:
                setPlaceHolder('请输入昵称');
                setHint('请输入正确的昵称');
                setMaxLength(16);
                break;
            case 2:
                setPlaceHolder('请输入手机号');
                setHint('请输入正确的手机号');
                setMaxLength(11);
                break;
            case 3:
                setPlaceHolder('请输入个人微信号');
                setHint('请输入正确的微信号');
                setMaxLength(32);
                break;
            case 4:
                setPlaceHolder('请输入个性签名~');
                setHint('');
                setMaxLength(200);
                break;
            default:
                break;
        }
    }, [type]);

    const handleSave = () => {
        let formData: { nickname?: string; contactMobile?: string; wechatNumber?: string; personalProfile?: string } = {};

        switch (type) {
            case 1:
                formData = { nickname: content };
                break;
            case 2:
                formData = { contactMobile: content };
                break;
            case 3:
                formData = { wechatNumber: content };
                break;
            case 4:
                formData = { personalProfile: content };
                break;
            default:
                console.warn("Invalid type");
                return;
        }

        // 调用 editUser 接口
        editUser(formData)
            .then((res) => {
                console.log(JSON.stringify(res))
                if (res && res.code === 0) {
                    // 成功处理
                    toast("success", {
                        content: '保存成功',
                        duration: 2000,
                    });
                    Taro.eventCenter.trigger("refreshUserInfo"); // 通知父页面刷新
                    Taro.navigateBack();
                } else {
                    // 错误处理
                    toast("error", {
                        content: '保存失败',
                        duration: 2000,
                    });
                }
            })
            .catch((err) => {
                toast("error", {
                    content: '保存失败',
                    duration: 2000,
                });
            });

    };

    return (
        <View className='edit'>
               {platform !== "WX" && <YkNavBar title={type === 1 ? '昵称' : type === 2 ? '手机号' : type === 3 ? '微信号' : '个性签名'} />}
            <View className='input-container'>
                {type === 4 ? (
                    <Textarea
                        border='none'
                        className={`custom-textarea ${isFocused && !content ? 'focused' : ''}`}
                        value={content}
                        onChange={(_, value) => setContent(value)}
                        placeholder={placeHolder}
                        maxLength={maxLength}
                    />
                ) : (
                    <Input
                        border='none'
                        className={`custom-input ${isFocused && !content ? 'focused' : ''}`}
                        value={content}
                        onFocus={() => setIsFocused(true)}
                        onBlur={() => setIsFocused(false)}
                        onChange={(_, value) => setContent(value)}
                        placeholder={placeHolder}
                        maxLength={maxLength}
                    />
                )}
                {type !== 4 && <Text className={`hint ${isFocused && !content ? 'visible' : ''}`}>{hint}</Text>}
            </View>
            <View className="footer-container">
                <Button
                    className={`footer-button ${!content ? 'disabled' : ''}`}
                    onClick={handleSave}
                    disabled={!content}
                >
                    保存
                </Button>
            </View>
        </View>
    );
};

