@import '@arco-design/mobile-react/style/mixin.less';
@import '@/utils/css/variables.less';

/* Album 组件样式 */
.album-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });
}

.album-content-page {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  padding-bottom: 50px; /* 为底部标签栏留出空间 */
}

.album-list-scroll {
  flex: 1;
}

.album-list-container {
  padding: 0;
}

/* 用户头部样式 */
.user-header {
  padding-top: 10px;
  background-color: #fff;
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });
}

.user-header-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 8px;
  padding: 0 16px;
}

.user-header-avatar-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  position: relative;
}

.user-header-avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  object-fit: cover;
  background: #f7f8fa;
}

.user-header-qrcode {
  width: 24px;
  height: 24px;
  cursor: pointer;
  position: absolute;
  left: 74px;
  top: 50%;
  transform: translateY(-50%);
}

.user-header-nick {
  font-size: 15px;
  font-weight: bold;
  color: #222;
  margin-top: 0px;
  max-width: 135px;
  text-align: center;
  .use-dark-mode-query({
    color: var(--dark-font-color);
  });
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  display: inline-block;
}

.user-header-desc {
  padding: 16px 0;
}

.user-header-desc-text {
  font-size: 12px;
  color: @--text-5;
  .use-dark-mode-query({
    color: @--text-1;
  });

  // 支持文本选择的样式
  &.selectable-text {
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
  }
}

.user-header-stats {
  display: flex;
  justify-content: center;
  margin-top: 12px;
  width: 100%;
}

.user-header-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 32px;
}

.user-header-stat-num {
  font-size: 16px;
  color: #222;
  font-weight: bold;
  .use-dark-mode-query({
    color: var(--dark-font-color);
  });
}

.user-header-stat-label {
  font-size: 11px;
  color: #8c8c8c;
  margin-top: 2px;
}

/* 搜索栏样式 */
.user-header-searchbar-wrap {
  display: flex;
  align-items: center;
  background-color: #fff;
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });
}

.user-header-searchbar {
  flex: 1;
}

.user-header-filter {
  color: var(--primary-color);
  font-size: 15px;
  margin: 0px;
  cursor: pointer;
  // margin-left: 10px;
}

/* 选中标签显示区域样式 */
.selected-tags-container {
  background-color: #fff;
  padding: 8px 16px 12px 16px;
  // border-bottom: 1px solid #f0f0f0;
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });

  border-bottom: 1px solid var(--line-color);
  .use-dark-mode-query({
  border-bottom: 1px solid @dark-line-color;
});
}

.selected-tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.selected-tag-item {
  display: inline-flex;
  align-items: center;
  background-color: #e8f4ff;
  border-radius: 16px;
  padding: 4px 12px;
  font-size: 12px;
  color: #165dff;
  border: 1px solid #bedaff;
  .use-dark-mode-query({
    background-color: rgba(22, 93, 255, 0.1);
    color: #4080ff;
    border: 1px solid rgba(22, 93, 255, 0.2);
  });
}

.selected-tag-text {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

/* 排序区域样式 */
.user-header-sort {
  // margin-bottom: 10px;
  padding: 15px 0px 15px 30px;
  // height: 60px;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  // padding: 4px 16px 8px 16px;
  background-color: #fff;
  font-size: 13px;
  color: #8c8c8c;
  .use-dark-mode-query({
    background-color: @dark-background-color;
    color: #ccc;
  });
}

.move-img-add-img {
  width: 24px;
  height: 24px;
  cursor: pointer;
}

.user-header-sort-view {
  padding-right: 10px;
}

.user-header-sort-text {
  font-size: 12px;
  color: var(--primary-color);
  cursor: pointer;
}

.user-header-sort-icon {
  width: 12px;
  height: 12px;
  margin-left: 4px;
  color: var(--primary-color);
}

/* 折叠面板样式复用 */
.dynamic-collapse {
  .arco-collapse-header {
    display: inline-flex;
    height: auto;
    margin-left: 0 !important;
    .use-var(background-color, background-color);
    .use-dark-mode-query({
      background-color: var(--dark-background-color) !important;
    });
    justify-content: flex-start;
    align-items: center;
    gap: 5px;

    &::after {
      display: none !important;
    }

    .arco-collapse-icon {
      .use-var(color, sub-font-color);
      .use-dark-mode-query({
        color: var(--dark-sub-font-color) !important;
      });
    }
  }
  .arco-collapse-content-container {
    padding: 0px;
  }
}

/* 商品属性 Collapse 样式 */
.attr-collapse {
  .arco-collapse-header {
    display: inline-flex;
    padding: 0;
    height: auto;
    &::after { display: none; }
    .arco-collapse-icon {
      .use-var(color, sub-font-color);
      .use-dark-mode-query({ color: var(--dark-sub-font-color) !important; });
    }
  }
  .arco-collapse-content-container { padding: 0; }
}

/* 商品列表样式 */
.move-list {
  flex: 1;
  background: #fff;
  padding: 0 10px;
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });
}

/* 置顶商品列表样式 */
.pinned-section {
  @pinned-bg: #f7f8fa;
  background: @pinned-bg;
  padding: 8px 10px;
  border-radius: 2px;

  .use-dark-mode-query({
    background: @dark-container-background-color;
    // 确保暗黑模式下子元素也透明，覆盖其自身的暗色背景
    .arco-collapse-header,
    .move-item,
    .move-list {
      background: transparent !important;
    }
  });

  // 子元素透明，继承父容器背景（亮色模式）
  .arco-collapse-header,
  .move-item,
  .move-list {
    background: transparent !important;
  }
}

.pinned-title {
  font-size: 15px;
  // font-weight: bold;
  // color: #F53F3F;
  display: flex;
  align-items: center;
}

.move-group-title {
  font-size: 15px;
  color: #222;
  margin: 10px 0 8px 0px;
  .use-dark-mode-query({
    color: var(--dark-font-color);
  });
}

.move-item-wrap {
  display: flex;
  flex-direction: column;
  padding: 10px 0px 10px 30px;
}

.move-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  background: #fff;
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });
}

.move-img {
  width: 80px;
  height: 80px;
  border-radius: 6px;
  object-fit: cover;
  background: #f7f8fa;
}

.move-img-add-img {
  width: 75px;
  height: 75px;
  cursor: pointer;
}

.move-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
}

.move-desc {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  height: 56px;
}

.move-title-text {
  padding-bottom: 3px;
  font-size: 12px;
  line-height: 1.4;
  color: #1D2129;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  .use-dark-mode-query({
    color: var(--dark-font-color);
  });

  // 支持文本选择的样式
  &.selectable-text {
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
  }
}

.move-price {
  color: #F53F3F;
  font-size: 12px;
  font-weight: 500;
}

.move-actions {
  display: flex;
  height: 24px;
  align-items: center;
  justify-content: space-between;
  gap: 6px;
  font-size: 12px;
  color: #002C8C;
}

.move-action-wrap {
  display: flex;
  align-items: center;
  gap: 6px;
}

.move-action-box {
  display: flex;
  align-items: center;
  gap: 8px;
}

.move-action-btn {
  color: #002C8C;
  cursor: pointer;
}

.move-action-share {
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  border-radius: 4px;
  padding: 0px 16px;
}

// .move-action-add-cart {
//   stroke: var(--primary-color);
//   fill: var(--primary-color);
// }

/* 商品属性样式 */
.move-attrs-wrap {
  margin-top: 5px;
}

.move-attrs {
  font-size: 12px;
  font-weight: 500;
  color: #4E5969;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
}

.card-attr-content {
  background: #F2F3F5;
  border-radius: 8px;
  padding: 12px;
  margin-top: 8px;
  margin-right: 10px;
  box-shadow: none;
  .use-dark-mode-query({
    background: @dark-container-background-color;
    box-shadow: none;
  });
}

.attr-row {
  display: flex;
  align-items: flex-start;
}

.attr-row + .attr-row {
  border-top: 1px dashed #eee;
  padding-top: 8px;
  margin-top: 8px;
  .use-dark-mode-query({
    border-top: 1px dashed #333;
  });
}

.attr-label {
  font-size: 12px;
  color: #222;
  min-width: 40px;
  margin-top: 0px;
  .use-dark-mode-query({ color: var(--dark-font-color); });
}

.attr-values {
  display: flex;
  flex-wrap: wrap;
  gap: 8px 16px;
}

.attr-value {
  font-size: 12px;
  color: #444;
  background: none;
  border: none;
  padding: 0;
  margin-bottom: 0px;
  position: relative;
  cursor: pointer;
  transition: none;
  .use-dark-mode-query({ color: var(--dark-font-color); });

  &.selected {
    color: #165DFF;
    font-weight: bold;
  }

  &:not(:last-child)::after {
    content: '|';
    position: static;
    transform: none;
    margin-left: 16px;
    height: auto;
    background: none;
    color: #e5e6eb;
    .use-dark-mode-query({ color: #333; });
  }
  &:last-child::after {
    display: none;
  }
}

/* 底部操作栏样式 */
.bottom-tabs {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 50px;
  background-color: #fff;
  border-top: 1px solid var(--line-color);
  .use-dark-mode-query({
  border-top: 1px solid @dark-line-color;
});
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  .use-dark-mode-query({
    background-color: @dark-background-color;
  });
}

.bottom-tab-item {
  flex: 1;
  text-align: center;
  cursor: pointer;
}

.bottom-tab-text {
  font-size: 15px;
  color: #222;
  .use-dark-mode-query({
    color: var(--dark-font-color);
  });
}

/* 加载状态样式 */
.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;
  background: #fff;
  .use-dark-mode-query({
    background: @dark-background-color;
  });
}

.loading-text {
  font-size: 14px;
  color: #86909C;
  display: flex;
  align-items: center;
  
  &::before {
    content: '';
    width: 16px;
    height: 16px;
    margin-right: 8px;
    border: 2px solid #E5E6EB;
    border-top-color: #165DFF;
    border-radius: 50%;
    animation: loading-spin 0.8s linear infinite;
  }
}

@keyframes loading-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;
  background: #fff;
  .use-dark-mode-query({
    background: @dark-background-color;
  });
}

.no-more-text {
  font-size: 14px;
  color: #86909C;
  position: relative;
  padding: 0 16px;
  
  &::before,
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 60px;
    height: 1px;
    background: #E5E6EB;
    .use-dark-mode-query({
      background: #333;
    });
  }
  
  &::before {
    right: 100%;
    margin-right: 16px;
  }
  
  &::after {
    left: 100%;
    margin-left: 16px;
  }
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  background: #fff;
  .use-dark-mode-query({
    background: @dark-background-color;
  });
}

.empty-image {
  width: 83px;
  height: 82px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 14px;
  color: #86909C;
}



.collapse-icon {
  transition: transform 0.2s;
}


