import { View, Text } from "@tarojs/components";
import { useLoad } from "@tarojs/taro";
import "./index.less";
import { Input, Button, CountDown, Cell } from "@arco-design/mobile-react";
import React from "react";
import Taro from "@tarojs/taro";
import { changePassword, getSmsCode } from "@/utils/api/common/common_user";
import { toast } from "@/utils/yk-common";
// 组件
import YkNavBar from "@/components/ykNavBar/index";
import { useEffect,useRef , useState } from "react";
export default function bindMobilePhone() {
  
  // 用户信息
  const [userInfo, setUserInfo] = React.useState<any>(false); // 类型：any[]
  // 验证码
  const [code, setCode] = React.useState("");
  // 密码
  const [password, setPassword] = React.useState("");
  // 二次密码
  const [repeat_password, setRepeatPassword] = React.useState("");
  //验证码倒计时
  const [countDown, setCountDown] = React.useState(60);
  // 是否开启倒计时
  const [autoStart, setAutoStart] = React.useState(false);
  const [platform,setPlatform] = useState<string>("H5");

  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
  }, []);
  // 倒计时结束
  const onFinishCountDown = () => {
    setAutoStart(false);
    setCountDown(60);
  };

  // 获取验证码
  const getCode = () => {
    if (userInfo.mobile.length !== 11) {
      toast("info", {
        content: "请输入正确的手机号",
        duration: 2000,
      });
      return;
    }
    setAutoStart(true);
    let data = {
      mobile: userInfo.mobile,
      scene: 3, 
    };
    getSmsCode(data)
      .then((res: any) => {
        console.log("getSmsCode", res);
        if (res && res.code == 0) {
          toast("success", {
            content: "验证码发送成功",
            duration: 2000,
          });
        } else {
          toast("error", {
            content: res.msg,
            duration: 2000,
          });
        }
      })
      .catch((err) => {
        toast("error", {
          content: err,
          duration: 2000,
        });
      });
  };

  // 确认绑定
  const handleConfirmBind = () => {
    if (code.length < 4) {
      toast("info", {
        content: "请输入正确的验证码",
        duration: 2000,
      });
      return;
    }
    if (password !== repeat_password) {
      toast("info", {
        content: "两次密码不一致",
        duration: 2000,
      });
      return;
    }
    // if (
    //   userInfo.unionid == "" ||
    //   !userInfo.unionid ||
    //   userInfo.unionid == undefined
    // ) {
    //   Taro.showToast({
    //     title: "请先绑定微信号",
    //     icon: "none",
    //   });
    //   return;
    // }
    let data = {
      code: code,
      password: password,
    };
    changePassword(data)
      .then((res: any) => {
        if (res && res.code == 0) {
          toast("success", {
            content: "绑定成功",
            duration: 2000,
          });
          setCode("");
          setAutoStart(false);
          setCountDown(60);
        } else {
          toast("error", {
            content: res.msg,
            duration: 2000,
          });
        }
      })
      .catch((err) => {
        toast("error", {
          content: err,
          duration: 2000,
        });
      });
  };

  useLoad(() => {
    // 获取用户信息
    setUserInfo(Taro.getStorageSync("userInfo"));
  });

  return (
    <View className="loginedResetPasswordPageContent">
       {platform!== "WX" &&<YkNavBar title="重置密码" />}
      <View className="pageDescribe">
        <Text className="pageDescribe-title">重置密码</Text>
        <Text className="pageDescribe-desc">
          验证手机号后即可重新设置密码
        </Text>
      </View>
      <View className="inputBox">
        {/* <Cell label="手机号"  bordered={false}> */}
          <Input
            label="手机号"
            clearable
            border="none"
            value={userInfo.mobile}
            disabled
          />
        {/* </Cell> */}
        <Input
          label="验证码"
          placeholder="请输入验证码"
          type="tel"
          validator={(val) => val.length <= 8}
          className="demo-input-btn-input"
          clearable
          value={code}
          onChange={(_, value) => setCode(value)}
          onClear={() => setCode("")}
          border="none"
          suffix={
            !autoStart ? (
              <Button
                inline
                size="mini"
                type="ghost"
                onClick={() => getCode()}
              >
                获取验证码
              </Button>
            ) : (
              <CountDown
                millisecond
                format="ss"
                time={{
                  days: 0,
                  hours: 0,
                  minutes: 0,
                  seconds: countDown,
                  milliseconds: 0,
                }}
                autoStart={autoStart}
                onFinish={onFinishCountDown}
                renderChild={(timeData) => (
                  <Button
                    inline
                    size="mini"
                    type="ghost"
                    disabled
                  >
                    重新获取({timeData.seconds}s)
                  </Button>
                )}
              />
            )
          }
        />
        <Input
          label="设置新密码"
          placeholder="请输入设置新密码"
          clearable
          border="none"
          value={password}
          onChange={(_, value) => setPassword(value)}
          onClear={() => setPassword("")}
        />
        <Input
          label="确认新密码"
          placeholder="请输入新密码"
          clearable
          border="none"
          value={repeat_password}
          onChange={(_, value) => setRepeatPassword(value)}
          onClear={() => setRepeatPassword("")}
        />
      </View>
      <View className="confirmBtnBox">
        <Button
          needActive
          onClick={() => handleConfirmBind()}
          disabled={!userInfo || userInfo?.mobile.length < 11 || code.length < 4}
        >
          完成
        </Button>
      </View>
    </View>
  );
}
