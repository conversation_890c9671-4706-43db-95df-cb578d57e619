import React, { useState, useEffect, useRef } from "react";
import Taro from "@tarojs/taro";
import { Dialog, ActionSheet } from "@arco-design/mobile-react";
import Album from "@/components/Album";
import {
  getMyAlbumList,
  getUserHomeTopData,
  deleteDynamic,
  updateDynamic,
  editUser,
  getTreeCatalogApi,
  dynamicRefresh,
} from "@/utils/api/common/common_user";
import { toast } from "@/utils/yk-common";
import ClassifyModal from "@/pageDynamic/categoryDynamic/ClassifyModal";
import wx from "weixin-webview-jssdk";
import { useSetPermission } from "@/stores/permissionStore";
import {
  AuthTypes,
  AuthStatusAndroid,
  AuthStatusIos,
} from "@/utils/config/authTypes";
import PermissionPopup from "@/components/PermissionPopup";
import { usePermission } from "@/hooks/usePermission";
import { useGlobalCallbacks } from "@/utils/globalCallbackManager";
interface Tag {
  id: number;
  userId: number | null;
  parentId: number | null;
  dynamicsId: string | null;
  name: string;
  coverImage: string;
  type: number;
  sort: number | null;
  isTop: number | null;
  sortType: number | null;
  children: Tag[];
}

export default function MyAlbum() {
  // 从本地存储获取用户信息
  const localUserInfo: any = Taro.getStorageSync("userInfo") || {};

  const currentItem = useRef<any>(null);
  // 添加一个状态来触发数据重新加载
  const [refreshKey, setRefreshKey] = useState(0);
  const [classifyModalVisible, setClassifyModalVisible] = useState(false);
  const [selectedTags, setSelectedTags] = useState<Tag[]>([]);
  // 控制 Album 的外部排序，同步子组件内部 sortType
  const [externalSortType, setExternalSortType] = useState<number | undefined>(
    undefined
  );
  // 自定义权限同意处理，处理首页特有的逻辑
  const customWebPermissonConsent = () => {
    // 下载操作
    if (platformRef.current === "Android") {
      downloadConfirm(currentItem.current);
    } else if (platformRef.current === "IOS") {
      window.webkit.messageHandlers.checkPermission.postMessage("");
    }
    return true;
  };

  // 使用全局权限管理
  const {
    initPermissions,
    hasPermission,
    requestPermission,
    webPermissonConsent,
    webPermissonDeny,
    permissionPopupProps,
    platformRef,
    authConfirmType,
  } = usePermission(customWebPermissonConsent);
  // 事件总线：监听发布页触发的刷新事件（适用于多页面同步刷新）
  useEffect(() => {
    const handler = () => setRefreshKey((prev) => prev + 1);
    Taro.eventCenter.on("refreshAlbumList", handler);
    return () => {
      Taro.eventCenter.off("refreshAlbumList", handler);
    };
  }, []);

  // 注册 H5/原生容器下载回调，避免原生回调调用未定义函数
  useEffect(() => {
    const cleanup = initPermissions();

    // 定义下载回调函数
    const webDownloadSuc = () => {
      if (currentItem.current.pictures && currentItem.current.content) {
        toast("success", {
          content: "图片下载成功，动态文案已复制",
          duration: 2000,
        });
      }

      if (currentItem.current.pictures && currentItem.current.content === "") {
        toast("success", {
          content: "图片下载成功",
          duration: 2000,
        });
      }
    };

    const webDownloadFail = () => {
      try {
        toast("error", { content: "下载失败", duration: 2000 });
      } catch (e) {}
    };

    // 使用全局回调管理器注册下载回调
    const callbackCleanup = useGlobalCallbacks("myAlbum", {
      webDownloadSuc: webDownloadSuc,
      webDownloadFail: webDownloadFail,
    });

    return () => {
      try {
        callbackCleanup && callbackCleanup();
        cleanup && cleanup();
      } catch (e) {}
    };
  }, []);

  // 获取用户信息
  const handleGetUserInfo = async (userId: string | number) => {
    try {
      const res: any = await getUserHomeTopData({ userId, homePageCountType: 2 });
      if (res && res.code === 0 && res.data) {
        return res.data;
      }
      return {};
    } catch (error) {
      console.error("getUserInfo failed:", error);
      return {};
    }
  };

  // 获取相册列表
  const handleGetAlbumList = async (params: any) => {
    try {
      const res: any = await getMyAlbumList(params);
      return res;
    } catch (error) {
      console.error("获取相册列表失败:", error);
      return { code: -1, msg: "获取失败" };
    }
  };

  // 删除动态
  const handleDelete = async (id: string | number): Promise<any> => {
    return new Promise((resolve) => {
      Dialog.confirm({
        platform: "ios",
        className: "dialog-input-demo",
        title: "温馨提示",
        contentAlign: "left",
        children: (
          <div className="dialog-input-demo-hint">
            删除动态后不可恢复，确认删除？
          </div>
        ),
        onOk: async () => {
          Taro.showLoading({
            title: "删除中...",
            mask: true,
          });

          try {
            const res: any = await deleteDynamic(id);
            if (res && res.code === 0) {
              setTimeout(() => {
                Taro.hideLoading();
                toast("success", {
                  content: "删除成功",
                  duration: 2000,
                });
                // 删除成功后刷新数据
                setRefreshKey((prev) => prev + 1);
                resolve(res);
              }, 1000);
            }
          } catch (error) {
            Taro.hideLoading();
            toast("error", {
              content: "删除失败，请重试",
              duration: 2000,
            });
            console.error("删除失败:", error);
            resolve({ code: -1, msg: "删除失败" });
          }
        },
        onCancel: () => {
          console.log("用户取消了操作");
          resolve({ code: -2, msg: "用户取消" });
        },
      });
    });
  };

  // 编辑
  const handleEdit = (item: any) => {
    Taro.setStorageSync("releaseDynamicList", item);
    Taro.navigateTo({
      url: `/pageDynamic/releaseDynamic/index?type=2`,
    });
  };

  // 查看详情
  const handleDetail = (item: any) => {
    Taro.navigateTo({
      url: `/pageDynamic/detail/index?dynamicId=${item.id}&dynamicUserId=${item.userId}`,
    });
  };

  // 下载
  const handleDownload = (item: any) => {
    console.log("handleDownload", item);
    currentItem.current = item;
    if (platformRef.current === "HM" || platformRef.current === "WX") {
      downloadConfirm(item);
    } else {
      // 检查存储权限
      if (!hasPermission(AuthTypes.STORAGE)) {
        console.log("没有权限");
        // 如果没有权限，请求权限
        requestPermission(AuthTypes.STORAGE);
        return;
      }
      // 有权限则执行下载
      downloadConfirm(item);
    }
  };

  const downloadConfirm = (item: any) => {
    let imageList = item.pictures.split(",");
    if (platformRef.current === "Android") {
      window.downloadImg.downloadImg(item.pictures);
    } else if (platformRef.current === "HM") {
      window.harmony.downloadImg(item.pictures);
    } else if (platformRef.current === "WX") {
      let imgs = imageList.join("|");
      wx.miniProgram.navigateTo({
        url: "/pages/downloadImgs/index?imgs=" + encodeURIComponent(imgs),
      });
    } else {
      for (let i = 0; i < imageList.length; i++) {
        // if (platformRef.current === "Android") {
        //   window.downloadImg.downloadImg(imageList[i]);
        // } else
        if (platformRef.current === "IOS") {
          window.webkit.messageHandlers.saveImgWithUrlStr.postMessage(
            imageList[i]
          );
        }
        // else if (platformRef.current === "HM") {
        //   window.harmony.downloadImg(imageList[i]);
        // }
      }
    }
  };

  // 刷新
  const handleRefresh = async (id: string) => {
    try {
      const res: any = await dynamicRefresh({ dynamicsId: id });
      if (res && res.code === 0) {
        toast("success", {
          content: "刷新成功",
          duration: 2000,
        });
        // 刷新成功后重新加载数据
        setRefreshKey((prev) => prev + 1);
      }
    } catch (error) {
      toast("error", {
        content: "刷新失败",
        duration: 2000,
      });
    }
  };

  // 置顶/取消置顶
  const handleToggleTop = async (id: string | number, isTop: number) => {
    try {
      const res: any = await updateDynamic({ id, isTop });
      if (res && res.code === 0) {
        toast("success", {
          content: isTop === 1 ? "置顶成功" : "取消置顶成功",
          duration: 2000,
        });

        // 置顶操作成功后，需要刷新数据
        // 由于置顶状态变化会影响数据排序，需要重新获取数据
        setTimeout(() => {
          // 延迟刷新，确保后端数据已更新
          setRefreshKey((prev) => prev + 1);
        }, 500);
      }
    } catch (error) {
      toast("error", {
        content: "操作失败",
        duration: 2000,
      });
    }
  };

  // 创建
  const handleCreate = () => {
      Taro.navigateTo({
        url: "/pageDynamic/releaseDynamic/index",
      });
  };

  // 批量操作
  const handleBatch = () => {
    ActionSheet.open({
      items: [
        {
          content: "批量编辑",
          onClick: () => {
            Taro.navigateTo({ url: `/pageDynamic/album/batchEdit/index` });
          },
        },
        {
          content: "批量上下架",
          onClick: () => {
            Taro.navigateTo({
              url: `/pageDynamic/album/upAndDownShelves/index`,
            });
          },
        },
        {
          content: "批量删除",
          onClick: () => {
            Taro.navigateTo({ url: `/pageDynamic/album/batchDelete/index` });
          },
          status: "danger",
        },
      ],
      cancelText: "取消",
    });
  };

  // 分享
  const handleShare = () => {
    toast("info", {
      content: "分享功能开发中",
      duration: 2000,
    });
  };

  // 排序
  const handleSort = () => {
    ActionSheet.open({
      items: [
        {
          content: "按发布时间排序",
          onClick: () => {
            Dialog.confirm({
              platform: "ios",
              className: "dialog-input-demo",
              title: "确认修改商品排序",
              contentAlign: "left",
              children: (
                <div
                  className="dialog-input-demo-hint"
                  style={{ textAlign: "center" }}
                >
                  确认后,客户在全部中看到的所有商品将按发布时间排序。
                </div>
              ),
              onOk: async () => {
                try {
                  const res = await editUser({ homePageSort: 1 });
                  if (res && res.code === 0) {
                    toast("success", {
                      content: "排序方式已更新",
                      duration: 2000,
                    });
                    // 更新排序后刷新数据
                    setExternalSortType(1);
                    setRefreshKey((prev) => prev + 1);
                  } else {
                    toast("error", {
                      content: res.msg || "更新失败",
                      duration: 2000,
                    });
                  }
                } catch (error) {
                  toast("error", {
                    content: "更新失败，请重试",
                    duration: 2000,
                  });
                  console.error("更新排序失败:", error);
                }
              },
              onCancel: () => {
                // 用户点击取消时执行
              },
            });
          },
        },
        {
          content: "按更新时间排序",
          onClick: () => {
            Dialog.confirm({
              platform: "ios",
              className: "dialog-input-demo",
              title: "确认修改商品排序",
              contentAlign: "left",
              children: (
                <div
                  className="dialog-input-demo-hint"
                  style={{ textAlign: "center" }}
                >
                  确认后,客户在全部中看到的所有商品将按更新时间排序。
                </div>
              ),
              onOk: async () => {
                try {
                  const res = await editUser({ homePageSort: 2 });
                  if (res && res.code === 0) {
                    toast("success", {
                      content: "排序方式已更新",
                      duration: 2000,
                    });
                    // 更新排序后刷新数据
                    setExternalSortType(2);
                    setRefreshKey((prev) => prev + 1);
                  } else {
                    toast("error", {
                      content: res.msg || "更新失败",
                      duration: 2000,
                    });
                  }
                } catch (error) {
                  toast("error", {
                    content: "更新失败，请重试",
                    duration: 2000,
                  });
                  console.error("更新排序失败:", error);
                }
              },
              onCancel: () => {
                // 用户点击取消时执行
              },
            });
          },
        },
      ],
      cancelText: "取消",
    });
  };

  // 商品分类
  const handleClassify = () => {
    setClassifyModalVisible(true);
  };

  // 确认选择分类
  const handleClassifyConfirm = (tags: Tag[]) => {
    setSelectedTags(tags);
    // 跳转到新页面，传递选择的标签信息
    const tagIds = tags.map((tag) => tag.id).join(",");
    const tagNames = tags.map((tag) => tag.name).join(",");

    Taro.navigateTo({
      url: `/pageDynamic/categoryDynamic/tagResult/index?userId=${
        localUserInfo?.id
      }&tagIds=${tagIds}&tagNames=${encodeURIComponent(tagNames)}`,
    });
  };

  // 关闭分类弹窗
  const handleClassifyClose = () => {
    setClassifyModalVisible(false);
  };

  return (
    <>
      <Album
        title="我的相册"
        userId={localUserInfo?.id}
        isOwnAlbum={true}
        externalRefreshKey={refreshKey} // 使用externalRefreshKey进行局部刷新
        onGetUserInfo={handleGetUserInfo}
        onGetAlbumList={handleGetAlbumList}
        onDeleteDynamic={handleDelete}
        onUpdateDynamic={updateDynamic}
        onCreate={handleCreate}
        onEdit={handleEdit}
        onDetail={handleDetail}
        onDownload={handleDownload}
        onRefresh={handleRefresh}
        onToggleTop={handleToggleTop}
        onBatch={handleBatch}
        onShare={handleShare}
        onSort={handleSort}
        onClassify={handleClassify}
        showCartModal
        externalSortType={externalSortType}
      />{" "}
      {/* 分类选择弹窗 */}
      <ClassifyModal
        visible={classifyModalVisible}
        userId={localUserInfo?.id as string}
        onClose={handleClassifyClose}
        onConfirm={handleClassifyConfirm}
        selectedIds={selectedTags.map((tag) => tag.id)}
        isOwnAlbum={true} // 自己的相册页面，设置为 true
      />
    </>
  );
}
