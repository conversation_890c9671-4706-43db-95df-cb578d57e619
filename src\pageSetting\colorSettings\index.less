@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageSetting/colorSettings/index"] {
  /* 在这里设置样式 */
  background-color: var(--page-primary-background-color) !important;
  .use-dark-mode-query({
    background-color: @dark-background-color !important;
  });

  .demo-picker-color {
    i {
      width: 14px;
      height: 14px;
      border-radius: 2px;
      .set-prop-with-rtl(margin-right, 8px);
      border: 1px solid rgba(#000000, 0.2);
      .use-dark-mode-query({
        border-color: @background-color !important;
      });
    }
    i,
    span {
      display: inline-block;
      vertical-align: middle;
    }
  }

  .module {
    width: 100%;
    box-sizing: border-box;
    padding: 10px;

    &-title {
      width: 100%;
      height: 32px;
      display: flex;
      align-items: center;

      &-text {
        font-size: 13px;
        color: #999999;
      }
    }

    &-content {
      width: 100%;
      box-sizing: border-box;
      border-radius: 10px;
      overflow: hidden;
    }
  }
}
