import React, { useState } from "react";
import Taro from "@tarojs/taro";
import Album from "@/components/Album";
import { toast } from "@/utils/yk-common";
import { Toast } from "@arco-design/mobile-react";
import { fetchCartCountUtil } from "@/utils/cartUtils";
import {
  getMyAlbumList,
  getTreeCatalogApi,
  getUserHomeTopData,
  addCart,
} from "@/utils/api/common/common_user";
import ClassifyModal from '@/pageDynamic/categoryDynamic/ClassifyModal';

interface Tag {
  id: number;
  userId: number | null;
  parentId: number | null;
  dynamicsId: string | null;
  name: string;
  coverImage: string;
  type: number;
  sort: number | null;
  isTop: number | null;
  sortType: number | null;
  children: Tag[];
}

export default function OtherAlbum() {
  // 从路由参数获取用户ID
  const { userId } = Taro.getCurrentInstance().router?.params || {};

  // 分类弹窗状态
  const [classifyModalVisible, setClassifyModalVisible] = useState(false);
  const [selectedTags, setSelectedTags] = useState<Tag[]>([]);

  // 获取用户信息
  const handleGetUserInfo = async (targetUserId: string | number) => {
    try {
      const res: any = await getUserHomeTopData({ userId: targetUserId });
      if (res && res.code === 0 && res.data) {
        return res.data;
      }
      return {};
    } catch (error) {
      console.error("getUserInfo failed:", error);
      return {};
    }
  };

  // 获取相册列表
  const handleGetAlbumList = async (params: any) => {
    try {
      const res: any = await getMyAlbumList(params);
      return res;
    } catch (error) {
      console.error("获取相册列表失败:", error);
      return { code: -1, msg: "获取失败" };
    }
  };

  // 查看详情
  const handleDetail = (item: any) => {
    Taro.navigateTo({
      url: `/pageDynamic/detail/index?dynamicId=${item.id}&dynamicUserId=${item.userId}`,
    });
  };

  // 下载
  const handleDownload = (item: any) => {
    Taro.showLoading({
      title: "下载中...",
      mask: true,
    });
    // if(platformRef.current === "Android"){  }

    Taro.downloadFile({
      url: item.pictures,
      success: (res) => {
        Taro.saveFile({
          tempFilePath: res.tempFilePath,
          success: (res) => {
            Taro.hideLoading();
            toast("info", {
              content: "下载成功",
              duration: 2000,
            });
          },
          fail: (res) => {
            Taro.hideLoading();
            toast("info", {
              content: "下载失败",
              duration: 2000,
            });
          },
        });
      },
      fail: (res) => {
        Taro.hideLoading();
        toast("info", {
          content: "下载失败",
          duration: 2000,
        });
      },
    });
  };

  // 分享
  const handleShare = () => {
    toast("info", {
      content: "分享功能开发中",
      duration: 2000,
    });
  };

  // 商品分类
  const handleClassify = () => {
    // 清空已选tags
    setSelectedTags([]);
    setClassifyModalVisible(true);
  };

  // 确认选择分类
  const handleClassifyConfirm = (tags: Tag[]) => {
    setSelectedTags(tags);

    // 跳转到新页面，传递选择的标签信息
    const tagIds = tags.map((tag) => tag.id).join(",");
    const tagNames = tags.map((tag) => tag.name).join(",");

    Taro.navigateTo({
      url: `/pageDynamic/categoryDynamic/tagResult/index?userId=${userId}&tagIds=${tagIds}&tagNames=${encodeURIComponent(
        tagNames
      )}`,
    });
  };

  // 关闭分类弹窗
  const handleClassifyClose = () => {
    setClassifyModalVisible(false);
  };

  return (
    <>
      <Album
        title='Ta的相册'
        userId={userId}
        isOwnAlbum={false}
        onGetUserInfo={handleGetUserInfo}
        onGetAlbumList={handleGetAlbumList}
        onDetail={handleDetail}
        onDownload={handleDownload}
        onClassify={handleClassify}
        showCreate={false} // 他人的相册不显示创建按钮
        showSort={false}
        showContactPopup // 启用内置联系弹窗
        showCartModal // 启用内置购物车弹窗
        showMore // 启用内置更多弹窗
      />

      {/* 分类选择弹窗 */}
      <ClassifyModal
        visible={classifyModalVisible}
        userId={userId as string}
        onClose={handleClassifyClose}
        onConfirm={handleClassifyConfirm}
        selectedIds={selectedTags.map((tag) => tag.id)}
        isOwnAlbum={false} // 他人相册页面，设置为 false
      />
    </>
  );
}
