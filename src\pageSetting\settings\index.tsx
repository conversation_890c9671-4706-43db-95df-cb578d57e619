import { useState, useEffect, useRef } from "react";
import { View, Text } from "@tarojs/components";
import "./index.less";
import { Cell, Dialog, Button, Toast } from "@arco-design/mobile-react";
import Taro from "@tarojs/taro";
import wx from "weixin-webview-jssdk";
// 组件
import YkNavBar from "@/components/ykNavBar/index";
import BottomPopup from "@/components/BottomPopup";

import { getWechatUser, bindWechat, unbindWechat } from "@/utils/api/common/common_user";

// const appVersion = Taro.getStorageSync(`${APP_NAME}_version`) || "";
const appVersion = window.localStorage.getItem(`${APP_NAME}_version`) || "";
export default function Index() {
  const [isPopupVisible, setPopupVisible] = useState(false);
  const [wxNickname, setWxNickname] = useState("");
  const [storageSize, setStorageSize] = useState("计算中...");
  const [platform,setPlatform] = useState<string>("H5"); 
  const popupType = useRef(null);

  // 前往绑定手机号页面
  const getInPageBindMobilePhone = () => {
    Taro.navigateTo({
      url: "/pageSetting/bindMobilePhone/index",
    });
  };

  // 前往重置密码页面
  const getInLoginedResetPassword = () => {
    const userInfo: any = Taro.getStorageSync("userInfo") || {};
    const hasMobile = !!userInfo.mobile;
    if (!hasMobile) {
      Dialog.confirm({
        title: "提示",
        children: "您暂未绑定手机号，请前往绑定",
        platform: "ios",
        okText: "去绑定",
        cancelText: "取消",
        onOk: () => {
          Taro.navigateTo({ url: "/pageSetting/bindMobilePhone/index" });
        }
      });
      return;
    }
    Taro.navigateTo({ url: "/pageSetting/loginedResetPassword/index" });
  };

  // 前往版本更新页面
  const handleVersionUpdate = () => {
    if(platform === "Android"){
      window.checkUpdate.checkUpdate();
    }else if(platform === "HM"){
      window.harmony.checkUpdate();
    }
  };

  // 退出登录
  const exitLogin = () => {
    let _window: any = window;
    _window.modalInstance = Dialog.confirm({
      title: "提示",
      children: "确定要退出登录吗？",
      platform: "ios",
      okText: "确定",
      cancelText: "取消",
      onOk: () => {
        // 清除用户信息
        Taro.removeStorageSync("userInfo");
        // 关闭所有页面，跳转到登录页面
        if (platform === "WX") {
          wx.miniProgram.reLaunch({
            url: "/pages/index/index?isLoginOut=true",
          });
        } else {
        Taro.reLaunch({
            url: "/pages/login/index",
          });
        }
        

      },
    });
  };

  // 显示手机号
  const showMobilePhone = () => {
    let userInfo: any = Taro.getStorageSync("userInfo");
    if (!userInfo.mobile) {
      return "";
    }
    return `${userInfo.mobile.slice(0, 3)} ${userInfo.mobile.slice(3, 7)} ${userInfo.mobile.slice(7)}`.trim();
  };

  //显示微信登录昵称
  const showWechatNickname = () => {
    return wxNickname || "";
  };

  // 获取缓存大小
  const getCacheSize = () => {
    Taro.getStorageInfo({
      success: (res) => {
        console.log('Storage info:', res);
        // Taro的getStorageInfo返回的是keys数组和limitSize、currentSize等
        // 在H5环境下，currentSize可能不准确，我们通过keys来计算
        let totalSize = 0;

        if (res.currentSize && res.currentSize > 0) {
          // 如果有currentSize，直接使用
          totalSize = res.currentSize;
        } else {
          // 否则通过计算所有存储项的大小来估算
          res.keys.forEach(key => {
            try {
              const value = Taro.getStorageSync(key);
              if (value) {
                totalSize += JSON.stringify(value).length;
              }
            } catch (e) {
              console.warn('Error getting storage item:', key, e);
            }
          });
        }

        console.log('Calculated total size:', totalSize);

        if (totalSize < 1024) {
          setStorageSize(totalSize + 'B');
        } else if (totalSize / 1024 >= 1 && totalSize / 1024 / 1024 < 1) {
          setStorageSize(Math.floor((totalSize / 1024) * 100) / 100 + 'KB');
        } else if (totalSize / 1024 / 1024 >= 1) {
          setStorageSize(Math.floor((totalSize / 1024 / 1024) * 100) / 100 + 'M');
        }
      },
      fail: (error) => {
        console.error('Failed to get storage info:', error);
        setStorageSize('0B');
      }
    });
  };

  // 清理缓存
  const clearCache = () => {
    Dialog.confirm({
      title: "提示",
      children: "确定要清理缓存吗？",
      platform: "ios",
      okText: "确定",
      cancelText: "取消",
      onOk: () => {
        const userInfo = Taro.getStorageSync("userInfo");
        const startPrivacy = Taro.getStorageSync("startPrivacy");
        console.log("startPrivacy",startPrivacy);
        const version = window.localStorage.getItem(`${APP_NAME}_version`);
        // const phone_model = Taro.getStorageSync("phone_model");
        const phone_model = window.localStorage.getItem('phone_model');
        // const mobile_model = Taro.getStorageSync("mobile_model");
        const mobile_model = window.localStorage.getItem('mobile_model');
        Taro.clearStorage({
          success: () => {
            // 保留用户信息，其他缓存清除
            if (userInfo) {
              Taro.setStorageSync("userInfo", userInfo);
            }
            if (startPrivacy) {
              Taro.setStorageSync("startPrivacy", startPrivacy);
            }
            if (version) {
              window.localStorage.setItem(`${APP_NAME}_version`, version);
            }
            if (phone_model) {
              window.localStorage.setItem('phone_model', phone_model);
              // Taro.setStorageSync("phone_model", phone_model);
            }
            if (mobile_model) {
              window.localStorage.setItem('mobile_model', mobile_model);
              // Taro.setStorageSync("mobile_model", mobile_model);
            }
            Toast.success("缓存清理成功");
            getCacheSize(); // 重新获取缓存大小
          },
          fail: () => {
            Toast.info("缓存清理失败");
          }
        });
      },
    });
  };

  const openPopup = (type) => {
    popupType.current = type;
    setPopupVisible(true);
  };

  const handleBindWechat = () => {
    console.log("[WX_BIND] Click bind wechat cell");
    // const userInfo: any = Taro.getStorageSync("userInfo") || {};
    // if (!userInfo.openid) {
    //   console.log("[WX_BIND] No local openid found, start binding flow");
      startWechatBind();
    // } else {
    //   console.log("[WX_BIND] Found local openid, show popup for unbind/replace");
    //   openPopup("bindWechat");
    // }
  };

  // 启动微信授权并绑定流程
  const startWechatBind = () => {
    console.log("[WX_BIND] Prepare to start native wechat authorization");
    const bindWithCode = (code: string) => {
      console.log("[WX_BIND] Received code from native:", code);
      const data = {
        type: 32,
        code,
        state: `${Date.now()}_${Math.random().toString(36).slice(2)}`,
      } as any;
      console.log("[WX_BIND] Call bindWechat API with payload:", data);
      bindWechat(data).then((res: any) => {
        console.log("[WX_BIND] bindWechat API response:", res);
        if (res && res.code === 0) {
          const userInfo: any = Taro.getStorageSync("userInfo") || {};
          const newUserInfo = { ...userInfo, openid: res.data };
          Taro.setStorageSync("userInfo", newUserInfo);
          Toast.success("微信号已绑定当前账号");
          // 绑定成功后，刷新昵称显示
          getWechatUser({ type: 32 }).then((r: any) => {
            if (r && r?.code === 0) {
              setWxNickname(r?.data?.nickname || "");
            }
          });
        } else {
          Toast.info(res.msg || "微信号已被其他账号绑定");
        }
        // 清理回调
        (window as any).wxLoginCode = null;
      });
    };

    // 注册一次性回调
    (window as any).wxLoginCode = (data: string) => {
      console.log("[WX_BIND] window.wxLoginCode callback invoked with:", data);
      if (!data) {
        Toast.info("获取微信授权失败");
        (window as any).wxLoginCode = null;
        return;
      }
      bindWithCode(data);
    };

    // 触发原生授权
    try {
      const uaAll = window.navigator.userAgent;
      const isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
      const isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
      const isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
      if (isAndroid && (window as any).wxLogin?.wxLogin) {
        (window as any).wxLogin.wxLogin();
      } else if (isIos && (window as any).webkit?.messageHandlers?.wxLogin) {
        (window as any).webkit.messageHandlers.wxLogin.postMessage("");
      } else if (isHM && (window as any).harmony?.wxLogin) {
        (window as any).harmony.wxLogin();
      } else {
        console.warn("[WX_BIND] Unsupported platform for wechat binding", {
          isAndroid,
          isIos,
          hasWxLoginObj: !!(window as any).wxLogin,
          hasWxLoginFn: !!(window as any).wxLogin?.wxLogin,
          hasIOSHandler: !!(window as any).webkit?.messageHandlers?.wxLogin,
        });
        Toast.info("当前平台不支持微信绑定");
        (window as any).wxLoginCode = null;
      }
    } catch (e) {
      console.error("[WX_BIND] Exception when invoking native wechat auth:", e);
      Toast.info("调用微信授权失败");
      (window as any).wxLoginCode = null;
    }
  };

  const handleClose = () => {
    setPopupVisible(false);
  };

  const handleConfirm = (index: number) => {
    if (index === 0) {
      // 解绑微信 - 需要二次确认
      const userInfo: any = Taro.getStorageSync("userInfo") || {};
      if (!userInfo.openid) {
        Toast.info("当前未绑定微信");
        setPopupVisible(false);
        return;
      }
      setPopupVisible(false);
      Dialog.confirm({
        title: "",
        children: "确定要解绑微信号吗？",
        platform: "ios",
        okText: "解绑",
        cancelText: "取消",
        onOk: () => {
          const params = { type: 32, openid: userInfo.openid } as any;
          unbindWechat(params).then((res: any) => {
            if (res && res.code === 0) {
              const newUserInfo = { ...userInfo };
              delete newUserInfo.openid;
              Taro.setStorageSync("userInfo", newUserInfo);
              Toast.success("微信号已解绑");
              setWxNickname("");
            } else {
              Toast.info(res.msg || "解绑失败");
            }
          });
        },
        onCancel: () => {
          // no-op
        }
      });
    } else if (index === 1) {
      // 绑定/更换微信：调用原生授权 -> 回调 code -> 绑定接口 -> 更新本地 userInfo
      setPopupVisible(false);
      Dialog.confirm({
        title: "",
        children: "确定要换绑微信号吗？",
        platform: "ios",
        okText: "换绑",
        cancelText: "取消",
        onOk: () => {
          startWechatBind();
        }
      });
    }
  };

  // 进入页面时，尝试获取微信绑定信息并显示昵称，同时获取缓存大小
  useEffect(() => {
    let uaAll = window.navigator.userAgent;
    let isAndroid = uaAll.indexOf(`android_${APP_NAME}`) > -1;
    let isIos = uaAll.indexOf(`ios_${APP_NAME}`) > -1;
    let isHM = uaAll.indexOf(`hm_${APP_NAME}`) > -1;
    if (isAndroid) {
      setPlatform("Android");
    } else if (isIos) {
      setPlatform("IOS");
    } else if (isHM) {
      setPlatform("HM");
    } else {
      setPlatform("H5");
    }
    if (window.__wxjs_environment === "miniprogram") {
      setPlatform("WX");
    }
    getWechatUser({ type: 32 }).then((r: any) => {
      if (r && r?.code === 0) {
        if(r?.data?.nickname) {
          setWxNickname(r?.data?.nickname || "");
        } else {
          getWechatUser({ type: 31 }).then((r: any) => {
            if (r && r?.code === 0) {
              setWxNickname(r?.data?.nickname || "");
            } else {
              setWxNickname("");
            }
          });
        }
      } else {
        setWxNickname("");
      }
    }).catch(() => setWxNickname(""));

    // 获取缓存大小
    getCacheSize();
  }, []);

  return (
    <View className="setPageContent">
      {platform !== "WX" && <YkNavBar title="设置" />}

      <View className="module">
        <View className="module-title">
          <Text className="module-title-text">账号与安全</Text>
        </View>
        <View className="module-content">
          <Cell.Group bordered={false}>
            {/* 绑定手机号 */}
            <Cell
              label="绑定手机号"
              className="module-content-cell"
              text={showMobilePhone()}
              showArrow
              onClick={getInPageBindMobilePhone}
            ></Cell>
            {/* 重置密码 */}
            <Cell
              label="重置密码"
              className="module-content-cell"
              text=""
              showArrow
              onClick={getInLoginedResetPassword}
            ></Cell>
            {/* 绑定微信 */}
            {platform !== "WX" && (
            <Cell
              label="绑定微信"
              className="module-content-cell"
              text={showWechatNickname()}
              showArrow={!wxNickname}
              onClick={!wxNickname? handleBindWechat: () => {}}
            ></Cell>  )}
          </Cell.Group>
        </View>
      </View>

      <View className="module">
        <View className="module-title">
          <Text className="module-title-text">系统设置</Text>
        </View>
        <View className="module-content">
          <Cell.Group bordered={false}>
            {/* 系统权限管理 */}
            {
              platform !== "HM" && platform !== "WX" && (
            <Cell
              label="系统权限管理"
              className="module-content-cell"
              text=""
              showArrow
              onClick={() => {
                Taro.navigateTo({
                  url: "/pageSetting/permissionSetting/index",
                });
              }}
            ></Cell>
            )}
            {/* 颜色设置 */}
            <Cell
              label="颜色设置"
              className="module-content-cell"
              text=""
              showArrow
              onClick={() => {
                Taro.navigateTo({
                  url: "/pageSetting/colorSettings/index",
                });
              }}
            ></Cell>
          </Cell.Group>
        </View>
      </View>

      <View className="module module-bottom">
        <View className="module-title">
          <Text className="module-title-text">其他</Text>
        </View>
        <View className="module-content">
          <Cell.Group bordered={false}>
            {/* 关于我们 */}
            <Cell
              label="关于我们"
              className="module-content-cell"
              text=""
              showArrow
              onClick={() => {
                Taro.navigateTo({
                  url: "/pageSetting/aboutUs/index",
                });
              }}
            ></Cell>
            <Cell
              label="意见反馈"
              className="module-content-cell"
              text=""
              showArrow
              onClick={() => {
                Taro.navigateTo({
                  url: `/pageDynamic/report/reportType/index?type=feedback`,
                });
              }}
            ></Cell>
            {/* 清理缓存 */}
            <Cell
              label="清理缓存"
              className="module-content-cell"
              text={storageSize}
              showArrow
              onClick={clearCache}
            ></Cell>
            {/* 账号注销 */}
            <Cell
              label="账号注销"
              className="module-content-cell"
              text=""
              showArrow
              onClick={() => {
                Taro.navigateTo({
                  url: "/pageSetting/accountCancellation/index",
                });
              }}
            ></Cell>
            {/* 个人信息收集清单 */}
            <Cell
              label="个人信息收集清单"
              className="module-content-cell"
              text=""
              showArrow
              onClick={() => {
                Taro.navigateTo({
                  url: "/pageSetting/PICC/index",
                });
              }}
            ></Cell>
            {/* 第三方SDK清单 */}
            {platform !== "WX" && (
            <Cell
              label="第三方SDK清单"
              className="module-content-cell"
              text=""
              showArrow
              onClick={() => {
                Taro.navigateTo({
                  url: `/pageSetting/webView/index?url=${THIRD_SDK}&name=第三方SDK清单`,
                });
              }}
            ></Cell>)}
            {/* 版本更新 */}
            {platform !== "WX" && (
            <Cell
              label="版本更新"
              className="module-content-cell"
              text={appVersion}
              showArrow
              onClick={handleVersionUpdate}
            ></Cell>)}
          </Cell.Group>
        </View>
      </View>

      <View className="module">
        {/* <Button
          className="moduleBtn moduleBtn-ghost"
          type="ghost"
          shape="square"
          needActive={false}
          onClick={() => Toast.info('开发中')}
        >
          <Text className="moduleBtn-text">切换账号</Text>
        </Button> */}

        <Button
          className="moduleBtn moduleBtn-primary"
          type="primary"
          shape="square"
          needActive={false}
          onClick={() => exitLogin()}
        >
          <Text className="moduleBtn-text">退出登录</Text>
        </Button>
      </View>

      {/* 取消解绑换绑*/}
      {false && 
        <BottomPopup
          options={["解绑", "更换微信"]}
          btnCloseText="取消"
          onConfirm={handleConfirm}
          onClose={handleClose}
          visible={isPopupVisible}
        />
      }
    </View>
  );
}
