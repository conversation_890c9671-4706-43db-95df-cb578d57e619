import Taro from "@tarojs/taro";
import sign from "@/utils/sign/sign.js"; // 若有 ts 版本可改为 .ts
import PostEventSource from "@/utils/api/fetch-post-sse-parser";
import { URL_BASE } from '@/utils/api/urls';

/**
 * 获取平台信息
 */
const getPlatform = () => {
  let http_platform = "H5";

  if (typeof window !== 'undefined') {
    const uaAll = window.navigator.userAgent;
    const isAndroid = uaAll.indexOf(`android_${typeof APP_NAME !== 'undefined' ? APP_NAME : 'default_app'}`) > -1;
    const isIos = uaAll.indexOf(`ios_${typeof APP_NAME !== 'undefined' ? APP_NAME : 'default_app'}`) > -1;
    const isHM = uaAll.indexOf(`hm_${typeof APP_NAME !== 'undefined' ? APP_NAME : 'default_app'}`) > -1;
    if (isAndroid) {
      http_platform = 'android';
    }  else if (isIos) {
      http_platform = 'ios';
    } else if (isHM) {
      http_platform = 'hm';
    }
  }

  return http_platform;
};

/**
 * 构建请求URL
 */
const buildUrl = (url: string) => {
  if (url.indexOf("http") !== -1) {
    return url;
  }

    return URL_BASE + url;
};

/**
 * 处理表单数据
 */
const processFormData = (data: any, contentType?: 'json' | 'form') => {
  if (contentType === 'form' && data && typeof data === 'object') {
    // 转换为 URLSearchParams 格式
    const formData = new URLSearchParams();
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        formData.append(key, String(value));
      }
    });
    return formData.toString();
  }
  return data || {};
};

/**
 * 构建签名请求头
 */
const buildSignedHeaders = (extraHeaders: Record<string, any> = {}) => {
  const userInfo: any = Taro.getStorageSync("userInfo");
  const http_platform = getPlatform();

  // 设置用户信息和签名
  if (!!userInfo) {
    sign.setUserId(userInfo.id);
    sign.setToken(userInfo.token);
  }
  sign.setPlatform(http_platform);

  // 获取签名
  const signHeaders = sign.getSign();

  // 构建基础请求头
  let headers: Record<string, any> = {
    "tenant-id": 1,
    "Accept": "text/event-stream",
    "Cache-Control": "no-cache",
    "Connection": "keep-alive",
    ...extraHeaders
  };

  // 添加签名头
  Object.assign(headers, signHeaders);

  // 添加用户认证头
  if (userInfo?.accessToken) {
    headers['Authorization'] = userInfo.accessToken;
  }

  return headers;
};

/**
 * SSE请求函数
 * 支持旧的调用方式: requestSSE(url, method, data)
 * 支持新的调用方式: requestSSE({ url, method, data, contentType })
 */
const requestSSE = (
  options: any,
  method?: string,
  data?: any
) => {
  // 兼容旧的调用方式
  let requestOptions: any;
  if (typeof options === 'string') {
    requestOptions = {
      url: options,
      method: method || 'POST',
      data: data || {},
      contentType: 'json',
      autoReconnect: true,
      retryInterval: 3000
    };
  } else {
    requestOptions = {
      autoReconnect: true,
      retryInterval: 3000,
      ...options
    };
  }

  const {
    url,
    method: requestMethod = 'POST',
    data: requestData = {},
    contentType = 'json',
    headers: extraHeaders = {},
    retryInterval = 3000,
    autoReconnect = true
  } = requestOptions;

  const urlPath = buildUrl(url);
  
  // 构建签名请求头
  const headers = buildSignedHeaders(extraHeaders);

  // 根据内容类型设置Content-Type
  if (contentType === 'json') {
    headers['Content-Type'] = 'application/json';
  } else if (contentType === 'form') {
    headers['Content-Type'] = 'application/x-www-form-urlencoded';
  }

  // 处理请求数据
  const processedData = processFormData(requestData, contentType);
  
  // 如果是表单数据，转换为字符串
  const body = contentType === 'form' ? processedData : JSON.stringify(processedData);

  console.log('[SSE] 发起SSE请求:', {
    url: urlPath,
    method: requestMethod,
    headers: headers,
    body: body
  });

  // 创建PostEventSource实例
  const sseClient = new PostEventSource(urlPath, {
    method: requestMethod,
    headers,
    body,
    autoReconnect
  } as any);

  // 设置重连间隔
  if (retryInterval) {
    (sseClient as any).retry = retryInterval;
  }

  // 添加默认错误处理
  sseClient.addEventListener('error', (error: any) => {
    console.error('[SSE] 连接错误:', error);
  });

  // 添加连接状态监听
  sseClient.addEventListener('open', () => {
    console.log('[SSE] 连接已建立');
  });

  sseClient.addEventListener('close', () => {
    console.log('[SSE] 连接已关闭');
  });

  return sseClient;
};

/**
 * AI聊天专用的SSE客户端工厂函数
 */
export const createAiChatSSEClient = (options: any = {}) => {
  const {
    conversationId,
    content,
    useContext = true,
    ...otherOptions
  } = options;

  if (!conversationId || !content) {
    throw new Error('conversationId 和 content 是必需的参数');
  }

  return requestSSE({
    url: '/app-api/ai/chat/message/send-stream',
    method: 'POST',
    data: {
      conversationId,
      content,
      useContext
    },
    contentType: 'json',
    ...otherOptions
  });
};

/**
 * 通用SSE客户端工厂函数
 */
export const createSSEClient = (url: string, options: any = {}) => {
  return requestSSE({
    url,
    ...options
  });
};

export default requestSSE;


