/**
 * 通用工具函数
 */

/**
 * 节流函数
 * @param func 要节流的函数
 * @param wait 等待时间（毫秒）
 * @param options 配置选项
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number = 250,
  options: { leading?: boolean; trailing?: boolean } = {}
): T & { cancel: () => void } {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastExecTime = 0;
  const { leading = true, trailing = true } = options;

  const throttled = ((...args: any[]) => {
    const now = Date.now();

    if (!lastExecTime && !leading) {
      lastExecTime = now;
    }

    const remaining = wait - (now - lastExecTime);

    if (remaining <= 0 || remaining > wait) {
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
      lastExecTime = now;
      func.apply(this, args);
    } else if (!timeoutId && trailing) {
      timeoutId = setTimeout(() => {
        lastExecTime = leading ? Date.now() : 0;
        timeoutId = null;
        func.apply(this, args);
      }, remaining);
    }
  }) as T & { cancel: () => void };

  throttled.cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
    lastExecTime = 0;
  };

  return throttled;
}


// 日期格式化工具函数
export const formatDate = (timestamp: number, separator: string = ''): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
  // const dayBeforeYesterday = new Date(today.getTime() - 2 * 24 * 60 * 60 * 1000);
  
  // 判断是否是今天、昨天、前天
  if (date.toDateString() === today.toDateString()) {
    return "今天";
  } else if (date.toDateString() === yesterday.toDateString()) {
    return "昨天";
  // } else if (date.toDateString() === dayBeforeYesterday.toDateString()) {
  //   return "前天";
  }
  
  // 判断是否是当年
  if (date.getFullYear() === now.getFullYear()) {
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    if (separator === '') {
      return `${month}月${day}日`;
    }
    return `${month}${separator}${day}`;
  } else {
    // 往年格式
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    if (separator === '') {
      return `${year}年${month}月${day}日`;
    }
    return `${year}${separator}${month}${separator}${day}`;
  }
};

/**
 * 获取CSS变量值的工具函数
 * @param variableName CSS变量名（如：--primary-color）
 * @param fallback 获取失败时的回退值，默认为项目主题蓝色
 * @returns CSS变量的值或回退值
 */
export const getCSSVariableValue = (variableName: string, fallback: string = '#165DFF'): string => {
  try {
    // 在浏览器环境中获取CSS变量
    if (typeof window !== 'undefined' && document.documentElement) {
      const value = getComputedStyle(document.documentElement).getPropertyValue(variableName).trim();
      return value || fallback;
    }
    return fallback;
  } catch (error) {
    console.warn('获取CSS变量失败:', error);
    return fallback;
  }
};

/**
 * 银行名称到短名称的映射表
 */
const BANK_SHORT_NAME_MAP: { [key: string]: string } = {
  // 标准短名称映射
  '招商银行': '招商银行',
  '工商银行': '工商银行',
  '建设银行': '建设银行',
  '农业银行': '农业银行',
  '民生银行': '民生银行',
  '兴业银行': '兴业银行',
  '平安银行': '平安银行',
  '交通银行': '交通银行',
  '中信银行': '中信银行',
  '光大银行': '光大银行',
  '上海银行': '上海银行',
  '华夏银行': '华夏银行',
  '广发银行': '广发银行',
  '南京银行': '南京银行',
  '宁波银行': '宁波银行',
  '邮政储蓄银行': '邮政储蓄银行',
  
  // 浦发银行的别名映射
  '上海浦东发展银行': '浦发银行',
  '浦发银行': '浦发银行',
  
  // 中国银行别名
  '中国银行': '中国银行',
  '中国工商银行': '工商银行',
  '中国农业银行': '农业银行',
  '中国建设银行': '建设银行',
  '中国交通银行': '交通银行',
  '中国民生银行': '民生银行',
  '中国光大银行': '光大银行',
  '中国华夏银行': '华夏银行',
  '中国中信银行': '中信银行',
  '中国招商银行': '招商银行',
};

/**
 * 将银行名称转换为标准短名称
 * @param bankName 银行名称（完整名称或简称）
 * @returns 标准短名称，未匹配返回"其他银行"
 */
export const convertBankNameToShortName = (bankName: string): string => {
  if (!bankName || typeof bankName !== 'string') {
    return '其他银行';
  }

  // 去除首尾空格
  const trimmedName = bankName.trim();
  if (!trimmedName) {
    return '其他银行';
  }

  // 直接匹配
  if (BANK_SHORT_NAME_MAP[trimmedName]) {
    return BANK_SHORT_NAME_MAP[trimmedName];
  }

  // 模糊匹配：查找包含关键词的银行
  
  // 遍历映射表，进行关键词匹配
  for (const [key, value] of Object.entries(BANK_SHORT_NAME_MAP)) {
    if (trimmedName.includes(key) || key.includes(trimmedName)) {
      return value;
    }
  }

  // 特殊处理：浦发银行关键词匹配
  if (trimmedName.includes('浦发') || trimmedName.includes('浦东发展')) {
    return '浦发银行';
  }

  return '其他银行';
};