import React, { useState, useEffect , useRef } from 'react';
import Taro from "@tarojs/taro";
import { View, Text } from '@tarojs/components';
import { Dialog } from "@arco-design/mobile-react";
import Album from "@/components/Album";
import { toast } from "@/utils/yk-common";
import wx from "weixin-webview-jssdk";
import { getMyAlbumList, getUserHomeTopData, deleteDynamic, updateDynamic } from "@/utils/api/common/common_user";
import './index.less';
import { useSetPermission } from "@/stores/permissionStore";
import {
  AuthTypes,
  AuthStatusAndroid,
  AuthStatusIos,
} from "@/utils/config/authTypes";
import PermissionPopup from "@/components/PermissionPopup";
import { usePermission } from "@/hooks/usePermission";
import { useGlobalCallbacks } from "@/utils/globalCallbackManager";
interface Tag {
  id: number;
  userId: number | null;
  parentId: number | null;
  dynamicsId: string | null;
  name: string;
  coverImage: string;
  type: number;
  sort: number | null;
  isTop: number | null;
  sortType: number | null;
  children: Tag[];
}

export default function TagResult() {
  // 从路由参数获取数据
  const { userId, tagIds, tagNames } = Taro.getCurrentInstance().router?.params || {};
  const [decodedTagNames, setDecodedTagNames] = useState<string>('');
  const [selectedTags, setSelectedTags] = useState<any[]>([]);
  const [refreshKey, setRefreshKey] = useState(0);
  const currentItem = useRef<any>(null);
  // 获取当前用户信息判断是否为自己的相册
  const localUserInfo: any = Taro.getStorageSync('userInfo') || {};
  const isOwnAlbum = localUserInfo?.id?.toString() === userId?.toString();
 // 自定义权限同意处理，处理首页特有的逻辑
 const customWebPermissonConsent = () => {
  // 下载操作
  if (platformRef.current === "Android") {
    downloadConfirm(currentItem.current);
  } else if (platformRef.current === "IOS") {
    window.webkit.messageHandlers.checkPermission.postMessage("");
  } 
return true;
};

// 使用全局权限管理
const {
initPermissions,
hasPermission,
requestPermission,
webPermissonConsent,
webPermissonDeny,
permissionPopupProps,
platformRef,
authConfirmType,
} = usePermission(customWebPermissonConsent);
  // 注册 H5/原生容器下载回调，避免原生回调调用未定义函数
  useEffect(() => {
    const cleanup = initPermissions();

     // 定义下载回调函数
     const webDownloadSuc = () => {
      if(currentItem.current.pictures&&currentItem.current.content){
        toast("success", {
          content: "图片下载成功，动态文案已复制",
          duration: 2000,
        });
      }
  
      if(currentItem.current.pictures&&currentItem.current.content===""){
        toast("success", {
          content: "图片下载成功",
          duration: 2000,
        });
      }
    };

    const webDownloadFail = () => {
      try {
        toast('error', { content: '下载失败', duration: 2000 });
      } catch (e) {}
    };

    // 使用全局回调管理器注册下载回调
    const callbackCleanup = useGlobalCallbacks('tagResult', {
      webDownloadSuc: webDownloadSuc,
      webDownloadFail: webDownloadFail,
    });

    return () => {
      try {
        callbackCleanup && callbackCleanup();
        cleanup && cleanup();
      } catch (e) {}
    };
  }, []);

  useEffect(() => {
    if (tagNames) {
      const decodedNames = decodeURIComponent(tagNames);
      setDecodedTagNames(decodedNames);
      
      // 解析标签数据，假设tagIds和tagNames用逗号分隔且一一对应
      if (tagIds && tagNames) {
        const ids = tagIds.split(',');
        const names = decodedNames.split(',');
        const tags = ids.map((id, index) => ({
          id: parseInt(id),
          name: names[index] || `标签${index + 1}`
        }));
        setSelectedTags(tags);
      }
    }
  }, [tagNames, tagIds]);

  // 获取用户信息
  const handleGetUserInfo = async (targetUserId: string | number) => {
    try {
      const res: any = await getUserHomeTopData({ userId: targetUserId, homePageCountType: 2 });
      if (res && res.code === 0 && res.data) {
        return res.data;
      }
      return {};
    } catch (error) {
      console.error('getUserInfo failed:', error);
      return {};
    }
  };

  // 获取相册列表（按标签筛选）
  const handleGetAlbumList = async (params: any) => {
    try {
      // 使用当前选中的标签ID进行筛选
      const currentTagIds = selectedTags.map(tag => tag.id).join(',');
      const filteredParams = {
        ...params,
        tagIds: currentTagIds || tagIds, // 优先使用当前选中的标签，回退到原始标签
        homePageCountType: 1
      };
      
      const res: any = await getMyAlbumList(filteredParams);
      return res;
    } catch (error) {
      console.error('获取相册列表失败:', error);
      return { code: -1, msg: '获取失败' };
    }
  };

  // 删除动态（仅自己的相册可用）
  const handleDelete = async (id: string | number): Promise<any> => {
    if (!isOwnAlbum) {
      toast("error", { content: '无权限操作', duration: 2000 });
      return { code: -1, msg: '无权限' };
    }

    return new Promise((resolve) => {
      Dialog.confirm({
        platform: 'ios',
        className: 'dialog-input-demo',
        title: '温馨提示',
        contentAlign: 'left',
        children: (
          <div className="dialog-input-demo-hint">
            删除动态后不可恢复，确认删除？
          </div>
        ),
        onOk: async () => {
          Taro.showLoading({
            title: '删除中...',
            mask: true
          });
          
          try {
            const res: any = await deleteDynamic(id);
            if (res && res.code === 0) {
              setTimeout(() => {
                Taro.hideLoading();
                toast("success", {
                  content: '删除成功',
                  duration: 2000
                });
                // 删除成功后刷新数据
                setRefreshKey(prev => prev + 1);
                resolve(res);
              }, 1000);
            }
          } catch (error) {
            Taro.hideLoading();
            toast("error", {
              content: '删除失败，请重试',
              duration: 2000
            });
            console.error('删除失败:', error);
            resolve({ code: -1, msg: '删除失败' });
          }
        },
        onCancel: () => {
          console.log('用户取消了操作');
          resolve({ code: -2, msg: '用户取消' });
        },
      });
    });
  };

  // 编辑（仅自己的相册可用）
  const handleEdit = (item: any) => {
    if (!isOwnAlbum) {
      toast("error", { content: '无权限操作', duration: 2000 });
      return;
    }
    
    Taro.setStorageSync('releaseDynamicList', item);
    Taro.navigateTo({
      url: `/pageDynamic/releaseDynamic/index?type=2`
    });
  };

  // 查看详情
  const handleDetail = (item: any) => {
    Taro.navigateTo({
      url: `/pageDynamic/detail/index?dynamicId=${item.id}&dynamicUserId=${item.userId}`
    });
  };

    // 下载
    const handleDownload = (item: any) => {
      currentItem.current = item;
      if (platformRef.current === "HM"  || platformRef.current === "WX") {
        downloadConfirm(item);
      }else{
        // 检查存储权限
        if (!hasPermission(AuthTypes.STORAGE)) {
          console.log("没有权限");
          // 如果没有权限，请求权限
          requestPermission(AuthTypes.STORAGE);
          return;
        }
        // 有权限则执行下载
        downloadConfirm(item);
      }
    };
  
    const downloadConfirm = (item: any) => {
      let imageList = item.pictures.split(",");
      if(platformRef.current === "Android"){
        window.downloadImg.downloadImg(item.pictures);
      }
      else if(platformRef.current === "HM"){
        window.harmony.downloadImg(item.pictures);
      }else if(platformRef.current === "WX"){
        let imgs = imageList.join("|");
        wx.miniProgram.navigateTo({ url: "/pages/downloadImgs/index?imgs=" + encodeURIComponent(imgs) });
      }
      else{
      for (let i = 0; i < imageList.length; i++) {
        // if (platformRef.current === "Android") {
        //   window.downloadImg.downloadImg(imageList[i]);
        // } else 
        if (platformRef.current === "IOS") {
          window.webkit.messageHandlers.saveImgWithUrlStr.postMessage(imageList[i]);
        } 
        // else if (platformRef.current === "HM") {
        //   window.harmony.downloadImg(imageList[i]);
        // }
      }
      }
    };

  // 刷新（仅自己的相册可用）
  const handleRefresh = async (id: string | number) => {
    if (!isOwnAlbum) {
      toast("error", { content: '无权限操作', duration: 2000 });
      return;
    }

    try {
      const res: any = await updateDynamic({ id, isListed: 1 });
      if (res && res.code === 0) {
        toast("success", {
          content: '刷新成功',
          duration: 2000
        });
        // 刷新成功后重新加载数据
        setRefreshKey(prev => prev + 1);
      }
    } catch (error) {
      toast("error", {
        content: '刷新失败',
        duration: 2000
      });
    }
  };

  // 置顶/取消置顶（仅自己的相册可用）
  const handleToggleTop = async (id: string | number, isTop: number) => {
    if (!isOwnAlbum) {
      toast("error", { content: '无权限操作', duration: 2000 });
      return;
    }

    try {
      const res: any = await updateDynamic({ id, isTop });
      if (res && res.code === 0) {
        toast("success", {
          content: isTop === 1 ? '置顶成功' : '取消置顶成功',
          duration: 2000
        });
        
        // 置顶操作成功后，需要刷新数据
        // 由于置顶状态变化会影响数据排序，需要重新获取数据
        setTimeout(() => {
          // 延迟刷新，确保后端数据已更新
          setRefreshKey(prev => prev + 1);
        }, 500);
      }
    } catch (error) {
      toast("error", {
        content: '操作失败',
        duration: 2000
      });
    }
  };

  // 返回上一页
  const handleBack = () => {
    Taro.navigateBack();
  };

  return (
    <View className="tag-result-page">
      {/* 相册组件 */}
      <Album
        key={refreshKey} // 使用refreshKey作为key来强制重新渲染
        title={decodedTagNames}
        userId={userId}
        isOwnAlbum={isOwnAlbum}
        onGetUserInfo={handleGetUserInfo}
        onGetAlbumList={handleGetAlbumList}
        onDeleteDynamic={isOwnAlbum ? handleDelete : undefined}
        onUpdateDynamic={isOwnAlbum ? updateDynamic : undefined}
        onEdit={isOwnAlbum ? handleEdit : undefined}
        onDetail={handleDetail}
        onDownload={handleDownload}
        onRefresh={isOwnAlbum ? handleRefresh : undefined}
        onToggleTop={isOwnAlbum ? handleToggleTop : undefined}
        showCreate={false}
        showSort={false}
        showContactPopup={!isOwnAlbum} // 只有他人相册才显示联系弹窗
        showCartModal={!isOwnAlbum} // 只有他人相册才显示购物车弹窗
        showMore={!isOwnAlbum} // 只有他人相册才显示更多操作
        showBottomTabs={false}
        showTags={true}
        selectedTags={selectedTags}
        renderHeader={() => null} // 隐藏Album组件的头部，使用自定义头部
      />
    </View>
  );
} 