@import "@arco-design/mobile-react/style/mixin.less";

[id^="/pageOrder/skuEms/index"] {
  .order-details-box {
    width: 100%;
    min-height: 100vh;
    background: #f7f8fa;
    .use-dark-mode-query({
    background: var(--dark-background-color);    //白色字体
  });
    padding-bottom: 80px;

    .order-goods-card {
      background: #fff;
      .use-dark-mode-query({
      background-color: @dark-cell-background-color !important;
    });
      border-radius: 10px;
      margin: 16px;
      box-shadow: 0 2px 8px rgba(36, 104, 242, 0.03);
      padding: 16px;
    }

    // 新的订单信息行样式
    .order-info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
    }

    .order-info-label {
      font-size: 14px;
      color: #666;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      width: 80px;
      flex-shrink: 0;
    }

    .order-info-value {
      font-size: 14px;
      color: #333;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      flex: 1;
    }

    .order-info-value-row {
      display: flex;
      align-items: center;
      flex: 1;
      justify-content: space-between;

      .order-info-value-row-left {
        display: flex;
        align-items: center;
        gap: 8px; // 图标之间的间距
      }
    }

    .copy-icon {
      width: 16px;
      height: 16px;
      cursor: pointer;
      flex-shrink: 0;
    }

    .expand-icon {
      width: 16px;
      height: 16px;
      cursor: pointer;
      transition: transform 0.3s ease;
      flex-shrink: 0;

      &.expanded {
        transform: rotate(180deg);
      }
    }

    .expanded-time-info {
      margin-top: 8px;
      padding-top: 8px;
      border-top: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-top: 1px solid @dark-line-color;
    });
    }

    // 商品卡片样式
    .shop-header {
      display: flex;
      align-items: center;
      padding-bottom: 10px;
      border-top: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-top: 1px solid @dark-line-color;
    });
    }

    .shop-name {
      font-size: 14px;
      color: #1d2129;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      font-weight: 500;
      margin-right: 6px;
    }

    .verified-icon {
      width: 16px;
      height: 16px;
      margin-right: auto;
    }

    .shop-arrow {
      margin-left: auto;
    }

    .arrow-right {
      width: 16px;
      height: 16px;
    }

    // 使用订单列表的商品样式
    .goods-list-detail {
      // padding: 0 12px;
      padding-bottom: 10px;
    }

    .goods-item {
      display: flex;
      align-items: flex-start;
      margin-top: 12px;
    }

    .goods-img {
      width: 60px;
      height: 60px;
      border-radius: 8px;
      background: #f5f5f5;
      margin-right: 10px;
    }

    .goods-info-detail {
      flex: 1;
      min-width: 0;
      display: flex;
      flex-direction: column;
    }

    .goods-title-row {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
    }

    .goods-title {
      flex: 1;
      font-size: 14px;
      color: #222;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      margin-right: 8px;
      /* 显示两行，超出省略号 */
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .goods-price-section {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      flex-shrink: 0;
    }

    .goods-price {
      color: #f53f3f;
      font-weight: 600;
      font-size: 16px;
    }

    .goods-quantity {
      color: #888;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      font-size: 13px;
    }

    .goods-sku-ems {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 8px;

      .sku-text {
        font-size: 12px;
        color: #666;
        .use-dark-mode-query({
        color: @dark-font-color !important;
      });
      }

      .sku-quantity {
        font-size: 12px;
        color: #666;
        .use-dark-mode-query({
        color: @dark-font-color !important;
      });
      }
    }

    .sku-text {
      color: #666;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      font-size: 13px;
    }

    .sku-count {
      color: #888;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      font-size: 13px;
    }

    // 订单总计样式
    .order-summary-details {
      display: flex;
      flex-direction: column;
      align-items: space-between;
      padding-top: 10px;
      // padding: 16px;
      border-top: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-top: 1px solid @dark-line-color;
    });
    }

    .summary-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      &.total-row {
        margin-top: 8px;
        padding-top: 8px;
        border-top: 1px solid var(--line-color);
        .use-dark-mode-query({
        border-top: 1px solid @dark-line-color;
      });
      }
    }

    .summary-label {
      font-size: 12px;
      color: #86909c;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    }

    .summary-value {
      font-size: 12px;
      color: #1d2129;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    }

    .summary-total {
      font-size: 16px;
      color: #e35848;
      font-weight: 500;
    }

    .order-user-row {
      display: flex;
      align-items: center;
      padding: 16px 0 0 16px;
    }
    .order-user-name {
      font-size: 15px;
      font-weight: bold;
      color: @font-color;
    }
    .order-user-phone {
      font-size: 12px;
      color: #888;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      margin-top: 2px;
    }

    .order-remark {
      background: #fff;
      .use-dark-mode-query({
      background-color: @dark-cell-background-color !important;
    });
      border-radius: 10px;
      margin: 0 16px 16px 16px;
      box-shadow: 0 2px 8px rgba(36, 104, 242, 0.03);
    }

    .order-img-list {
      display: flex;
      flex-wrap: wrap;
      padding: 0 0 12px 0;
      margin: 0 16px 16px 16px;
    }

    .order-goods-title {
      display: flex;
      align-items: center;
      font-size: 15px;
      font-weight: bold;
      padding: 16px 0 0 16px;
    }
    .order-goods-item {
      display: flex;
      align-items: flex-start;
      padding: 12px 16px 0 16px;
    }
    .order-goods-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
    .order-goods-name {
      font-size: 14px;
      font-weight: bold;
      color: @font-color;
      margin-bottom: 6px;
    }
    .order-goods-spec {
      font-size: 12px;
      color: #888;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      margin-bottom: 8px;
    }
    .order-goods-price {
      font-size: 13px;
      color: #e35848;
      font-weight: bold;
    }
    .order-goods-summary {
      font-size: 13px;
      color: #333;
      padding: 0 16px 12px 16px;
      display: flex;
      flex-wrap: wrap;
      gap: 12px 24px;
      .order-goods-actual {
        color: #e35848;
        font-weight: bold;
      }
    }

    .order-footer-bar-sell {
      height: 50px;
      position: fixed;
      left: 0;
      right: 0;
      bottom: 0;
      background: #fff;
      .use-dark-mode-query({
      background-color: @dark-background-color !important;
    });
      box-shadow: 0 -2px 8px rgba(36, 104, 242, 0.03);
      padding: 10px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      z-index: 10;
      .order-footer-bar-icon {
        width: 20px;
        height: 20px;
      }
      .order-footer-bar-btn {
        background: #2468f2;
        color: #fff;
        padding: 6px 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
      }

      .order-info-btn-row {
        display: flex;
        align-items: center;
        gap: 15px;
      }

      .order-info-cancel-btn {
        border: 1px solid #2468f2;
        color: #2468f2;
        padding: 6px 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
      }
    }
  }

  .empty-order {
    text-align: center;
    color: #bbb;
    font-size: 15px;
    padding: 48px 0 32px 0;
  }

  // 物流模块样式
  .logistics-card {
    background: #fff;
    .use-dark-mode-query({
    background-color: @dark-card-background-color !important;
  });
    border-radius: 10px;
    margin: 16px;
    box-shadow: 0 2px 8px rgba(36, 104, 242, 0.03);
    padding: 12px;
  }

  .logistics-card-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .logistics-card-content-time {
      font-size: 14px;
      color: #86909c;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    }

    .logistics-card-content-status {
      font-size: 14px;
      color: #1d2129;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
    }
  }

  .logistics-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-bottom: 1px solid @dark-line-color;
  });
  }

  .logistics-header {
    background: #f7f8fa;
    padding: 10px;
    border-radius: 4px;
    .use-dark-mode-query({
    background-color: @dark-cell-background-color !important;
  });
    margin-bottom: 12px;
  }

  .logistics-status-row {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .logistics-status {
    font-size: 14px;
    color: #1d2129;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-weight: 500;
  }

  .logistics-time {
    font-size: 12px;
    color: #86909c;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
  }

  .logistics-tracking-row {
    display: flex;
    align-items: center;
    padding: 12px 0 0 0;
    gap: 10px;
  }

  .logistics-tracking-btn {
    padding: 4px 16px;
    font-size: 12px;
    color: #165dff;
    border-radius: 100px;
    border: 1px solid #165dff;
  }

  .logistics-label {
    font-size: 12px;
    color: #86909c;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
  }

  .logistics-tracking-info {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .logistics-tracking-number {
    font-size: 12px;
    color: #1d2129;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
  }

  .logistics-goods-list {
    margin-bottom: 12px;
  }

  .logistics-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 12px;
    border-top: 1px solid var(--line-color);
    .use-dark-mode-query({
    border-top: 1px solid @dark-line-color;
  });
  }

  .logistics-summary-text {
    font-size: 12px;
    color: #86909c;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
  }

  .logistics-summary-count {
    font-size: 12px;
    color: #1d2129;
    .use-dark-mode-query({
    color: @dark-font-color !important;
  });
    font-weight: 500;
  }

  // 修改快递单号弹框样式
  .tracking-modal {
    .tracking-modal-content {
      background: #fff;
      .use-dark-mode-query({
      background-color: @dark-card-background-color !important;
    });
      border-radius: 16px 16px 0 0;
      padding: 0;
      max-height: 80vh;
    }

    .tracking-modal-header {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 16px;
      border-bottom: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-bottom: 1px solid @dark-line-color;
    });
      position: relative;

      .close-icon {
        position: absolute;
        left: 16px;
        width: 12px;
        height: 12px;
        cursor: pointer;
      }

      .tracking-modal-title {
        font-size: 16px;
        font-weight: 500;
        color: #1d2129;
        .use-dark-mode-query({
        color: @dark-font-color !important;
      });
      }
    }

    .tracking-modal-body {
      padding: 20px 16px;
    }

    .tracking-input-row {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .tracking-input-label {
      font-size: 14px;
      color: #1d2129;
      .use-dark-mode-query({
      color: @dark-font-color !important;
    });
      width: 80px;
      flex-shrink: 0;
    }

    .tracking-input {
      flex: 1;
      padding: 0 12px;
      font-size: 14px;
      color: #1d2129;
      .use-dark-mode-query({
      color: @dark-font-color !important;
      background-color: @dark-card-background-color !important;
    });

      &::placeholder {
        color: #86909c;
      }
    }

    .tracking-select {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 0 0 12px;
      cursor: pointer;
      .use-dark-mode-query({
      background-color: @dark-card-background-color !important;
    });

      .tracking-select-text {
        font-size: 14px;
        color: #1d2129;
        .use-dark-mode-query({
        color: @dark-font-color !important;
      });

        &.placeholder {
          color: #86909c;
        }
      }

      .arrow-right {
        width: 16px;
        height: 16px;
      }
    }

    .tracking-modal-footer {
      padding: 16px;
      border-top: 1px solid var(--line-color);
      .use-dark-mode-query({
      border-top: 1px solid @dark-line-color;
    });

      .tracking-save-btn {
        width: 100%;
        height: 44px;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 500;
      }
    }
  }
}
