// 原生App与H5页面通信桥梁 - 基于WebViewActivity.java实际接口
// Native App and H5 communication bridge - Based on actual WebViewActivity.java interfaces
// 苹果内购结果接口
export interface ApplePurchaseResult {
  jws: string;
  transactionId: string;
}

// 恢复购买结果接口
export interface RestorePurchasesResult {
  restoredTransactions: Array<{
    jws: string;
    transactionId: string;
    productId: string;
  }>;
}

// 原生桥接管理类 - 基于WebViewActivity.java实际接口
export class NativeBridge {
  // ==================== 苹果内购功能 ====================

  // 发起苹果内购
  static purchaseProduct(productId: string): Promise<ApplePurchaseResult> {
    return new Promise((resolve, reject) => {

      if (!productId || typeof productId !== 'string') {
        reject(new Error('产品ID不能为空'));
        return;
      }

      try {
        // 设置苹果内购回调监听
        const originalSuccessCallback = window.purchaseProduct;
        const originalErrorCallback = window.purchaseProductError;

        window.purchaseProduct = (jws: string, transactionId: string) => {
          // 恢复原回调
          window.purchaseProduct = originalSuccessCallback;
          window.purchaseProductError = originalErrorCallback;
          resolve({ jws, transactionId });
        };

        window.purchaseProductError = (errorMessage: string) => {
          // 恢复原回调
          window.purchaseProduct = originalSuccessCallback;
          window.purchaseProductError = originalErrorCallback;
          reject(new Error(errorMessage || '苹果内购失败'));
        };

        // 调用原生苹果内购
        window.webkit?.messageHandlers?.purchaseProduct?.postMessage(productId);

        // 设置超时（苹果内购可能需要较长时间）
        setTimeout(() => {
          window.purchaseProduct = originalSuccessCallback;
          window.purchaseProductError = originalErrorCallback;
          reject(new Error('苹果内购超时'));
        }, 600000); // 600秒超时
      } catch (error) {
        console.error('发起苹果内购失败:', error);
        reject(error);
      }
    });
  }

  // 完成交易
  static finishTransaction(transactionId: string): Promise<string> {
    return new Promise((resolve, reject) => {

      if (!transactionId || typeof transactionId !== 'string') {
        reject(new Error('交易ID不能为空'));
        return;
      }

      try {
        // 设置完成交易回调监听
        const originalSuccessCallback = window.finishTransactionSuccess;
        const originalErrorCallback = window.finishTransactionError;

        window.finishTransactionSuccess = (completedTransactionId: string) => {
          // 恢复原回调
          window.finishTransactionSuccess = originalSuccessCallback;
          window.finishTransactionError = originalErrorCallback;
          resolve(completedTransactionId);
        };

        window.finishTransactionError = (errorMessage: string) => {
          // 恢复原回调
          window.finishTransactionSuccess = originalSuccessCallback;
          window.finishTransactionError = originalErrorCallback;
          reject(new Error(errorMessage || '完成交易失败'));
        };

        // 调用原生完成交易
        window.webkit?.messageHandlers?.finishTransaction?.postMessage(transactionId);

        // 设置超时
        setTimeout(() => {
          window.finishTransactionSuccess = originalSuccessCallback;
          window.finishTransactionError = originalErrorCallback;
          reject(new Error('完成交易超时'));
        }, 30000); // 30秒超时
      } catch (error) {
        console.error('完成交易失败:', error);
        reject(error);
      }
    });
  }

  // 恢复购买
  static restorePurchases(): Promise<RestorePurchasesResult> {
    return new Promise((resolve, reject) => {

      try {
        console.log('[Restore Purchases] 🔄 开始恢复购买...');
        
        // 设置恢复购买回调监听
        const originalSuccessCallback = window.restorePurchasesSuccess;
        const originalErrorCallback = window.restorePurchasesError;

        window.restorePurchasesSuccess = (restoredTransactionsJson: string) => {
          console.log('[Restore Purchases] ✅ 恢复购买成功:', restoredTransactionsJson);
          
          // 恢复原回调
          window.restorePurchasesSuccess = originalSuccessCallback;
          window.restorePurchasesError = originalErrorCallback;
          
          try {
            // 解析JSON字符串
            const restoredTransactions = JSON.parse(restoredTransactionsJson);
            resolve({ restoredTransactions });
          } catch (parseError) {
            console.error('[Restore Purchases] ❌ 解析恢复购买结果失败:', parseError);
            reject(new Error('解析恢复购买结果失败'));
          }
        };

        window.restorePurchasesError = (errorMessage: string) => {
          console.log('[Restore Purchases] ❌ 恢复购买失败:', errorMessage);
          
          // 恢复原回调
          window.restorePurchasesSuccess = originalSuccessCallback;
          window.restorePurchasesError = originalErrorCallback;
          reject(new Error(errorMessage || '恢复购买失败'));
        };

        // 调用原生恢复购买
        console.log('[Restore Purchases] 📱 调用原生恢复购买方法...');
        window.webkit?.messageHandlers?.restorePurchases?.postMessage();

        // 设置超时（恢复购买可能需要较长时间）
        setTimeout(() => {
          console.log('[Restore Purchases] ⏰ 恢复购买超时');
          window.restorePurchasesSuccess = originalSuccessCallback;
          window.restorePurchasesError = originalErrorCallback;
          reject(new Error('恢复购买超时'));
        }, 300000); // 300秒超时
      } catch (error) {
        console.error('[Restore Purchases] ❌ 恢复购买异常:', error);
        reject(error);
      }
    });
  }
}

// 导出默认实例
export default NativeBridge;